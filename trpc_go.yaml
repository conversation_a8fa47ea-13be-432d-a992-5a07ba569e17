global:                             #全局配置
  namespace: Development            #环境类型，分正式production和非正式development两种类型
  env_name: test                    #环境名称，非正式环境下多环境的名称

server:                                            #服务端配置
  app: media_event_hub                                        #业务的应用名
  server: processor                             #进程服务名
  bin_path: /usr/local/trpc/bin/                   #二进制可执行文件和框架配置文件所在路径
  conf_path: /usr/local/trpc/conf/                 #业务配置文件所在路径
  data_path: /usr/local/trpc/data/                 #业务数据文件所在路径
  filter:                                          #针对所有service处理函数前后的拦截器列表
    - recovery                                     #拦截框架创建的业务处理协程panic
  service:                                         #业务服务提供的service，可以有多个
    - name: trpc.${app}.${server}.ori_event_queue
      address: **************:9092?topics=media_image_style_trans&group=cg_media_event_hub_processor&strategy=range&maxRetry=10
      protocol: kafka
      timeout: 10000000
    

client:                                            #客户端调用的后端配置
  timeout: 1000                                    #针对所有后端的请求最长处理时间
  namespace: Development                           #针对所有后端的环境
  filter:                                          #针对所有后端调用函数前后的拦截器列表
    - galileo
  service:                                         #针对单个后端的配置
    - name: trpc.media_event_hub.processor.Greeter      #后端服务的service name
      namespace: Development                   #后端服务的环境
      network: tcp                             #后端服务的网络类型 tcp udp 配置优先
      protocol: trpc               #应用层协议 trpc http
      target: ip://127.0.0.1:8000              #请求服务地址
      timeout: 1000                            #请求最长处理时间
    
plugins:
  config:
    tconf:                                                  # tconf远程配置中心的名字
      timeout: 3000                                         # 超时设置，单位毫秒
      address_list: ${tconf_address_list}                   # tconf云端寻址，**********:10054
      providers:
        - name: tconf                                       # provider名字，代码使用如：`config.WithProvider("tconf")`
          appid: ${app}.${server}                           # appid格式：应用名.服务名; tconf平台注册生成.
          env_name: ${env_name}                             # 环境信息
          namespace: ${namespace}                           # 配置命名空间
          tick: 2000                                        # 后台协程同步文件间隔，单位毫秒
  telemetry:
    galileo:
      verbose: error    # 用于诊断监控上报是否异常，输出到 trpc.log，取值范围[debug, info, error]，默认error
      config: # 本地配置
        metrics_config: # 指标配置
          enable: true  # 是否启用(当前不能单独关闭metric)
        traces_config: # Trace 配置
          enable: true    # 是否启用
          processor: # 数据处理相关配置
            sampler: # 采样器配置
              fraction: 1   # 采样比例，默认 0.0001
              error_fraction: 1  # 出现错误时的采样比例
            disable_trace_body: false          # 若为 true，则关闭 trace 中对 req 和 rsp 的上报，可以提高上报性能。默认 true。
            enable_deferred_sample: false     # 是否开启延迟采样，默认 false。
            deferred_sample_error: false      # 延迟采样 - 出错采样，默认 false。
            deferred_sample_slow_duration_ms: 1000    # 延迟采样 - 慢操作采样，慢操作阈值，单位 ms，默认 1000。
            disable_parent_sampling: false            # 忽略上游的采样结果，默认 false。
        logs_config: # 日志配置
          enable: true    # 是否启用
          processor: # 数据处理相关配置
            level: INFO  # 日志级别，取值范围： ERROR, INFO, DEBUG. 默认 ERROR, V0.2.5以前的版本，大小写敏感。
        version: 1        # 版本号，默认 0，此版本号用于控制远程配置和本地配置的优先级，版本号高的优先，一般设置成 1 即可。
      resource: # 资源信息，在 SDK 运行期间不会改变。resource 中的字段一般不需要配置，默认会填充。
        platform: PCG-123   # 服务部署的平台，如 PCG-123, STKE, DevNet , 默认 PCG-123

  registry:
    polaris:                                                                    #名字注册服务的远程对象
      register_self: false                                                 #是否框架自注册
      heartbeat_interval: ${polaris_heartbeat_interval} #名字注册服务心跳上报间隔
      address_list: ${polaris_address_grpc_list}             #名字服务远程地址列表, ip1:port1,ip2:port2,ip3:port3
      protocol: grpc                                                       #北极星交互协议支持 http，grpc，trpc

  selector:
    polaris:
      address_list: ${polaris_address_grpc_list}          #名字服务远程地址列表
      protocol: grpc                                                    #北极星交互协议支持 http，grpc，trpc
      discovery:
        refresh_interval: ${polaris_refresh_interval}  # 北极星服务发现刷新间隔，123默认10000，即10s

  log:
    default:
      - writer: galileo
      - writer: file                                 #本地文件日志
        level: info                                  #本地文件滚动日志的级别
        writer_config:                            #本地文件输出具体配置
          log_path: ${log_path}              #本地文件日志路径
          filename: trpc.log                    #本地文件日志文件名
          roll_type: size                          #文件滚动类型,size为按大小滚动
          max_age: 7                              #最大日志保留天数
          max_size: 10                            #本地文件滚动日志的大小 单位 MB
          max_backups: 10                     #最大日志文件数
          compress:  false                       #日志文件是否压缩

