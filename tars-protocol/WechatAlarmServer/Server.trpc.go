// Package WechatAlarmServer comment
// Code generated by trpc4tars 2.0. DO NOT EDIT.
// source: wechatAlarm.jce
package WechatAlarmServer

import (
	"context"
	"fmt"
	"unsafe"

	tars "git.code.oa.com/trpc-go/trpc-codec/tars"
	"git.code.oa.com/trpc-go/trpc-codec/tars/model"
	"git.code.oa.com/trpc-go/trpc-codec/tars/protocol/res/basef"
	"git.code.oa.com/trpc-go/trpc-codec/tars/protocol/res/requestf"
	"git.code.oa.com/trpc-go/trpc-codec/tars/protocol/wup"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/server"
	"git.woa.com/jce/jce"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = fmt.Errorf
var _ = jce.Marshal
var _ = unsafe.Pointer(nil)

/**********************************************************************************/
// Server
/**********************************************************************************/

// define service interface Server
type Server interface {
	Send(ctx context.Context, Message string, Receivers []string) (ret Result, err error)
}

// Send used to call the worker side implemnet for the method defined in the tars file
func Server_Send_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (rspbody interface{}, err error) {
	_req := &XXX_Server_Send_FunReq{}
	_rsp := &XXX_Server_Send_FunRsp{}

	_reqhead := tars.Request(ctx)
	_req.XXX_Version = _reqhead.IVersion
	_rsp.XXX_Version = _reqhead.IVersion

	filters, err := f(_req)
	if err != nil {
		return nil, err
	}

	handleFunc := func(ctx context.Context, reqbody interface{}, rspbody interface{}) error {
		req := reqbody.(*XXX_Server_Send_FunReq)
		rsp := rspbody.(*XXX_Server_Send_FunRsp)
		req.MakesureNotNil() // for json decode, pointer maybe nil
		rsp.MakesureNotNil() // for json decode, pointer maybe nil

		var err error
		rsp.Ret, err = svr.(Server).Send(ctx, req.Message, req.Receivers)
		if err != nil {
			return err
		}

		_ = req
		_ = rsp

		return nil
	}

	err = filters.Handle(ctx, _req, _rsp, handleFunc)
	if err != nil {
		return nil, err
	}

	return _rsp, nil
}

// descriptor for worker.RegisterService
var Server_ServiceDesc = server.ServiceDesc{
	HandlerType: ((*Server)(nil)),
	Methods: []server.Method{
		{Name: "Send", Func: Server_Send_Handler},
		{Name: "/Send", Func: Server_Send_Handler},
	},
}

// register service
func RegisterServerService(s server.Service, svr Server) error {
	return s.Register(&Server_ServiceDesc, svr)
}

/**********************************************************************************/
// Client
/**********************************************************************************/
// ServerProxy defines service client proxy
type ServerProxy interface {
	TarsSetOptions(opt ...client.Option)
	TarsClearOptions()
	Send(ctx context.Context, Message string, Receivers []string, _opt ...interface{}) (ret Result, err error)
}

func NewServerProxy(name string, opts ...client.Option) ServerProxy {
	return &ServerProxyImpl{client: client.DefaultClient, name: name, options: opts}
}

type ServerProxyImpl struct {
	client  client.Client
	name    string
	options []client.Option
}

// TarsSetOptions set options
func (_obj *ServerProxyImpl) TarsSetOptions(opts ...client.Option) {
	_obj.options = _obj.options[:0]
	_obj.options = append(_obj.options, opts...)
}

// TarsClearOptions clear options
func (_obj *ServerProxyImpl) TarsClearOptions() {
	_obj.options = _obj.options[:0]
}

//Send is the client proxy function for the method defined in the tars file
func (_obj *ServerProxyImpl) Send(ctx context.Context, Message string, Receivers []string, _opt ...interface{}) (ret Result, err error) {

	// msg
	ctx, msg := codec.WithCloneMessage(ctx)
	msg.WithCalleeServiceName(_obj.name)
	msg.WithClientRPCName("Send")

	// options
	_opts := &model.ClientOptions{}
	_opts.Options = _obj.options[:]
	_opts.Options = append(_opts.Options, client.WithProtocol("tars"))
	for _, o := range _opt {
		switch v := o.(type) {
		case client.Option:
			_opts.Options = append(_opts.Options, v)
		case model.ClientOption:
			v(_opts)
		}
	}

	// req version && context && status
	var _version int16 = basef.JCEVERSION
	var _context map[string]string
	var _status map[string]string
	if _opts.Version != 0 {
		_version = _opts.Version
	}
	if _opts.Context != nil {
		_context = _opts.Context
	}
	if _opts.Status != nil {
		_status = _opts.Status
	}

	// req head
	_reqhead, _ok := msg.ClientReqHead().(*requestf.RequestPacket)
	if !_ok {
		_reqhead = &requestf.RequestPacket{}
		msg.WithClientReqHead(_reqhead)
	}
	_reqhead.IVersion = _version
	_reqhead.Context = _context
	_reqhead.Status = _status

	_req := &XXX_Server_Send_FunReq{}
	_rsp := &XXX_Server_Send_FunRsp{}
	_req.Message = Message
	_req.Receivers = Receivers
	_req.XXX_Version = _version
	_rsp.XXX_Version = _version
	if _reqhead.IVersion == basef.WUPVERSION || _reqhead.IVersion == basef.WUPVERSION2 {
		msg.WithSerializationType(wup.SerializationTypeWup)
	} else {
		msg.WithSerializationType(codec.SerializationTypeJCE)
	}

	// client invoke
	err = _obj.client.Invoke(ctx, _req, _rsp, _opts.Options...)
	if err != nil {
		return _rsp.Ret, err
	}

	// rsp context && status
	_rsphead, _ := msg.ClientRspHead().(*requestf.ResponsePacket)
	if _context != nil {
		for k := range _context {
			delete(_context, k)
		}
		for k, v := range _rsphead.Context {
			_context[k] = v
		}
	}
	if _status != nil {
		for k := range _status {
			delete(_status, k)
		}
		for k, v := range _rsphead.Status {
			_status[k] = v
		}
	}

	return _rsp.Ret, nil
}

type XXX_Server_Send_FunReq_Receivers_List struct {
	Receivers []string
}

func (attr *XXX_Server_Send_FunReq_Receivers_List) WupDecode(raw []byte) (err error) {
	_is := jce.NewReader(raw)
	var length int32
	var have bool
	var ty byte

	err, have, ty = _is.SkipToNoCheck(0, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		attr.Receivers = make([]string, length)
		for i0, e0 := int32(0), length; i0 < e0; i0++ {

			err = _is.Read_string(&attr.Receivers[i0], 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {
		err = fmt.Errorf("not support simple_list type")
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}
	_ = length
	_ = have
	_ = ty
	return nil
}
func (attr *XXX_Server_Send_FunReq_Receivers_List) WupEncode(_os *jce.Buffer) (err error) {

	err = _os.WriteHead(jce.LIST, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(attr.Receivers)), 0)
	if err != nil {
		return err
	}

	for _, v := range attr.Receivers {

		err = _os.Write_string(v, 0)
		if err != nil {
			return err
		}

	}
	return nil
}
func (attr *XXX_Server_Send_FunReq_Receivers_List) WupTypeName() string {
	return "list<string>"
}

// XXX_Server_Send_FunReq struct implement
type XXX_Server_Send_FunReq struct {
	Message     string   `json:"message"`
	Receivers   []string `json:"receivers"`
	XXX_Version int16    `json:"-"`
}

func (st *XXX_Server_Send_FunReq) MakesureNotNil() {
}
func (st *XXX_Server_Send_FunReq) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *XXX_Server_Send_FunReq) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Message, 1, true)
	if err != nil {
		return err
	}

	err, have, ty = _is.SkipToNoCheck(2, true)
	if err != nil {
		return err
	}

	if ty == jce.LIST {
		err = _is.Read_int32(&length, 0, true)
		if err != nil {
			return err
		}

		st.Receivers = make([]string, length)
		for i1, e1 := int32(0), length; i1 < e1; i1++ {

			err = _is.Read_string(&st.Receivers[i1], 0, false)
			if err != nil {
				return err
			}

		}
	} else if ty == jce.SIMPLE_LIST {
		err = fmt.Errorf("not support simple_list type")
		if err != nil {
			return err
		}

	} else {
		err = fmt.Errorf("require vector, but not")
		if err != nil {
			return err
		}

	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *XXX_Server_Send_FunReq) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require XXX_Server_Send_FunReq, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *XXX_Server_Send_FunReq) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Message, 1)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.LIST, 2)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.Receivers)), 0)
	if err != nil {
		return err
	}

	for _, v := range st.Receivers {

		err = _os.Write_string(v, 0)
		if err != nil {
			return err
		}

	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *XXX_Server_Send_FunReq) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *XXX_Server_Send_FunReq) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *XXX_Server_Send_FunReq) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *XXX_Server_Send_FunReq) WupTypeName() string {
	return "WechatAlarmServer.XXX_Server_Send_FunReq"
}
func (st *XXX_Server_Send_FunReq) GetIVersion() int16 {
	return st.XXX_Version
}

// ReadFromWup reads  from _is and put into struct.
func (st *XXX_Server_Send_FunReq) ReadFromWup(_rWup *wup.UniPacket) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	_, err = _rWup.Get("message", (*wup.String)(unsafe.Pointer(&st.Message)))
	if err != nil {
		return err
	}
	var ReceiversAttr XXX_Server_Send_FunReq_Receivers_List
	_, err = _rWup.Get("receivers", &ReceiversAttr)
	st.Receivers = ReceiversAttr.Receivers
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//WriteTo encode struct to buffer
func (st *XXX_Server_Send_FunReq) WriteToWup(_wWup *wup.UniPacket) error {
	var err error
	st.MakesureNotNil()
	err = _wWup.Set("message", (*wup.String)(unsafe.Pointer(&st.Message)))
	if err != nil {
		return err
	}
	var ReceiversAttr XXX_Server_Send_FunReq_Receivers_List
	ReceiversAttr.Receivers = st.Receivers
	err = _wWup.Set("receivers", &ReceiversAttr)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

// XXX_Server_Send_FunRsp struct implement
type XXX_Server_Send_FunRsp struct {
	Ret         Result `json:"ret"`
	XXX_Version int16  `json:"-"`
}

func (st *XXX_Server_Send_FunRsp) MakesureNotNil() {
}
func (st *XXX_Server_Send_FunRsp) ResetDefault() {
	st.MakesureNotNil()
	st.Ret.ResetDefault()
}

// ReadFrom reads  from _is and put into struct.
func (st *XXX_Server_Send_FunRsp) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = st.Ret.ReadBlock(_is, 0, true)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *XXX_Server_Send_FunRsp) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require XXX_Server_Send_FunRsp, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *XXX_Server_Send_FunRsp) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = st.Ret.WriteBlock(_os, 0)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *XXX_Server_Send_FunRsp) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *XXX_Server_Send_FunRsp) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *XXX_Server_Send_FunRsp) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *XXX_Server_Send_FunRsp) WupTypeName() string {
	return "WechatAlarmServer.XXX_Server_Send_FunRsp"
}
func (st *XXX_Server_Send_FunRsp) GetIVersion() int16 {
	return st.XXX_Version
}

// ReadFromWup reads  from _is and put into struct.
func (st *XXX_Server_Send_FunRsp) ReadFromWup(_rWup *wup.UniPacket) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	_, err = _rWup.Get("", &st.Ret)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//WriteTo encode struct to buffer
func (st *XXX_Server_Send_FunRsp) WriteToWup(_wWup *wup.UniPacket) error {
	var err error
	st.MakesureNotNil()
	err = _wWup.Set("", &st.Ret)
	if err != nil {
		return err
	}

	_ = err

	return nil
}
