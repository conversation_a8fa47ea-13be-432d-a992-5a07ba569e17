// Package WechatAlarmServer comment
// Code generated by trpc4tars 2.0. DO NOT EDIT.
// source: wechatAlarm.jce
package WechatAlarmServer

import (
	"fmt"

	"git.woa.com/jce/jce"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = fmt.Errorf
var _ = jce.Marshal

type ResultCode int32

const (
	ResultCode_Success        = 0
	ResultCode_Failed         = 1
	ResultCode_PartialSuccess = 2
)

// Result struct implement
type Result struct {
	Message string     `json:"message"`
	Code    ResultCode `json:"code"`
}

func (st *Result) MakesureNotNil() {
}
func (st *Result) ResetDefault() {
	st.MakesureNotNil()
	st.Code = ResultCode_Success
}

// ReadFrom reads  from _is and put into struct.
func (st *Result) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Message, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32((*int32)(&st.Code), 1, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *Result) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require Result, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *Result) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Message, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(st.Code), 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *Result) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *Result) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *Result) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *Result) WupTypeName() string {
	return "WechatAlarmServer.Result"
}
