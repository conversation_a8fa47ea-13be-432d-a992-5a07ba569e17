// Package tafNotifier comment
// Code generated by trpc4tars 2.0. DO NOT EDIT.
// source: notifier.jce
package tafNotifier

import (
	"fmt"

	"git.woa.com/jce/jce"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = fmt.Errorf
var _ = jce.Marshal

// MediaMessage struct implement
type MediaMessage struct {
	Vid              string                     `json:"vid"`
	Timestamp        int32                      `json:"timestamp"`
	DataSetId        int32                      `json:"dataSetId"`
	FieldInfos       map[string]FieldInfo       `json:"fieldInfos"`
	ModifyFieldInfos map[string]ModifyFieldInfo `json:"modifyFieldInfos"`
	OperatorName     string                     `json:"operatorName"`
}

func (st *MediaMessage) MakesureNotNil() {
}
func (st *MediaMessage) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *MediaMessage) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_string(&st.Vid, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.Timestamp, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&st.DataSetId, 2, true)
	if err != nil {
		return err
	}

	err, have = _is.SkipTo(jce.MAP, 3, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.FieldInfos = make(map[string]FieldInfo)
	for i0, e0 := int32(0), length; i0 < e0; i0++ {
		var k0 string
		var v0 FieldInfo

		err = _is.Read_string(&k0, 0, false)
		if err != nil {
			return err
		}

		err = v0.ReadBlock(_is, 1, false)
		if err != nil {
			return err
		}

		st.FieldInfos[k0] = v0
	}

	err, have = _is.SkipTo(jce.MAP, 4, true)
	if err != nil {
		return err
	}

	err = _is.Read_int32(&length, 0, true)
	if err != nil {
		return err
	}

	st.ModifyFieldInfos = make(map[string]ModifyFieldInfo)
	for i1, e1 := int32(0), length; i1 < e1; i1++ {
		var k1 string
		var v1 ModifyFieldInfo

		err = _is.Read_string(&k1, 0, false)
		if err != nil {
			return err
		}

		err = v1.ReadBlock(_is, 1, false)
		if err != nil {
			return err
		}

		st.ModifyFieldInfos[k1] = v1
	}

	err = _is.Read_string(&st.OperatorName, 5, true)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *MediaMessage) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require MediaMessage, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *MediaMessage) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_string(st.Vid, 0)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.Timestamp, 1)
	if err != nil {
		return err
	}

	err = _os.Write_int32(st.DataSetId, 2)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.MAP, 3)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.FieldInfos)), 0)
	if err != nil {
		return err
	}

	for k2, v2 := range st.FieldInfos {

		err = _os.Write_string(k2, 0)
		if err != nil {
			return err
		}

		err = v2.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	err = _os.WriteHead(jce.MAP, 4)
	if err != nil {
		return err
	}

	err = _os.Write_int32(int32(len(st.ModifyFieldInfos)), 0)
	if err != nil {
		return err
	}

	for k3, v3 := range st.ModifyFieldInfos {

		err = _os.Write_string(k3, 0)
		if err != nil {
			return err
		}

		err = v3.WriteBlock(_os, 1)
		if err != nil {
			return err
		}

	}

	err = _os.Write_string(st.OperatorName, 5)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *MediaMessage) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *MediaMessage) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *MediaMessage) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *MediaMessage) WupTypeName() string {
	return "tafNotifier.MediaMessage"
}

// FieldInfo struct implement
type FieldInfo struct {
	Id    int32  `json:"id"`
	Value string `json:"value"`
}

func (st *FieldInfo) MakesureNotNil() {
}
func (st *FieldInfo) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *FieldInfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32(&st.Id, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Value, 1, true)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *FieldInfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require FieldInfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *FieldInfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(st.Id, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Value, 1)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *FieldInfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *FieldInfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *FieldInfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *FieldInfo) WupTypeName() string {
	return "tafNotifier.FieldInfo"
}

// ModifyFieldInfo struct implement
type ModifyFieldInfo struct {
	Id  int32  `json:"id"`
	Old string `json:"old"`
	New string `json:"new"`
}

func (st *ModifyFieldInfo) MakesureNotNil() {
}
func (st *ModifyFieldInfo) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *ModifyFieldInfo) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32(&st.Id, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.Old, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.New, 2, true)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *ModifyFieldInfo) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require ModifyFieldInfo, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *ModifyFieldInfo) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(st.Id, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.Old, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.New, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *ModifyFieldInfo) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *ModifyFieldInfo) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *ModifyFieldInfo) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *ModifyFieldInfo) WupTypeName() string {
	return "tafNotifier.ModifyFieldInfo"
}
