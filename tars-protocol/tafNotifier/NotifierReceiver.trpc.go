// Package tafNotifier comment
// Code generated by trpc4tars 2.0. DO NOT EDIT.
// source: notifier.jce
package tafNotifier

import (
	"context"
	"fmt"
	"unsafe"

	tars "git.code.oa.com/trpc-go/trpc-codec/tars"
	"git.code.oa.com/trpc-go/trpc-codec/tars/model"
	"git.code.oa.com/trpc-go/trpc-codec/tars/protocol/res/basef"
	"git.code.oa.com/trpc-go/trpc-codec/tars/protocol/res/requestf"
	"git.code.oa.com/trpc-go/trpc-codec/tars/protocol/wup"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/server"
	"git.woa.com/jce/jce"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = fmt.Errorf
var _ = jce.Marshal
var _ = unsafe.Pointer(nil)

/**********************************************************************************/
// Server
/**********************************************************************************/

// define service interface NotifierReceiver
type NotifierReceiver interface {
	Receive(ctx context.Context, Message *MediaMessage, ErrorString *string) (ret int32, err error)
}

// Receive used to call the worker side implemnet for the method defined in the tars file
func NotifierReceiver_Receive_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (rspbody interface{}, err error) {
	_req := &XXX_NotifierReceiver_Receive_FunReq{}
	_rsp := &XXX_NotifierReceiver_Receive_FunRsp{}

	_reqhead := tars.Request(ctx)
	_req.XXX_Version = _reqhead.IVersion
	_rsp.XXX_Version = _reqhead.IVersion

	filters, err := f(_req)
	if err != nil {
		return nil, err
	}

	handleFunc := func(ctx context.Context, reqbody interface{}, rspbody interface{}) error {
		req := reqbody.(*XXX_NotifierReceiver_Receive_FunReq)
		rsp := rspbody.(*XXX_NotifierReceiver_Receive_FunRsp)
		req.MakesureNotNil() // for json decode, pointer maybe nil
		rsp.ErrorString = req.ErrorString
		rsp.MakesureNotNil() // for json decode, pointer maybe nil

		var err error
		rsp.Ret, err = svr.(NotifierReceiver).Receive(ctx, req.Message, &rsp.ErrorString)
		if err != nil {
			return err
		}

		_ = req
		_ = rsp

		return nil
	}

	err = filters.Handle(ctx, _req, _rsp, handleFunc)
	if err != nil {
		return nil, err
	}

	return _rsp, nil
}

// descriptor for worker.RegisterService
var NotifierReceiver_ServiceDesc = server.ServiceDesc{
	HandlerType: ((*NotifierReceiver)(nil)),
	Methods: []server.Method{
		{Name: "Receive", Func: NotifierReceiver_Receive_Handler},
		{Name: "/Receive", Func: NotifierReceiver_Receive_Handler},
	},
}

// register service
func RegisterNotifierReceiverService(s server.Service, svr NotifierReceiver) error {
	return s.Register(&NotifierReceiver_ServiceDesc, svr)
}

/**********************************************************************************/
// Client
/**********************************************************************************/
// NotifierReceiverProxy defines service client proxy
type NotifierReceiverProxy interface {
	TarsSetOptions(opt ...client.Option)
	TarsClearOptions()
	Receive(ctx context.Context, Message *MediaMessage, ErrorString *string, _opt ...interface{}) (ret int32, err error)
}

func NewNotifierReceiverProxy(name string, opts ...client.Option) NotifierReceiverProxy {
	return &NotifierReceiverProxyImpl{client: client.DefaultClient, name: name, options: opts}
}

type NotifierReceiverProxyImpl struct {
	client  client.Client
	name    string
	options []client.Option
}

// TarsSetOptions set options
func (_obj *NotifierReceiverProxyImpl) TarsSetOptions(opts ...client.Option) {
	_obj.options = _obj.options[:0]
	_obj.options = append(_obj.options, opts...)
}

// TarsClearOptions clear options
func (_obj *NotifierReceiverProxyImpl) TarsClearOptions() {
	_obj.options = _obj.options[:0]
}

//Receive is the client proxy function for the method defined in the tars file
func (_obj *NotifierReceiverProxyImpl) Receive(ctx context.Context, Message *MediaMessage, ErrorString *string, _opt ...interface{}) (ret int32, err error) {

	// msg
	ctx, msg := codec.WithCloneMessage(ctx)
	msg.WithCalleeServiceName(_obj.name)
	msg.WithClientRPCName("Receive")

	// options
	_opts := &model.ClientOptions{}
	_opts.Options = _obj.options[:]
	_opts.Options = append(_opts.Options, client.WithProtocol("tars"))
	for _, o := range _opt {
		switch v := o.(type) {
		case client.Option:
			_opts.Options = append(_opts.Options, v)
		case model.ClientOption:
			v(_opts)
		}
	}

	// req version && context && status
	var _version int16 = basef.JCEVERSION
	var _context map[string]string
	var _status map[string]string
	if _opts.Version != 0 {
		_version = _opts.Version
	}
	if _opts.Context != nil {
		_context = _opts.Context
	}
	if _opts.Status != nil {
		_status = _opts.Status
	}

	// req head
	_reqhead, _ok := msg.ClientReqHead().(*requestf.RequestPacket)
	if !_ok {
		_reqhead = &requestf.RequestPacket{}
		msg.WithClientReqHead(_reqhead)
	}
	_reqhead.IVersion = _version
	_reqhead.Context = _context
	_reqhead.Status = _status

	_req := &XXX_NotifierReceiver_Receive_FunReq{}
	_rsp := &XXX_NotifierReceiver_Receive_FunRsp{}
	_req.Message = Message
	_req.ErrorString = *ErrorString
	_req.XXX_Version = _version
	_rsp.XXX_Version = _version
	if _reqhead.IVersion == basef.WUPVERSION || _reqhead.IVersion == basef.WUPVERSION2 {
		msg.WithSerializationType(wup.SerializationTypeWup)
	} else {
		msg.WithSerializationType(codec.SerializationTypeJCE)
	}

	// client invoke
	err = _obj.client.Invoke(ctx, _req, _rsp, _opts.Options...)
	if err != nil {
		return _rsp.Ret, err
	}
	*ErrorString = _rsp.ErrorString

	// rsp context && status
	_rsphead, _ := msg.ClientRspHead().(*requestf.ResponsePacket)
	if _context != nil {
		for k := range _context {
			delete(_context, k)
		}
		for k, v := range _rsphead.Context {
			_context[k] = v
		}
	}
	if _status != nil {
		for k := range _status {
			delete(_status, k)
		}
		for k, v := range _rsphead.Status {
			_status[k] = v
		}
	}

	return _rsp.Ret, nil
}

// XXX_NotifierReceiver_Receive_FunReq struct implement
type XXX_NotifierReceiver_Receive_FunReq struct {
	Message     *MediaMessage `json:"message"`
	ErrorString string        `json:"errorString"`
	XXX_Version int16         `json:"-"`
}

func (st *XXX_NotifierReceiver_Receive_FunReq) MakesureNotNil() {
	if st.Message == nil {
		st.Message = &MediaMessage{}
	}
}
func (st *XXX_NotifierReceiver_Receive_FunReq) ResetDefault() {
	st.MakesureNotNil()
	st.Message.ResetDefault()
}

// ReadFrom reads  from _is and put into struct.
func (st *XXX_NotifierReceiver_Receive_FunReq) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = st.Message.ReadBlock(_is, 1, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.ErrorString, 2, false)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *XXX_NotifierReceiver_Receive_FunReq) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require XXX_NotifierReceiver_Receive_FunReq, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *XXX_NotifierReceiver_Receive_FunReq) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = st.Message.WriteBlock(_os, 1)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.ErrorString, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *XXX_NotifierReceiver_Receive_FunReq) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *XXX_NotifierReceiver_Receive_FunReq) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *XXX_NotifierReceiver_Receive_FunReq) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *XXX_NotifierReceiver_Receive_FunReq) WupTypeName() string {
	return "tafNotifier.XXX_NotifierReceiver_Receive_FunReq"
}
func (st *XXX_NotifierReceiver_Receive_FunReq) GetIVersion() int16 {
	return st.XXX_Version
}

// ReadFromWup reads  from _is and put into struct.
func (st *XXX_NotifierReceiver_Receive_FunReq) ReadFromWup(_rWup *wup.UniPacket) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	_, err = _rWup.Get("message", st.Message)
	if err != nil {
		return err
	}
	_, err = _rWup.Get("errorString", (*wup.String)(unsafe.Pointer(&st.ErrorString)))

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//WriteTo encode struct to buffer
func (st *XXX_NotifierReceiver_Receive_FunReq) WriteToWup(_wWup *wup.UniPacket) error {
	var err error
	st.MakesureNotNil()
	err = _wWup.Set("message", st.Message)
	if err != nil {
		return err
	}
	err = _wWup.Set("errorString", (*wup.String)(unsafe.Pointer(&st.ErrorString)))
	if err != nil {
		return err
	}

	_ = err

	return nil
}

// XXX_NotifierReceiver_Receive_FunRsp struct implement
type XXX_NotifierReceiver_Receive_FunRsp struct {
	Ret         int32  `json:"ret"`
	ErrorString string `json:"errorString"`
	XXX_Version int16  `json:"-"`
}

func (st *XXX_NotifierReceiver_Receive_FunRsp) MakesureNotNil() {
}
func (st *XXX_NotifierReceiver_Receive_FunRsp) ResetDefault() {
	st.MakesureNotNil()
}

// ReadFrom reads  from _is and put into struct.
func (st *XXX_NotifierReceiver_Receive_FunRsp) ReadFrom(_is *jce.Reader) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	err = _is.Read_int32(&st.Ret, 0, true)
	if err != nil {
		return err
	}

	err = _is.Read_string(&st.ErrorString, 2, true)
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//ReadBlock reads struct from the given tag , require or optional.
func (st *XXX_NotifierReceiver_Receive_FunRsp) ReadBlock(_is *jce.Reader, tag byte, require bool) error {
	var err error
	var have bool
	st.ResetDefault()

	err, have = _is.SkipTo(jce.STRUCT_BEGIN, tag, require)
	if err != nil {
		return err
	}
	if !have {
		if require {
			return fmt.Errorf("require XXX_NotifierReceiver_Receive_FunRsp, but not exist. tag %d", tag)
		}
		return nil
	}

	err = st.ReadFrom(_is)
	if err != nil {
		return err
	}

	err = _is.SkipToStructEnd()
	if err != nil {
		return err
	}
	_ = have
	return nil
}

//WriteTo encode struct to buffer
func (st *XXX_NotifierReceiver_Receive_FunRsp) WriteTo(_os *jce.Buffer) error {
	var err error
	st.MakesureNotNil()

	err = _os.Write_int32(st.Ret, 0)
	if err != nil {
		return err
	}

	err = _os.Write_string(st.ErrorString, 2)
	if err != nil {
		return err
	}

	_ = err

	return nil
}

//WriteBlock encode struct
func (st *XXX_NotifierReceiver_Receive_FunRsp) WriteBlock(_os *jce.Buffer, tag byte) error {
	var err error
	err = _os.WriteHead(jce.STRUCT_BEGIN, tag)
	if err != nil {
		return err
	}

	err = st.WriteTo(_os)
	if err != nil {
		return err
	}

	err = _os.WriteHead(jce.STRUCT_END, 0)
	if err != nil {
		return err
	}
	return nil
}

func (st *XXX_NotifierReceiver_Receive_FunRsp) WupDecode(raw []byte) error {
	return st.ReadBlock(jce.NewReader(raw), 0, true)
}

func (st *XXX_NotifierReceiver_Receive_FunRsp) WupEncode(_os *jce.Buffer) error {
	return st.WriteBlock(_os, 0)
}

func (st *XXX_NotifierReceiver_Receive_FunRsp) WupTypeName() string {
	return "tafNotifier.XXX_NotifierReceiver_Receive_FunRsp"
}
func (st *XXX_NotifierReceiver_Receive_FunRsp) GetIVersion() int16 {
	return st.XXX_Version
}

// ReadFromWup reads  from _is and put into struct.
func (st *XXX_NotifierReceiver_Receive_FunRsp) ReadFromWup(_rWup *wup.UniPacket) error {
	var err error
	var length int32
	var have bool
	var ty byte
	st.ResetDefault()

	_, err = _rWup.Get("", (*wup.Int32)(unsafe.Pointer(&st.Ret)))
	if err != nil {
		return err
	}
	_, err = _rWup.Get("errorString", (*wup.String)(unsafe.Pointer(&st.ErrorString)))
	if err != nil {
		return err
	}

	_ = err
	_ = length
	_ = have
	_ = ty
	return nil
}

//WriteTo encode struct to buffer
func (st *XXX_NotifierReceiver_Receive_FunRsp) WriteToWup(_wWup *wup.UniPacket) error {
	var err error
	st.MakesureNotNil()
	err = _wWup.Set("", (*wup.Int32)(unsafe.Pointer(&st.Ret)))
	if err != nil {
		return err
	}
	err = _wWup.Set("errorString", (*wup.String)(unsafe.Pointer(&st.ErrorString)))
	if err != nil {
		return err
	}

	_ = err

	return nil
}
