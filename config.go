package common

import (
	"context"
	"sync/atomic"

	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"

	"gopkg.in/yaml.v2"

	// 引入trpc框架tconf包
	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
)

// ObserveConfig 动态监听t-conf配置变更结构
type ObserveConfig struct {
	// PlatName 配置平台名，目前只支持tconf和rainbow
	PlatName string
	// ConfigName 配置名
	ConfigName string
	atomic.Value
}

// NewTConfConfig 创建tconf动态监听配置对象
func NewTConfConfig(name string) *ObserveConfig {
	return &ObserveConfig{
		PlatName:   "tconf",
		ConfigName: name,
		Value:      atomic.Value{},
	}
}

// NewRainBowConfig 创建七彩石动态监听配置对象
func NewRainBowConfig(name string) *ObserveConfig {
	return &ObserveConfig{
		PlatName:   "rainbow",
		ConfigName: name,
		Value:      atomic.Value{},
	}
}

// LoadYaml 加载框架配置，必须放在NewServer之后执行
func (o *ObserveConfig) LoadYaml(configIns interface{}) error {
	// 加载配置
	response, err := config.Get(o.PlatName).Get(context.TODO(), o.ConfigName)
	if err != nil {
		return err
	}
	if err := yaml.Unmarshal([]byte(response.Value()), configIns); err != nil {
		return err
	}
	o.Store(configIns)

	// 使用trpc-go/config中Watch接口监听t-conf远程配置变化
	c, _ := config.Get(o.PlatName).Watch(context.TODO(), o.ConfigName)
	go func() {
		for r := range c {
			log.Infof("event: %d, value: %s.", r.Event(), r.Value())
			if err := yaml.Unmarshal([]byte(r.Value()), configIns); err == nil {
				o.Store(configIns)
			}
		}
	}()

	log.Infof("loading config --------------------------------------")
	log.Infof("Config: %+v", configIns)
	log.Infof("loading config end --------------------------------------")
	return nil
}

// GetValue 读取配置值(线程安全)
func (o *ObserveConfig) GetValue() interface{} {
	return o.Load()
}
