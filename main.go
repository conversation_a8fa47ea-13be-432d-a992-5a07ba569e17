package main

import (
	"git.code.oa.com/pcg-csd/trpc-ext/trpc-task/tasksdk"
	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	"git.code.oa.com/trpc-go/trpc-database/kafka"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/consumer"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/logic"
	_ "git.woa.com/galileo/trpc-go-galileo"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc"
	_ "git.woa.com/video_media/traceplus"
	_ "go.uber.org/automaxprocs"
)

func main() {
	s := trpc.NewServer()
	if err := conf.NewConf(); err != nil {
		log.Fatal(err)
	}

	tasksdk.RegisterHandlerService(s, "trpc.crawler_schedule.outer_circulation_monitor.Schedule",
		[]tasksdk.TaskSet{
			{ // 使用 trpc-task 定时任务系统：定时构建IP官号和IP话题的抓取池
				Name:       "build_ip_crawler_task",     //任务
				HandleFunc: logic.HandleBuildIPCrawTask, //任务处理函数
			},
			{ // 使用 trpc-task 定时任务系统：定时构建长尾IP的抓取池
				Name:       "build_longtailip_crawler_task",     //任务
				HandleFunc: logic.HandleBuildLongTailIPCrawTask, //任务处理函数
			},
			{ // 使用 trpc-task 定时任务系统：定时触发长尾IP信息抓取
				Name:       "push_longtailip_crawler_task",
				HandleFunc: logic.HandlePushLongtailIPInfoCrawTask,
			},
			{ // 使用 trpc-task 定时任务系统：定时触发CP信息抓取
				Name:       "push_cp_crawler_task",
				HandleFunc: logic.HandlePushCPInfoCrawTask,
			},
			{ // 使用 trpc-task 定时任务系统：定时触发IP信息抓取
				Name:       "push_ip_crawler_task",
				HandleFunc: logic.HandlePushIPInfoCrawTask,
			},
			{ // 使用 trpc-task 定时任务系统：定时触发 排播列表抓取
				Name:       "push_playlist_crawler_task",
				HandleFunc: logic.HandlePushPlaylistCrawTask,
			},
			{ // 使用 trpc-task 定时任务系统：定时触发 排播剧集详情页抓取
				Name:       "push_playlist_detail_crawler_task",
				HandleFunc: logic.HandlePushPlaylistDetailCrawTask,
			},
			{ // 使用 trpc-task 定时任务系统：定时触发 企微通知
				Name:       "qywx_notify",
				HandleFunc: logic.HandleNotifier,
			},

			{ // 使用 trpc-task 定时任务系统：定时构建maoyan搜索抓取任务
				Name:       "build_maoyan_search_crawler_job", //任务
				HandleFunc: logic.HandleBuildMaoYanSearchJob,  //任务处理函数
			},
			{ // 使用 trpc-task 定时任务系统：定时触发maoyan抓取任务（通用回调，页面设置参数不同）
				Name:       "push_maoyan_crawler_job",
				HandleFunc: logic.HandlePushCrawJobs,
			},

			{ // 使用 trpc-task 定时任务系统：定时构建maoyan剧集详情抓取任务
				Name:       "build_maoyan_detail_crawler_job", //任务
				HandleFunc: logic.HandleBuildMaoYanDetailJob,  //任务处理函数
			},

			{ // 使用 trpc-task 定时任务系统：定时构建douyin搜索抓取任务
				Name:       "build_douyin_search_crawler_job", //任务
				HandleFunc: logic.HandleBuildDouYinSearchJob,  //任务处理函数
			},
			{ // 使用 trpc-task 定时任务系统：定时构建douyin剧集详情抓取任务
				Name:       "build_douyin_detail_crawler_job", //任务
				HandleFunc: logic.HandleBuildDouYinDetailJob,  //任务处理函数
			},

			{ // 使用 trpc-task 定时任务系统：定时触发douyin抓取任务（通用回调，页面设置参数不同）
				Name:       "push_douyin_crawler_job",
				HandleFunc: logic.HandlePushCrawJobs,
			},
		})

	kafka.RegisterKafkaHandlerService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.competitor"),
		consumer.CompetitorHandler) // 接收竞品抓取信息（只管接收，抓取不在这里触发）
	kafka.RegisterKafkaHandlerService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.crawler_article"),
		consumer.ArticleHandler) // 接收抓取的文章信息
	kafka.RegisterKafkaHandlerService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.ip_office_search"),
		consumer.AccountListHandler) // ip 官号搜索回调
	kafka.RegisterKafkaHandlerService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.crawler_cp"),
		consumer.AccountHandler) // 接收抓取的账号信息（CP账号、IP账号）
	kafka.RegisterKafkaHandlerService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.topic_info"),
		consumer.TopicListHandler) // 接收根据topic中文名，返回的topicID列表信息
	kafka.RegisterKafkaHandlerService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.topic_field_info"),
		consumer.TopicFieldHandler)
	kafka.RegisterKafkaHandlerService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.playlist"),
		consumer.PlaylistHandler) // 接收竞品排播列表抓取返回信息
	kafka.RegisterKafkaHandlerService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.playlist_detail"),
		consumer.PlaylistDetailHandler) // 接收竞品排播剧集详情页抓取返回信息

	kafka.RegisterKafkaHandlerService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.common_result"),
		consumer.OrderResultHandler) // 接收通用的返回结果（实际是猫眼的结果）

	thttp.HandleFunc("/cover_info_update", logic.HandleCoverMsg)
	thttp.RegisterNoProtocolService(s.Service("trpc.crawler_schedule.outer_circulation_monitor.http"))
	if e := s.Serve(); e != nil {
		log.Fatal(e)
	}
}
