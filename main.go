package main

import (
	"time"

	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/plugin"
	"git.code.oa.com/trpc-go/trpc-go/server"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	pb "git.code.oa.com/trpcprotocol/storage_service/access_layer"
	"git.code.oa.com/video_media/storage_service/access_layer/config"
	"git.code.oa.com/video_media/storage_service/access_layer/util"
	"git.code.oa.com/video_media/storage_service/access_layer/util/checker"
	"git.code.oa.com/video_media/storage_service/access_layer/util/report"
	"git.code.oa.com/video_media/storage_service/auth"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/otelcarriers/otelkafka"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc"
	_ "git.woa.com/video_media/traceplus"

	"github.com/cch123/gogctuner"
	_ "go.uber.org/automaxprocs"
)

// mediaInterfaceServiceImpl 框架生成协议对象
type mediaInterfaceServiceImpl struct{}

// InitService 服务初始化
func InitService(s *server.Server) {
	go gogctuner.NewTuner(true, 70)
	// 加载配置信息
	if err := config.InitConfig(); err != nil {
		log.Fatalf("Fail to load config, err is %v.", err)
	}

	util.InitRetryMQHandler(s)

	// 初始化表配置信息进程内缓存
	if err := cache.InitSingleDBCache(config.GetDBCfg(), item.AppInfo{}, item.AppFieldInfo{}, item.FieldInfo{},
		item.DataSet{}, item.RouteCfg{}); err != nil {
		log.Fatalf("Fail to init cache, err is %v.", err)
	}
	// 初始化鉴权频控
	if err := auth.InitAuthentication(&auth.LimitCfg{
		ServiceName: "trpc.storage_service.access_layer.MediaInterface",
		Namespace:   trpc.GlobalConfig().Global.Namespace,
		Expire:      time.Duration(config.GetLimitCfg().ExpireMin) * time.Minute,
	}, cache.GetSingeDBCache()); err != nil {
		log.Fatalf("Fail to init auth singleton, err is %v.", err)
	}
	// 初始化放号
	if err := report.InitIDCreator(); err != nil {
		log.Fatal(err)
	}

	// 初始化元数据校验器
	checker.InitMetadataValidator(trpc.BackgroundContext())
	log.Info("Finish init.")
}

func main() {
	// 初始化阿塔上报插件
	plugin.Register("report", log.DefaultLogFactory)
	plugin.Register("kafka", log.DefaultLogFactory)
	s := trpc.NewServer()
	// 初始化服务相关
	InitService(s)
	servicePrefix := "trpc." + trpc.GlobalConfig().Server.App + "." + trpc.GlobalConfig().Server.Server
	pb.RegisterMediaInterfaceService(s.Service(servicePrefix+".MediaInterface"), &mediaInterfaceServiceImpl{})
	pb.RegisterMediaInterfaceService(s.Service(servicePrefix+".MediaInterfaceHttp"), &mediaInterfaceServiceImpl{})
	if err := s.Serve(); err != nil {
		log.Fatalf("Fail to start access layer, err is %v.", err)
	}
}
