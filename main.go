package main

import (
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.woa.com/galileo/trpc-go-galileo"
	"git.woa.com/video_media/media_event_hub/processor/logic"

	_ "go.uber.org/automaxprocs"
)

func main() {
	s := trpc.NewServer()

	// 初始化处理器服务并启动事件总线管理器
	imp, err := logic.NewProcessorService()
	if err != nil {
		log.Error(err)
		return
	}
	log.Infof("媒体事件处理器启动成功，开始消费事件总线")

	imp.Start()
	// 服务结束时关闭事件总线管理器
	defer imp.Stop()

	if err := s.Serve(); err != nil {
		log.Error(err)
		return
	}
}
