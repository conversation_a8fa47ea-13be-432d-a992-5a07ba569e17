package main

import (
	"git.code.oa.com/pcg-csd/trpc-ext/trpc-task/tasksdk"
	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	"git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	"git.code.oa.com/video_media/ip_status_update/config"
	"git.code.oa.com/video_media/ip_status_update/handle"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/ip_status_update/repo"
	"git.code.oa.com/video_media/ip_status_update/service"
	_ "git.woa.com/galileo/trpc-go-galileo"
	_ "git.woa.com/video_media/traceplus"

	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc"
	_ "github.com/go-sql-driver/mysql"
)

// MustInit 进程初始化
func MustInit() {
	handle.RegisterTypeFilter()
	parse.InitMap()
	err := config.InitConfig()
	if err != nil {
		log.Fatal("init config failed", err)
	}
	model.NewUniversalProxy()
	repo.NewIpStatusCheckImp()
	service.InitRetry()
}

func main() {
	s := trpc.NewServer()

	MustInit()

	thttp.HandleFunc("/fill_plan_handle", service.HandlePlanMsg)
	thttp.HandleFunc("/ip_rel_time_cal", service.HandlePlanMsg)
	thttp.HandleFunc("/ip_rel_status_update", service.HandleCalIPStatus)
	// 短剧ip实时状态计算独立接口（数据总线回调）
	thttp.HandleFunc("/short_drama_ip_online_status", service.HandleCalShortDramaStatus)
	// 短剧ip实时状态重算接口（手动重试&ETS回调）
	thttp.HandleFunc("/recal_short_drama_status", service.HandleReCalShortDramaStatus)

	// 只检查，不更新数据，给数据巡查用
	thttp.HandleFunc("/check_ip_online_status", service.HandleCheckIPStatus)
	// ip价值时间状态计算
	thttp.HandleFunc("/ip_cal_value_time", service.HandleCalIPValueTime)
	// ip价值时间状态重算，用于ets回调或者洗数据
	thttp.HandleFunc("/recal_value_time", service.HandleReCalValueTime)

	// 只检查，不更新数据，给数据巡查用
	thttp.HandleFunc("/check_ip_value_status", service.HandleCheckIPValueStatus)
	// 服务注册
	thttp.RegisterDefaultService(s)

	// 注册任务调度
	tasksdk.RegisterHandlerService(s.Service("trpc.media_video.ip_status_update.schedule"), []tasksdk.TaskSet{
		// 使用Trpc定时任务每隔10分钟检查需要到点重算价值认定的数据
		{
			Name:       "timer_recal_value_status",
			HandleFunc: service.TimerReCalValueStatus,
		},
		// 使用Trpc定时任务每隔10分钟检查需要到点重算ip实时状态的数据
		{
			Name:       "timer_recal_ip_online_status",
			HandleFunc: service.TimerReCalIPOnlineStatus,
		},
	})

	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
