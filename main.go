package main

import (
	_ "go.uber.org/automaxprocs"

	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
	"git.code.oa.com/video_media/cover_msghub_cb/cover_standard_series_flag/handler"
)

func main() {
	s := trpc.NewServer()
	thttp.HandleFunc("/cover_msghub_cb/cover_standard_series_flag", handler.HandleCoverMsg)
	thttp.HandleFunc("/video_msghub_cb/cover_standard_series_flag", handler.HandleVideoMsg)
	thttp.RegisterNoProtocolService(s.Service("trpc.cover_msghub_cb.cover_standard_series_flag.http"))
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
