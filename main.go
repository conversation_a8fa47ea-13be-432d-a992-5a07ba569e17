package main

import (
	"git.code.oa.com/pcg-csd/trpc-ext/trpc-task/tasksdk"
	_ "git.code.oa.com/trpc-go/trpc-codec/videopacket"
	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/handler"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/timer"
	_ "git.woa.com/galileo/trpc-go-galileo"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc"
	_ "git.woa.com/video_media/traceplus"
	_ "go.uber.org/automaxprocs"
)

func main() {
	s := trpc.NewServer()
	tasksdk.RegisterHandlerService(s.Service("trpc.video_msghub_cb.cover_videos_auto_operation.Schedule"),
		[]tasksdk.TaskSet{
			{ // 使用 trpc-task 定时任务系统：定时处理视频下架移未发布列表操作
				Name:       "process_video_unshelve",  //任务名
				HandleFunc: timer.HandleVideoUnshelve, //任务处理函数
			},
		})

	thttp.HandleFunc("/video_msghub_cb/cover_videos_auto_operation", handler.HandleMsg)
	thttp.RegisterNoProtocolService(s.Service("trpc.video_msghub_cb.cover_videos_auto_operation.Greeter"))
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
