package main

import (
	_ "git.code.oa.com/vlib/go/trpc_plugins/video_component_tjg"
	_ "go.uber.org/automaxprocs"

	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"

	wujilog "git.code.oa.com/open-wuji/go-sdk/wujiclient/log"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/media_go_commlib/xhttp"
	"github.com/gin-gonic/gin"
)

type syncMediaFormalTestDataServiceImpl struct{}

func main() {
	s := trpc.NewServer()
	wujilog.SetStdLog()
	if err := initWujiConf(); err != nil {
		log.Fatal(err)
	}

	r := gin.New()
	r.Use(gin.Recovery())
	r.POST("/sync/callback", handleSync)
	xhttp.RegisterDefaultService(s, r)
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
