package main

import (
	_ "git.code.oa.com/tpstelemetry/tps-sdk-go/instrumentation/trpctelemetry"
	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	_ "git.code.oa.com/trpc-go/trpc-database/hbase"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/plugin"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	pb "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/config"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"

	"github.com/cch123/gogctuner"
	_ "go.uber.org/automaxprocs"
)

// dataAdaptorServiceImpl 框架生成协议结构体
type dataAdaptorServiceImpl struct{}

func main() {
	// 初始化阿塔上报插件
	plugin.Register("report", log.DefaultLogFactory)
	go gogctuner.NewTuner(true, 70)
	s := trpc.NewServer()
	// 初始化服务配置信息
	if err := config.LoadYaml(); err != nil {
		log.Fatalf("Fail to load config, err is %s.", err)
	}
	// 初始化配置进程内缓存(t_dev_cfg、t_route_cfg、t_new_field_tb_map_cfg)
	if err := cache.InitSingleDBCache(config.GetCacheCfg(), item.DevCfg{}, item.RouteCfg{},
		item.FieldInfo{}); err != nil {
		log.Fatalf("Fail to init cache, err is %s.", err)
	}

	pb.RegisterDataAdaptorService(s, &dataAdaptorServiceImpl{})
	if err := s.Serve(); err != nil {
		log.Fatalf("Fail to start adaptor layer, err is %s.", err)
	}
}
