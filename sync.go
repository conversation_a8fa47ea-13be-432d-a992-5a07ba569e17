package main

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/media_go_commlib/dataaccess"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	"github.com/gin-gonic/gin"

	wuji "git.code.oa.com/open-wuji/go-sdk/wujiclient"
)

var clientTestData wuji.FilterInterface
var clientFieldType wuji.FilterInterface

// TestDataStruct 测试数据的无极配置
type TestDataStruct struct {
	Ctime    time.Time `json:"_ctime"`
	ID       string    `json:"_id"`
	Mtime    time.Time `json:"_mtime"`
	DataID   string    `json:"data_id"`
	DataType string    `json:"data_type"`
	Remark   string    `json:"remark"`
}

// FieldInfoStruct 底层存储服务字段信息的无极配置
type FieldInfoStruct struct {
	CID            int       `json:"c_id"`
	CFieldID       int       `json:"c_field_id"`
	CInterfaceName string    `json:"c_interface_name"`
	CInnerName     string    `json:"c_inner_name"`
	CName          string    `json:"c_name"`
	CDataSet       int       `json:"c_data_set"`
	CDatasourceID  int       `json:"c_datasource_id"`
	CDesc          string    `json:"c_desc"`
	CFieldType     int       `json:"c_field_type"`
	CMtime         time.Time `json:"c_mtime"`
	ID             int       `json:"_id"`
}

func initWujiConf() error {
	var err error
	clientTestData, err = wuji.NewClientWithFilter([]string{"data_type", "data_id"}, "", TestDataStruct{},
		wuji.WithAppID("media_common_cfg"),
		wuji.WithSchemaID("sync_media_formal_test_data"),
		wuji.WithSchemaKey("82d4ab49f6d84754a88e747b01ea41ee"),
		wuji.EnableFilter())
	if err != nil {
		log.Errorf("NewClientWithFilter() error: " + err.Error())
		return err
	}

	clientFieldType, err = wuji.NewClientWithFilter([]string{"c_data_set", "c_field_id"},
		"c_data_set=2001|c_data_set=2003|c_data_set=2005", FieldInfoStruct{},
		wuji.WithAppID("media_proxy_route"),
		wuji.WithSchemaID("t_new_field_tb_map_cfg"),
		wuji.WithSchemaKey("0a0f275627544f94a9e2992f88c231ad"),
		wuji.EnableFilter())
	if err != nil {
		log.Errorf("NewClientWithFilter() error: " + err.Error())
		return err
	}

	// 默认15s更新一轮无极的数据，第一次启动需要先 Sleep(15) 秒来等待同步
	// 因为已经在data目录下提供了缓存，因此重启这里可以跳过等待，直接拿到上次的缓存数据
	// 如果想重新拉取数据，可以删除 data/ 目录并取消下面的注释
	time.Sleep(16 * time.Second)
	return nil
}

func getWujiTestData(filter wuji.FilterInterface, dataType int, dataID string) (bool, error) {
	obj, ok := filter.Get(fmt.Sprintf("data_type=%d&data_id=%s", dataType, dataID)).(*TestDataStruct)
	if !ok { // 不存在
		return false, nil
	}

	if len(obj.DataID) > 0 {
		return true, nil
	}
	return false, nil
}

// 获取fieldID的类型
func getWujiFieldType(filter wuji.FilterInterface, dataSetID int,
	fieldID int) (common_storage_common.EnumFieldType, error) {
	log.Infof("getWujiFieldType enter-%d,%d", dataSetID, fieldID)
	obj, ok := filter.Get(fmt.Sprintf("c_data_set=%d&c_field_id=%d", dataSetID, fieldID)).(*FieldInfoStruct)
	if !ok {
		log.Errorf("field is not exist!")
		return -1, errors.New("field is not exist!")
	}
	return common_storage_common.EnumFieldType(obj.CFieldType), nil
}

func getdataAccessProxy(dataSetID int) *dataaccess.DataAccess {
	// 注意这里我们一定要请求测试环境，不要弄错了！
	var setName string
	if dataSetID == 2001 {
		setName = "video.gz.*"
	} else if dataSetID == 2003 {
		setName = "cover.gz.*"
	} else if dataSetID == 2005 {
		setName = "column.gz.*"
	}
	var dataAccessObj dataaccess.DataAccess
	dataAccessObj.AccessSetOptions(
		dataaccess.WithDataSet(dataSetID),
		dataaccess.WithAppID("sync_media_formal_test_data"),
		dataaccess.WithAppKey("dde1a876423e970867fcaeb7525fee1c"),
		dataaccess.WithNamespace("Development"),
		dataaccess.WithSetName(setName))
	return &dataAccessObj
}

// makeUpdateFieldInfo 从msghub的结构体中提取信息，生成更新底层的结构体
func makeUpdateFieldInfo(fieldName string, fieldType common_storage_common.EnumFieldType,
	fieldInfo msghub.FieldInfo) *common_storage_common.UpdateFieldInfo {
	var updateFieldInfo common_storage_common.UpdateFieldInfo
	updateFieldInfo.UpdateType = common_storage_common.EnumUpdateType_UpdateTypeSet

	var updateFieldInfoIterm common_storage_common.FieldInfo
	updateFieldInfoIterm.FieldName = fieldName
	updateFieldInfoIterm.FieldType = fieldType
	if len(fieldInfo.Value) == 0 {
		updateFieldInfoIterm.StrValue = ""
		updateFieldInfo.FieldInfo = &updateFieldInfoIterm
		return &updateFieldInfo
	}

	if fieldType == common_storage_common.EnumFieldType_FieldTypeStr {
		updateFieldInfoIterm.StrValue = fieldInfo.Value
	} else if fieldType == common_storage_common.EnumFieldType_FieldTypeIntVec {
		for _, val := range strings.Split(fieldInfo.Value, ",") {
			fieldVal, _ := strconv.Atoi(val)
			updateFieldInfoIterm.VecInt = append(updateFieldInfoIterm.VecInt, uint32(fieldVal))
		}
	} else if fieldType == common_storage_common.EnumFieldType_FieldTypeSet {
		for _, val := range strings.Split(fieldInfo.Value, ",") {
			updateFieldInfoIterm.VecStr = append(updateFieldInfoIterm.VecStr, val)
		}
	}
	updateFieldInfo.FieldInfo = &updateFieldInfoIterm
	return &updateFieldInfo
}

func writeDataToTestEnv(dataSetID int, dataID string, mediaFieldInfo map[string]msghub.FieldInfo) error {
	log.Infof("writeDataToTestEnv enter:%s", dataID)
	var fields []*common_storage_common.UpdateFieldInfo
	for fieldName, fieldInfo := range mediaFieldInfo {
		fieldType, err := getWujiFieldType(clientFieldType, dataSetID, fieldInfo.Id)
		if err != nil {
			log.Error("getFieldType failed:%+v", err)
			continue
		}
		fields = append(fields, makeUpdateFieldInfo(fieldName, fieldType, fieldInfo))
	}

	code, err := getdataAccessProxy(dataSetID).DataUpdate(dataID, fields, "cover_standard_series_flag")
	if err != nil {
		log.Error("DataUpdate CID info failed:%d %+v", code, err)
		return err
	}
	log.Infof("writeDataToTestEnv success:%s", dataID)
	return nil
}

func handleSync(c *gin.Context) {
	// 将通知报文解析出来
	var mediaInfo msghub.MediaInfo
	if err := c.BindJSON(&mediaInfo); err != nil {
		log.Error("Unmarshal the notify body failed!")
		c.JSON(200, gin.H{"code": -1, "msg": "Unmarshal the notify body failed!"})
		return
	}

	// 非测试数据，直接跳过
	hasTestData, err := getWujiTestData(clientTestData, mediaInfo.DataSetId, mediaInfo.Id)
	if err != nil {
		log.Errorf("getWujiTestData error: %s", err)
		c.JSON(500, gin.H{"code": -1, "msg": "getWujiTestData error!"})
		return
	}
	if hasTestData {
		// 写入测试环境（由于正式测试环境配置不对齐，可能有个别字段写入失败，为了避免上层不断重试，这里我们对于写入失败的也不报错了）
		_ = writeDataToTestEnv(mediaInfo.DataSetId, mediaInfo.Id, mediaInfo.FieldInfos)
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success!"})
	return
}
