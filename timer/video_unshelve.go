package timer

import (
	"context"
	"strings"
	"time"

	tRpcErrs "git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/dao"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/errcodes"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/model"
	"github.com/avast/retry-go/v4"
)

func processVideoUnshelveEvent(ctx context.Context, videoCovers []string, videoID string) error {
	log.InfoContextf(ctx, "processVideoUnshelveEvent : %s-%v", videoID, videoCovers)
	for _, coverID := range videoCovers {
		listType, listID, err := dao.GetVideoCoversListType(ctx, videoID, coverID)
		if err != nil {
			return tRpcErrs.New(errcodes.ERR_INNER_FAIL, err.Error())
		}
		if listType <= 0 { // 没找到属于哪个视频列表(需要把视频所属专辑中的cid删除)
			if err = dao.DelVideoCover(ctx, videoID, coverID); err != nil {
				return err
			}
		} else {
			if err = dao.RemoveCoverVideosList(ctx, coverID, videoID, listType, listID); err != nil {
				return err
			}
		}
	}
	log.InfoContextf(ctx, "processVideoUnshelveEvent suc")
	return nil
}

// HandleVideoUnshelve 定时处理下架视频自动移动未发布
func HandleVideoUnshelve(ctx context.Context, _, _ string, _ int32) error {
	var uv dao.UnshelvedVideo
	// 先上报当前最旧待处理数据的时间间隔
	maxTimeStamps, _ := uv.GetWaitingVideosMaxTimeStamps(ctx)
	metrics.SetGauge("MaxWaitingTime_Second", float64(time.Now().Unix()-maxTimeStamps))

	unshelvedVideos, err := uv.GetWaitingVideos(ctx, 2)
	if err != nil {
		return err
	}
	for _, v := range unshelvedVideos {
		// 获取视频的当前值（状态及所属专辑），避免异步执行期间数据有变更
		videoCovers, err := dao.GetUnshelveVideoCovers(ctx, v.VideoID)
		if err != nil {
			log.ErrorContextf(ctx, "GetUnshelveVideoCovers err:%s", err.Error())
			return err
		}
		// videoCovers 为空值的，processVideoUnshelveEvent会直接返回成功，不影响数据被扭转状态

		err = retry.Do(
			func() error {
				e := processVideoUnshelveEvent(ctx, strings.Split(videoCovers, model.MediaDelimiter), v.VideoID)
				if e != nil {
					log.ErrorContextf(ctx, "processVideoUnshelveEvent ERR:%s-%+v", v.VideoID, e)
					return e
				}
				return nil
			},
			retry.Attempts(3),         // 重试次数
			retry.LastErrorOnly(true), // 仅返回最后一次错误
			retry.OnRetry(func(n uint, err error) { // 每次重试的时候调用方法
				log.ErrorContextf(ctx, "retry processVideoUnshelveEvent #%d, because got err: %s", n, err)
			}))

		// 处理失败（我们会重试3次），设置状态为失败
		if err != nil {
			if err = uv.SetTaskFailed(ctx, v.DataID); err != nil {
				log.ErrorContextf(ctx, "SetTaskFailed ERR:%s-%+v", v.VideoID, err)
			}
			continue
		}
		// 处理成功，设置状态为成功；
		if err = uv.SetTaskSucceed(ctx, v.DataID); err != nil {
			log.ErrorContextf(ctx, "SetTaskSucceed ERR:%s-%+v", v.VideoID, err)
		}
	}
	return nil
}
