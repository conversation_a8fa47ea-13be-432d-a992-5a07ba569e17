2020-11-06 15:23:19.448317Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["maglev","ringHash","weightedRandom","hash"],"localRegistry":["inmemory"],"outlierDetector":["udp","http","tcp"],"rateLimiter":["reject","unirate"],"serverConnector":["grpc"],"serviceRouter":["ruleBasedRouter","setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter"],"statReporter":["lbInfo","stat2Monitor","pluginInfo","serviceCache"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:24:18.144597Z	info	stat	monitor/stat_monitor.go:412	
uid: E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E | id: svc2df3910d-926a-47c1-a113-823bc2c20189 | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: *************:10033 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2000ms
2020-11-06 15:24:18.144799Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb053b8276-6031-4c3c-8b79-21075414c956" token:<pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > namespace:"Production" service:"********:65536" loadbalancer:"weightedRandom" total_choose_num:3 instance_info:<ip:"*************" port:10033 choose_num:1 > instance_info:<ip:"*************" port:10099 choose_num:1 > instance_info:<ip:"************" port:10048 choose_num:1 > 
2020-11-06 15:24:18.145181Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin56cc16ae-b040-45d7-a059-9254d88e99fc" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:37 > 
2020-11-06 15:24:18.152051Z	info	stat	monitor/stat_monitor.go:412	
uid: E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E | id: svc20b203f0-b4b0-4ebb-b6d3-d05af87aeeae | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: *************:10099 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2000ms
2020-11-06 15:24:18.152473Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbd71d2243-1d29-4a9e-8593-17583594eb07" token:<pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > namespace:"Polaris" service:"polaris.discover" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"***********" port:8081 choose_num:1 > 
2020-11-06 15:24:18.163767Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugincbdf5f50-1fde-4246-9ebd-da5abd71e915" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:37 > 
2020-11-06 15:24:18.170701Z	info	stat	monitor/stat_monitor.go:412	
uid: E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E | id: svca9742d12-295c-4b20-bc14-fa88707815eb | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: ************:10048 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2000ms
2020-11-06 15:24:18.170845Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin6d936088-473e-45d7-858d-e759be2749ee" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:24:18.170954Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb39dfa496-672f-4a92-93b3-0be734b27f78" token:<pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"************" port:8081 choose_num:1 > instance_info:<ip:"***********" port:8081 choose_num:1 > 
2020-11-06 15:24:18.179803Z	info	stat	monitor/stat_monitor.go:412	
uid: E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E | id: svc5b6fd62f-6046-45dc-9422-b35dfddca827 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ***********:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 1066ms
2020-11-06 15:24:18.179854Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc37dca5e-2776-4c3c-8934-3936668e3de4" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:3 > 
2020-11-06 15:24:18.186691Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginba6d4a98-4163-4283-8d1a-ee23d97d78aa" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadInstances" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:24:18.187253Z	info	stat	monitor/stat_monitor.go:356	
uid: E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E | id: sdk966999d6-5edb-435c-b77b-b250ab0e0428 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 4
2020-11-06 15:24:18.193905Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin9097dd9b-f65d-4678-b6c4-fb03e7c3ddfe" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadServiceRouteRule" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:24:18.194339Z	info	stat	monitor/stat_monitor.go:356	
uid: E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E | id: sdk7d90c980-6855-466f-8b0b-5f4d4359b1d8 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [200ms,) | total_requests_in_minute: 1
2020-11-06 15:24:18.209791Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugine69994db-ec93-415f-bc55-32602df70a09" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 15:24:18.216790Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina94b7fc8-9973-4c47-b123-7a9ed3ac8c6a" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:24:18.224457Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin4acd8d12-d973-4f15-9f3e-ca0a5930a72f" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"ReportClient" > results:<ret_code:"ErrCodeServerError" total_requests_per_minute:1 > 
2020-11-06 15:24:18.230282Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginfcd30e70-4f65-4de8-822d-79b13fd6424f" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"RegisterServiceHandler" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:24:18.237444Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin1750593c-ddd5-45d6-a5f8-54b8f216be29" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 15:24:18.245071Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin6dfd3ed3-e4dc-4d27-a0f8-bf351b570eb9" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"UpdateServers" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:24:18.261098Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin04193e85-d0c0-4ff0-b800-bc77f87499d7" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"weightedRandom" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:4 > 
2020-11-06 15:25:18.194461Z	info	stat	monitor/stat_monitor.go:412	
uid: E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E | id: svc9860fb3f-4040-40e8-a3ea-db97302fc751 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ***********:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 647ms
2020-11-06 15:25:18.194479Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina93346f3-e9bf-42f3-b687-688e9c25a4ec" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:25:18.194611Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb3695f074-0c44-4863-b487-a678b0db4984" token:<pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"***********" port:8081 choose_num:1 > 
2020-11-06 15:25:18.387520Z	info	stat	monitor/stat_monitor.go:356	
uid: E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E | id: sdk88c58b1f-e487-48fe-b9f5-0e141c48cc8d | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:25:18.459902Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginbd8d2bf4-9d2a-4043-ae8c-e37232cfd165" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:25:18.527204Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugindfa67bc7-67ed-4b86-bcfe-12ee9decdb54" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:25:18.582708Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin685bd0d6-57b5-4e21-886b-d939b2b17179" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:25:18.621680Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc9b15ece-b8e8-4a32-9684-70c484329839" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:25:18.651133Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin88279f21-1792-4e49-a2ea-7c17bdd381b5" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:25:18.688353Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin4059019c-7b1e-4c28-9472-cf46519838be" token:<ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:25:48.170201Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:68210 uid:"E72805CA-1AFD-4DBE-A3E2-C73EBD41F38E" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["maglev","ringHash","weightedRandom","hash"],"localRegistry":["inmemory"],"outlierDetector":["udp","http","tcp"],"rateLimiter":["reject","unirate"],"serverConnector":["grpc"],"serviceRouter":["ruleBasedRouter","setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter"],"statReporter":["lbInfo","stat2Monitor","pluginInfo","serviceCache"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:28:30.855315Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:69043 uid:"97E2790B-529A-4B65-8CD6-382F55B5B268" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["reject","unirate"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["lbInfo","stat2Monitor","pluginInfo","serviceCache"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:31:55.308155Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorRate","errorCount"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter","setDivisionRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:32:55.054248Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina23f5eaa-2fdb-43a4-963c-3117408c2815" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:32:55.054422Z	info	stat	monitor/stat_monitor.go:412	
uid: A85354CA-ABFA-4715-B205-7C599EEF93CA | id: svc87e271fb-821d-4d8e-a1b1-1d01b97055fc | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: *************:10027 | resCode: 10000 | success: false | total_requests_in_minute: 3 | total_delay_in_minute: 6014ms
2020-11-06 15:32:55.054315Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb067da2b6-7630-44cf-8900-af680c83c4e6" token:<pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > namespace:"Polaris" service:"polaris.discover" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"************" port:8081 choose_num:1 > 
2020-11-06 15:32:55.063355Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbd63931f3-b43f-4f08-8573-dc5f4d694388" token:<pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"************" port:8081 choose_num:2 > 
2020-11-06 15:32:55.063365Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin31b47b72-8144-40ba-abf8-db1abda0747e" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:3 > 
2020-11-06 15:32:55.074804Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb0f4eff77-21d1-49e6-a995-bbf4a9d84e27" token:<pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > namespace:"Production" service:"********:65536" loadbalancer:"weightedRandom" total_choose_num:3 instance_info:<ip:"*************" port:10027 choose_num:3 > 
2020-11-06 15:32:55.074860Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginedf84a08-53bd-40bc-9706-e0a1c92fec84" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:32:55.080364Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginab039a22-9aa2-4192-beee-8076ee2f48ea" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadInstances" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:32:55.089340Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugincfb7780e-4dee-4b5a-b3c1-ca4b93d30fd3" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"UpdateServers" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:32:55.095517Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf24dad57-736b-4338-8da4-1e8c96f6cf85" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadServiceRouteRule" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:32:55.101512Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugindb41f0a5-38fe-443d-9bfe-8b9753e7ce6e" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:32:55.108577Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugine4560318-1328-4a3a-b00f-29c07305dcd9" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 15:32:55.114319Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin647b5f5c-41f3-4e0b-bbaf-615b720a4302" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"weightedRandom" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:4 > 
2020-11-06 15:32:55.141883Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf5854b9b-faac-40ec-b27b-4e47873ace0e" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"ReportClient" > results:<ret_code:"ErrCodeServerError" total_requests_per_minute:1 > 
2020-11-06 15:32:55.146829Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin80fa37dd-dfe8-4db5-928a-a0f607804a9a" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"RegisterServiceHandler" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:32:55.154315Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin836b42ed-**************-b45a52c24a20" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:32:55.160117Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5aa254de-9a88-4768-8001-c7de1087d4bb" token:<ip:"127.0.0.1" pid:69421 uid:"A85354CA-ABFA-4715-B205-7C599EEF93CA" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:32:55.257030Z	info	stat	monitor/stat_monitor.go:412	
uid: A85354CA-ABFA-4715-B205-7C599EEF93CA | id: svc449ae0e6-262c-4d38-a28e-99cf5b48f99a | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 25 | total_delay_in_minute: 440ms
2020-11-06 15:32:55.262887Z	info	stat	monitor/stat_monitor.go:356	
uid: A85354CA-ABFA-4715-B205-7C599EEF93CA | id: sdk5f68a6ac-8894-4208-bf48-9680fe5d12f1 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 4
2020-11-06 15:32:55.267748Z	info	stat	monitor/stat_monitor.go:356	
uid: A85354CA-ABFA-4715-B205-7C599EEF93CA | id: sdke3d534cf-dbb6-4276-aabb-9f6050b282d7 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [200ms,) | total_requests_in_minute: 1
2020-11-06 15:33:47.742306Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70083 uid:"59279FC4-D768-479C-9E7F-CDC6F88C898B" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["maglev","ringHash","weightedRandom","hash"],"localRegistry":["inmemory"],"outlierDetector":["tcp","udp","http"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["nearbyBasedRouter","ruleBasedRouter","setDivisionRouter","dstMetaRouter","filterOnlyRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:34:56.600860Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["maglev","ringHash","weightedRandom","hash"],"localRegistry":["inmemory"],"outlierDetector":["udp","http","tcp"],"rateLimiter":["reject","unirate"],"serverConnector":["grpc"],"serviceRouter":["dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter","setDivisionRouter"],"statReporter":["lbInfo","stat2Monitor","pluginInfo","serviceCache"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:35:55.226447Z	info	stat	monitor/stat_monitor.go:412	
uid: 29BA26D8-0BA5-4C3A-BD30-1EC290E99F92 | id: svcc8f67124-e0b3-4e88-8d40-27ac15fda64b | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 23 | total_delay_in_minute: 909ms
2020-11-06 15:35:55.226583Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin0b89e3ad-8031-4e9c-91e6-a7620e6800f9" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadServiceRouteRule" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:35:55.226599Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lba069a165-59b4-4777-80e3-9f276c7edbac" token:<pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > namespace:"Polaris" service:"polaris.discover" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"************" port:8081 choose_num:1 > 
2020-11-06 15:35:55.232775Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbf0490c4b-83ac-4a3e-a032-442d2b4b6122" token:<pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"************" port:8081 choose_num:1 > instance_info:<ip:"************" port:8081 choose_num:1 > 
2020-11-06 15:35:55.232835Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8346b4f0-0810-4ed9-b156-389085e89c3d" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadInstances" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:35:55.232869Z	info	stat	monitor/stat_monitor.go:412	
uid: 29BA26D8-0BA5-4C3A-BD30-1EC290E99F92 | id: svc94f8f2ec-37b3-4061-afb9-99e67f83a799 | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: *************:10027 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2001ms
2020-11-06 15:35:55.239573Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin30a0e47d-c53b-4154-a478-a42fd6dee7d2" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:35:55.239681Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb607a0beb-5225-4380-96ca-9982858d9327" token:<pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > namespace:"Production" service:"********:65536" loadbalancer:"weightedRandom" total_choose_num:3 instance_info:<ip:"*************" port:10027 choose_num:1 > instance_info:<ip:"*************" port:10033 choose_num:2 > 
2020-11-06 15:35:55.239892Z	info	stat	monitor/stat_monitor.go:412	
uid: 29BA26D8-0BA5-4C3A-BD30-1EC290E99F92 | id: svcb39137dd-1642-4100-aa54-f26b248690d1 | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: *************:10033 | resCode: 10000 | success: false | total_requests_in_minute: 2 | total_delay_in_minute: 4005ms
2020-11-06 15:35:55.246143Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf4b082ec-cd79-4e1d-8bc5-0c8ce1784392" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"weightedRandom" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:4 > 
2020-11-06 15:35:55.246270Z	info	stat	monitor/stat_monitor.go:356	
uid: 29BA26D8-0BA5-4C3A-BD30-1EC290E99F92 | id: sdk3c5ce961-f981-4565-9c81-4c3c844392f6 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 4
2020-11-06 15:35:55.253332Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8eb9663e-b626-4866-98e8-e3d439360465" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 15:35:55.253347Z	info	stat	monitor/stat_monitor.go:356	
uid: 29BA26D8-0BA5-4C3A-BD30-1EC290E99F92 | id: sdk0db99447-aac4-4f85-bc8c-5878bff82665 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [200ms,) | total_requests_in_minute: 1
2020-11-06 15:35:55.261300Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7c7bf9c9-8ec4-4cb5-abd8-0a2e38d374d0" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 15:35:55.268874Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginbe36a922-84dd-49bd-a407-49677c4f46cc" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"UpdateServers" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:35:55.275290Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin18775802-3de3-4afc-b987-acf8a1c12992" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:35:55.281827Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5b745b46-6cce-40a7-b1d9-aa5dba97542b" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:28 > 
2020-11-06 15:35:55.289289Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin211fd1ca-c127-422e-9e7c-b7304565afe4" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"ReportClient" > results:<ret_code:"ErrCodeServerError" total_requests_per_minute:1 > 
2020-11-06 15:35:55.295493Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin237843a0-4666-4b71-98df-1401723284a0" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:3 > 
2020-11-06 15:35:55.302258Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin6aab999d-fdc4-4b93-9760-ef8158076c4f" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:28 > 
2020-11-06 15:35:55.312791Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7eb50f9c-c052-48d2-a508-b7e74260bbc8" token:<ip:"127.0.0.1" pid:70210 uid:"29BA26D8-0BA5-4C3A-BD30-1EC290E99F92" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"RegisterServiceHandler" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:37:36.929571Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:38:35.654074Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svcbde39e57-8dd2-45dd-ba17-85034d2ce9c8 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 1007 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 400ms
2020-11-06 15:38:35.654178Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugind1834dc6-8942-40a9-a901-637b3aa41578" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadServiceRouteRule" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:38:35.654117Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbc5fcf8a0-7081-4a82-8583-c96ce306cebd" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"**********" port:8081 choose_num:1 > instance_info:<ip:"************" port:8081 choose_num:1 > 
2020-11-06 15:38:35.684665Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc66a2ca54-b996-4790-8993-963acfb37a22 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 28 | total_delay_in_minute: 1270ms
2020-11-06 15:38:35.684751Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginb96c3608-5da4-402c-b985-559c68186628" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 15:38:35.684841Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbe8edc180-11c4-4423-acf4-7ff0b371fdd1" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Production" service:"********:65536" loadbalancer:"weightedRandom" total_choose_num:3 instance_info:<ip:"*************" port:10033 choose_num:1 > instance_info:<ip:"*************" port:10099 choose_num:1 > instance_info:<ip:"************" port:10048 choose_num:1 > 
2020-11-06 15:38:35.714514Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc9b66f5e6-ba0e-47b8-911e-ef6953d0a009 | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: *************:10033 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2003ms
2020-11-06 15:38:35.714645Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7f15ae43-45b6-49bb-a181-2c7955501783" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:4 > 
2020-11-06 15:38:35.714885Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbb721bbaf-699a-4171-b6cb-dbd806575cf0" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.discover" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"************" port:8081 choose_num:1 > instance_info:<ip:"************" port:8081 choose_num:1 > 
2020-11-06 15:38:35.742947Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svcb8c4b050-ca43-45fb-8e42-684845f82de2 | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: *************:10099 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2000ms
2020-11-06 15:38:35.743046Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5c91ec86-6fd2-4757-bd81-9430df75957d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"weightedRandom" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:5 > 
2020-11-06 15:38:35.772129Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc235ca715-1671-4e12-8eed-14b972d57807 | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: ************:10048 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2000ms
2020-11-06 15:38:35.772250Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin00ccf6fb-b578-41f3-8898-57d49a2936f8" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:33 > 
2020-11-06 15:38:35.801832Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugind2bd36e7-027a-4084-8e96-f7c0a0276aa3" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadInstances" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:38:35.802252Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdka09ae6df-9bfa-4ebb-9ff7-bea82c5f40cb | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 5
2020-11-06 15:38:35.832798Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkfe0d57ed-94ce-42ac-8201-faa536713219 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [200ms,) | total_requests_in_minute: 1
2020-11-06 15:38:35.832854Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugincf1aa320-bbac-4520-b719-927076f0a316" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 15:38:35.861561Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin78ac56e4-ca53-4423-9440-3173c31bd0d4" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"UpdateServers" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:38:35.890041Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7fc3fe7d-9af4-4816-9558-ecfe44fa686d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 15:38:35.919194Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin0c4d5d41-4315-46f3-82bc-df9ad89f8233" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:33 > 
2020-11-06 15:38:35.949592Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin72c1dec5-c0b1-48df-99f6-8f69ba31a4e7" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"ReportClient" > results:<ret_code:"ErrCodeNetworkError" total_requests_per_minute:1 > results:<ret_code:"ErrCodeServerError" total_requests_per_minute:1 > 
2020-11-06 15:38:35.978534Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin080836c2-1f3b-409f-9a7a-f34127fd8aa7" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:38:36.007578Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugindb0ba001-fa48-4ae7-a7aa-d5e6f3534726" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"RegisterServiceHandler" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:39:35.636926Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin4a631dcd-80ee-4307-b9cd-c301914d8e07" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:39:35.637031Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svcdefdc205-3a85-4e12-9fb3-6cb344d97dc9 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1632ms
2020-11-06 15:39:35.637077Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb3f5e61cf-b432-4087-83d9-8ae9c7b46291" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:39:35.832460Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugine6c056b0-1110-4f42-b277-bd75ef4afa0f" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:39:35.832583Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk72bc2d34-f2f9-49bf-bfe6-1089b01f9623 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:39:35.863022Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5f25f498-580d-4c31-8bc6-bc22f7d6d99d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:39:35.893705Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginfa406b36-8477-47ef-ad08-b9c45004dcb9" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:39:35.925358Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin67d0899f-f3b0-4343-a421-ee5b3dced10d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:39:35.954700Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin3384b6da-c758-4733-8cdf-672479407fcd" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:39:35.983852Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin22169cfb-c2ee-40aa-a976-cce383264171" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:40:05.699636Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:40:35.626867Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc33e944e2-5356-4988-ba31-bd787c350cad | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1540ms
2020-11-06 15:40:35.626890Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb66bd9793-45b4-48f0-8b66-80e1fd5ca133" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"**********" port:8081 choose_num:2 > 
2020-11-06 15:40:35.626928Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin967a2cf2-72f7-4858-92de-b197ae6e5f9b" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:40:35.627482Z	info	stat	serviceinfo/service_info.go:310	
sdk cache stat:
id:"d0bd6a2a-870b-49bf-a6e5-51e159c22519" sdk_token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Production" service:"********:65536" instances_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"b03c2b458ab2cf990a4ef24728fdcc0e6238590d" > > routing_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 > > 
2020-11-06 15:40:35.658038Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkcec58f81-d8bd-4d56-b1c8-c44a950988de | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 15:40:35.658147Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin10d34899-3066-4511-972c-48ed21a230af" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:3 > 
2020-11-06 15:40:35.658363Z	info	stat	serviceinfo/service_info.go:310	
sdk cache stat:
id:"7071dfb5-4441-402e-b040-44d491b9ca1f" sdk_token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.discover" instances_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"8ab8e24cb04a74e505de6cb6bfea8aee627bfd60" > > routing_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"6f20d31f220046ccb69f43f51fcabfc3" > > 
2020-11-06 15:40:35.689289Z	info	stat	serviceinfo/service_info.go:310	
sdk cache stat:
id:"ae9a1005-b7a9-45c6-abb9-81b7a33416ac" sdk_token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" instances_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"0f0173eaff836190a59dabda335bd538e4a70e9e" > > routing_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"3a00e45a08dc4b45b5b3dd83110835e9" > > 
2020-11-06 15:40:35.689300Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin07d34c55-9d19-455c-90ec-56d15b2ccbc5" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:40:35.719565Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugincb905da5-d055-4416-ac6e-3b1039f52765" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:40:35.750020Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin3996aec1-8fcd-4b79-9202-63be7a04a630" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:3 > 
2020-11-06 15:40:35.780302Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginfe8273bf-40a9-4173-a416-d085db8ef7ee" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:40:35.810197Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin49b6e85d-f8c7-4353-8447-6843e85abff8" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:41:35.621540Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugindd0e5697-67e1-4999-af96-1d8cb333d787" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:41:35.621592Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svcf527bec7-ddb4-4015-b82a-ae040db1d27d | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 1458ms
2020-11-06 15:41:35.621655Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb692bae41-c1c7-4bd3-aadf-b86dd81e5d7e" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:41:35.656320Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk143a5ebb-a2e8-4fec-8244-dda12fef2541 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:41:35.656400Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5183a520-3cec-457e-8b1d-c0b3362aee03" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:41:35.684713Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin73ccc57d-946b-43da-9477-a09334d769f3" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:41:35.715232Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7838539c-96af-4021-843a-a17868f046c1" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:41:35.752571Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugine0dc22b0-71da-461e-87d0-69811b3a866b" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:41:35.780806Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginecabd065-b294-472b-8b81-58a53098627b" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:41:35.808834Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin4be119a2-242f-4f4c-a228-257a1c9464da" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:42:35.626986Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:42:35.627045Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugind3b1e793-45b1-4573-9885-f24f72fdcdbe" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:42:35.627047Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb4c5ddc3e-c017-4b48-a099-d531d2953a9c" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:42:35.627099Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc265ca805-abe3-40c8-a149-bfd7799907ef | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1556ms
2020-11-06 15:42:35.661494Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkf988e631-bd59-49b9-a4a3-3c4efcc3886e | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:42:35.661981Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf28db857-10f3-42bd-ba16-fd27893ef4f7" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:42:35.694008Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin6ca94a84-22d2-40fc-8f59-00e1316a31c3" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:42:35.727953Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin06d8e2bb-1051-4738-84bd-fc733b36744b" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:42:35.764974Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin814da7ce-9e74-4132-892e-d7ac8f5002eb" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:42:35.796982Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin952e20c2-1ae7-47b8-a1ae-130ed663763c" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:42:35.828156Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin3473a772-0fce-46ed-8219-9619712e51c5" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:43:35.621951Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc1aed2548-5e5a-440c-8272-ccdaf20cd95e | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1215ms
2020-11-06 15:43:35.622034Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb17699b8a-c2ad-42af-8e71-ce488c06bb64" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:43:35.652803Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkc1756c90-e9ac-4482-9a11-b48caa4df4f3 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:43:35.685962Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin39ff3f0e-6c99-4c6e-b4ed-787021a46dea" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:43:35.716122Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugineb6c934b-4f99-4546-b9e0-ee974134cd32" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:43:35.745308Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin20b8edb1-961b-43e6-89b2-53a2f5766b04" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:43:35.774884Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugind6669de5-14cf-44b3-bc59-3a53c16817d6" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:43:35.804440Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin95dca2e4-68c9-4bb5-802d-0b1abb76d095" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:43:35.834486Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf0597e14-cbb2-4efc-8d53-88a0f2e8f44e" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:43:35.863438Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc9800b61-d04d-4d14-af1c-2560640d1a4c" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:44:35.632581Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina8bc85c1-1982-4e41-96cd-1600aecb4981" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:44:35.632605Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb96c4b344-2802-4c83-b3f4-f4c9e463cb76" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"**********" port:8081 choose_num:2 > 
2020-11-06 15:44:35.632617Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svceb293507-6d51-40fd-b7ce-246ceae2b785 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 1333ms
2020-11-06 15:44:35.674361Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk3481dd06-dd22-462b-80f2-88b40a90d336 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 15:44:35.674470Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginb7c13cf5-ca0c-4f00-b18c-33798ed06838" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:44:35.723035Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin1f128320-fb87-47bd-926f-678ade234ec5" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:44:35.772085Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin2d8326e2-a130-44bf-9ee5-cecf12590a2f" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:44:35.822498Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5ec9c246-05d6-45c5-9d2e-4a7b57bf2ce4" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:44:35.857625Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin603252ab-95e1-4894-9d5e-5f3c8d708da9" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:44:35.886637Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin1946294d-c047-430c-9cb1-3f671dca32ed" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:45:05.668938Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:45:35.626807Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb31c08279-dca1-41c3-8bf3-44f2c904f01a" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"**********" port:8081 choose_num:2 > 
2020-11-06 15:45:35.626849Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin467e8053-f0a7-447b-a18e-070cd4c38bae" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:45:35.626896Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc56c92640-da7a-4c71-8bf2-a00cc69409e0 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 1314ms
2020-11-06 15:45:35.656444Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5391edf5-c069-4fc1-8f3d-ef94ef994055" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:45:35.656459Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdke303d90b-94a0-470e-8f6e-84315df5cd81 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 15:45:35.686287Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin917a91bf-c917-47e7-bbbd-5b81bd1a90c1" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:32 > 
2020-11-06 15:45:35.715757Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugincb03c15b-b8a2-4602-8f31-da3ae84f575b" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:45:35.746003Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5bd887ba-d7b2-436f-86d1-6f6a60339ab8" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:45:35.775239Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginaa19e511-3750-46c1-a0b6-415f6efb3d8f" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:32 > 
2020-11-06 15:45:35.804624Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin999f51bb-518c-479b-98c3-140ce7a6b390" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:46:35.631491Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lba5a47e49-2c72-4ed3-907f-e01e9d95b9f8" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:46:35.631501Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc196d944f-750d-4641-b0b4-eaa9900e8ac2 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1548ms
2020-11-06 15:46:35.631509Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf7b7c33a-bf45-486d-8a1c-eb236145f230" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:46:35.666367Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk044baeb8-b91b-4c10-85ec-01886a8162a4 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:46:35.666520Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8eebed11-8e01-4bd4-af3c-cb0d3c1a06ed" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:46:35.696411Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8cb30c28-**************-5743d8d11047" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:46:35.726107Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin819fc72f-3817-4f3f-9c62-f1bd3f447251" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:46:35.755898Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin0bddaa72-180a-4b43-9c16-cdcdd4c79c04" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:46:35.786427Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin9842d2cf-2d5d-4047-8a94-6cfee9571b86" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:46:35.816554Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginbb62a53c-7c35-46a9-b852-a08498b3c9db" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:47:35.630798Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:47:35.630863Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin2a35c4b1-5c81-4e9d-b11d-ab17f14bd97a" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:47:35.630894Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc0a95a07c-5f65-4cee-b5b5-880fc93d6352 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 1286ms
2020-11-06 15:47:35.630941Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbeb00733b-dfbc-4890-9df3-8bdc64be602e" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.discover" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:47:35.664453Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk92bf3287-604b-48e9-b95f-73fafd385aa5 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:47:35.664459Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb0be92042-1ea0-43e8-9d94-e480e843f0bd" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:47:35.664459Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin23ca2a41-bfda-4f2b-8f88-61e354ac53ec" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:47:35.696098Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin4bbae274-374a-42a3-b8e1-d192026dc991" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"weightedRandom" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:47:35.728282Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina526d31b-6815-4293-b8d5-5cfe13e73cb4" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:47:35.759633Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin9a560253-8f3b-4700-88e6-21523d535a2b" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:47:35.793152Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin742c4170-1572-4713-b4ac-5d671c5840ec" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:47:35.824137Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin23a73040-db51-4fb4-8335-4b4e94a65b71" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:47:35.855836Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin054bad52-8772-4797-8ce1-5d173c03a829" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:48:35.626332Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svca14f36b0-0ae3-47e1-9ad5-3c7fec150f87 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 32 | total_delay_in_minute: 1113ms
2020-11-06 15:48:35.626328Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb7f0a7b5b-76c5-4139-bc99-90a7274b38c8" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:48:35.626379Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin70160f9c-2ae7-4a03-b387-22c15dc01c7c" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:48:35.659045Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk61f30762-1f39-4ceb-aebe-7eb17c3082dd | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 15:48:35.659104Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin245579c1-2ab4-40ae-82c7-cf473804fc85" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:48:35.691244Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin2e63a2b5-ace3-4337-b079-509f290b0871" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:34 > 
2020-11-06 15:48:35.723317Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf7496525-4da5-4ff8-955c-b3e2567fe2fd" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:48:35.754559Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin92a9df5f-0a73-49dd-9d99-c3d49da543fb" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:48:35.786318Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin4d837599-a387-4155-83b4-7e0e739f11e2" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:34 > 
2020-11-06 15:48:35.819097Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc11cd366-c792-4266-bff4-8e0e8d6efecb" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:49:35.634669Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc40c794bf-96fb-4cba-80f5-8e08b6106339 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1135ms
2020-11-06 15:49:35.634945Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb43177994-7694-4acc-911c-6ff1cbf09485" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:49:35.634806Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin6a6eb993-ce42-408e-8be9-fc03ccad300d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:49:35.668590Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk44eac5b2-93dd-4183-959b-7def9d62ee34 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:49:35.668624Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugind8f09383-3a5d-4ac2-a3c7-2ed441eba6dd" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:49:35.702417Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin0cac4dd9-a38f-4660-b81c-03e542952caf" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:49:35.739052Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin1e1804f1-24ef-44f0-975b-894cd77332e8" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:49:35.773207Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginee37ea70-bd06-42b1-9a72-fc266617e21b" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:49:35.806508Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginba750ed5-dbd9-48c2-91c1-68a9b6df63a8" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:49:35.841736Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin596db47b-0088-4e6e-8bf3-0f8be290ae94" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:50:05.689047Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:50:35.666422Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8e71e61d-e59b-4436-9d09-2c8e2f406281" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:50:35.666459Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb2fdf6348-d06a-4b89-9215-27b536efbf2e" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"**********" port:8081 choose_num:2 > 
2020-11-06 15:50:35.666636Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc5f72ec14-c53b-4ca6-9056-4e173d8b2114 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 1520ms
2020-11-06 15:50:35.712081Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkdcb9ac07-ba8b-415f-a301-7abf3023644b | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 15:50:35.712117Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc63e4b9b-0936-47f0-ad7e-650bf600ce1f" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:50:35.753842Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf8deeef0-1adb-4d24-a763-68cccfc78212" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:50:35.792363Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin57deb3b0-904b-4655-871b-39a8667c4354" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:50:35.831160Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin37d1b307-746c-404f-951d-9165625dc1a1" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:50:35.874184Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin68efbd6d-5a3c-4272-8967-2f68227da61a" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:50:35.920686Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginde2dd9e0-c1bc-455f-a227-11b3c143f739" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:51:35.636096Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginef3ed92c-5936-4262-af0f-37f7e4ccb2d5" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:51:35.636203Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc11a9113a-6692-49d3-b5ce-4b20f9ef6459 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1184ms
2020-11-06 15:51:35.636303Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbe9e677bd-409c-4200-bafc-e3d7b0fd95a5" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:51:35.670434Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk5fb13759-e5e4-4451-a3c6-8d7c77675934 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:51:35.670473Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugincf48e1af-375f-4844-bd1b-6fefe0ba76b6" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:51:35.707016Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginef842a21-ab21-4e08-b1a3-039f108706be" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:51:35.740929Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin748c7f60-7fdf-4061-b2c7-db9e0dc295ae" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:51:35.774646Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin95826004-18b9-4e56-bb92-b182d4283379" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:51:35.810335Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin06c90438-7830-4b03-a4b2-709562abaaf5" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:51:35.851685Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7c237e0c-6a89-4752-b871-1116027d095c" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:52:35.647047Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc270d57c8-ef24-4048-bad9-2f84649e74f8 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1200ms
2020-11-06 15:52:35.647056Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin736bd41c-1e96-42be-886a-e6678894c929" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:52:35.647262Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:52:35.647362Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbb257198b-6e28-45ff-b7a1-935c6338b35f" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:52:35.684965Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin850761c2-d9ec-4a8b-90a7-28579d7ca59d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:52:35.684978Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkdad5f2be-e611-43c7-b0cc-3058e6c037c5 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:52:35.718681Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc5f00163-f5cf-4289-8291-64becca254f7" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:52:35.759332Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8de2d6ba-4ac9-4f18-9086-14f99f4b6288" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:32 > 
2020-11-06 15:52:35.802524Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin577bb6ee-4c78-49ef-a0da-1e063448318f" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"ReportClient" > results:<ret_code:"ErrCodeServerError" total_requests_per_minute:1 > 
2020-11-06 15:52:35.841871Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf78627dc-3bb2-436e-bbd8-41b70dfdda55" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:52:35.877420Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin0b7cea7c-819c-4b7f-b293-47ff7026b342" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:52:35.911810Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin474cdef9-528c-41c6-b308-dda17c69c8b5" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:32 > 
2020-11-06 15:53:35.631099Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lba5145f4b-775e-4c02-a8c6-0d024c4daab0" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:53:35.631096Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginff5cadfb-78da-4a65-896b-667c369a6edb" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:53:35.631144Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svcd768f0f2-85b7-4dd9-82ac-d16375aecae5 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 32 | total_delay_in_minute: 1348ms
2020-11-06 15:53:35.663832Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk840ad771-728e-4304-8cab-d9b9c72cc53f | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:53:35.663879Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin272b979b-ce86-4d24-9a92-3ecf820a64b6" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:53:35.699250Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginb729155b-3006-4dd8-9c51-c8d4a30c8284" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:53:35.731335Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin52d309da-6387-445b-af46-ab525564c55d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:53:35.763682Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8649cf66-8529-4a6a-baf6-dc53e1ef3302" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:53:35.797507Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin1622e024-f1fd-46e3-b38c-31bd56241280" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:53:35.829448Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginaa7e8496-9529-46f0-a7e9-3b025211e943" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:54:35.637992Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8134d7fd-f05b-4e75-a360-99086d15ba4d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:54:35.638079Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb578dc534-12dc-4511-9482-896cb0edb14d" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:54:35.638083Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svca71b6279-b431-42a3-ac7f-ad7071605cbc | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1196ms
2020-11-06 15:54:35.668843Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkcf016985-c7c3-4273-ab17-4260c2cfd2c5 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:54:35.668882Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin13c52a67-afb2-424d-88be-6d76df666dab" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:54:35.699436Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginaa9aab14-6f95-4ba8-a595-f535b54f6337" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:54:35.731206Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin198354d5-b488-4da6-9adc-ccb518b3800d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:54:35.761843Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc1024d16-e73a-4630-82e0-b9fff2ab39da" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:54:35.792087Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin3af5e0cb-a92a-4dd2-bb48-19b94653a3a9" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:54:35.822319Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin2c9447a1-b5fa-489f-a5fe-7e6ccf891ea5" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:55:05.673388Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:55:35.638624Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8f8f9f70-44d9-4083-a777-f2d18b6ea2d8" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:55:35.638784Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc026a4bb2-5205-4e27-8d71-d749b770db6f | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 1218ms
2020-11-06 15:55:35.638805Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb5e858cee-3734-4fa4-8678-4a9830c1f53f" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"**********" port:8081 choose_num:2 > 
2020-11-06 15:55:35.673351Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdka90d7ffb-e568-47ac-8130-ec0738316ac6 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 15:55:35.673375Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin65bfce4b-bddb-4863-8115-3d05032a9512" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:55:35.714772Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin4b42903f-af12-45a8-9e71-806d244adb11" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:55:35.753349Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin88a4b67b-b230-4f91-9c79-174fba00c381" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:55:35.786316Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugine501f5fb-37e4-4ce8-8f47-bd6d9bc1a1b2" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:55:35.818018Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugindd7f6326-0a0c-4c45-8f83-43a199e4156f" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:55:35.850603Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin0e849710-4490-430d-9a4e-f0b8dd4fcc11" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:56:35.642610Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugince477db0-08a5-442a-b10e-6669c6fd5a01" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:56:35.642673Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbb1f6e02d-37aa-4a1b-917f-bc32cc80a4b1" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:56:35.642861Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svcf2b82d00-3c9c-4b54-a37e-1b04a7d91b5b | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 1513ms
2020-11-06 15:56:35.679743Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk898f5f61-33fe-4867-af56-7f43c849a179 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:56:35.679820Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugind0d85d09-2dfb-4e86-ae84-a2599b63e071" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:56:35.722781Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina6d1b3e4-e360-4ab6-a163-807b1ef6560a" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:56:35.815618Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7f2b8e30-bd05-46da-ab0a-9e7574b7cba6" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 15:56:35.867718Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7f00fe56-252c-4d87-9a3b-4d014d13aa2a" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:56:35.908405Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginecd3cbad-b262-4243-9fb2-c90027b562e4" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:56:35.945337Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin42dadaac-fd99-4696-9ed4-5be5338c4bdb" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:57:35.652588Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb3ae30a49-2695-44e2-8ef0-b27434aa334a" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:57:35.652591Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7eab299f-ef1e-4267-90e9-a3717cac42da" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:57:35.652660Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svce2abd6dc-d9b0-4d3f-ade5-bbf811c0fa26 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: **********:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1210ms
2020-11-06 15:57:35.652687Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 15:57:35.689761Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk7e9c2955-4d8c-478c-a94c-f00ee1a3a911 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:57:35.689897Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin764de5d9-06c2-4c0a-8ef7-1f9754ecd21c" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:57:35.689908Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb1c67c146-fc9c-4769-b5a8-118348012797" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.discover" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"***********" port:8081 choose_num:1 > 
2020-11-06 15:57:35.724803Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginff75786c-dc6e-4081-ac97-1f6595396c09" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"weightedRandom" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:57:35.759687Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin69f3761b-a11a-4469-af92-fb78f8073646" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:57:35.796115Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin237ec152-a8b0-447e-9777-41b9ad48cebe" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:57:35.830133Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin80c1b612-9b01-4fb1-aaa4-36b6f5a3bf45" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 15:57:35.872053Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin7f84bd0d-d0e5-4be5-b5aa-264aaa9fe27c" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 15:57:35.906182Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin9987a1f9-a405-458d-a850-543ed1f4378a" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:58:35.636113Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb9a0b9b71-d326-4679-9503-c4959bd6b903" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:58:35.636113Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin38c8dc0f-4683-41d5-be18-ce91a0dcdd92" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:58:35.636147Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svcbe09b1b2-cc12-4ac1-af17-440dde3a88d7 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ***********:8081 | resCode: 0 | success: true | total_requests_in_minute: 32 | total_delay_in_minute: 1368ms
2020-11-06 15:58:35.666779Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkc9a57d80-f21d-4d1d-a648-4f338f48233b | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 15:58:35.666826Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin0a01f924-c6d0-46b6-8eed-26a1eca58267" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:58:35.696993Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin9ab16ace-3112-459a-b544-3c88f4730e6b" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:34 > 
2020-11-06 15:58:35.727777Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginadc87721-de37-45b4-b8ba-b26e8ef9caf3" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:58:35.764772Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf234d924-f7a1-47b4-8879-e3852f1fa283" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:58:35.797151Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin0aa791b0-da8d-469d-9d22-************" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:58:35.830219Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin74a39315-a4d1-4643-a1ee-d05a2fd4b019" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:34 > 
2020-11-06 15:59:35.636965Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb35ed2801-5ec3-4de7-980b-a420492041b8" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 15:59:35.636977Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc757ba5b8-5060-4238-b63e-5c6699e1f462 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ***********:8081 | resCode: 0 | success: true | total_requests_in_minute: 31 | total_delay_in_minute: 1465ms
2020-11-06 15:59:35.637003Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin6646d141-2fe9-4d03-8177-4adb89d511df" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:59:35.667161Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdke5a13a98-d433-47fa-a020-12f19cebe80f | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 15:59:35.667250Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc37e7d9e-a4ae-489b-bb02-4b789b333d87" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:59:35.699919Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin22f199c8-0c64-4fa1-aacd-4c53ca2f0c48" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 15:59:35.732543Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina2b29f46-5d94-43ec-b0cb-b43c17ae93fd" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:32 > 
2020-11-06 15:59:35.762302Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin4190a689-77b9-49ec-a4cd-51e22f5f8fbe" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:32 > 
2020-11-06 15:59:35.792539Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina362711e-75cf-4808-8ca1-e13d3e866b8f" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 15:59:35.822818Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina76eedd5-a371-49c4-a6c3-0d0ff2130d8b" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:00:05.686581Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 16:00:35.632122Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svccf61b93d-149a-4f31-851a-e686ef6c5238 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ***********:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1226ms
2020-11-06 16:00:35.632105Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc4bf3b20-0574-4c3f-b0d8-892605b5484c" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 16:00:35.632160Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb17dc4188-8652-45a7-b2e5-1d4415338b50" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"**********" port:8081 choose_num:2 > 
2020-11-06 16:00:35.663156Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin879880b5-b730-46da-ba5a-e2ce82c38575" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:00:35.663302Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkdab3a44a-58a9-4060-a04a-ad60cf080151 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 16:00:35.693493Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5d3f99c8-0874-4f70-ae74-cc320dc13631" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:00:35.725173Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin128df634-cd22-433f-a57f-ba6c21b90aa6" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 16:00:35.755157Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugine11fd94a-0d28-4085-a822-215781c62f9c" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:00:35.785599Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin3556cb4f-5739-40c1-8e0a-eb4640f1af3d" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:00:35.816123Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin05ca3a2d-ac86-404e-9580-d58c2518e87a" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:01:35.643431Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svc1011d310-cac0-4198-8230-206a33de26c7 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ***********:8081 | resCode: 0 | success: true | total_requests_in_minute: 32 | total_delay_in_minute: 1274ms
2020-11-06 16:01:35.643479Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb987e3a70-e783-4ae7-bee2-442f1ac17234" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"**********" port:8081 choose_num:1 > 
2020-11-06 16:01:35.678455Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdk64b5afa1-2737-4d96-a799-9ae72f2d17f9 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 16:01:35.720094Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginbf920f1b-a6bd-4772-9ceb-ea6281efe368" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:01:35.755099Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin01e7e6de-1325-438c-8e84-0bee84155faa" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:01:35.788290Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf8289a99-881d-487b-ab86-1b1c38cd4cb8" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 16:01:35.821458Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin523d29fc-d495-436c-b0cf-8fe4fb762276" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:01:35.854757Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf1776a5c-bd36-467c-b25d-dc6e48261704" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:01:35.888845Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginccc1c3a3-d4d4-4952-a7df-b13ab59f7423" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:01:35.921130Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin49c1d7ec-8f93-47e0-900e-ba5b9044f9a6" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:31 > 
2020-11-06 16:02:35.640961Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf91c4ae4-9aaf-426a-a03d-028e60784481" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:02:35.641038Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb03198b69-bffc-4fc1-8e10-ee66319c46f2" token:<pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"**********" port:8081 choose_num:2 > 
2020-11-06 16:02:35.641050Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["hash","maglev","ringHash","weightedRandom"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["unirate","reject"],"serverConnector":["grpc"],"serviceRouter":["setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter"],"statReporter":["stat2Monitor","pluginInfo","serviceCache","lbInfo"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 16:02:35.641067Z	info	stat	monitor/stat_monitor.go:412	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: svcd2435737-880c-4dcb-b781-685705a49574 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ***********:8081 | resCode: 0 | success: true | total_requests_in_minute: 30 | total_delay_in_minute: 1427ms
2020-11-06 16:02:35.674476Z	info	stat	monitor/stat_monitor.go:356	
uid: AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800 | id: sdkab48f17f-a947-4e76-9f83-462a694131b1 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 16:02:35.674655Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina1869e15-f9fc-445d-b7aa-d486ddd4431a" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:02:35.708150Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin465795f1-b8f9-4554-a229-86bf71d71e82" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 16:02:35.750729Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin93ade9bb-5724-4240-9e8b-eedbb86ebbe7" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:02:35.782973Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin30997f2a-7f53-4a4e-a4ea-8740399fc3af" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:02:35.814775Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin441ab938-ea8a-4735-869b-6c9659fde5e4" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:02:35.847501Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc986467c-f104-440a-bb7a-dfa391e775e0" token:<ip:"127.0.0.1" pid:70757 uid:"AAFDD9F6-4416-42CA-9F7E-2E7BFBC26800" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:30 > 
2020-11-06 16:19:23.362863Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:74075 uid:"8CD00B70-CDC6-4CA6-BD23-4D9D0A410874" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorRate","errorCount"],"loadBalancer":["maglev","ringHash","weightedRandom","hash"],"localRegistry":["inmemory"],"outlierDetector":["http","tcp","udp"],"rateLimiter":["reject","unirate"],"serverConnector":["grpc"],"serviceRouter":["filterOnlyRouter","nearbyBasedRouter","ruleBasedRouter","setDivisionRouter","dstMetaRouter"],"statReporter":["lbInfo","stat2Monitor","pluginInfo","serviceCache"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 16:40:47.748257Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["weightedRandom","hash","maglev","ringHash"],"localRegistry":["inmemory"],"outlierDetector":["tcp","udp","http"],"rateLimiter":["reject","unirate"],"serverConnector":["grpc"],"serviceRouter":["ruleBasedRouter","setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter"],"statReporter":["pluginInfo","serviceCache","lbInfo","stat2Monitor"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 16:41:46.406578Z	info	stat	monitor/stat_monitor.go:412	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: svceb33dc21-f467-4173-b1c4-b71c463678c2 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 41 | total_delay_in_minute: 1512ms
2020-11-06 16:41:46.406958Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf9a9bbdf-daba-4906-8365-8897faa47e43" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"weightedRandom" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:4 > 
2020-11-06 16:41:46.407088Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb72bb11dd-e8a7-45ba-b3a9-8903260ea2d2" token:<pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Production" service:"********:65536" loadbalancer:"weightedRandom" total_choose_num:3 instance_info:<ip:"*************" port:10033 choose_num:1 > instance_info:<ip:"*************" port:10099 choose_num:1 > instance_info:<ip:"************" port:10048 choose_num:1 > 
2020-11-06 16:41:46.440390Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin51c8cd8c-05b2-48c7-9d4f-4ebed57562ad" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadServiceRouteRule" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:41:46.440508Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb15de3afc-26b3-4376-8638-3a0638bde5f4" token:<pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Polaris" service:"polaris.discover" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"************" port:8081 choose_num:1 > 
2020-11-06 16:41:46.440520Z	info	stat	monitor/stat_monitor.go:412	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: svc48cfe96b-0a0c-4963-af7d-9a5c1addd27e | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: *************:10033 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2005ms
2020-11-06 16:41:46.474258Z	info	stat	monitor/stat_monitor.go:412	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: svc2bf23cd3-4e6d-4c7b-98ed-fd301e1f2983 | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: *************:10099 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2005ms
2020-11-06 16:41:46.474382Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugine95cc46f-b0c1-49f9-88fb-4ca0925f631c" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 16:41:46.474444Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbf5a7ec05-62dd-42ca-b0de-cc1e6f767c16" token:<pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"***********" port:8081 choose_num:2 > 
2020-11-06 16:41:46.506971Z	info	stat	monitor/stat_monitor.go:412	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: svcdcd975bb-7a31-4a75-8d1c-bc1bbd70fc3d | caller_host: 127.0.0.1 | namespace: Production | service: ********:65536 | instance_host: ************:10048 | resCode: 10000 | success: false | total_requests_in_minute: 1 | total_delay_in_minute: 2000ms
2020-11-06 16:41:46.507084Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin09b9542d-36d7-45d8-9e74-045ec9402608" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:41:46.544051Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin02db0ed8-18dc-4736-90e4-ff0038242bab" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:47 > 
2020-11-06 16:41:46.544512Z	info	stat	monitor/stat_monitor.go:356	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: sdkd5eb44b2-4399-4a02-9cc7-a9c631525cbd | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 4
2020-11-06 16:41:46.580814Z	info	stat	monitor/stat_monitor.go:356	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: sdk7f6ef7d9-e471-4777-b9bc-98c3e7635f8d | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [200ms,) | total_requests_in_minute: 1
2020-11-06 16:41:46.580929Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugine9b182f4-02c3-43bc-8a12-7daecb03dbda" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"localRegistry" plugin_name:"inmemory" plugin_method:"LoadInstances" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:41:46.614356Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugincb4c2027-cebb-4359-8b97-106b4a6a2a6b" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:7 > 
2020-11-06 16:41:46.648246Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin0af494b3-b8a9-464a-aaf2-7ccb1bc6e34b" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:47 > 
2020-11-06 16:41:46.681225Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin9fde64e1-05e2-4ee9-a2ec-bf1310cd5585" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:3 > 
2020-11-06 16:41:46.715368Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin65324e3a-77a3-41c6-b507-91c06b7664e6" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:41:46.750606Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin3b63f4b7-5657-4d40-87ca-8396ae9e9779" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"ReportClient" > results:<ret_code:"ErrCodeServerError" total_requests_per_minute:1 > 
2020-11-06 16:41:46.783992Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin67af9f7f-c241-4b5f-9305-4d29c7afd955" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"RegisterServiceHandler" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:41:46.818166Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin2406569e-44fc-4cfc-b4d8-8d3c5007bc1e" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serverConnector" plugin_name:"grpc" plugin_method:"UpdateServers" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:42:46.406482Z	info	stat	monitor/stat_monitor.go:412	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: svce2eb82ca-6e3d-45ef-af56-95904170cad7 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 41 | total_delay_in_minute: 1371ms
2020-11-06 16:42:46.406495Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin1216e891-10fa-49f2-9499-f41abfb6dd86" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:41 > 
2020-11-06 16:42:46.406702Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb22d363df-9185-4efb-a04e-e0365cd6ef21" token:<pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"***********" port:8081 choose_num:1 > 
2020-11-06 16:42:46.439541Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin03ba0f57-3269-4caa-8e2d-66df52093b8f" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:42:46.439932Z	info	stat	monitor/stat_monitor.go:356	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: sdka89a524f-ff74-4259-975d-4c4a61013e97 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 16:42:46.473599Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf3f2b0cc-898e-4220-ace8-f8562c8ee470" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:42:46.506621Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin8d79f6c9-732b-49d7-800a-25bcc7272e15" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:5 > 
2020-11-06 16:42:46.537036Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf0e75064-2d28-4904-851c-6868a55e70c7" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:5 > 
2020-11-06 16:42:46.568071Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginbcd5c3bc-8ef4-409d-aa40-517bfb9b722d" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:42:46.598114Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugind9428d95-161c-4078-8443-003177ac3911" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:41 > 
2020-11-06 16:43:16.437733Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["weightedRandom","hash","maglev","ringHash"],"localRegistry":["inmemory"],"outlierDetector":["tcp","udp","http"],"rateLimiter":["reject","unirate"],"serverConnector":["grpc"],"serviceRouter":["ruleBasedRouter","setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter"],"statReporter":["pluginInfo","serviceCache","lbInfo","stat2Monitor"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 16:43:46.548839Z	info	stat	monitor/stat_monitor.go:412	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: svc9a34d59f-8ed0-4c4e-b96a-f356a5175bee | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 41 | total_delay_in_minute: 1389ms
2020-11-06 16:43:46.549109Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbff327b1a-71e8-471e-a43a-a47d09343be9" token:<pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:2 instance_info:<ip:"***********" port:8081 choose_num:2 > 
2020-11-06 16:43:46.548988Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc10a020d-1db1-456f-8904-2e013b70682e" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:42 > 
2020-11-06 16:43:46.549228Z	info	stat	serviceinfo/service_info.go:310	
sdk cache stat:
id:"56dc3512-a960-4b34-aaf5-62540e4115fd" sdk_token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Polaris" service:"polaris.discover" instances_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"8ab8e24cb04a74e505de6cb6bfea8aee627bfd60" > > routing_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"6f20d31f220046ccb69f43f51fcabfc3" > > 
2020-11-06 16:43:46.585436Z	info	stat	serviceinfo/service_info.go:310	
sdk cache stat:
id:"c841331c-3ce0-4686-9aa2-c8667237d313" sdk_token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Polaris" service:"polaris.monitor" instances_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"0f0173eaff836190a59dabda335bd538e4a70e9e" > > routing_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"3a00e45a08dc4b45b5b3dd83110835e9" > > 
2020-11-06 16:43:46.585598Z	info	stat	monitor/stat_monitor.go:356	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: sdkb28b7d0b-6322-4583-923e-467d57a8161e | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 2
2020-11-06 16:43:46.585764Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin817540ef-cd37-4d77-80f8-32e33abf1043" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:43:46.618303Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin4d030e46-e1a0-4d51-aff7-608b01ef9e54" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:43:46.618404Z	info	stat	serviceinfo/service_info.go:310	
sdk cache stat:
id:"5ed5cf3b-3982-49e2-8084-a700a50c9fad" sdk_token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Production" service:"********:65536" instances_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 revision:"b03c2b458ab2cf990a4ef24728fdcc0e6238590d" > > routing_history:<revision:<time:<seconds:********** nanos:********* > change_seq:1 > > 
2020-11-06 16:43:46.648392Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginfb5cae2c-7b3c-445a-8bda-b8e4d99e252a" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:43:46.688269Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin675283ee-9f20-413e-9a44-bae362adae26" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:43:46.719066Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin9623a400-d528-4d98-9277-6bbebcf30482" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:42 > 
2020-11-06 16:43:46.751639Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin3709844e-c934-45c4-8743-ffc44534f9e4" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:44:46.395696Z	info	stat	monitor/stat_monitor.go:412	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: svc343038af-0357-4d51-931a-7c1ffb4a8dfa | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 41 | total_delay_in_minute: 1403ms
2020-11-06 16:44:46.395709Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginb1651ca1-63d2-47e5-a768-a7306e1f7dbf" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:41 > 
2020-11-06 16:44:46.395766Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb1ee83379-3b26-4960-a889-1cb0cdf3f155" token:<pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"***********" port:8081 choose_num:1 > 
2020-11-06 16:44:46.428250Z	info	stat	monitor/stat_monitor.go:356	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: sdkc0023b68-61f0-400f-ba76-0e29b44e3b92 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 16:44:46.428372Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginfdd2ba4a-fc7f-4935-9d3c-c1f8fce06582" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:44:46.458736Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginf10e1067-ba34-4bda-af76-9d0cbacd51d8" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:44:46.490977Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugind7459608-475f-4d02-ba2b-0d9c0fb01708" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:44:46.521503Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin71e31564-436b-4fde-b8a5-7fe0a456900f" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:41 > 
2020-11-06 16:44:46.558737Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina6255aa4-2ca5-49d4-b1ee-c649021cb696" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:44:46.588734Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginea57ea57-8b88-4046-b3a5-230b51b217f4" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:45:46.407209Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lb3368c9ff-1d9b-408f-8ba1-92523d09e048" token:<pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"***********" port:8081 choose_num:1 > 
2020-11-06 16:45:46.407283Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin2f927c80-2f37-4bfe-b7ce-fed3ecabe7bc" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:45:46.407360Z	info	stat	monitor/stat_monitor.go:412	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: svcddb88b6d-a485-4cb6-be6d-8fca0bc657e6 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 41 | total_delay_in_minute: 1429ms
2020-11-06 16:45:46.407394Z	info	stat	monitor/stat_monitor.go:548	
Config of ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" , client polaris-go, version 0.5.4, location 
plugins {"consumer":{"circuitBreaker":{"chain":["errorCount","errorRate"],"checkPeriod":"30s","enable":true,"plugin":{"errorCount":{"continuousErrorThreshold":10,"metricNumBuckets":10,"metricStatTimeWindow":"1m0s"},"errorRate":{"errorRatePercent":50,"errorRateThreshold":0,"metricNumBuckets":5,"metricStatTimeWindow":"1m0s","requestVolumeThreshold":10}},"recoverNumBuckets":10,"recoverWindow":"1m0s","requestCountAfterHalfOpen":10,"sleepWindow":"30s","successCountAfterHalfOpen":8},"loadbalancer":{"plugin":{"hash":{"hashFunction":"murmur3"},"maglev":{"hashFunction":"murmur3","tableSize":65537},"ringHash":{"hashFunction":"murmur3","vnodeCount":1024}},"type":"weightedRandom"},"localCache":{"persistDir":"$HOME/polaris/backup","persistMaxReadRetry":1,"persistMaxWriteRetry":5,"persistRetryInterval":"1s","plugin":{},"serviceExpireTime":"24h0m0s","serviceRefreshInterval":"2s","type":"inmemory"},"outlierDetection":{"chain":[],"checkPeriod":"10s","enable":false,"plugin":{"http":{"path":"","timeout":"100ms"},"tcp":{"receive":"","retry":0,"send":"","timeout":"100ms"},"udp":{"receive":"","retry":0,"send":"","timeout":"100ms"}}},"serviceRouter":{"chain":["ruleBasedRouter","nearbyBasedRouter"],"enableRecoverAll":true,"percentOfMinInstances":0,"plugin":{"nearbyBasedRouter":{"enableDegradeByUnhealthyPercent":true,"matchLevel":"zone","maxMatchLevel":"","strictNearby":false,"unhealthyPercentToDegrade":100}}}},"global":{"api":{"bindIP":"","bindIf":"","maxRetryTimes":5,"reportInterval":"10m0s","retryInterval":"1s","timeout":"1s"},"serverConnector":{"addresses":["**********:8081","**********4:8081","************:8081","***********:8081","************:8081","************:8081","*************:8081","*************:8081","************:8081","*************:8081"],"connectTimeout":"400ms","connectionIdleTimeout":"3s","messageTimeout":"1s","plugin":{"grpc":{"maxCallRecvMsgSize":52428800}},"protocol":"grpc","requestQueueSize":1000,"serverSwitchInterval":"10m0s"},"statReporter":{"chain":["stat2Monitor","serviceCache","pluginInfo","lbInfo"],"enable":true,"plugin":{"lbInfo":{"reportInterval":"1m0s"},"pluginInfo":{"reportInterval":"1m0s"},"serviceCache":{"reportInterval":"3m0s"},"stat2Monitor":{"metricsNumBuckets":12,"metricsReportWindow":"1m0s"}}},"system":{"discoverCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.discover"},"healthCheckCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.healthcheck"},"mode":0,"monitorCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.monitor"},"rateLimitCluster":{"namespace":"Polaris","refreshInterval":"10m0s","service":"polaris.ratelimit"}}},"provider":{"rateLimit":{"enable":true,"plugin":{"unirate":{"maxQueuingTime":"1s"}}}}} 
config: {"alarmReporter":["alarm2file"],"circuitBreaker":["errorCount","errorRate"],"loadBalancer":["weightedRandom","hash","maglev","ringHash"],"localRegistry":["inmemory"],"outlierDetector":["tcp","udp","http"],"rateLimiter":["reject","unirate"],"serverConnector":["grpc"],"serviceRouter":["ruleBasedRouter","setDivisionRouter","dstMetaRouter","filterOnlyRouter","nearbyBasedRouter"],"statReporter":["pluginInfo","serviceCache","lbInfo","stat2Monitor"],"weightAdjuster":["rateDelayAdjuster"]}
2020-11-06 16:45:46.444612Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin50130f9e-d766-4427-8970-00b634547a8b" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:6 > 
2020-11-06 16:45:46.444616Z	info	stat	monitor/stat_monitor.go:356	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: sdkc03f5d7e-df0c-48cb-9929-00c274c7c542 | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 16:45:46.476835Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin3bfb97d6-c02a-4c4f-a0cd-517f3d99cfac" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:45:46.545166Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc68942ac-5bca-4f7d-831d-737eb7249869" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:41 > 
2020-11-06 16:45:46.593457Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin5a7b593c-ee89-4fc2-90e8-6dee01f71650" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:41 > 
2020-11-06 16:45:46.645654Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina612f0fd-164a-4b17-96ad-e95d0833c729" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:45:46.681214Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugindd57cc7a-f11b-4089-8834-03dc3419dab5" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:1 > 
2020-11-06 16:46:46.405813Z	info	stat	monitor/stat_monitor.go:412	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: svcad418f3a-ba26-4e01-9f95-328bd0df1f21 | caller_host: 127.0.0.1 | namespace: Polaris | service: polaris.discover | instance_host: ************:8081 | resCode: 0 | success: true | total_requests_in_minute: 42 | total_delay_in_minute: 1466ms
2020-11-06 16:46:46.405972Z	info	stat	loadbalance/lbinfo.go:256	loadBalance info: 
id:"lbecde0145-55df-4cf0-81a4-dceb032bd6b0" token:<pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > namespace:"Polaris" service:"polaris.monitor" loadbalancer:"weightedRandom" total_choose_num:1 instance_info:<ip:"***********" port:8081 choose_num:1 > 
2020-11-06 16:46:46.439311Z	info	stat	monitor/stat_monitor.go:356	
uid: A7EA21F7-B3EB-4EC1-9295-FAC9562B288E | id: sdk71dca50b-dec1-46a3-8f75-6b795adc2b0b | client_type: polaris-go | client_version: 0.5.4 | api: Consumer::GetOneInstance | ret_code: Success | success: true | result_type: Success | delay_range: [0ms,50ms) | total_requests_in_minute: 1
2020-11-06 16:46:46.483183Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin61dce900-6118-44a9-b2c5-0e96e1888623" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:41 > 
2020-11-06 16:46:46.514711Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginb6a457cb-f970-4f52-b9f3-e62f8067b6c4" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"dstMetaRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:46:46.546026Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"pluginc9760c19-54a4-4bce-96e9-a155a6521e78" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"serviceRouter" plugin_name:"filterOnlyRouter" plugin_method:"GetFilteredInstances" > results:<ret_code:"Success" total_requests_per_minute:2 > 
2020-11-06 16:46:46.578224Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin004691b5-b9d3-458c-862b-af862ad33283" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"Stat" > results:<ret_code:"Success" total_requests_per_minute:41 > 
2020-11-06 16:46:46.611891Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugina5ac81fb-d47c-404e-80a8-74fe514c1aad" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorRate" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:3 > 
2020-11-06 16:46:46.645429Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin747921da-0126-4638-a3d5-22abc9ed0392" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"circuitBreaker" plugin_name:"errorCount" plugin_method:"CircuitBreak" > results:<ret_code:"Success" total_requests_per_minute:3 > 
2020-11-06 16:46:46.678666Z	info	stat	plugininfo/plugin_info.go:243	plugin info:
id:"plugin2e62e531-1f65-491a-b5ae-b046837ad6d8" token:<ip:"127.0.0.1" pid:75891 uid:"A7EA21F7-B3EB-4EC1-9295-FAC9562B288E" > plugin_api:<plugin_type:"loadBalancer" plugin_name:"maglev" plugin_method:"ChooseInstance" > results:<ret_code:"Success" total_requests_per_minute:2 > 
