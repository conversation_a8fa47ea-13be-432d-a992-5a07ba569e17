package polaris

import (
	"git.code.oa.com/polaris/polaris-go/api"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
)

// ZkHost zk结构体
type ZkHost struct {
	Ip   string
	Port uint16
}

// GetHostByKey 获取host对应路由ip信息
func GetHostByKey(name string) (host ZkHost, err error) {
	consumer := GetConsumerAPI()
	instResp, err := consumer.GetOneInstance(&api.GetOneInstanceRequest{
		GetOneInstanceRequest: model.GetOneInstanceRequest{
			// Service:   "trpc.QQvideoMedia.getAccount.getAccount",
			Service:   name,
			Namespace: "Production",
		},
	})
	if err != nil {
		return host, err
	}
	host = ZkHost{instResp.Instances[0].GetHost(), uint16(instResp.Instances[0].GetPort())}
	return host, err
}
