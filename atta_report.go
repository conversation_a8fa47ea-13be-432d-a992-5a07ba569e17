package common

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"

	"git.code.oa.com/atta/attaapi_go"
	"git.code.oa.com/trpc-go/trpc-go/log"

	// 引入trpc框架atta上报
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
)

var (
	attaOnce   sync.Once
	attaReport *AttaReporter
)

// ReportDataInf 阿塔流水上报接口
type ReportDataInf interface {
	// GetReportInfo 返回上报流水内容
	GetReportInfo() []string
	// GetAuth 返回attaId, attaToken
	GetAuth() (string, string)
}

// ReportInf 阿塔对象上报接口
type ReportInf interface {
	// ReportToAtta 上报阿塔接口
	ReportToAtta(info ReportInf)
}

// AttaReporter 阿塔上报者
type AttaReporter struct {
	// api 阿塔上报对象
	api *attaapi_go.AttaApi
}

// NewAttaReporter 创建阿塔上报者
func NewAttaReporter() (*AttaReporter, error) {
	var attaApi attaapi_go.AttaApi
	if ret := attaApi.InitTCP(); ret != attaapi_go.M_ATTA_REPORT_CODE_SUCCESS {
		return nil, fmt.Errorf("fail to init atta api, ret is %d", ret)
	}
	return &AttaReporter{
		api: &attaApi,
	}, nil
}

// ReportToAtta 上报阿塔流水
func (reporter *AttaReporter) ReportToAtta(info ReportDataInf) {
	id, token := info.GetAuth()
	if ret := reporter.api.Send_fields(id, token, info.GetReportInfo(),
		true); ret != attaapi_go.M_ATTA_REPORT_CODE_SUCCESS {
		log.Errorf("Fail to report info to aTTa, ret:%d", ret)
	}
}

// getAttaSingleton 获取阿塔单例
func getAttaSingleton() *AttaReporter {
	attaOnce.Do(func() {
		var err error
		attaReport, err = NewAttaReporter()
		if err != nil {
			log.Error(err)
		}
	})
	return attaReport
}

// ReportToAtta 使用全局单例上报阿塔流水
func ReportToAtta(info ReportDataInf) {
	attaSingleton := getAttaSingleton()
	if attaSingleton == nil {
		return
	}

	attaSingleton.ReportToAtta(info)
}

// RemoteLog 远程日志结构
type RemoteLog struct {
	// loggerName 日志name
	loggerName string
	// ctx 上下文
	ctx context.Context
	// fields 上报字段
	fields []string
}

// NewRemoteLog 创建远程日志
func NewRemoteLog(ctx context.Context, name string) *RemoteLog {
	return &RemoteLog{
		loggerName: name,
		ctx:        ctx,
	}
}

// AddField 追加日志字段
func (r *RemoteLog) AddField(name, value string) *RemoteLog {
	r.fields = append(r.fields, name, value)
	return r
}

// AddIntField 追加整数字段
func (r *RemoteLog) AddIntField(name string, value int) *RemoteLog {
	r.fields = append(r.fields, name, strconv.Itoa(value))
	return r
}

// AddMapFields 追加map类型字段
func (r *RemoteLog) AddMapFields(fields []string, value map[string]string) *RemoteLog {
	for _, field := range fields {
		r.fields = append(r.fields, field, value[field])
	}
	return r
}

// ReportDebug 上报debug信息
func (r *RemoteLog) ReportDebug(info string) {
	if r.loggerName == "" {
		log.WithContextFields(r.ctx, r.fields...)
		log.DebugContext(r.ctx, info)
		return
	}

	logger := log.Get(r.loggerName)
	if logger == nil {
		return
	}
	logger.WithFields(r.fields...).Debug(info)
}

// ReportWarn 上报warn信息
func (r *RemoteLog) ReportWarn(info string) {
	if r.loggerName == "" {
		log.WithContextFields(r.ctx, r.fields...)
		log.WarnContext(r.ctx, info)
		return
	}

	logger := log.Get(r.loggerName)
	if logger == nil {
		return
	}
	logger.WithFields(r.fields...).Warn(info)
}

// ReportError 上报error信息
func (r *RemoteLog) ReportError(info string) {
	if r.loggerName == "" {
		log.WithContextFields(r.ctx, r.fields...)
		log.ErrorContext(r.ctx, info)
		return
	}

	logger := log.Get(r.loggerName)
	if logger == nil {
		return
	}
	logger.WithFields(r.fields...).Error(info)
}

// DefaultRLog 默认模版日志对象
type DefaultRLog struct {
	// id 上报id
	id string
	// scene 上报场景
	scene string
	// namespace 上报命名空间
	namespace string
}

// NewDefaultRLog 创建新的远程日志对象
func NewDefaultRLog(id, scene, namespace string) *DefaultRLog {
	return &DefaultRLog{
		id:        id,
		scene:     scene,
		namespace: namespace,
	}
}

func marshalJson(data interface{}) string {
	if data == nil {
		return ""
	}
	jsonStr, _ := json.Marshal(data)
	return string(jsonStr)
}

// Report 上报流水信息
func (d *DefaultRLog) Report(step int, reportData interface{}, report string, a ...interface{}) {
	if len(a) > 0 {
		report = fmt.Sprintf(report, a...)
	}

	NewRemoteLog(nil, "report").AddField("scene", d.scene).
		AddField("step", strconv.Itoa(step)).AddField("id", d.id).
		AddField("namespace", d.namespace).AddField("reportData", marshalJson(reportData)).
		ReportDebug(report)
}
