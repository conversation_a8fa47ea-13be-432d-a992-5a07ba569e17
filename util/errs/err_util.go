package errs

import (
	"git.code.oa.com/trpc-go/trpc-go/errs"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
)

var errorCodes = map[protocol.EnumMediaErrorCode]string{
	protocol.EnumMediaErrorCode_RetNoApp:          "app is not exist",
	protocol.EnumMediaErrorCode_RetNoAuth:         "check auth not pass",
	protocol.EnumMediaErrorCode_RetOverApplyReq:   "request is too frequent",
	protocol.EnumMediaErrorCode_RetNoAppField:     "can not find app of dataset",
	protocol.EnumMediaErrorCode_RetInnerErr:       "service inner err",
	protocol.EnumMediaErrorCode_RetCallAdaptorErr: "fail to call adaptorLayer service:",
	protocol.EnumMediaErrorCode_RetNoOpRight:      "forbid to get or update fields",
	protocol.EnumMediaErrorCode_RetInvalidDataSet: "invalid dataSet id",
}

// New 创建错误对象
func New(errCode protocol.EnumMediaErrorCode) error {
	return errs.New(int(errCode), errorCodes[errCode])
}

// Code 获取错误码
func Code(err error) protocol.EnumMediaErrorCode {
	return protocol.EnumMediaErrorCode(errs.Code(err))
}

// Msg 获取错误信息
func Msg(err error) string {
	return errs.Msg(err)
}
