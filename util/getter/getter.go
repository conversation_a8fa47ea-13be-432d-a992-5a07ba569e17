// Package getter 读相关工具函数
package getter

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/trpcprotocol/video_media/metadata_api"
	"git.code.oa.com/video_media/storage_service/access_layer/config"
	"git.code.oa.com/video_media/storage_service/access_layer/util"
	"git.code.oa.com/video_media/storage_service/access_layer/util/cipher"
	"git.code.oa.com/video_media/storage_service/access_layer/util/errs"
	"git.code.oa.com/video_media/storage_service/common"
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"

	"go.uber.org/multierr"
)

// Getter Get请求数据
type Getter struct {
	getRsps []*getRsp
	// Priority 请求优先级
	Priority int32
	// DataSetID 数据集id
	DataSetID int32
	// DataSourceID 指定数据源ID
	DataSourceID int32
	// ShowDel 是否展示删除字段(only mysql)
	ShowDel bool
}

// New 创建数据接入层读请求对象
func New(priority, dataSetID, dataSourceID int32) *Getter {
	return &Getter{
		Priority:     priority,
		DataSetID:    dataSetID,
		DataSourceID: dataSourceID,
		ShowDel:      false,
	}
}

// SetShowDel 设置是否展示删除数据
func (g *Getter) SetShowDel(showDel bool) *Getter {
	g.ShowDel = showDel
	return g
}

// getRsp get接口响应数据
type getRsp struct {
	dataSourceID int32
	rsp          *adaptor_layer.BatchGetFieldsResponse
}

func parseBatchGetRetInfo(err error, rsp *adaptor_layer.BatchGetFieldsResponse) (protocol.EnumMediaErrorCode, string) {
	if err != nil {
		return errs.Code(err), errs.Msg(err)
	}
	return rsp.ErrCode, rsp.ErrMsg
}

// SendGetRequests 并发发送各数据源数据适配层服务请求
func (g *Getter) SendGetRequests(ctx context.Context, adaptorReps []*adaptor_layer.BatchGetFieldsRequest) error {
	var (
		idx     int32 = -1
		handles []func() error
		mu      sync.Mutex
		mErr    error
	)
	g.getRsps = make([]*getRsp, len(adaptorReps))
	// 按照数据源类型并行发送到数据适配层
	for _, req := range adaptorReps {
		req := req
		dataSourceID := req.GetDataSourceID()
		handles = append(handles, func() error {
			log.DebugContextf(ctx, "req of BatchGetMediaInfos is %s.", req.String())
			if util.IsDatasourceOverload(g.Priority, dataSourceID, "GetMediaInfo") {
				g.getRsps[atomic.AddInt32(&idx, 1)] = &getRsp{
					dataSourceID: dataSourceID,
					// 被数据源频控，直接看作该数据源整个ID均失败
					rsp: &adaptor_layer.BatchGetFieldsResponse{FailList: util.NewGetFailList(req.GetId(), "overload")},
				}
				return nil
			}

			proxy := tool.NewAdaptorLayerProxy(dataSourceID, req.Id)
			rsp, err := proxy.SetDataSetID(req.DataSetID).AddGetFieldNames(req.FieldNames...).
				BatchGetMediaInfos(ctx, util.AdaptorLayerOptions(dataSourceID, config.GetSetName())...)
			if code, msg := parseBatchGetRetInfo(err, rsp); code != protocol.EnumMediaErrorCode_RetSuccess {
				mu.Lock()
				mErr = multierr.Append(mErr, fmt.Errorf("%d:%d:%s", req.GetDataSourceID(), code, msg))
				mu.Unlock()
			}
			g.getRsps[atomic.AddInt32(&idx, 1)] = &getRsp{dataSourceID: dataSourceID, rsp: rsp}
			return nil
		})
	}
	_ = trpc.GoAndWait(handles...)
	return mErr
}

func (g *Getter) fetchGetRsp(id string, get *getRsp, failList map[string]protocol.EnumMediaErrorCode,
	fieldInfo map[string]*protocol.FieldInfo,
) {
	log.Debugf("[fetchGetRsp]rsp of %d is %s.", get.dataSourceID, get.rsp.String())
	for k := range get.rsp.GetFailList() {
		// 数据适配层接口返回错误信息为string，因此统一使用EnumMediaErrorCode_RetFailSelect错误码
		failList[k] = protocol.EnumMediaErrorCode_RetFailSelect
	}

	doc, ok := get.rsp.GetDocInfos()[id]
	if !ok {
		return
	}

	// 合并docInfo内部failList
	for fieldName, fail := range doc.GetFailList() {
		failList[fieldName] = fail.ErrCode
	}
	for _, field := range doc.GetFields() {
		if err := g.AddFieldInfo(get.dataSourceID, fieldInfo, field); err != nil {
			failList[field.FieldName] = protocol.EnumMediaErrorCode(errs.Code(err))
		}
	}
}

// MergeGetRsps 合并各数据源数据适配层服务返回信息
func (g *Getter) MergeGetRsps(id string, failList map[string]protocol.EnumMediaErrorCode,
) map[string]*protocol.FieldInfo {
	fieldInfo := make(map[string]*protocol.FieldInfo)
	for _, rsp := range g.getRsps {
		if rsp == nil {
			continue
		}

		g.fetchGetRsp(id, rsp, failList, fieldInfo)
	}
	return fieldInfo
}

func (g *Getter) mergeFailList() map[string]string {
	failList := make(map[string]string)
	failCnt := make(map[string]int)
	rspCnt := 0
	for _, get := range g.getRsps {
		if get == nil {
			continue
		}
		for id, msg := range get.rsp.GetFailList() {
			failList[id] += fmt.Sprintf("{datasource:%d, errMsg:%s}", get.dataSourceID, msg)
			failCnt[id]++
		}
		rspCnt++
	}

	for id, cnt := range failCnt {
		if cnt != rspCnt {
			// 只有所有数据源的failList都返回的id才需要放到最外层的failList
			delete(failList, id)
		}
	}
	return failList
}

func getDocInfoByID(id string, docInfos map[string]*protocol.DocInfo) *protocol.DocInfo {
	doc, ok := docInfos[id]
	if ok {
		return doc
	}
	// 如果不存在则初始化一个新的doc
	doc = &protocol.DocInfo{
		Id:       id,
		Fields:   make(map[string]*protocol.FieldInfo),
		FailList: make(map[string]*protocol.FailInfo),
	}
	docInfos[id] = doc
	return doc
}

func (g *Getter) fetchBatchGetRsp(get *getRsp, docInfos map[string]*protocol.DocInfo) {
	log.Debugf("[fetchBatchGetRsp]rsp of datasource:%d is %s.", get.dataSourceID, get.rsp.String())
	for id, doc := range get.rsp.GetDocInfos() {
		if doc == nil {
			continue
		}

		d := getDocInfoByID(id, docInfos)
		for _, field := range doc.GetFields() {
			if err := g.AddFieldInfo(get.dataSourceID, d.GetFields(), field); err != nil {
				d.FailList[field.FieldName] = &protocol.FailInfo{
					ErrCode: errs.Code(err),
				}
			}
		}
	}
}

// MergeBatchGetRsps 批量读接口合并各数据源数据适配层服务返回信息
// 每个docInfo的failList代表具体某个id的失败情况，key为fieldName，外层failList代表整个id都失败了，key为id
func (g *Getter) MergeBatchGetRsps(ids []string, fieldNum int, fieldFailList map[string]protocol.EnumMediaErrorCode) (
	map[string]*protocol.DocInfo, map[string]string,
) {
	docInfos := make(map[string]*protocol.DocInfo)
	failList := g.mergeFailList()
	for _, rsp := range g.getRsps {
		if rsp == nil {
			continue
		}
		g.fetchBatchGetRsp(rsp, docInfos)
	}

	for _, id := range ids {
		doc := getDocInfoByID(id, docInfos)
		for field, errCode := range fieldFailList {
			// 每个doc都需要添加通用的字段错误
			doc.GetFailList()[field] = &protocol.FailInfo{
				ErrCode: errCode,
			}
		}
		if len(fieldFailList) == fieldNum {
			// 如果所有请求的字段都失败了也需要移入外部failList
			failList[id] = "all fields failed"
		}
	}
	return docInfos, failList
}

func (g *Getter) filterOtherDatasource(dataSourceID, fieldDataSourceID int32) bool {
	if g.DataSourceID == 0 {
		// 未指定读取数据源ID，过滤未配置在该数据源的字段
		return fieldDataSourceID != dataSourceID
	}
	// 过滤非指定数据源的字段
	return g.DataSourceID != dataSourceID
}

// addVersion 读字段添加version字段值
func (g *Getter) addVersion(fieldInfo map[string]*protocol.FieldInfo, val *protocol.FieldInfo) {
	if val.FieldName != "entity_version" {
		return
	}

	v, ok := fieldInfo["entity_version"]
	if !ok {
		fieldInfo["entity_version"] = val
		return
	}

	num1, _ := strconv.Atoi(v.StrValue)
	num2, _ := strconv.Atoi(val.StrValue)
	v.StrValue = strconv.Itoa(num1 + num2)
}

// AddFieldInfo 生成字段数据结构
func (g *Getter) AddFieldInfo(dataSourceID int32, fieldInfo map[string]*protocol.FieldInfo, val *protocol.FieldInfo,
) error {
	f := cache.GetFieldInfoByID(val.FieldId, g.DataSetID)
	if f == nil {
		log.Errorf("FieldId:%d is not exist.", val.FieldId)
		return errs.New(protocol.EnumMediaErrorCode_RetFieldInfoNotExist)
	}
	if f.FieldStatus >= int32(metadata_api.EFieldStatus_BACKEND_READ_OFFLINE) {
		// 读退场字段不返回
		log.Debugf("field:%+v offline, pass read.", f)
		return nil
	}

	// 数据源配置为0说明需要合并各个数据源的字段值
	if f.DataSourceID == 0 {
		g.addVersion(fieldInfo, val)
	}

	// 不属于该数据源的字段信息过滤掉
	if g.filterOtherDatasource(dataSourceID, f.DataSourceID) {
		return nil
	}

	// 已删除字段不返回
	if !g.ShowDel && common.IsFieldEmpty(val) {
		return nil
	}

	cipher.DecryptGetField(f, val)
	fieldInfo[val.FieldName] = val
	return nil
}
