package getter

import (
	"fmt"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"

	"github.com/agiledragon/gomonkey/v2"
)

func mockFieldInfoCache() {
	// mock cache
	fields := map[string]*item.FieldInfo{
		"9_2001": {
			DataSetID:      2001,
			DataSourceID:   1,
			FieldID:        9,
			FieldType:      1,
			InnerFieldName: "titleX",
			FieldName:      "titleX",
		}, "80045_2001": {
			DataSetID:      2001,
			DataSourceID:   1,
			FieldID:        9,
			FieldType:      2,
			InnerFieldName: "c_vcats",
			FieldName:      "vcats",
		}, "80086_2001": {
			DataSetID:      2001,
			DataSourceID:   5,
			FieldID:        80086,
			FieldType:      3,
			InnerFieldName: "c_covers",
			FieldName:      "covers",
		},
	}

	gomonkey.ApplyFunc(cache.GetFieldInfoByID, func(fieldID uint32, datasetID int32) *item.FieldInfo {
		return fields[fmt.Sprintf("%d_%d", fieldID, datasetID)]
	})
}

func TestGetter_MergeGetRsps(t *testing.T) {
	type fields struct {
		getRsps      []*getRsp
		Priority     int32
		DataSetID    int32
		DataSourceID int32
		ShowDel      bool
	}
	type args struct {
		id       string
		failList map[string]protocol.EnumMediaErrorCode
	}

	mockFieldInfoCache()
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[string]*protocol.FieldInfo
	}{
		{
			name: "测试合并请求",
			fields: fields{
				getRsps: []*getRsp{{
					dataSourceID: 1,
					rsp: &adaptor.BatchGetFieldsResponse{
						DocInfos: map[string]*protocol.DocInfo{"testVID": {
							Id: "testVID",
							Fields: map[string]*protocol.FieldInfo{"titleX": {
								FieldName:      "titleX",
								InnerFieldName: "titleX",
								FieldId:        9,
								FieldType:      protocol.EnumFieldType_FieldTypeStr,
								StrValue:       "",
							}, "vcats": {
								FieldName:      "vcats",
								InnerFieldName: "c_vcats",
								FieldId:        80045,
								FieldType:      protocol.EnumFieldType_FieldTypeIntVec,
								VecInt:         []uint32{1, 2, 3},
							}},
						}},
					},
				}, {
					dataSourceID: 5,
					rsp: &adaptor.BatchGetFieldsResponse{
						DocInfos: map[string]*protocol.DocInfo{"testVID": {
							Id: "testVID",
							Fields: map[string]*protocol.FieldInfo{"covers": {
								FieldName:      "covers",
								InnerFieldName: "c_covers",
								FieldId:        80086,
								FieldType:      protocol.EnumFieldType_FieldTypeSet,
								VecStr:         []string{"cid1", "cid2"},
							}},
						}},
					},
				}},
				DataSetID:    2001,
				DataSourceID: 0,
			},
			args: args{
				id:       "testVID",
				failList: make(map[string]protocol.EnumMediaErrorCode),
			},
			want: map[string]*protocol.FieldInfo{"vcats": {
				FieldName:      "vcats",
				InnerFieldName: "c_vcats",
				FieldId:        80045,
				FieldType:      protocol.EnumFieldType_FieldTypeIntVec,
				VecInt:         []uint32{1, 2, 3},
			}, "covers": {
				FieldName:      "covers",
				InnerFieldName: "c_covers",
				FieldId:        80086,
				FieldType:      protocol.EnumFieldType_FieldTypeSet,
				VecStr:         []string{"cid1", "cid2"},
			}},
		}, {
			name: "测试合并指定数据源ID",
			fields: fields{
				getRsps: []*getRsp{{
					dataSourceID: 1,
					rsp: &adaptor.BatchGetFieldsResponse{
						DocInfos: map[string]*protocol.DocInfo{"testVID": {
							Id: "testVID",
							Fields: map[string]*protocol.FieldInfo{"titleX": {
								FieldName:      "titleX",
								InnerFieldName: "titleX",
								FieldId:        9,
								FieldType:      protocol.EnumFieldType_FieldTypeStr,
								StrValue:       "",
							}, "vcats": {
								FieldName:      "vcats",
								InnerFieldName: "c_vcats",
								FieldId:        80045,
								FieldType:      protocol.EnumFieldType_FieldTypeIntVec,
								VecInt:         []uint32{1, 2, 3},
							}},
						}},
					},
				}, {
					dataSourceID: 5,
					rsp: &adaptor.BatchGetFieldsResponse{
						DocInfos: map[string]*protocol.DocInfo{"testVID": {
							Id: "testVID",
							Fields: map[string]*protocol.FieldInfo{"covers": {
								FieldName:      "covers",
								InnerFieldName: "c_covers",
								FieldId:        80086,
								FieldType:      protocol.EnumFieldType_FieldTypeSet,
								VecStr:         []string{"cid1", "cid2"},
							}},
						}},
					},
				}},
				DataSetID:    2001,
				DataSourceID: 1,
				ShowDel:      true,
			},
			args: args{
				id:       "testVID",
				failList: map[string]protocol.EnumMediaErrorCode{},
			},
			want: map[string]*protocol.FieldInfo{"titleX": {
				FieldName:      "titleX",
				InnerFieldName: "titleX",
				FieldId:        9,
				FieldType:      protocol.EnumFieldType_FieldTypeStr,
				StrValue:       "",
			}, "vcats": {
				FieldName:      "vcats",
				InnerFieldName: "c_vcats",
				FieldId:        80045,
				FieldType:      protocol.EnumFieldType_FieldTypeIntVec,
				VecInt:         []uint32{1, 2, 3},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &Getter{
				getRsps:      tt.fields.getRsps,
				Priority:     tt.fields.Priority,
				DataSetID:    tt.fields.DataSetID,
				DataSourceID: tt.fields.DataSourceID,
				ShowDel:      tt.fields.ShowDel,
			}
			if got := g.MergeGetRsps(tt.args.id, tt.args.failList); !checkFieldInfo(got, tt.want) {
				t.Errorf("MergeGetRsps() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func checkFieldInfo(got, want map[string]*protocol.FieldInfo) bool {
	for k, src := range got {
		dst, ok := want[k]
		if !ok {
			log.Errorf("%s is not exist", k)
			return false
		}
		if src.String() != dst.String() {
			log.Errorf("src is %v, dst is %v", src, dst)
			return false
		}
	}
	return true
}

func checkDocInfo(got, want map[string]*protocol.DocInfo) bool {
	for k, src := range got {
		dst, ok := want[k]
		if !ok {
			log.Errorf("%s is not exist", k)
			return false
		}
		if src.String() != dst.String() {
			log.Errorf("src is %v, dst is %v", src, dst)
			return false
		}
	}
	return true
}

func TestGetter_MergeBatchGetRsps(t *testing.T) {
	type fields struct {
		getRsps      []*getRsp
		Priority     int32
		DataSetID    int32
		DataSourceID int32
		ShowDel      bool
	}
	type args struct {
		ids           []string
		fieldNum      int
		fieldFailList map[string]protocol.EnumMediaErrorCode
	}
	mockFieldInfoCache()
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[string]*protocol.DocInfo
		want1  map[string]string
	}{
		{
			name: "测试合并批量接口",
			fields: fields{
				getRsps: []*getRsp{{
					dataSourceID: 1,
					rsp: &adaptor.BatchGetFieldsResponse{
						DocInfos: map[string]*protocol.DocInfo{"testVID1": {
							Id: "testVID1",
							Fields: map[string]*protocol.FieldInfo{"titleX": {
								FieldName:      "titleX",
								InnerFieldName: "titleX",
								FieldId:        9,
								FieldType:      protocol.EnumFieldType_FieldTypeStr,
								StrValue:       "测试标题1",
							}, "vcats": {
								FieldName:      "vcats",
								InnerFieldName: "c_vcats",
								FieldId:        80045,
								FieldType:      protocol.EnumFieldType_FieldTypeIntVec,
								VecInt:         []uint32{1, 2, 3},
							}},
						}, "testVID2": {
							Id: "testVID2",
							Fields: map[string]*protocol.FieldInfo{"titleX": {
								FieldName:      "titleX",
								InnerFieldName: "titleX",
								FieldId:        9,
								FieldType:      protocol.EnumFieldType_FieldTypeStr,
								StrValue:       "测试标题2",
							}, "vcats": {
								FieldName:      "vcats",
								InnerFieldName: "c_vcats",
								FieldId:        80045,
								FieldType:      protocol.EnumFieldType_FieldTypeIntVec,
								VecInt:         []uint32{1, 2, 3},
							}},
						}},
					},
				}, {
					dataSourceID: 5,
					rsp: &adaptor.BatchGetFieldsResponse{
						DocInfos: map[string]*protocol.DocInfo{"testVID1": {
							Id: "testVID1",
							Fields: map[string]*protocol.FieldInfo{"covers": {
								FieldName:      "covers",
								InnerFieldName: "c_covers",
								FieldId:        80086,
								FieldType:      protocol.EnumFieldType_FieldTypeSet,
								VecStr:         []string{"cid1", "cid2"},
							}},
						}},
					},
				}},
				DataSetID:    2001,
				DataSourceID: 0,
			},
			args: args{
				ids:      []string{"testVID1", "testVID2"},
				fieldNum: 4,
				fieldFailList: map[string]protocol.EnumMediaErrorCode{
					"invalidField": protocol.EnumMediaErrorCode_RetFieldInfoNotExist,
				},
			},
			want: map[string]*protocol.DocInfo{"testVID1": {
				Id: "testVID1",
				Fields: map[string]*protocol.FieldInfo{"titleX": {
					FieldName:      "titleX",
					InnerFieldName: "titleX",
					FieldId:        9,
					FieldType:      protocol.EnumFieldType_FieldTypeStr,
					StrValue:       "测试标题1",
				}, "vcats": {
					FieldName:      "vcats",
					InnerFieldName: "c_vcats",
					FieldId:        80045,
					FieldType:      protocol.EnumFieldType_FieldTypeIntVec,
					VecInt:         []uint32{1, 2, 3},
				}, "covers": {
					FieldName:      "covers",
					InnerFieldName: "c_covers",
					FieldId:        80086,
					FieldType:      protocol.EnumFieldType_FieldTypeSet,
					VecStr:         []string{"cid1", "cid2"},
				}},
				FailList: map[string]*protocol.FailInfo{"invalidField": {
					ErrCode: protocol.EnumMediaErrorCode_RetFieldInfoNotExist,
				}},
			}, "testVID2": {
				Id: "testVID2",
				Fields: map[string]*protocol.FieldInfo{"titleX": {
					FieldName:      "titleX",
					InnerFieldName: "titleX",
					FieldId:        9,
					FieldType:      protocol.EnumFieldType_FieldTypeStr,
					StrValue:       "测试标题2",
				}, "vcats": {
					FieldName:      "vcats",
					InnerFieldName: "c_vcats",
					FieldId:        80045,
					FieldType:      protocol.EnumFieldType_FieldTypeIntVec,
					VecInt:         []uint32{1, 2, 3},
				}},
				FailList: map[string]*protocol.FailInfo{"invalidField": {
					ErrCode: protocol.EnumMediaErrorCode_RetFieldInfoNotExist,
				}},
			}},
			want1: map[string]string{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &Getter{
				getRsps:      tt.fields.getRsps,
				Priority:     tt.fields.Priority,
				DataSetID:    tt.fields.DataSetID,
				DataSourceID: tt.fields.DataSourceID,
				ShowDel:      tt.fields.ShowDel,
			}
			got, got1 := g.MergeBatchGetRsps(tt.args.ids, tt.args.fieldNum, tt.args.fieldFailList)
			if !checkDocInfo(got, tt.want) {
				t.Errorf("MergeBatchGetRsps() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("MergeBatchGetRsps() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestGetter_mergeFailList(t *testing.T) {
	type fields struct {
		getRsps      []*getRsp
		Priority     int32
		DataSetID    int32
		DataSourceID int32
		ShowDel      bool
	}
	tests := []struct {
		name   string
		fields fields
		want   map[string]string
	}{
		{
			name: "测试外部failList合并逻辑",
			fields: fields{
				getRsps: []*getRsp{{
					dataSourceID: 1,
					rsp: &adaptor.BatchGetFieldsResponse{
						FailList: map[string]string{"testVID1": "overload", "testVID2": "overload"},
					},
				}, {
					dataSourceID: 5,
					rsp: &adaptor.BatchGetFieldsResponse{
						FailList: map[string]string{"testVID2": "overload"},
					},
				}},
				DataSetID: 2001,
			},
			want: map[string]string{"testVID2": "{datasource:1, errMsg:overload}{datasource:5, errMsg:overload}"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &Getter{
				getRsps:      tt.fields.getRsps,
				Priority:     tt.fields.Priority,
				DataSetID:    tt.fields.DataSetID,
				DataSourceID: tt.fields.DataSourceID,
				ShowDel:      tt.fields.ShowDel,
			}
			if got := g.mergeFailList(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("mergeFailList() = %v, want %v", got, tt.want)
			}
		})
	}
}
