// Package group 请求分组逻辑
package group

import (
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
)

// Group get请求分组
type Group struct {
	batchReqs []*adaptor.BatchGetFieldsRequest
	// checker 用于分组去重，key:dataSourceID value: index of batchReqs
	checker map[int32]int
	// TenantID 租户ID
	TenantID string
}

// New 创建分组对象
func New(tenantID string) *Group {
	return &Group{
		checker:  make(map[int32]int),
		TenantID: tenantID,
	}
}

// GetBatchRequests 获取分组后请求信息
func (g *Group) GetBatchRequests() []*adaptor.BatchGetFieldsRequest {
	if g == nil {
		return nil
	}
	return g.batchReqs
}

func getDataSourceID(dataSourceID, fDataSourceID int32) int32 {
	// dataSourceID为0时使用字段本身配置，否则使用传入数据源ID
	if dataSourceID == 0 {
		return fDataSourceID
	}
	return dataSourceID
}

func (g *Group) addField(dataSourceID, dataSetID int32, ids []string, field string) {
	i, ok := g.checker[dataSourceID]
	if ok {
		g.batchReqs[i].FieldNames = append(g.batchReqs[i].FieldNames, field)
		return
	}

	g.checker[dataSourceID] = len(g.batchReqs)
	g.batchReqs = append(g.batchReqs, &adaptor.BatchGetFieldsRequest{
		DataSourceID: dataSourceID,
		Id:           ids,
		DataSetID:    dataSetID,
		FieldNames:   []string{field},
	})
}

// DoGrouping 进行分组
func (g *Group) DoGrouping(dataSetID, dataSourceID, rawDataSourceID int32, rawField string, ids []string) error {
	dataSourceID = getDataSourceID(dataSourceID, rawDataSourceID)
	if dataSourceID > 0 {
		g.addField(dataSourceID, dataSetID, ids, rawField)
		return nil
	}

	// 数据源ID配置为0的字段需要到所有数据源查询
	for _, dataSourceID = range cache.GetDataSources(dataSetID, g.TenantID) {
		g.addField(dataSourceID, dataSetID, ids, rawField)
	}
	return nil
}
