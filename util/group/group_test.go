package group

import (
	"reflect"
	"testing"

	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	"github.com/agiledragon/gomonkey/v2"
)

func TestGroup_DoGrouping(t *testing.T) {
	type fields struct {
		batchReqs []*adaptor.BatchGetFieldsRequest
		checker   map[int32]int
	}
	type args struct {
		dataSetID       int32
		dataSourceID    int32
		rawDataSourceID []int32
		rawField        []string
		ids             []string
	}
	// mock dataSources
	gomonkey.ApplyFunc(cache.GetDataSources, func(datasetID int32, tenant string) []int32 {
		return []int32{1, 5, 105}
	})
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  bool
		wantReqs []*adaptor.BatchGetFieldsRequest
	}{
		{
			name:   "测试分组逻辑",
			fields: fields{checker: make(map[int32]int)},
			args: args{
				dataSetID:       2001,
				rawDataSourceID: []int32{1, 1, 5, 1, 0},
				rawField:        []string{"titleX", "vcats", "covers", "alias_multi_language.en", "entity_version"},
				ids:             []string{"VID1", "VID2"},
			},
			wantErr: false,
			wantReqs: []*adaptor.BatchGetFieldsRequest{{
				DataSourceID: 1,
				Id:           []string{"VID1", "VID2"},
				FieldNames:   []string{"titleX", "vcats", "alias_multi_language.en", "entity_version"},
				DataSetID:    2001,
			}, {
				DataSourceID: 5,
				Id:           []string{"VID1", "VID2"},
				FieldNames:   []string{"covers", "entity_version"},
				DataSetID:    2001,
			}, {
				DataSourceID: 105,
				Id:           []string{"VID1", "VID2"},
				FieldNames:   []string{"entity_version"},
				DataSetID:    2001,
			}},
		}, {
			name:   "测试指定数据源分组逻辑",
			fields: fields{checker: make(map[int32]int)},
			args: args{
				dataSetID:       2001,
				dataSourceID:    1,
				rawDataSourceID: []int32{1, 1, 5, 0},
				rawField:        []string{"titleX", "vcats", "covers", "entity_version"},
				ids:             []string{"VID1", "VID2"},
			},
			wantErr: false,
			wantReqs: []*adaptor.BatchGetFieldsRequest{{
				DataSourceID: 1,
				Id:           []string{"VID1", "VID2"},
				FieldNames:   []string{"titleX", "vcats", "covers", "entity_version"},
				DataSetID:    2001,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &Group{
				batchReqs: tt.fields.batchReqs,
				checker:   tt.fields.checker,
			}
			for i := range tt.args.rawDataSourceID {
				if err := g.DoGrouping(tt.args.dataSetID, tt.args.dataSourceID, tt.args.rawDataSourceID[i], tt.args.rawField[i],
					tt.args.ids); (err != nil) != tt.wantErr {
					t.Errorf("DoGrouping() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
			if !reflect.DeepEqual(g.batchReqs, tt.wantReqs) {
				t.Errorf("DoGrouping() got = %v, want = %v", g.batchReqs, tt.wantReqs)
			}
		})
	}
}
