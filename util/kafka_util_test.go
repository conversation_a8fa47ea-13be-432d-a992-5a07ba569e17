package util

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/server"
	"git.code.oa.com/trpcprotocol/storage_service/access_layer"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	tool "git.code.oa.com/video_media/storage_service/common"

	"github.com/Shopify/sarama"
	"github.com/golang/mock/gomock"
	"github.com/prashantv/gostub"
	"google.golang.org/protobuf/proto"
)

func Test_produceErrHandle(t *testing.T) {
	type args struct {
		err    error
		topic  string
		key    string
		target string
		value  []byte
	}
	var tests []struct {
		name string
		args args
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			produceErrHandle(tt.args.err, tt.args.topic, tt.args.key, tt.args.target, tt.args.value)
		})
	}
}

func Test_parseKafkaInfo(t *testing.T) {
	type args struct {
		topicInfo string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
	}{
		{"非空", args{"a|b"}, "a", "b"},
		{"空", args{"a"}, "a", ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := parseKafkaInfo(tt.args.topicInfo)
			if got != tt.want {
				t.Errorf("parseKafkaInfo() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("parseKafkaInfo() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestSendModifyMsg(t *testing.T) {
	type args struct {
		key    string
		topic  string
		target string
		value  []byte
	}
	var tests []struct {
		name    string
		args    args
		wantErr bool
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SendModifyMsg(context.TODO(), tt.args.key, tt.args.topic, tt.args.target,
				tt.args.value); (err != nil) != tt.wantErr {
				t.Errorf("SendModifyMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_checkUpdateEffect(t *testing.T) {
	type args struct {
		src *protocol.ModifyFieldInfo
		dst *protocol.FieldInfo
	}
	tests := []struct {
		name       string
		args       args
		want       bool
		wantMapVal map[string]*protocol.MapValue
	}{
		{
			name: "测试文本字段是否变更",
			args: args{
				src: &protocol.ModifyFieldInfo{
					FieldName:   "title",
					FieldType:   protocol.EnumFieldType_FieldTypeStr,
					OldStrValue: "",
					NewStrValue: "new title",
				},
				dst: &protocol.FieldInfo{
					FieldName: "title",
					FieldType: protocol.EnumFieldType_FieldTypeStr,
					StrValue:  "new title",
				},
			},
			want: true,
		}, {
			name: "测试选项类型字段是否变更",
			args: args{
				src: &protocol.ModifyFieldInfo{
					FieldName: "category_value",
					FieldType: protocol.EnumFieldType_FieldTypeIntVec,
					OldVecInt: nil,
					NewVecInt: []uint32{10001},
				},
				dst: &protocol.FieldInfo{
					FieldName: "category_value",
					FieldType: protocol.EnumFieldType_FieldTypeIntVec,
					VecInt:    []uint32{10001},
				},
			},
			want: true,
		}, {
			name: "测试set类型字段是否变更-1",
			args: args{
				src: &protocol.ModifyFieldInfo{
					FieldName: "video_list",
					FieldType: protocol.EnumFieldType_FieldTypeSet,
					OldVecStr: nil,
					NewVecStr: []string{"vid1", "vid2", "vid3"},
				},
				dst: &protocol.FieldInfo{
					FieldName: "video_list",
					FieldType: protocol.EnumFieldType_FieldTypeSet,
					VecStr:    []string{"vid1", "vid2", "vid3"},
				},
			},
			want: true,
		}, {
			name: "测试set类型字段是否变更-2",
			args: args{
				src: &protocol.ModifyFieldInfo{
					FieldName: "video_list",
					FieldType: protocol.EnumFieldType_FieldTypeSet,
					OldVecStr: nil,
					NewVecStr: []string{"vid1", "vid2", "vid3"},
				},
				dst: &protocol.FieldInfo{
					FieldName: "video_list",
					FieldType: protocol.EnumFieldType_FieldTypeSet,
					VecStr:    []string{"vid2", "vid3"},
				},
			},
			want: false,
		}, {
			name: "测试map类型字段是否变更-1",
			args: args{
				src: &protocol.ModifyFieldInfo{
					FieldName: "title_map",
					FieldType: protocol.EnumFieldType_FieldTypeMapKV,
					OldMapVal: nil,
					NewMapVal: map[string]*protocol.MapValue{"en": {
						Type:     protocol.EnumFieldType_FieldTypeStr,
						StrValue: "new title",
					}, "cn": {
						Type:     protocol.EnumFieldType_FieldTypeStr,
						StrValue: "新标题",
					}},
				},
				dst: &protocol.FieldInfo{
					FieldName: "title_map",
					FieldType: protocol.EnumFieldType_FieldTypeMapKV,
					MapVal: map[string]*protocol.MapValue{"en": {
						Type:     protocol.EnumFieldType_FieldTypeStr,
						StrValue: "new title1",
					}, "cn": {
						Type:     protocol.EnumFieldType_FieldTypeStr,
						StrValue: "新标题",
					}},
				},
			},
			want: true,
			wantMapVal: map[string]*protocol.MapValue{"cn": {
				Type:     protocol.EnumFieldType_FieldTypeStr,
				StrValue: "新标题",
			}},
		}, {
			name: "测试map类型字段是否变更-2",
			args: args{
				src: &protocol.ModifyFieldInfo{
					FieldName: "title_map",
					FieldType: protocol.EnumFieldType_FieldTypeMapKV,
					OldMapVal: nil,
					NewMapVal: map[string]*protocol.MapValue{"en": {
						Type:     protocol.EnumFieldType_FieldTypeStr,
						StrValue: "new title",
					}, "cn": {
						Type:     protocol.EnumFieldType_FieldTypeStr,
						StrValue: "新标题",
					}},
				},
				dst: &protocol.FieldInfo{
					FieldName: "title_map",
					FieldType: protocol.EnumFieldType_FieldTypeMapKV,
					MapVal: map[string]*protocol.MapValue{"en": {
						Type:     protocol.EnumFieldType_FieldTypeStr,
						StrValue: "new title1",
					}, "cn": {
						Type:     protocol.EnumFieldType_FieldTypeStr,
						StrValue: "新标题1",
					}},
				},
			},
			want:       false,
			wantMapVal: map[string]*protocol.MapValue{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkUpdateEffect(tt.args.src, tt.args.dst); got != tt.want {
				t.Errorf("checkUpdateEffect() = %v, want %v", got, tt.want)
			}
			if tt.wantMapVal != nil && !reflect.DeepEqual(tt.args.src.NewMapVal, tt.wantMapVal) {
				t.Errorf("checkUpdateEffect() = %v, want %v", tt.args.src.NewMapVal, tt.wantMapVal)
			}
		})
	}
}

func Test_parseMessageKey(t *testing.T) {
	type args struct {
		key string
	}
	tests := []struct {
		name    string
		args    args
		want    int
		wantErr bool
	}{
		{
			name:    "非法message key-1",
			args:    args{key: "invalid_key"},
			want:    0,
			wantErr: true,
		}, {
			name:    "非法message key-2",
			args:    args{key: "invalid_1_vid1"},
			want:    0,
			wantErr: true,
		}, {
			name:    "正确message key",
			args:    args{key: "timeout_1_vid1"},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseMessageKey(tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseMessageKey() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("parseMessageKey() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getModifyFieldNames(t *testing.T) {
	type args struct {
		modifyInfos []*protocol.ModifyFieldInfo
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "测试生成变更字段读请求字段列表",
			args: args{modifyInfos: []*protocol.ModifyFieldInfo{{
				FieldName:   "title",
				FieldType:   protocol.EnumFieldType_FieldTypeStr,
				OldStrValue: "",
				NewStrValue: "new title",
			}, {
				FieldName: "category_value",
				FieldType: protocol.EnumFieldType_FieldTypeIntVec,
				OldVecInt: nil,
				NewVecInt: []uint32{10001},
			}, {
				FieldName: "title_map",
				FieldType: protocol.EnumFieldType_FieldTypeMapKV,
				OldMapVal: nil,
				NewMapVal: map[string]*protocol.MapValue{"cn": {
					Type:     protocol.EnumFieldType_FieldTypeStr,
					StrValue: "新标题",
				}},
			}}},
			want: []string{"title", "category_value", "title_map.cn"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getModifyFieldNames(tt.args.modifyInfos); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getModifyFieldNames() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInitRetryMQHandler(t *testing.T) {
	type args struct {
		s *server.Server
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "测试初始化重试队列handler",
			args: args{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			InitRetryMQHandler(tt.args.s)
		})
	}
}

func TestSendJceModifyMsg(t *testing.T) {
	type args struct {
		ctx         context.Context
		modifyConv  *tool.ModifyInfoConv
		topicName   string
		baseInfo    map[string]*protocol.FieldInfo
		modifyInfos []*protocol.ModifyFieldInfo
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "测试发变更消息",
			args: args{
				ctx: context.TODO(),
				modifyConv: &tool.ModifyInfoConv{
					ID:           "testVID",
					DataSetID:    2001,
					AppID:        "testApp",
					OperatorName: "testApp",
				},
				topicName: "test_topic",
				baseInfo: map[string]*protocol.FieldInfo{"media_flag": {
					FieldName: "media_flag",
					FieldId:   80056,
					FieldType: protocol.EnumFieldType_FieldTypeStr,
					StrValue:  "1",
				}},
				modifyInfos: []*protocol.ModifyFieldInfo{{
					FieldName:   "title",
					FieldId:     9,
					FieldType:   protocol.EnumFieldType_FieldTypeStr,
					OldStrValue: "",
					NewStrValue: "new title",
				}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SendJceModifyMsg(tt.args.ctx, tt.args.modifyConv, tt.args.topicName, tt.args.baseInfo, tt.args.modifyInfos)
		})
	}
}

func newModifyInfoValue(notify *access_layer.ModifyNotify) []byte {
	data, _ := proto.Marshal(notify)
	return data
}

func TestRetryHandler_Handle(t *testing.T) {
	type args struct {
		ctx context.Context
		msg *sarama.ConsumerMessage
	}
	// mock adaptor
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	proxy := adaptor.NewMockDataAdaptorClientProxy(ctrl)
	stub := gostub.Stub(&adaptor.NewDataAdaptorClientProxy, func(opts ...client.Option) adaptor.DataAdaptorClientProxy {
		return proxy
	})
	defer stub.Reset()
	proxy.EXPECT().BatchGetFieldInfos(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&adaptor.BatchGetFieldsResponse{
			DocInfos: map[string]*protocol.DocInfo{"testVID": {
				Id: "testVID",
				Fields: map[string]*protocol.FieldInfo{"title": {
					FieldName: "title",
					FieldId:   9,
					FieldType: protocol.EnumFieldType_FieldTypeStr,
					StrValue:  "new title",
				}},
			}},
		}, nil).AnyTimes()

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试消费handler",
			args: args{
				ctx: context.TODO(),
				msg: &sarama.ConsumerMessage{
					Key: []byte("timeout_1_testVID"),
					Value: newModifyInfoValue(&access_layer.ModifyNotify{
						Id:        "testVID",
						DataSetId: 2001,
						ModifyFieldInfos: []*protocol.ModifyFieldInfo{{
							FieldName:   "title",
							FieldId:     9,
							FieldType:   protocol.EnumFieldType_FieldTypeStr,
							OldStrValue: "",
							NewStrValue: "new title",
						}},
						AppId:        "testApp",
						OperatorName: "testApp",
					}),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RetryHandler{}
			if err := r.Handle(tt.args.ctx, tt.args.msg); (err != nil) != tt.wantErr {
				t.Errorf("Handle() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
