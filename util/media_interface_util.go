package util

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	access "git.code.oa.com/trpcprotocol/storage_service/access_layer"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/trpcprotocol/video_media/metadata_api"
	"git.code.oa.com/video_media/storage_service/access_layer/config"
	"git.code.oa.com/video_media/storage_service/access_layer/util/checker"
	"git.code.oa.com/video_media/storage_service/access_layer/util/errs"
	"git.code.oa.com/video_media/storage_service/access_layer/util/group"
	"git.code.oa.com/video_media/storage_service/access_layer/util/report"
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"
)

// 系统内部字段
const (
	modifyTime = "modify_time"
	primaryKey = "primary_key"
	eventID    = "sys_event_id"
)

// isOpTypeInvalid 检查更新操作是否合法
// rule:EnumUpdateType_UpdateTypeInsert,EnumUpdateType_UpdateTypeReorder和EnumUpdateType_UpdateTypeReverse只支持Set类型字段
// Pos为-1时为插入或者重排序到尾部
func isOpTypeInvalid(upFieldInfo *protocol.UpdateFieldInfo) bool {
	OpType := upFieldInfo.UpdateType
	if OpType < protocol.EnumUpdateType_UpdateTypeSet || OpType > protocol.EnumUpdateType_UpdateTypeReverse {
		return true
	}

	if upFieldInfo.Pos < -1 {
		return true
	}

	fieldType := upFieldInfo.FieldInfo.FieldType
	if fieldType < protocol.EnumFieldType_FieldTypeSet && OpType > protocol.EnumUpdateType_UpdateTypeAppend {
		return true
	}
	return false
}

// AddBaseInfoByDataSetID 添加基础信息到update请求里面，用于填充变更消息
func AddBaseInfoByDataSetID(id string, dataSetInfo *item.DataSet, repMap map[int32]*adaptor.SetFieldInfosRequest) {
	// 未配置基础字段直接跳过
	if dataSetInfo.BaseInfo == "" {
		return
	}

	baseFields := strings.Split(dataSetInfo.BaseInfo, "|")
	for _, fieldName := range baseFields {
		if f := cache.GetFieldInfoByName(fieldName, dataSetInfo.DataSetID); f != nil {
			req, ok := repMap[f.DataSourceID]
			if !ok {
				req = &adaptor.SetFieldInfosRequest{
					DataSourceId: f.DataSourceID,
					Id:           id,
				}
				repMap[f.DataSourceID] = req
			}
			req.BaseFieldIds = append(req.BaseFieldIds, &protocol.FieldInfo{
				FieldName:      f.FieldName,
				FieldType:      protocol.EnumFieldType(f.FieldType),
				FieldId:        f.FieldID,
				InnerFieldName: f.InnerFieldName,
			})
		}
	}
}

func addUpdateRequestMap(dataSourceID int32, id string, adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest,
	updateField *protocol.UpdateFieldInfo,
) {
	if updateField.GetFieldInfo().GetFieldName() == primaryKey {
		// 主键字段不使用更新请求里的值
		updateField.GetFieldInfo().StrValue = id
	}

	if v, ok := adaptorRepMap[dataSourceID]; ok {
		v.FieldInfos = append(v.FieldInfos, updateField)
	} else {
		adaptorRepMap[dataSourceID] = &adaptor.SetFieldInfosRequest{
			DataSourceId: dataSourceID,
			Id:           id,
			FieldInfos:   []*protocol.UpdateFieldInfo{updateField},
		}
	}
}

// ClassifyUpdateReqBySourceID 按照数据源id分类数据适配层更新请求
func ClassifyUpdateReqBySourceID(ctx context.Context, adaptorReps map[int32]*adaptor.SetFieldInfosRequest,
	req *access.MediaUpdateRequest, tenantID string, failList map[string]protocol.EnumMediaErrorCode,
) error {
	// 从缓存中拉取该appId在指定数据集下可读写字段列表
	appInfo := cache.GetAppFieldInfo(req.GetAuthInfo().GetAppId(), req.GetDataSetId())
	if appInfo == nil {
		log.ErrorContextf(ctx, "AppInfo is not exist, appID:%s, dataSetID:%d.", req.GetAuthInfo().GetAppId(),
			req.GetDataSetId())
		return errs.New(protocol.EnumMediaErrorCode_RetNoAppField)
	}

	// 判断appId对字段是否有写权限，有则透传到数据适配层请求，没有则放入相应的FailList当中
	c := checker.New(tenantID, appInfo.WriteListText)
	for _, updateField := range req.GetUpdateFieldInfos() {
		field := updateField.GetFieldInfo()
		f := cache.GetFieldInfoByName(field.FieldName, req.GetDataSetId())
		if f == nil {
			failList[field.GetFieldName()] = protocol.EnumMediaErrorCode_RetFieldInfoNotExist
			continue
		}
		if f.FieldStatus >= int32(metadata_api.EFieldStatus_BACKEND_WRITE_OFFLINE) {
			// 写退场的字段不需要更新，直接跳过
			log.DebugContextf(ctx, "field:%+v need offline, pass update.", f)
			continue
		}

		// 透传operatorName operatorIp 和 appID
		if err := c.CheckUpdateField(
			ctx,
			&checker.CallerInfo{
				ID:           req.GetId(),
				OperatorName: req.GetOperatorName(),
				AppID:        req.GetAuthInfo().GetAppId(),
			},
			f,
			updateField,
		); err != nil {
			failList[field.GetFieldName()] = errs.Code(err)
			continue
		}

		// 填充请求字段id
		field.FieldId = f.FieldID
		datasourceID := f.DataSourceID

		if req.GetDataSourceId() > 0 {
			// 指定数据源ID则使用指定值
			datasourceID = req.GetDataSourceId()
		}
		// 根据字段不同的数据源拆分数据适配层服务请求
		addUpdateRequestMap(datasourceID, req.GetId(), adaptorReps, updateField)
	}
	// 添加更新系统内部字段
	addMtimeField(req.GetId(), req.GetDataSetId(), adaptorReps)
	addPrimaryKeyField(req.GetId(), req.GetDataSetId(), adaptorReps)
	addEventIDField(ctx, req.GetId(), req.GetDataSetId(), adaptorReps)
	log.DebugContextf(ctx, "Update requests classified by datasource is:%+v.", adaptorReps)
	return nil
}

func addPrimaryKeyField(id string, dataSetID int32, adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest) {
	f := cache.GetFieldInfoByName(primaryKey, dataSetID)
	// 没有配置主键字段的数据集直接跳过
	if f == nil {
		return
	}

	addUpdateRequestMap(f.DataSourceID, id, adaptorRepMap, &protocol.UpdateFieldInfo{
		FieldInfo: &protocol.FieldInfo{
			FieldName:      f.FieldName,
			InnerFieldName: f.InnerFieldName,
			FieldId:        f.FieldID,
			FieldType:      protocol.EnumFieldType_FieldTypeStr,
			StrValue:       id,
		},
		UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
	})
}

// addEventIDField 添加eventID字段(用于事件总线)
func addEventIDField(ctx context.Context, id string, dataSetID int32,
	adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest) {
	metaEventID := string(trpc.GetMetaData(ctx, eventID))
	if metaEventID == "" {
		return
	}

	f := cache.GetFieldInfoByName(eventID, dataSetID)
	// 没有配置的数据集直接跳过
	if f == nil {
		return
	}

	addUpdateRequestMap(f.DataSourceID, id, adaptorRepMap, &protocol.UpdateFieldInfo{
		FieldInfo: &protocol.FieldInfo{
			FieldName:      f.FieldName,
			InnerFieldName: f.InnerFieldName,
			FieldId:        f.FieldID,
			FieldType:      protocol.EnumFieldType_FieldTypeStr,
			StrValue:       metaEventID,
		},
		UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
	})
}

// addMtimeField 视频和专辑数据追加修改时间字段 todo:最好做成配置方式，代替现有hardcode方式
func addMtimeField(id string, dataSetID int32, adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest) {
	// 目前只有2001，2003数据集支持追加修改时间字段
	if dataSetID != 2001 && dataSetID != 2003 {
		return
	}
	// 没有修改字段直接跳过
	if len(adaptorRepMap) == 0 {
		return
	}

	f := cache.GetFieldInfoByName(modifyTime, dataSetID)
	// 查找变更字段失败
	if f == nil {
		return
	}

	// 添加一个修改时间字段
	addUpdateRequestMap(f.DataSourceID, id, adaptorRepMap, &protocol.UpdateFieldInfo{
		FieldInfo: &protocol.FieldInfo{
			FieldName:      f.FieldName,
			InnerFieldName: f.InnerFieldName,
			FieldId:        f.FieldID,
			FieldType:      protocol.EnumFieldType_FieldTypeStr,
			StrValue:       time.Now().Format("2006-01-02 15:04:05"),
		},
		UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
	})
}

func buildGetAllReqMap(dataSetID int32, id, tenantID string) ([]*adaptor.BatchGetFieldsRequest, error) {
	// 获取指定dataSetId绑定数据源id列表
	dataSourceList := cache.GetDataSources(dataSetID, tenantID)
	if len(dataSourceList) == 0 {
		log.Errorf("Can't find dataSource list of dataSet:%d, tenantID is %s.", dataSetID, tenantID)
		return nil, errs.New(protocol.EnumMediaErrorCode_RetFieldInfoNotExist)
	}

	log.Infof("[ClassifyGetAllReqBySourceID] dataSourceList is %+v.", dataSourceList)
	batchGetReqs := make([]*adaptor.BatchGetFieldsRequest, 0, len(dataSourceList))
	for _, dataSourceID := range dataSourceList {
		batchGetReqs = append(batchGetReqs, &adaptor.BatchGetFieldsRequest{
			DataSourceID: dataSourceID,
			Id:           []string{id},
			FieldNames:   []string{"*"},
			DataSetID:    dataSetID,
		})
	}
	return batchGetReqs, nil
}

// parseGetReqFieldName 解析get请求字段名
func parseGetReqFieldName(rawField string) string {
	if strings.Contains(rawField, ".") {
		// map类型字段请求样式为{fieldName}.{key}
		return strings.Split(rawField, ".")[0]
	}
	return rawField
}

// ClassifyGetReqBySourceID 按照数据源id分类get请求
func ClassifyGetReqBySourceID(ctx context.Context, appID string, datasetID, datasourceID int32, ids, fields []string) (
	[]*adaptor.BatchGetFieldsRequest, map[string]protocol.EnumMediaErrorCode, error,
) {
	// 获取appID配置字段读权限
	appInfo := cache.GetAppFieldInfo(appID, datasetID)
	if appInfo == nil {
		log.Errorf("AppInfo is not exist, appID:%s, dataSetID:%d.", appID, datasetID)
		return nil, nil, errs.New(protocol.EnumMediaErrorCode_RetNoAppField)
	}

	// 判断app对字段是否有读权限，有透传到数据适配层请求，没有则放入相应的failList当中
	tenantID := config.GetTenantID(ctx)
	g := group.New(tenantID)
	c := checker.New(tenantID, appInfo.ReadListText)
	failList := make(map[string]protocol.EnumMediaErrorCode)
	for _, rawField := range fields {
		fieldName := parseGetReqFieldName(rawField)
		f := cache.GetFieldInfoByName(fieldName, datasetID)
		if f == nil {
			log.Errorf("Field:%s-%d is not exist.", fieldName, datasetID)
			failList[fieldName] = protocol.EnumMediaErrorCode_RetFieldInfoNotExist
			continue
		}

		if c.CheckPermission(fieldName) != nil || c.CheckTenantInfo(f.TenantID) != nil {
			failList[fieldName] = protocol.EnumMediaErrorCode_RetNoPermission
			continue
		}

		err := g.DoGrouping(datasetID, datasourceID, f.DataSourceID, rawField, ids)
		if err != nil {
			failList[fieldName] = errs.Code(err)
		}
	}
	return g.GetBatchRequests(), failList, nil
}

// ClassifyGetAllReqBySourceID 根据数据源id分类getAll请求
func ClassifyGetAllReqBySourceID(ctx context.Context, req *access.MediaGetAllRequest,
	failList map[string]protocol.EnumMediaErrorCode,
) ([]*adaptor.BatchGetFieldsRequest, error) {
	// 整理可读权限字段
	datasetID := req.GetDataSetId()
	appInfo := cache.GetAppFieldInfo(req.GetAuthInfo().GetAppId(), datasetID)
	if appInfo == nil {
		log.Errorf("AppInfo is not exist, appID:%s, datasetID:%d.", req.GetAuthInfo().GetAppId(), datasetID)
		return nil, errs.New(protocol.EnumMediaErrorCode_RetNoAppField)
	}

	tenantID := config.GetTenantID(ctx)
	if appInfo.ReadListText == "*" {
		// *号的单独优化，只获取数据源id用于分发
		return buildGetAllReqMap(datasetID, req.GetId(), tenantID)
	}

	g := group.New(tenantID)
	for _, rawField := range strings.Split(appInfo.ReadListText, ",") {
		fieldName := parseGetReqFieldName(rawField)
		f := cache.GetFieldInfoByName(fieldName, datasetID)
		if f == nil {
			log.Errorf("Field:%s-%d is not exist.", fieldName, datasetID)
			failList[fieldName] = protocol.EnumMediaErrorCode_RetFieldInfoNotExist
			continue
		}

		if err := g.DoGrouping(datasetID, 0, f.DataSourceID, rawField,
			[]string{req.GetId()}); err != nil {
			failList[fieldName] = errs.Code(err)
		}
	}
	return g.GetBatchRequests(), nil
}

// UpdateReqData Update请求数据
type UpdateReqData struct {
	// Priority 请求优先级
	Priority int32
	// CallID 请求id
	CallID int64
	// ClientIP 客户端ip
	ClientIP string
	// SetName 数据集对应set名
	SetName string
	// TopicName 数据集对应topic
	TopicName   string
	BaseInfo    map[string]*protocol.FieldInfo
	ModifyInfos []*protocol.ModifyFieldInfo
	AdaptorReqs map[int32]*adaptor.SetFieldInfosRequest
}

func (u *UpdateReqData) addModifyInfos(adaptorRsp *adaptor.SetFieldInfosResponse) {
	if adaptorRsp == nil {
		return
	}

	u.ModifyInfos = append(u.ModifyInfos, adaptorRsp.ModifyInfos...)
	for key, val := range adaptorRsp.BaseInfo {
		u.BaseInfo[key] = val
	}
}

func handleUpdateErr(retInfo *protocol.CommRetInfo, setRsp *setRspData) {
	if setRsp.rsp != nil {
		// 合并failList
		rspRetInfo := setRsp.rsp.RetInfo
		for key, val := range rspRetInfo.GetFailList() {
			retInfo.FailList[key] = val
		}
	}

	// 将不同数据源上的错误响应添加到整体的错误信息里面
	errCode := errs.Code(setRsp.err)
	if errCode != protocol.EnumMediaErrorCode_RetSuccess {
		retInfo.ErrCode = protocol.EnumMediaErrorCode_RetCallAdaptorErr
		retInfo.ErrMsg += fmt.Sprintf("%d:%d:%s|", setRsp.dataSourceID, errCode, errs.Msg(setRsp.err))
	}
}

// setRspData set接口响应数据
type setRspData struct {
	// dataSourceID 数据源id
	dataSourceID int32
	// id 数据id
	id string
	// baseFields 基础信息字段
	baseFields []*protocol.FieldInfo
	// updateFields 数据源原始请求
	updateFields []*protocol.UpdateFieldInfo
	// rsp 数据源响应
	rsp *adaptor.SetFieldInfosResponse
	// err 数据源响应错误
	err error
}

// AdaptorLayerOptions 生成数据适配层调用Option
func AdaptorLayerOptions(dataSourceID int32, setName string, options ...client.Option) []client.Option {
	node := &registry.Node{}
	// 路由规则:如果是混布(以common开头)或者saas场景则使用原set名
	// 否则使用setName+数据源id作为set名
	if !strings.HasPrefix(setName, "common") && !strings.HasPrefix(setName, "saas") {
		setName = strings.ReplaceAll(setName, "*", strconv.Itoa(int(dataSourceID)))
	}

	options = append(options, client.WithSelectorNode(node), client.WithCalleeSetName(setName))
	return options
}

// 未修改的不需要发变更通知，只有modify_time发生变化也不发变更通知
// isPassSendModifyMsg 是否跳过发送变更数据
func isPassSendModifyMsg(retInfo *protocol.CommRetInfo, modifyInfos []*protocol.ModifyFieldInfo) bool {
	if len(modifyInfos) == 0 {
		if retInfo.ErrCode == protocol.EnumMediaErrorCode_RetCallAdaptorErr {
			// 由于更新失败导致没有变更的情况，仍需要发一个modifyFieldInfos为空的变更消息
			report.AllUpdateTimeoutCounter.Incr()
			return false
		}
		return true
	}

	if len(modifyInfos) == 1 && modifyInfos[0].FieldName == modifyTime {
		return true
	}
	return false
}

func buildUpdateFailList(upFields []*protocol.UpdateFieldInfo,
	errCode protocol.EnumMediaErrorCode,
) map[string]protocol.EnumMediaErrorCode {
	failList := make(map[string]protocol.EnumMediaErrorCode)
	for _, field := range upFields {
		failList[field.GetFieldInfo().GetFieldName()] = errCode
	}
	return failList
}

func newExtraInfoOfSetReq(ctx context.Context, updateReq *access.MediaUpdateRequest, upReqData *UpdateReqData) string {
	extra := &protocol.UpdateInfo{
		DatasetID:    updateReq.GetDataSetId(),
		AppID:        updateReq.GetAuthInfo().GetAppId(),
		OperatorName: updateReq.GetOperatorName(),
		LocalIP:      config.GetLocalIp(),
		RemoteIP:     upReqData.ClientIP,
		ExtInfo:      updateReq.GetExtInfo(),
		SequenceID:   upReqData.CallID,
		TenantID:     config.GetTenantID(ctx),
	}
	data, _ := json.Marshal(extra)
	return string(data)
}

// SendAndRevAdaptorSetReq 发送并聚集数据适配层UPDATE请求响应
func SendAndRevAdaptorSetReq(ctx context.Context, updateReq *access.MediaUpdateRequest,
	retInfo *protocol.CommRetInfo, upReqData *UpdateReqData,
) {
	var (
		// 并发结果数组下标
		idx        int32 = -1
		handles    []func() error
		setRspList = make([]*setRspData, len(upReqData.AdaptorReqs))
	)
	// 发送请求到数据适配层
	for dataSourceID, req := range upReqData.AdaptorReqs {
		dataSourceID := dataSourceID
		req := req
		handles = append(handles, func() error {
			var (
				rsp *adaptor.SetFieldInfosResponse
				err error
			)
			log.Debugf("Update request of dataSource:%d is %+v.", dataSourceID, req)
			if !IsDatasourceOverload(upReqData.Priority, dataSourceID, "UpdateMediaInfo") {
				rsp, err = tool.NewAdaptorLayerProxy(dataSourceID, []string{req.Id}).AddUpdateFields(req.FieldInfos...).
					AddBaseInfo(req.BaseFieldIds...).SetExtraInfo(newExtraInfoOfSetReq(ctx, updateReq, upReqData)).
					SetMediaInfos(ctx, AdaptorLayerOptions(dataSourceID, upReqData.SetName)...)
			} else {
				rsp = &adaptor.SetFieldInfosResponse{RetInfo: &protocol.CommRetInfo{FailList: buildUpdateFailList(
					req.FieldInfos, protocol.EnumMediaErrorCode_RetDatasourceOverLoad)}}
			}
			// 响应信息并发填入结果列表
			setRspList[atomic.AddInt32(&idx, 1)] = &setRspData{
				dataSourceID: dataSourceID, id: req.Id, baseFields: req.BaseFieldIds, updateFields: req.FieldInfos,
				rsp: rsp, err: err,
			}
			return nil
		})
	}
	_ = trpc.GoAndWait(handles...)
	// 合并多数据源并发请求结果
	for _, adaptorRsp := range setRspList {
		if adaptorRsp == nil {
			continue
		}
		log.Debugf("[SendAndRevAdaptorSetReq] dataSourceID:%d, rsp is %s, err is %v.", adaptorRsp.dataSourceID,
			adaptorRsp.rsp.String(), adaptorRsp.err)

		// 处理汇总的各数据源失败结果
		handleUpdateErr(retInfo, adaptorRsp)
		// 如果找不到对应的topic，则通过handleForSendProducerErr/handleForRevProducerErr发送失败消息到boss
		if upReqData.TopicName == "NONE" {
			// topic为NONE说明该数据集不需要发送消息
			log.Debugf("Topic of dataSet:%d is NONE, pass.", updateReq.GetDataSetId())
			continue
		}
		// 聚集变更信息
		upReqData.addModifyInfos(adaptorRsp.rsp)
	}

	if isPassSendModifyMsg(retInfo, upReqData.ModifyInfos) {
		log.Infof("Topic:%s, updateReq:%+v is no change.", upReqData.TopicName, updateReq.GetUpdateFieldInfos())
		return
	}
	SendJceModifyMsg(ctx, &tool.ModifyInfoConv{
		ID:           updateReq.GetId(),
		DataSetID:    updateReq.GetDataSetId(),
		AppID:        updateReq.GetAuthInfo().GetAppId(),
		OperatorName: updateReq.GetOperatorName(),
		LocalIP:      config.GetLocalIp(),
		RemoteIP:     upReqData.ClientIP,
		ExtInfo:      updateReq.GetExtInfo(),
		SequenceID:   upReqData.CallID,
		TenantID:     config.GetTenantID(ctx),
	}, upReqData.TopicName, upReqData.BaseInfo, upReqData.ModifyInfos)
	return
}

// FilterAdaptorReqs 过滤数据适配层服务请求
func FilterAdaptorReqs(ids []int32, adaptorReps []*adaptor.BatchGetFieldsRequest) []*adaptor.BatchGetFieldsRequest {
	newReqs := make([]*adaptor.BatchGetFieldsRequest, 0, len(ids))
	for _, req := range adaptorReps {
		dataSourceID := req.GetDataSourceID()
		for _, id := range ids {
			if id == dataSourceID {
				newReqs = append(newReqs, req)
				break
			}
		}
	}
	return newReqs
}

// NewGetFailList 根据字段名创建失败列表
func NewGetFailList(ids []string, errMsg string) map[string]string {
	failList := make(map[string]string)
	for _, f := range ids {
		failList[f] = errMsg
	}
	return failList
}
