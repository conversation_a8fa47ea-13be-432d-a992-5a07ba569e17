package util

import (
	"context"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"

	access "git.code.oa.com/trpcprotocol/storage_service/access_layer"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"
)

func Test_isPassSendModifyMsg(t *testing.T) {
	type args struct {
		retInfo     *protocol.CommRetInfo
		modifyInfos []*protocol.ModifyFieldInfo
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "测试跳过-1",
			args: args{&protocol.CommRetInfo{}, []*protocol.ModifyFieldInfo{}},
			want: true,
		}, {
			name: "测试跳过-2",
			args: args{&protocol.CommRetInfo{}, []*protocol.ModifyFieldInfo{{FieldName: "modify_time"}}},
			want: true,
		}, {
			name: "测试调用adaptor_layer失败导致modifyInfo为空，需要发送空变更消息",
			args: args{&protocol.CommRetInfo{
				ErrCode: protocol.EnumMediaErrorCode_RetCallAdaptorErr,
			}, []*protocol.ModifyFieldInfo{}},
			want: false,
		}, {
			name: "测试发送变更消息-1",
			args: args{&protocol.CommRetInfo{}, []*protocol.ModifyFieldInfo{{FieldName: "duration"}}},
			want: false,
		}, {
			name: "测试发送变更消息-2",
			args: args{&protocol.CommRetInfo{}, []*protocol.ModifyFieldInfo{
				{FieldName: "modify_time"},
				{FieldName: "duration"},
				{FieldName: "duration"},
			}},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isPassSendModifyMsg(tt.args.retInfo, tt.args.modifyInfos); got != tt.want {
				t.Errorf("isPassSendModifyMsg() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isOpTypeInvalid(t *testing.T) {
	type args struct {
		upFieldInfo *protocol.UpdateFieldInfo
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "OpType非法",
			args: args{&protocol.UpdateFieldInfo{}},
			want: true,
		}, {
			name: "Pos非法",
			args: args{&protocol.UpdateFieldInfo{UpdateType: protocol.EnumUpdateType_UpdateTypeSet, Pos: -2}},
			want: true,
		}, {
			name: "字段类型与操作不匹配",
			args: args{&protocol.UpdateFieldInfo{
				FieldInfo: &protocol.FieldInfo{
					FieldType: protocol.EnumFieldType_FieldTypeStr,
				},
				UpdateType: protocol.EnumUpdateType_UpdateTypeReverse,
			}},
			want: true,
		}, {
			name: "字段类型与操作匹配",
			args: args{&protocol.UpdateFieldInfo{
				FieldInfo: &protocol.FieldInfo{
					FieldType: protocol.EnumFieldType_FieldTypeStr,
				},
				UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
			}},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isOpTypeInvalid(tt.args.upFieldInfo); got != tt.want {
				t.Errorf("isOpTypeInvalid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUpdateReqData_addModifyInfos(t *testing.T) {
	type fields struct {
		CallID        int64
		ClientIP      string
		SetName       string
		TopicName     string
		BaseInfo      map[string]*protocol.FieldInfo
		ModifyInfos   []*protocol.ModifyFieldInfo
		AdaptorReqMap map[int32]*adaptor.SetFieldInfosRequest
	}
	type args struct {
		adaptorRsp *adaptor.SetFieldInfosResponse
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{"参数非法", fields{}, args{nil}},
		{"正常逻辑", fields{
			ModifyInfos: []*protocol.ModifyFieldInfo{{FieldName: "a"}},
		}, args{&adaptor.SetFieldInfosResponse{
			BaseInfo: map[string]*protocol.FieldInfo{},
		}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			u := &UpdateReqData{
				CallID:      tt.fields.CallID,
				ClientIP:    tt.fields.ClientIP,
				SetName:     tt.fields.SetName,
				TopicName:   tt.fields.TopicName,
				BaseInfo:    tt.fields.BaseInfo,
				ModifyInfos: tt.fields.ModifyInfos,
				AdaptorReqs: tt.fields.AdaptorReqMap,
			}
			u.addModifyInfos(tt.args.adaptorRsp)
		})
	}
}

func Test_addMtimeField(t *testing.T) {
	type args struct {
		id            string
		dataSetID     int32
		adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{"错误的数据集", args{"", 2005, nil}},
		{"没有修改字段", args{"", 2001, map[int32]*adaptor.SetFieldInfosRequest{}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			addMtimeField(tt.args.id, tt.args.dataSetID, tt.args.adaptorRepMap)
		})
	}
}

func TestAddBaseInfoByDataSetID(t *testing.T) {
	type args struct {
		id            string
		dataSetInfo   *item.DataSet
		adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest
	}
	tests := []struct {
		name string
		args args
	}{
		{"未配置基础字段", args{"", &item.DataSet{}, map[int32]*adaptor.SetFieldInfosRequest{}}},
		{"配置了基础字段", args{"", &item.DataSet{
			BaseInfo:  "a|b",
			DataSetID: 1,
		}, map[int32]*adaptor.SetFieldInfosRequest{}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			AddBaseInfoByDataSetID(tt.args.id, tt.args.dataSetInfo, tt.args.adaptorRepMap)
		})
	}
}

func Test_addUpdateRequestMap(t *testing.T) {
	type args struct {
		dataSourceID  int32
		id            string
		adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest
		updateField   *protocol.UpdateFieldInfo
	}
	tests := []struct {
		name string
		args args
	}{
		{"not ok", args{
			1, "1",
			map[int32]*adaptor.SetFieldInfosRequest{},
			&protocol.UpdateFieldInfo{},
		}},
		{"ok", args{
			1, "1",
			map[int32]*adaptor.SetFieldInfosRequest{
				1: {FieldInfos: []*protocol.UpdateFieldInfo{}},
			},
			&protocol.UpdateFieldInfo{},
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			addUpdateRequestMap(tt.args.dataSourceID, tt.args.id, tt.args.adaptorRepMap, tt.args.updateField)
		})
	}
}

func Test_handleUpdateErr(t *testing.T) {
	type args struct {
		retInfo *protocol.CommRetInfo
		setRsp  *setRspData
	}
	tests := []struct {
		name string
		args args
	}{
		{"not nil", args{
			&protocol.CommRetInfo{
				FailList: map[string]protocol.EnumMediaErrorCode{},
			},
			&setRspData{rsp: &adaptor.SetFieldInfosResponse{
				RetInfo: &protocol.CommRetInfo{
					FailList: map[string]protocol.EnumMediaErrorCode{
						"a": protocol.EnumMediaErrorCode_RetNoApp,
					},
				},
			}},
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handleUpdateErr(tt.args.retInfo, tt.args.setRsp)
		})
	}
}

func TestFilterAdaptorReqs(t *testing.T) {
	type args struct {
		ids         []int32
		adaptorReps []*adaptor.BatchGetFieldsRequest
	}
	tests := []struct {
		name string
		args args
		want []*adaptor.BatchGetFieldsRequest
	}{
		{
			name: "测试过滤指定数据集ID",
			args: args{
				ids: []int32{1, 2},
				adaptorReps: []*adaptor.BatchGetFieldsRequest{
					{DataSourceID: 11},
					{DataSourceID: 1},
					{DataSourceID: 12},
					{DataSourceID: 111},
				},
			},
			want: []*adaptor.BatchGetFieldsRequest{{DataSourceID: 1}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FilterAdaptorReqs(tt.args.ids, tt.args.adaptorReps); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FilterAdaptorReqs() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestClassifyUpdateReqBySourceID 测试按数据源ID分类数据适配层更新请求
func TestClassifyUpdateReqBySourceID(t *testing.T) {
	tests := []struct {
		name         string
		adaptorReps  map[int32]*adaptor.SetFieldInfosRequest
		req          *access.MediaUpdateRequest
		tenantID     string
		failList     map[string]protocol.EnumMediaErrorCode
		expectsPanic bool
		expectsError bool
	}{
		{
			name:         "空请求",
			adaptorReps:  make(map[int32]*adaptor.SetFieldInfosRequest),
			req:          nil,
			tenantID:     "test_tenant",
			failList:     make(map[string]protocol.EnumMediaErrorCode),
			expectsPanic: false,
			expectsError: true,
		},
		{
			name:        "基本请求结构验证",
			adaptorReps: make(map[int32]*adaptor.SetFieldInfosRequest),
			req: &access.MediaUpdateRequest{
				AuthInfo:  &access.AuthInfo{AppId: "test_app"},
				DataSetId: 1001,
				Id:        "test_id",
				UpdateFieldInfos: []*protocol.UpdateFieldInfo{
					{
						FieldInfo: &protocol.FieldInfo{
							FieldName: "title",
							FieldType: protocol.EnumFieldType_FieldTypeStr,
							StrValue:  "测试标题",
						},
						UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
					},
				},
			},
			tenantID:     "test_tenant",
			failList:     make(map[string]protocol.EnumMediaErrorCode),
			expectsPanic: false,
			expectsError: true, // 因为AppInfo不存在，会返回错误
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				if r := recover(); r != nil {
					if !tt.expectsPanic {
						t.Errorf("ClassifyUpdateReqBySourceID panicked unexpectedly: %v", r)
					}
				} else if tt.expectsPanic {
					t.Error("Expected panic but function completed normally")
				}
			}()

			ctx := context.Background()
			err := ClassifyUpdateReqBySourceID(ctx, tt.adaptorReps, tt.req, tt.tenantID, tt.failList)

			// 验证错误情况
			if tt.expectsError {
				assert.Error(t, err)
			} else if !tt.expectsPanic {
				assert.NoError(t, err)
			}

			// 基本验证：检查方法是否能正常调用
			if !tt.expectsPanic {
				assert.NotNil(t, tt.adaptorReps)
				assert.NotNil(t, tt.failList)
			}
		})
	}
}

// TestAddPrimaryKeyField 测试添加主键字段
func TestAddPrimaryKeyField(t *testing.T) {
	tests := []struct {
		name          string
		id            string
		dataSetID     int32
		adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest
		expectsPanic  bool
	}{
		{
			name:          "基本参数测试",
			id:            "test_id_123",
			dataSetID:     1001,
			adaptorRepMap: make(map[int32]*adaptor.SetFieldInfosRequest),
			expectsPanic:  false,
		},
		{
			name:          "空ID测试",
			id:            "",
			dataSetID:     1001,
			adaptorRepMap: make(map[int32]*adaptor.SetFieldInfosRequest),
			expectsPanic:  false,
		},
		{
			name:      "已存在的数据源",
			id:        "test_id_456",
			dataSetID: 1001,
			adaptorRepMap: map[int32]*adaptor.SetFieldInfosRequest{
				1: {
					DataSourceId: 1,
					Id:           "test_id_456",
					FieldInfos:   []*protocol.UpdateFieldInfo{},
				},
			},
			expectsPanic: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				if r := recover(); r != nil {
					if !tt.expectsPanic {
						t.Errorf("addPrimaryKeyField panicked unexpectedly: %v", r)
					}
				}
			}()

			originalSize := len(tt.adaptorRepMap)
			addPrimaryKeyField(tt.id, tt.dataSetID, tt.adaptorRepMap)

			// 基本验证：函数正常执行
			if !tt.expectsPanic {
				// 验证map没有被破坏
				assert.GreaterOrEqual(t, len(tt.adaptorRepMap), originalSize)
			}
		})
	}
}

// TestAddEventIDField 测试添加事件ID字段
func TestAddEventIDField(t *testing.T) {
	tests := []struct {
		name          string
		setupContext  func() context.Context
		id            string
		dataSetID     int32
		adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest
		expectsPanic  bool
	}{
		{
			name: "空事件ID",
			setupContext: func() context.Context {
				return context.Background()
			},
			id:            "test_id",
			dataSetID:     1001,
			adaptorRepMap: make(map[int32]*adaptor.SetFieldInfosRequest),
			expectsPanic:  false,
		},
		{
			name: "已存在的数据源",
			setupContext: func() context.Context {
				return context.Background()
			},
			id:        "test_id_abc",
			dataSetID: 1001,
			adaptorRepMap: map[int32]*adaptor.SetFieldInfosRequest{
				3: {
					DataSourceId: 3,
					Id:           "test_id_abc",
					FieldInfos: []*protocol.UpdateFieldInfo{
						{
							FieldInfo: &protocol.FieldInfo{
								FieldName: "other_field",
							},
						},
					},
				},
			},
			expectsPanic: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				if r := recover(); r != nil {
					if !tt.expectsPanic {
						t.Errorf("addEventIDField panicked unexpectedly: %v", r)
					}
				}
			}()

			ctx := tt.setupContext()
			originalSize := len(tt.adaptorRepMap)

			addEventIDField(ctx, tt.id, tt.dataSetID, tt.adaptorRepMap)

			// 基本验证：函数正常执行
			if !tt.expectsPanic {
				// 验证map没有被破坏
				assert.GreaterOrEqual(t, len(tt.adaptorRepMap), originalSize)
			}
		})
	}
}

// TestAddUpdateRequestMap 测试添加更新请求映射（补充测试）
func TestAddUpdateRequestMap(t *testing.T) {
	tests := []struct {
		name          string
		dataSourceID  int32
		id            string
		adaptorRepMap map[int32]*adaptor.SetFieldInfosRequest
		updateField   *protocol.UpdateFieldInfo
		expectSize    int
		expectFields  int
	}{
		{
			name:          "主键字段处理",
			dataSourceID:  1,
			id:            "test_id_123",
			adaptorRepMap: make(map[int32]*adaptor.SetFieldInfosRequest),
			updateField: &protocol.UpdateFieldInfo{
				FieldInfo: &protocol.FieldInfo{
					FieldName: "primary_key",
					StrValue:  "original_value", // 应该被ID覆盖
				},
			},
			expectSize:   1,
			expectFields: 1,
		},
		{
			name:          "普通字段添加到新数据源",
			dataSourceID:  2,
			id:            "test_id_456",
			adaptorRepMap: make(map[int32]*adaptor.SetFieldInfosRequest),
			updateField: &protocol.UpdateFieldInfo{
				FieldInfo: &protocol.FieldInfo{
					FieldName: "title",
					StrValue:  "测试标题",
				},
			},
			expectSize:   1,
			expectFields: 1,
		},
		{
			name:         "添加到已存在的数据源",
			dataSourceID: 1,
			id:           "test_id_789",
			adaptorRepMap: map[int32]*adaptor.SetFieldInfosRequest{
				1: {
					DataSourceId: 1,
					Id:           "test_id_789",
					FieldInfos: []*protocol.UpdateFieldInfo{
						{
							FieldInfo: &protocol.FieldInfo{
								FieldName: "existing_field",
							},
						},
					},
				},
			},
			updateField: &protocol.UpdateFieldInfo{
				FieldInfo: &protocol.FieldInfo{
					FieldName: "new_field",
					StrValue:  "新字段值",
				},
			},
			expectSize:   1,
			expectFields: 2, // 原有1个 + 新增1个
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("addUpdateRequestMap panicked: %v", r)
				}
			}()

			addUpdateRequestMap(tt.dataSourceID, tt.id, tt.adaptorRepMap, tt.updateField)

			assert.Equal(t, tt.expectSize, len(tt.adaptorRepMap))

			req := tt.adaptorRepMap[tt.dataSourceID]
			assert.NotNil(t, req)
			assert.Equal(t, tt.id, req.Id)
			assert.Equal(t, tt.dataSourceID, req.DataSourceId)
			assert.Equal(t, tt.expectFields, len(req.FieldInfos))

			// 验证主键字段的特殊处理
			if tt.updateField.FieldInfo.FieldName == "primary_key" {
				assert.Equal(t, tt.id, tt.updateField.FieldInfo.StrValue, "主键字段值应该被设置为ID")
			}
		})
	}
}
