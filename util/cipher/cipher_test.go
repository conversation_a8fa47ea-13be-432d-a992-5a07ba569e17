package cipher

import (
	"context"
	"testing"

	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/access_layer/config"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"

	"github.com/agiledragon/gomonkey/v2"
)

const cipherKey = "phVt1j/rKPd4rFQzjv+s97d1ArcH6mJz"

func TestDecryptGetField(t *testing.T) {
	type args struct {
		f         *item.FieldInfo
		fieldInfo *protocol.FieldInfo
	}
	// mock config
	patch := gomonkey.ApplyFunc(config.GetCipherKey, func() string {
		return cipherKey
	})
	defer patch.Reset()
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "测试字段解密",
			args: args{
				f: &item.FieldInfo{
					DataSetID:    2001,
					DataSourceID: 1,
					FieldID:      9,
					FieldType:    int32(protocol.EnumFieldType_FieldTypeStr),
					Encrypted:    1,
					FieldName:    "title",
				},
				fieldInfo: &protocol.FieldInfo{
					FieldName: "title",
					FieldId:   9,
					StrValue:  "xY8Sh1S08GRuPgncl_VPcdIl1-MhGeg5rW4=",
				},
			},
			want: "test title",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			DecryptGetField(tt.args.f, tt.args.fieldInfo)
			if tt.args.fieldInfo.StrValue != tt.want {
				t.Errorf("DecryptGetField() got = %s, want = %s", tt.args.fieldInfo.StrValue, tt.want)
			}
		})
	}
}

func TestEncryptUpdateField(t *testing.T) {
	type args struct {
		ctx    context.Context
		f      *item.FieldInfo
		update *protocol.UpdateFieldInfo
	}
	// mock config
	patch := gomonkey.ApplyFunc(config.GetCipherKey, func() string {
		return cipherKey
	})
	defer patch.Reset()
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试字段加密",
			args: args{
				ctx: context.TODO(),
				f: &item.FieldInfo{
					DataSetID:    2001,
					DataSourceID: 1,
					FieldID:      9,
					FieldType:    int32(protocol.EnumFieldType_FieldTypeStr),
					Encrypted:    1,
					FieldName:    "title",
				},
				update: &protocol.UpdateFieldInfo{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "title",
						FieldId:   9,
						FieldType: protocol.EnumFieldType_FieldTypeStr,
						StrValue:  "test title",
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := EncryptUpdateField(tt.args.ctx, tt.args.f, tt.args.update); (err != nil) != tt.wantErr {
				t.Errorf("EncryptUpdateField() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
