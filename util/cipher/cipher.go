// Package cipher 加密工具包
package cipher

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"io"

	"git.code.oa.com/trpc-go/trpc-go/log"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/access_layer/config"
	"git.code.oa.com/video_media/storage_service/access_layer/util/errs"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"
)

var errInvalidCipherKey = errors.New("cipher key is nil")

// Encrypt 使用AES-256进行加密并且返回base64编码密文
func Encrypt(plaintext, key string) (string, error) {
	if key == "" {
		return "", errInvalidCipherKey
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	// Generate a random initialization vector (IV)
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	// Encrypt the plaintext
	stream := cipher.NewCFBEncrypter(block, iv)
	ciphertext := make([]byte, len(plaintext))
	stream.XORKeyStream(ciphertext, []byte(plaintext))

	// Combine the IV and ciphertext as a base64 encoded string
	encrypted := make([]byte, aes.BlockSize+len(ciphertext))
	copy(encrypted, iv)
	copy(encrypted[aes.BlockSize:], ciphertext)
	return base64.URLEncoding.EncodeToString(encrypted), nil
}

// Decrypt 解密AES-256密文
func Decrypt(ciphertext string, key string) (string, error) {
	if key == "" {
		return "", errInvalidCipherKey
	}

	encrypted, err := base64.URLEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	// Check that the ciphertext has the correct length
	if len(encrypted) < aes.BlockSize {
		return "", errors.New("ciphertext too short")
	}

	// Extract the IV and ciphertext
	iv := encrypted[:aes.BlockSize]
	ciphertext = string(encrypted[aes.BlockSize:])

	// Create the cipher using the key and IV
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	// Create the stream cipher and decrypt the ciphertext
	stream := cipher.NewCFBDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	stream.XORKeyStream(plaintext, []byte(ciphertext))
	return string(plaintext), nil
}

func isEncryptField(f *item.FieldInfo) bool {
	// 仅有文本类型且配置了加密的字段支持加密
	return f.Encrypted == item.IsEncrypted && f.FieldType == int32(protocol.EnumFieldType_FieldTypeStr)
}

// EncryptUpdateField 加密更新字段
func EncryptUpdateField(ctx context.Context, f *item.FieldInfo, update *protocol.UpdateFieldInfo) error {
	if !isEncryptField(f) {
		return nil
	}

	cipherText, err := Encrypt(update.GetFieldInfo().GetStrValue(), config.GetCipherKey())
	if err != nil {
		log.ErrorContextf(ctx, "fail to encrypt field, err is %v.", err)
		return errs.New(protocol.EnumMediaErrorCode_RetInnerErr)
	}
	update.FieldInfo.StrValue = cipherText
	return nil
}

// DecryptGetField 解密读取字段值
func DecryptGetField(f *item.FieldInfo, fieldInfo *protocol.FieldInfo) {
	if !isEncryptField(f) {
		return
	}

	plainText, err := Decrypt(fieldInfo.StrValue, config.GetCipherKey())
	if err != nil {
		log.Errorf("fail to decrypt field, src is %s, err is %v.", fieldInfo.StrValue, err)
		return
	}
	fieldInfo.StrValue = plainText
}
