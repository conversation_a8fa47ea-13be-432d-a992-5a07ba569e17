package util

import (
	"context"
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/kafka"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/trpc-go/trpc-go/server"
	"git.code.oa.com/trpcprotocol/storage_service/access_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/access_layer/config"
	"git.code.oa.com/video_media/storage_service/access_layer/util/report"
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"

	"github.com/Shopify/sarama"
	"google.golang.org/protobuf/proto"
)

// producerErrorCounter 消息队列生产者上报失败
var producerErrorCounter = metrics.Counter("KafkaProducerError")

func init() {
	// 重写异步发送变更消息失败
	kafka.AsyncProducerErrorCallback = func(err error, topic string, key, value []byte, headers []sarama.RecordHeader) {
		target := ""
		if len(headers) > 0 {
			target = string(headers[0].Value)
		}
		produceErrHandle(err, topic, string(key), target, value)
	}
}

func produceErrHandle(err error, topic, key, target string, value []byte) {
	log.Errorf("Fail to send modify message, err is %v.", err)
	// 上报特性告警
	producerErrorCounter.Incr()
	// 上报失败流水，用于重试
	tool.NewRemoteLog(context.Background(), "kafka").AddField("topic", topic).AddField("kafkaKey", key).
		AddField("namespace", config.GetNameSpace()).AddField("targetInfo", target).
		ReportDebug(base64.StdEncoding.EncodeToString(value))
}

func parseKafkaInfo(topicInfo string) (string, string) {
	infos := strings.Split(topicInfo, "|")
	if len(infos) > 1 {
		return infos[0], infos[1]
	}
	return infos[0], ""
}

// SendModifyMsg 异步发送变更消息
func SendModifyMsg(ctx context.Context, key, topic, target string, value []byte) error {
	var options []client.Option
	if target != "" {
		options = append(options, client.WithTarget(target))
	}
	if err := kafka.NewClientProxy("trpc.kafka.server.service", options...).AsyncSendMessage(ctx, topic,
		[]byte(key), value, sarama.RecordHeader{Key: []byte("targetInfo"), Value: []byte(target)}); err != nil {
		produceErrHandle(err, topic, key, target, value)
		return err
	}
	return nil
}

// SendJceModifyMsg 发送变更消息(jce协议)
func SendJceModifyMsg(ctx context.Context, modifyConv *tool.ModifyInfoConv, topicName string,
	baseInfo map[string]*protocol.FieldInfo, modifyInfos []*protocol.ModifyFieldInfo,
) {
	modifyInfo, msg := modifyConv.ToBytes(baseInfo, modifyInfos)
	scene := "ProducerSendAsyncMsg"
	topic, target := parseKafkaInfo(topicName)
	if err := SendModifyMsg(ctx, modifyConv.ID, topic, target, msg); err != nil {
		scene = "KafkaProducerError"
	}
	// 上报变更消息流水
	tool.NewRemoteLog(context.Background(), "report").AddField("clientIp", modifyConv.RemoteIP).
		AddField("appId", modifyConv.AppID).AddIntField("dataSetId", int(modifyConv.DataSetID)).
		AddField("id", modifyConv.ID).AddIntField("callId", int(modifyConv.SequenceID)).
		AddField("scane", scene).AddIntField("step", int(report.UpdateProducer)).
		AddIntField("errCode", 0).AddField("namespace", config.GetNameSpace()).
		ReportDebug(fmt.Sprintf("%+v", modifyInfo))
}

func checkIntArray(a, b []uint32) bool {
	if len(a) != len(b) {
		return false
	}
	if len(a) == 0 {
		return true
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

func checkStrArray(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	if len(a) == 0 {
		return true
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

func checkMapValue(src, dst *protocol.MapValue) bool {
	switch src.GetType() {
	case protocol.EnumFieldType_FieldTypeStr:
		return src.GetStrValue() == dst.GetStrValue()
	case protocol.EnumFieldType_FieldTypeSet:
		return checkStrArray(src.GetVecStr(), dst.GetVecStr())
	default:
		return false
	}
}

func checkUpdateEffect(src *protocol.ModifyFieldInfo, dst *protocol.FieldInfo) bool {
	if dst == nil {
		// 原数据为空，构造一个空值FieldInfo
		dst = &protocol.FieldInfo{
			FieldType: src.FieldType,
		}
	}
	switch src.FieldType {
	case protocol.EnumFieldType_FieldTypeStr:
		return src.NewStrValue == dst.StrValue
	case protocol.EnumFieldType_FieldTypeIntVec:
		return checkIntArray(src.NewVecInt, dst.VecInt)
	case protocol.EnumFieldType_FieldTypeSet:
		return checkStrArray(src.NewVecStr, dst.VecStr)
	case protocol.EnumFieldType_FieldTypeMapKV, protocol.EnumFieldType_FieldTypeMapKList:
		for k, v := range src.NewMapVal {
			if !checkMapValue(v, dst.GetMapVal()[k]) {
				// map类型在变更消息中只保留生效的key
				delete(src.NewMapVal, k)
				delete(src.OldMapVal, k)
			}
		}
		return len(src.NewMapVal) > 0
	default:
	}
	return false
}

// RetryHandler 超时重试handler
type RetryHandler struct{}

func getModifyFieldNames(modifyInfos []*protocol.ModifyFieldInfo) []string {
	var fields []string
	for _, m := range modifyInfos {
		if m.GetFieldType() < protocol.EnumFieldType_FieldTypeMapKV {
			fields = append(fields, m.FieldName)
		} else if m.GetFieldType() <= protocol.EnumFieldType_FieldTypeMapKList {
			for k := range m.NewMapVal {
				fields = append(fields, m.FieldName+"."+k)
			}
		}
	}
	return fields
}

func parseMessageKey(key string) (int, error) {
	tokens := strings.Split(key, "_")
	if len(tokens) < 3 || tokens[0] != "timeout" {
		return 0, fmt.Errorf("invalid message key")
	}
	return strconv.Atoi(tokens[1])
}

func getModifyNotify(ctx context.Context, value []byte) (*access_layer.ModifyNotify, error) {
	var notify access_layer.ModifyNotify
	if err := proto.Unmarshal(value, &notify); err != nil {
		log.ErrorContextf(ctx, "fail to unmarshal pb data, err is %v.", err)
		return nil, err
	}

	log.InfoContextf(ctx, "receive timeout notify info is %s", notify.String())
	// 上报消费原始数据
	tool.NewRemoteLog(context.Background(), "report").AddField("clientIp", notify.GetRemoteIp()).
		AddField("appId", notify.GetAppId()).AddIntField("dataSetId", int(notify.GetDataSetId())).
		AddField("id", notify.GetId()).AddIntField("callId", int(notify.GetSequenceId())).
		AddField("scane", "TimeoutCheck").AddIntField("step", int(report.UpdateProducer)).
		AddIntField("errCode", 0).AddField("namespace", config.GetNameSpace()).
		ReportDebug(notify.String())
	if len(notify.ModifyFieldInfos) == 0 {
		return nil, fmt.Errorf("modify list is nil")
	}
	return &notify, nil
}

func getEffectedModifyInfos(ctx context.Context, datasourceID, datasetID int32, id string,
	src []*protocol.ModifyFieldInfo,
) ([]*protocol.ModifyFieldInfo, error) {
	rsp, err := tool.NewAdaptorLayerProxy(datasourceID, []string{id}).
		SetDataSetID(datasetID).AddGetFieldNames(getModifyFieldNames(src)...).
		// 只有公共set部署超时队列消费者，因此使用公共set调用数据适配层服务获取数据
		BatchGetMediaInfos(ctx, client.WithCalleeSetName(config.GetSetName()))
	if err != nil {
		log.ErrorContextf(ctx, "fail to call BatchGetMediaInfos of %s, err is %v.", id, err)
		return nil, err
	}

	docInfo := rsp.GetDocInfos()[id]
	var modifyInfos []*protocol.ModifyFieldInfo
	for _, m := range src {
		if checkUpdateEffect(m, docInfo.GetFields()[m.FieldName]) {
			modifyInfos = append(modifyInfos, m)
		}
	}
	log.InfoContextf(ctx, "current data is %s, modify infos is %+v.", docInfo.String(), modifyInfos)
	return modifyInfos, nil
}

// waitEffectTime db超时情况下不能马上去检查值是否生效，需要等待最多5秒钟
func waitEffectTime(timestamp int32) {
	if timestamp == 0 {
		return
	}

	if time.Now().Before(time.Unix(int64(timestamp), 0).Add(5 * time.Second)) {
		time.Sleep(time.Until(time.Unix(int64(timestamp), 0).Add(5 * time.Second)))
	}
}

// Handle 超时补发消息消费者处理逻辑
func (r *RetryHandler) Handle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	log.DebugContextf(ctx, "receive kafka msg, key is %s.", msg.Key)
	datasourceID, err := parseMessageKey(string(msg.Key))
	if err != nil {
		log.ErrorContextf(ctx, "fail to parse message key:%s, err is %v.", string(msg.Key), err)
		return nil
	}

	notify, err := getModifyNotify(ctx, msg.Value)
	if err != nil {
		// 获取变更消息失败的不需要重试
		return nil
	}

	waitEffectTime(notify.TimeStamp)
	notify.ModifyFieldInfos, err = getEffectedModifyInfos(ctx, int32(datasourceID), notify.GetDataSetId(),
		notify.GetId(), notify.GetModifyFieldInfos())
	if err != nil {
		return err
	}
	dataSetInfo := cache.GetDataSet(notify.GetDataSetId(), notify.GetTenantID())
	log.InfoContextf(ctx, "dataset info is %+v, notify info is %s.", dataSetInfo, notify.String())
	if dataSetInfo != nil && len(notify.ModifyFieldInfos) > 0 {
		SendJceModifyMsg(ctx, &tool.ModifyInfoConv{
			ID:           notify.GetId(),
			DataSetID:    notify.GetDataSetId(),
			AppID:        notify.GetAppId(),
			OperatorName: "timeout_check_" + notify.GetOperatorName(),
			LocalIP:      notify.GetLocalIp(),
			RemoteIP:     notify.GetRemoteIp(),
			ExtInfo:      notify.GetExtInfo(),
			SequenceID:   notify.GetSequenceId(),
			TenantID:     notify.GetTenantID(),
		}, dataSetInfo.Topic, notify.GetBaseInfo(), notify.GetModifyFieldInfos())
	}
	return nil
}

// InitRetryMQHandler 初始化重试队列handler
func InitRetryMQHandler(s *server.Server) {
	// 设置kafka全局超时时间
	timeout := config.GetKafkaCfg().Timeout
	if timeout > 0 {
		kafka.Timeout = time.Duration(timeout) * time.Millisecond
	}

	// 配置了重试队列的set服务开启失败变更消息补发
	addr := config.GetKafkaCfg().RetryMQAddr
	setName := config.GetKafkaCfg().SetName
	if setName != trpc.GlobalConfig().Global.FullSetName || addr == "" {
		return
	}
	s.AddService("trpc.kafka.retry.consumer", server.New(server.WithProtocol("kafka"),
		server.WithAddress(addr), server.WithTimeout(time.Duration(timeout)*time.Millisecond)))
	kafka.RegisterKafkaConsumerService(s.Service("trpc.kafka.retry.consumer"), &RetryHandler{})
	log.Infof("finish init retry mq handler.")
}
