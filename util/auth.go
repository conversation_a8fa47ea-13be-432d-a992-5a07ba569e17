package util

import (
	"strconv"

	"git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/access_layer/util/errs"
	"git.code.oa.com/video_media/storage_service/auth"
)

// 调用方优先级枚举值
const (
	LowLevel  = 0
	HighLevel = 1
)

// CheckRequest 检查请求是否合法(appID权限检查+频控检查)
func CheckRequest(appID, appKey string, dataSet int32) error {
	if err := auth.CheckAuth(appID, appKey); err != nil {
		return errs.New(common_storage_common.EnumMediaErrorCode_RetNoAuth)
	}

	labels := map[string]string{"appID": appID, "datasetID": strconv.Itoa(int(dataSet))}
	if err := auth.CheckFrequency(labels); err != nil {
		return errs.New(common_storage_common.EnumMediaErrorCode_RetOverApplyReq)
	}
	return nil
}

// IsDatasourceOverload 检查数据源是否过载
func IsDatasourceOverload(priority, datasourceID int32, apiName string) bool {
	labels := map[string]string{"datasourceID": strconv.Itoa(int(datasourceID)), "api": apiName}
	err := auth.CheckFrequency(labels)
	if err != nil {
		// 检查是否是高优先级用户，高优先级用户一定不频控
		return priority == LowLevel
	}
	return false
}
