package checker

import (
	"errors"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.woa.com/video_media/metadata/metadata-sdk/pkg/fixed"
	"git.woa.com/video_media/metadata/metadata-sdk/pkg/metadata"
	"git.woa.com/video_media/metadata/metadata-sdk/pkg/validate"
	"git.woa.com/video_media/metadata/metadata-sdk/pkg/validate/validatemocks"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
)

func injectMetadataValidator(validator validate.Interface) func() {
	metadataValidator = validator
	return func() {
		metadataValidator = nil
	}
}

func TestValidateFieldValues(t *testing.T) {
	validator := validatemocks.NewMockInterface(gomock.NewController(t))
	defer injectMetadataValidator(validator)()

	type args struct {
		id          string
		dataSetID   int32
		fieldID     metadata.FieldID
		updateField *protocol.FieldInfo
	}
	tests := []struct {
		name      string
		args      args
		wantErr   bool
		setupMock func()
	}{
		{
			name: "test validate text success",
			args: args{
				dataSetID: 2001,
				fieldID:   12,
				updateField: &protocol.FieldInfo{
					FieldId:   12,
					FieldType: protocol.EnumFieldType_FieldTypeStr,
					StrValue:  "123",
					VecStr:    []string{"321"},
				},
			},
			wantErr: false,
			setupMock: func() {
				validator.EXPECT().
					Validate(
						gomock.Any(),
						&validate.Request{
							DataSetID:    2001,
							AppID:        "123",
							OperatorName: "test",
							CallerIP:     "***********:12345",
							FieldValues:  []validate.FieldValue{{FieldID: 12, Values: []string{"123"}}},
							FixedOption: fixed.FixedOption{
								NeedFix: true,
							},
						},
					).Return(&validate.ValidateRsp{}, nil)
			},
		},
		{
			name: "test validate option success",
			args: args{
				dataSetID: 2001,
				fieldID:   13,
				updateField: &protocol.FieldInfo{
					FieldId:   13,
					FieldType: protocol.EnumFieldType_FieldTypeIntVec,
					VecInt:    []uint32{1, 23},
				},
			},
			wantErr: false,
			setupMock: func() {
				validator.EXPECT().
					Validate(
						gomock.Any(),
						&validate.Request{
							DataSetID:    2001,
							AppID:        "123",
							OperatorName: "test",
							CallerIP:     "***********:12345",
							FieldValues:  []validate.FieldValue{{FieldID: 13, Values: []string{"1", "23"}}},
							FixedOption: fixed.FixedOption{
								NeedFix: true,
							},
						},
					).Return(&validate.ValidateRsp{}, nil)
			},
		},
		{
			name: "test validate text set success",
			args: args{
				dataSetID: 2001,
				fieldID:   23,
				updateField: &protocol.FieldInfo{
					FieldId:   23,
					FieldType: protocol.EnumFieldType_FieldTypeSet,
					VecStr:    []string{"123", "312"},
				},
			},
			wantErr: false,
			setupMock: func() {
				validator.EXPECT().
					Validate(
						gomock.Any(),
						&validate.Request{
							DataSetID:    2001,
							AppID:        "123",
							OperatorName: "test",
							CallerIP:     "***********:12345",
							FieldValues:  []validate.FieldValue{{FieldID: 23, Values: []string{"123", "312"}}},
							FixedOption: fixed.FixedOption{
								NeedFix: true,
							},
						},
					).Return(&validate.ValidateRsp{}, nil)
			},
		},
		{
			name: "test validate text failure",
			args: args{
				dataSetID: 2001,
				fieldID:   33,
				updateField: &protocol.FieldInfo{
					FieldId:   33,
					FieldType: protocol.EnumFieldType_FieldTypeSet,
					VecStr:    []string{"123$", "312"},
				},
			},
			wantErr: true,
			setupMock: func() {
				validator.EXPECT().
					Validate(
						gomock.Any(),
						&validate.Request{
							DataSetID:    2001,
							AppID:        "123",
							OperatorName: "test",
							CallerIP:     "***********:12345",
							FieldValues:  []validate.FieldValue{{FieldID: 33, Values: []string{"123$", "312"}}},
							FixedOption: fixed.FixedOption{
								NeedFix: true,
							},
						},
					).Return(&validate.ValidateRsp{}, errors.New("error"))
			},
		},
	}
	operatorName := "test"
	appID := "123"

	ctx := trpc.BackgroundContext()
	customAddr := &CustomAddr{
		address: "***********:12345",
	}

	trpc.Message(ctx).WithRemoteAddr(customAddr)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setupMock != nil {
				tt.setupMock()
			}
			info := CallerInfo{ID: tt.args.id, AppID: appID, OperatorName: operatorName}
			err := ValidateFieldValues(ctx, tt.args.dataSetID, &info, tt.args.fieldID, tt.args.updateField)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

type CustomAddr struct {
	address string
}

func (a *CustomAddr) Network() string {
	return "tcp"
}

func (a *CustomAddr) String() string {
	return a.address
}
