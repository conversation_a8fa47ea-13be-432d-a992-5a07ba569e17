// Package checker 检查逻辑
package checker

import (
	"context"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/access_layer/config"
	"git.code.oa.com/video_media/storage_service/access_layer/util/cipher"
	"git.code.oa.com/video_media/storage_service/access_layer/util/errs"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"
	"git.woa.com/video_media/metadata/metadata-sdk/pkg/metadata"
)

// Checker 字段检查对象
type Checker struct {
	// Permission 权限信息
	Permission  string
	permissions map[string]bool
	// TenantID 租户信息
	TenantID string
}

// CallerInfo 调用方信息
type CallerInfo struct {
	// ID 数据id
	ID string
	// OperatorName 操作人
	OperatorName string
	// AppID 调用方appID
	AppID string
}

// New 创建字段检查对象
func New(tenantID, permission string) *Checker {
	checker := make(map[string]bool)
	if permission != "*" {
		// 权限列表转化为map[string]bool进行判断
		for _, p := range strings.Split(permission, ",") {
			checker[p] = true
		}
	}

	return &Checker{
		Permission:  permission,
		permissions: checker,
		TenantID:    tenantID,
	}
}

// CheckPermission 检查字段权限
func (c *Checker) CheckPermission(fieldName string) error {
	if c.Permission != "*" && !c.permissions[fieldName] {
		return errs.New(protocol.EnumMediaErrorCode_RetNoPermission)
	}
	return nil
}

// isOpTypeInvalid 检查更新操作是否合法
// rule:EnumUpdateType_UpdateTypeInsert,EnumUpdateType_UpdateTypeReorder和EnumUpdateType_UpdateTypeReverse只支持Set类型字段
// Pos为-1时为插入或者重排序到尾部
func isOpTypeInvalid(update *protocol.UpdateFieldInfo) bool {
	OpType := update.UpdateType
	if OpType < protocol.EnumUpdateType_UpdateTypeSet || OpType > protocol.EnumUpdateType_UpdateTypeReverse {
		return true
	}

	if update.Pos < -1 {
		return true
	}

	if update.FieldInfo.FieldType < protocol.EnumFieldType_FieldTypeSet &&
		OpType > protocol.EnumUpdateType_UpdateTypeAppend {
		return true
	}
	return false
}

// CheckTenantInfo 检查租户信息
func (c *Checker) CheckTenantInfo(fieldTenantID string) error {
	log.Debugf("tenantID is {%s}, fieldTenantID is {%s}.", c.TenantID, fieldTenantID)
	if c.TenantID == "" || fieldTenantID == "" {
		// 未配置跳过检查
		return nil
	}

	if c.TenantID != fieldTenantID {
		return errs.New(protocol.EnumMediaErrorCode_RetNoPermission)
	}
	return nil
}

// CheckUpdateField 检查更新字段权限
func (c *Checker) CheckUpdateField(ctx context.Context,
	info *CallerInfo,
	f *item.FieldInfo,
	update *protocol.UpdateFieldInfo,
) error {
	field := update.GetFieldInfo()
	checkFuncList := []func() error{
		func() error {
			// 1.检查是否有修改权限，*权限单独处理
			return c.CheckPermission(field.FieldName)
		}, func() error {
			// 2.检查变更字段类型是否正确
			if f.FieldType != int32(field.FieldType) || isOpTypeInvalid(update) {
				return errs.New(protocol.EnumMediaErrorCode_RetInvalidOpType)
			}
			return nil
		}, func() error {
			// 3.检查字段是否属于该租户
			return c.CheckTenantInfo(f.TenantID)
		}, func() error {
			// 4.校验字段值数据是否符合元数据校验规则
			if config.ValidationEnabled() {
				if err := ValidateFieldValues(
					ctx,
					f.DataSetID,
					info,
					metadata.FieldID(f.FieldID),
					field,
				); err != nil {
					log.WarnContextf(ctx, "fail to validate field %d, err is %v", f.FieldID, err)
					return errs.New(protocol.EnumMediaErrorCode_RetValidateFieldValues)
				}
			}
			return nil
		},
	}

	for _, check := range checkFuncList {
		if err := check(); err != nil {
			return err
		}
	}
	return cipher.EncryptUpdateField(ctx, f, update)
}
