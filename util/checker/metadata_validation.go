package checker

import (
	"context"
	"strconv"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.woa.com/video_media/metadata/metadata-sdk/pkg/fixed"
	"git.woa.com/video_media/metadata/metadata-sdk/pkg/metadata"
	"git.woa.com/video_media/metadata/metadata-sdk/pkg/validate"

	"github.com/samber/lo"
)

var (
	metadataValidator validate.Interface
	once              sync.Once
)

// InitMetadataValidator 初始化全局的元数据校验器
func InitMetadataValidator(ctx context.Context) {
	once.Do(func() {
		metadataValidator = validate.NewFieldValueValidator(ctx)
	})
}

// ValidateFieldValues 校验字段值数据 使用元数据配置
func ValidateFieldValues(
	ctx context.Context,
	dataSetID int32,
	info *CallerInfo,
	fieldID metadata.FieldID,
	updateField *protocol.FieldInfo,
) error {
	callerIP := trpc.Message(ctx).RemoteAddr().String()

	r := &validate.Request{
		FieldValues: []validate.FieldValue{
			{
				FieldID: fieldID,
				Values:  fieldValuesFrom(updateField),
			},
		},
		DataSetID:    dataSetID,
		ID:           info.ID,
		AppID:        info.AppID,
		OperatorName: info.OperatorName,
		CallerIP:     callerIP,
		SkipReport:   false,
		FixedOption: fixed.FixedOption{
			NeedFix: true,
		},
	}
	rsp, err := metadataValidator.Validate(ctx, r)
	fixedValues2UpdateField(ctx, rsp.FixedFieldValues, updateField)
	return err
}

func fixedValues2UpdateField(ctx context.Context, fieldValues []validate.FieldValue, updateField *protocol.FieldInfo) {
	if len(fieldValues) == 0 {
		return
	}
	for _, value := range fieldValues {
		log.DebugContextf(ctx, "updateInfoType : %+v", updateField.GetFieldType())
		switch updateField.GetFieldType() {
		case protocol.EnumFieldType_FieldTypeStr:
			if len(value.Values) != 0 {
				updateField.StrValue = value.Values[0]
				log.DebugContextf(ctx, "fixed value 2 updateInfo : %+v", updateField)
			}
			return
		default:
			return
		}
	}

}

func fieldValuesFrom(field *protocol.FieldInfo) []string {
	switch field.GetFieldType() {
	case protocol.EnumFieldType_FieldTypeStr:
		if len(field.GetStrValue()) == 0 {
			return nil
		}
		return []string{field.GetStrValue()}
	case protocol.EnumFieldType_FieldTypeIntVec:
		return lo.Map(field.GetVecInt(), func(item uint32, index int) string {
			return strconv.Itoa(int(item))
		})
	case protocol.EnumFieldType_FieldTypeSet:
		return field.GetVecStr()
	default:
		return nil
	}
}
