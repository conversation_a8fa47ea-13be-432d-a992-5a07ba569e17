// Package report 封装服务上报相关逻辑，包括放号服务以及鹰眼上报
package report

import (
	"context"
	"fmt"
	"hash/crc32"
	"math"
	"regexp"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/access_layer/config"
	tool "git.code.oa.com/video_media/storage_service/common"
	"git.woa.com/polaris/polaris-go/v2/pkg/algorithm/rand"

	"github.com/bwmarrin/snowflake"
)

// Step 上报步骤
type Step int

const (
	_ Step = iota
	// UpdateReqBegin 更新请求开始上报点
	UpdateReqBegin
	_
	// UpdateProducer 生产者流水上报点
	UpdateProducer
	// UpdateReqEnd 更新请求结束上报点
	UpdateReqEnd
	// GetReqBegin Get请求开始上报点
	GetReqBegin
	// GetReqEnd Get请求结束上报点
	GetReqEnd
	// FailProducer 生产流水失败上报点
	FailProducer
	// SearchReqBegin 搜索开始
	SearchReqBegin
	// SearchReqEnd 搜索结束
	SearchReqEnd
	// FieldStatistics 字段统计
	FieldStatistics
)

// 放号系统相关全局变量
var (
	snowNode     *snowflake.Node
	containerRE  = regexp.MustCompile(`\.[A-Za-z]+(\d+)`)
	scalableRand = rand.NewScalableRand()
)

// AllUpdateTimeoutCounter 所有数据源adaptor_layer调用失败
var AllUpdateTimeoutCounter = metrics.Counter("AllUpdateTimeout")

// getContainerID 获取容器ID
func getContainerID(containerName string) (int64, error) {
	containerMatches := containerRE.FindStringSubmatch(containerName)
	if len(containerMatches) == 0 {
		return 0, fmt.Errorf("regex fail to match container name")
	}
	return strconv.ParseInt(containerMatches[1], 10, 64)
}

// getNodeID 获取nodeID，不保证完全不冲突
func getNodeID(containerName, localIP string) int64 {
	if nodeID, err := getContainerID(containerName); err == nil {
		// 通过容器ID生成nodeID eg:formal.storage_service.access_layer.sz100213
		return int64(nodeID) % 1024
	}
	// 否则使用容器IP生成nodeID
	return int64(crc32.ChecksumIEEE([]byte(localIP))) % 1024
}

// InitIDCreator 初始化放号器
func InitIDCreator() error {
	containerName, localIP := trpc.GlobalConfig().Global.ContainerName, trpc.GlobalConfig().Global.LocalIP
	nodeID := getNodeID(containerName, localIP)

	var err error
	snowNode, err = snowflake.NewNode(nodeID)
	if err != nil {
		return err
	}
	log.Infof("Finish init id creator, nodeID is %d.", nodeID)
	return nil
}

// GetNextID 获取放号
func GetNextID() int64 {
	if snowNode == nil {
		// 异常情况降级为放号随机数
		return int64(scalableRand.Intn(math.MaxInt64))
	}
	return snowNode.Generate().Int64()
}

// Reporter 服务上报对象结构
type Reporter struct {
	// CallID 调用全局id
	CallID int64
	// ClientIP 客户端ip
	ClientIP string
	// FuncName 接口名称
	FuncName string
	// AppID 应用Id
	AppID string
	// DataSetID 数据集Id
	DataSetID int32
	// IDs id列表，用，连接
	IDs    string
	logger log.Logger
}

// New 创建新的服务上报对象
func New(ctx context.Context, funcName, appID string, ids string, dataSetID int32) *Reporter {
	return &Reporter{
		CallID:    GetNextID(),
		ClientIP:  tool.GetClientIp(ctx),
		FuncName:  funcName,
		AppID:     appID,
		DataSetID: dataSetID,
		IDs:       ids,
		logger:    log.Get("report"),
	}
}

func shouldSample(rate float64) bool {
	randSample := float64(scalableRand.Intn(100)) / 100.0
	log.Debugf("random sample is %f.", randSample)
	return randSample < rate
}

// GetFields 获取上报字段
func (r *Reporter) GetFields(step Step, errCode protocol.EnumMediaErrorCode) []log.Field {
	return []log.Field{
		{Key: "client", Value: r.ClientIP},
		{Key: "appId", Value: r.AppID},
		{Key: "dataSetId", Value: r.DataSetID},
		{Key: "id", Value: r.IDs},
		{Key: "callId", Value: r.CallID},
		{Key: "scane", Value: r.FuncName},
		{Key: "step", Value: step},
		{Key: "errCode", Value: errCode},
		{Key: "namespace", Value: config.GetNameSpace()},
	}
}

// ReportFields 上报字段数据
func (r *Reporter) ReportFields(fields []string) *Reporter {
	if r.logger == nil || !shouldSample(config.ReportSampleRate()) {
		return r
	}

	reportFields := r.GetFields(FieldStatistics, 0)
	for _, field := range fields {
		r.logger.With(reportFields...).Debug(field)
	}
	return r
}

// DoReport 上报流水
func (r *Reporter) DoReport(needReport bool, step Step, errCode protocol.EnumMediaErrorCode, info string) {
	if r.logger == nil || !needReport {
		return
	}

	r.logger.With(r.GetFields(step, errCode)...).Debug(info)
}

func (r *Reporter) newMetricsHeader() []*metrics.Dimension {
	return []*metrics.Dimension{
		{Name: "appID", Value: r.AppID},
		{Name: "clientIP", Value: r.ClientIP},
		{Name: "funcName", Value: r.FuncName},
		{Name: "dataSetID", Value: strconv.Itoa(int(r.DataSetID))},
	}
}

// DoReportFailList 按appID,clientIP,func,dataSetID,errCode上报失败列表具体信息
func (r *Reporter) DoReportFailList(errCode protocol.EnumMediaErrorCode, retInfo *protocol.CommRetInfo) {
	failList := retInfo.GetFailList()
	if errCode == protocol.EnumMediaErrorCode_RetSuccess && len(failList) == 0 {
		// 仅上报发生调用错误或存在失败列表的情况
		return
	}

	// 统计每个错误码的出现次数用于上报告警
	header := r.newMetricsHeader()
	errCounter := make(map[protocol.EnumMediaErrorCode]int)
	for _, code := range failList {
		errCounter[code]++
	}

	// 上报请求流水
	r.DoReport(true, UpdateReqEnd, errCode, retInfo.String())
	const maxBatchReportNum = 5
	if len(errCounter) > maxBatchReportNum {
		// 单条请求错误码超过5个防止性能损耗只上报流水，否则按维度批量同步上报监控
		return
	}
	for code, num := range errCounter {
		_ = metrics.Report(metrics.NewMultiDimensionMetricsX("FailListCnt", append(header, &metrics.Dimension{
			Name:  "errCode",
			Value: code.String(),
		}), []*metrics.Metrics{metrics.NewMetrics("errCount", float64(num), metrics.PolicySUM)}))
	}
}
