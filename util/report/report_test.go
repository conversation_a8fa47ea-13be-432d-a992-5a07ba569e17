package report

import (
	"hash/crc32"
	"testing"

	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
)

func Test_getNodeID(t *testing.T) {
	type args struct {
		containerName string
		localIP       string
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "测试使用容器名",
			args: args{
				containerName: "formal.storage_service.access_layer.sz100213",
			},
			want: 100213 % 1024,
		}, {
			name: "测试使用IP",
			args: args{
				localIP: "127.0.0.1",
			},
			want: int64(crc32.ChecksumIEEE([]byte("127.0.0.1"))) % 1024,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getNodeID(tt.args.containerName, tt.args.localIP); got != tt.want {
				t.<PERSON>rrorf("getNodeID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestReporter_DoReportWithFailList(t *testing.T) {
	type fields struct {
		CallID    int64
		ClientIP  string
		FuncName  string
		AppID     string
		DataSetID int32
		IDs       string
	}
	type args struct {
		errCode protocol.EnumMediaErrorCode
		retInfo *protocol.CommRetInfo
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name:   "测试无失败列表",
			fields: fields{},
			args: args{
				errCode: 0,
				retInfo: &protocol.CommRetInfo{},
			},
		}, {
			name:   "测试短失败列表",
			fields: fields{},
			args: args{
				errCode: 0,
				retInfo: &protocol.CommRetInfo{
					FailList: map[string]protocol.EnumMediaErrorCode{"title": protocol.EnumMediaErrorCode_RetInvalidOpType},
				},
			},
		}, {
			name:   "测试长失败列表（超过5个错误码）",
			fields: fields{},
			args: args{
				errCode: 0,
				retInfo: &protocol.CommRetInfo{
					FailList: map[string]protocol.EnumMediaErrorCode{
						"field1": protocol.EnumMediaErrorCode_RetFailSelect,
						"field2": protocol.EnumMediaErrorCode_RetInvalidOpType,
						"field3": protocol.EnumMediaErrorCode_RetInnerErr,
						"field4": protocol.EnumMediaErrorCode_RetFieldInfoNotExist,
						"field5": protocol.EnumMediaErrorCode_RetDatasourceOverLoad,
						"field6": protocol.EnumMediaErrorCode_RetNoPermission,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &Reporter{
				CallID:    tt.fields.CallID,
				ClientIP:  tt.fields.ClientIP,
				FuncName:  tt.fields.FuncName,
				AppID:     tt.fields.AppID,
				DataSetID: tt.fields.DataSetID,
				IDs:       tt.fields.IDs,
			}
			r.DoReportFailList(tt.args.errCode, tt.args.retInfo)
		})
	}
}
