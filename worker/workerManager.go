package worker

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/alarm"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/db"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/extcfg"
	"github.com/pkg/errors"
)

// Manage 消息
type Manage interface {
	Get(appKey string) *Worker
}

type manager struct {
	lockWork      *sync.RWMutex
	workers       map[string]*Worker
	latestVersion time.Time
	repository    *db.Repository
}

var workerMgr *manager

// Run 执行
func Run() {
	workerMgr = NewManager()
	workerMgr.Start()
}

// Get 获取
func Get(appKey string) *Worker {
	return workerMgr.Get(appKey)
}

// NewManager 新建处理者
func NewManager() *manager {
	return &manager{
		lockWork:      &sync.RWMutex{},
		workers:       map[string]*Worker{},
		latestVersion: time.Unix(0, 0),
		repository:    &db.Repository{},
	}
}

var locker sync.Mutex

func (mgr *manager) Shutdown() {
	locker.Lock()
	defer locker.Unlock()
	for _, worker := range mgr.workers {
		e := worker.Shutdown()
		if e != nil {
			log.Errorf("Error to shutdown worker %+v with error %+v\n", worker, errors.WithStack(e))
		}
	}
}

func (mgr *manager) Start() {
	log.Infof("start, mgr:[%+v]", *mgr)
	go mgr.run()
}

func (mgr *manager) run() {
	log.Infof("run, mgr:[%+v]", *mgr)
	rand.Seed(time.Now().Unix())
	tick := time.NewTicker(time.Second * 15)
	defer tick.Stop()

	ctx := trpc.BackgroundContext()
	log.WithContextFields(ctx,
		"mediaSet", trpc.GlobalConfig().Global.FullSetName,
		"serverName", trpc.GlobalConfig().Server.Server,
		"debug_flag", "syncWorkers")
	for {
		select {
		case <-tick.C:
			err := mgr.syncWorkers(ctx)
			if err != nil {
				log.ErrorContextf(ctx, "mgr.syncWorkers() err=%s", err)
			}
		}
	}
}

func (mgr *manager) syncWorkers(ctx context.Context) error {
	locker.Lock()
	defer locker.Unlock()
	config := extcfg.GetExtCfg()
	subscribers, err := mgr.repository.SubscribersSurpassUpdateTime(mgr.latestVersion)
	if err != nil {
		return errors.WithStack(err)
	}
	if len(subscribers) == 0 {
		return nil
	}
	log.InfoContextf(ctx, "subscribers:[%+v]", subscribers)
	for _, subscriber := range subscribers {
		// 状态切换逻辑： 先把订阅关闭，再依据状态做逻辑
		currentWorker := mgr.Get(subscriber.AppKey)
		if currentWorker != nil {
			err := currentWorker.Shutdown()
			if err != nil {
				log.Errorf("Get error while shutdown worker for %d, error: %+v", subscriber.Id, err)
			}
		}
		// 判断是否有特殊字段推送逻辑
		status := mgr.calFieldsStatus(config.CalcFields, subscriber)
		switch status {
		case db.StatusStop:
			if currentWorker != nil {
				mgr.Remove(subscriber.AppKey)
			}
		case db.StatusRunning:
			fallthrough
		case db.StatusPreSubscribe:
			newWorker, err := NewWorker(subscriber)
			if err != nil {
				log.Errorf("[%s]无法创建工作进程, %+v", subscriber.Name, err)
				_ = alarm.GetInstance().SendWithSystemAdminIncluded(
					fmt.Sprintf("[%s]无法创建工作进程，错误消息为%s", subscriber.Name, err), subscriber.Admins)
			}
			if newWorker == nil { // 是应对NewWorker中产生Panic而加的特殊逻辑。当NewWorker panic后，这个为nil，为了不影响其他订阅者，这里continue
				continue
			}
			err = newWorker.Start()
			if err != nil {
				return errors.WithStack(err)
			}
			mgr.Set(newWorker)
		}
	}
	mgr.latestVersion = subscribers[len(subscribers)-1].UpdateTime
	return nil
}

func (mgr *manager) Set(w *Worker) {
	mgr.lockWork.Lock()
	defer mgr.lockWork.Unlock()
	mgr.workers[w.subscriber.AppKey] = w
}

func (mgr *manager) Remove(appKey string) {
	mgr.lockWork.Lock()
	defer mgr.lockWork.Unlock()
	delete(mgr.workers, appKey)
}

func (mgr *manager) Get(appKey string) *Worker {
	mgr.lockWork.RLock()
	defer mgr.lockWork.RUnlock()
	return mgr.workers[appKey]
}

func (mgr *manager) calFieldsStatus(calFields extcfg.CalFields, subscriber db.Subscriber) int {
	// 没有配置特殊字段流量，那么直接使用历史配置状态
	if !calFields.NeedCalFieldsPush {
		return subscriber.Status
	}
	// 判断是否配置特殊字段流量
	hasFieldFlag := false
	for _, field := range subscriber.SubscribedColumns {
		if calFields.CalFields[field] {
			hasFieldFlag = true
			break
		}
	}
	// 包含特殊字段流量配置且状态为运行或预处理，那么等待预处理
	if hasFieldFlag && (subscriber.Status == db.StatusRunning || subscriber.Status == db.StatusPreSubscribe) {
		return subscriber.Status
	}
	// 没有配置特殊字段流量配置或状态为停止，配置停止状态
	return db.StatusStop
}
