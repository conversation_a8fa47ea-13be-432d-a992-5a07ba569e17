package worker

import (
	"reflect"
	"testing"

	"git.code.oa.com/video_media/media_msg_hub/distribution_server/db"
	"github.com/Shopify/sarama"
)

var content []byte

func init() {
	content = []byte(`{
    "id":"l0907m5j4dk",
    "version":1,
    "dataSetId":2001,
    "timeStamp":1569247774,
"processStartTimestamp":1569247774,
    "modifyFieldInfos":{
        "media_flag":{
            "id":80053,
            "old":"",
            "new":"1",
            "version":1
        },
        "hg_pool_list_id":{
            "id":2735,
            "old":"",
            "new":"1041,10008",
            "version":1
        }
    },
    "appId":"media_video_sync",
    "fieldInfos":{
        "duration":{
            "id":80079,
            "value":"0",
            "fieldName":"duration",
            "fieldType":1,
            "strValue":"0",
            "vecInt":[

            ],
            "fieldId":80079
        },
        "upload_qq":{
            "id":80054,
            "value":"1751500235",
            "fieldName":"upload_qq",
            "fieldType":1,
            "strValue":"1751500235",
            "vecInt":[

            ],
            "fieldId":80054
        },
        "vcats":{
            "fieldName":"vcats",
            "fieldType":1,
            "strValue":"0",
            "vecInt":[

            ],
            "fieldId":80045
        },
        "vsubcats":{
            "id":80047,
            "value":"0",
            "fieldName":"vsubcats",
            "fieldType":1,
            "strValue":"0",
            "vecInt":[

            ],
            "fieldId":80047
        },
        "vtags":{
            "id":80049,
            "value":"老酒馆 朋友们",
            "fieldName":"vtags",
            "fieldType":1,
            "strValue":"老酒馆 朋友们",
            "vecInt":[

            ],
            "fieldId":80049
        },
        "vtitle":{
            "id":80050,
            "value":"老酒馆：老掌柜深夜不睡觉，缅怀逝去的朋友，让人唏嘘！",
            "fieldName":"vtitle",
            "fieldType":1,
            "strValue":"老酒馆：老掌柜深夜不睡觉，缅怀逝去的朋友，让人唏嘘！",
            "vecInt":[

            ],
            "fieldId":80050
        },
        "vuid":{
            "id":2751,
            "value":"0",
            "fieldName":"vuid",
            "fieldType":1,
            "strValue":"0",
            "vecInt":[

            ],
            "fieldId":2751
        },
        "vv_upload_type":{
            "id":80089,
            "value":"24",
            "fieldName":"vv_upload_type",
            "fieldType":1,
            "strValue":"24",
            "vecInt":[

            ],
            "fieldId":80089
        },
        "vvideo_status":{
            "id":80058,
            "value":"2",
            "fieldName":"vvideo_status",
            "fieldType":1,
            "strValue":"2",
            "vecInt":[

            ],
            "fieldId":80058
        }
    }
}`)
}

func TestMessageSendManager_MessageHasModification(t *testing.T) {
	kafkaMessage := &sarama.ConsumerMessage{
		Value: content,
	}
	message, err := DefaultMediaMessageFactory.BuildMessage(kafkaMessage, []string{"vv_upload_type", "duration"})
	if err != nil {
		t.Error(err)
	}
	if len(message.ModifyFieldInfos) == 0 {
		t.Error("Message1 should has no modification")
	}

	message, _ = DefaultMediaMessageFactory.BuildMessage(kafkaMessage, []string{"media_flag", "duration"})
	if len(message.ModifyFieldInfos) == 0 {
		t.Error("Message2 should has modification")
	}
}

func TestGetNotifiers(t *testing.T) {
	notifier, err := GetNotifier(&db.Subscriber{
		CallbackUrl: "test",
	})
	if err != nil {
		t.Error(err)
	}
	if reflect.TypeOf(notifier).String() != "*worker.httpNotifier" {
		t.Error("notifier should be httpNotifier")
	}
}
