package worker

import (
	"context"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/kafka"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/extcfg"
	jsoniter "github.com/json-iterator/go"
)

var (
	kafkaProducer kafka.Client
	kafkaOnce     sync.Once
)

func pushFailedMsg(ctx context.Context, errMsg *msghub.ErrMessage) error {
	if !extcfg.GetExtCfg().KafkaProducer.Enable {
		return nil
	}
	kafkaOnce.Do(func() {
		opts := []client.Option{
			client.WithTarget(extcfg.GetExtCfg().KafkaProducer.Target),
			client.WithTimeout(time.Millisecond * time.Duration(extcfg.GetExtCfg().KafkaProducer.Timeout)),
		}
		kafkaProducer = kafka.NewClientProxy(extcfg.GetExtCfg().KafkaProducer.Name, opts...)
	})
	key := []byte(errMsg.ID)
	value, _ := jsoniter.Marshal(errMsg)
	return kafkaProducer.Produce(ctx, key, value)
}
