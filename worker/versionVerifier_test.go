package worker

import (
	"fmt"
	// "git.code.oa.com/video_media/media_msg_hub/distribution_server/version"
	"testing"
)

func TestFilterColumns(t *testing.T) {
	fmt.Println("*******")
	// t.Error("the drop column should be testProperty2")

	// // var verifier VersionVerifier
	// verifier := &versionVerifier{}

	// testPropertyVersions := map[string]int{
	// 	"testProperty1": 1,
	// 	"testProperty2": 2,
	// 	"testProperty3": 3,
	// }
	// // 写入
	// version.GetInstance().WriteVersion("testVid", testPropertyVersions)

	// // 读取
	// columnsToDrop := verifier.filterColumns("testVid", testPropertyVersions)
	// if columnsToDrop[0] != "testProperty2" {
	// 	t.Error("the drop column should be testProperty2")
	// }
}
