package worker

import (
	"fmt"
	"math"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/alarm"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/utils"
	"github.com/pkg/errors"
)

type errorMessage struct {
	vid           string
	err           error
	sequenceId    int64
	subscribeName string
}

// 想订阅用户发送失败消息的notifier. 此notifier在积累一定消息后批量发送
type errorNotifier struct {
	messages             chan *errorMessage
	admins               []string
	clientName           string
	lastTickTime         time.Time
	messageFlushInterval time.Duration
}

// NewErrorNotifier 新建错误通知实例
func NewErrorNotifier(clientName string, admins []string) *errorNotifier {
	notifier := &errorNotifier{
		messages:             make(chan *errorMessage, 1024),
		lastTickTime:         time.Now(),
		clientName:           clientName,
		admins:               admins,
		messageFlushInterval: 60 * time.Second,
	}
	go notifier.run()
	return notifier
}

func (notifier *errorNotifier) send(message *errorMessage) {
	notifier.messages <- message
}

func (notifier *errorNotifier) run() {
	messagesToSend := make([]*errorMessage, 0, math.MaxInt16)
	timer := time.NewTicker(notifier.messageFlushInterval)
	do := func() {
		now := time.Now()
		defer func() { notifier.lastTickTime = now }()
		if len(messagesToSend) == 0 {
			return
		}
		// 1. 构建告警消息
		builder := &strings.Builder{}
		_, _ = builder.WriteString(
			fmt.Sprintf("在%s到%s之间\n",
				notifier.lastTickTime.Format("2006-01-02 15:04:05"), now.Format("2006-01-02 15:04:05")))
		failedMessageCount := make(map[string]int)
		for _, message := range messagesToSend {
			if count, exists := failedMessageCount[message.vid]; exists {
				failedMessageCount[message.vid] = count + 1
			} else {
				failedMessageCount[message.vid] = 0
			}
		}
		_, _ = builder.WriteString(fmt.Sprintf("[%s]\n环境[%s]服务器[%s]\n共有%d条消息推送失败并重试了%d次\n",
			notifier.clientName, trpc.GlobalConfig().Global.Namespace, utils.GetIp(), len(failedMessageCount),
			len(messagesToSend)))
		_, _ = builder.WriteString("请检查服务是否稳定\n")
		_, _ = builder.WriteString(fmt.Sprintf("其中一条错误消息(id:%s)为：\n%s", messagesToSend[len(messagesToSend)-1].vid,
			messagesToSend[len(messagesToSend)-1].err))
		alarmMsg := builder.String()

		// 2.处理告警消息
		if len(messagesToSend) > 2 {
			err := alarm.GetInstance().Send(alarmMsg, notifier.admins)
			if err != nil {
				log.Errorf("Error while sending error message to admins, error is %+v\n", errors.WithStack(err))
			}
		} else {
			ctx := log.WithContextFields(trpc.BackgroundContext(),
				"mediaSet", trpc.GlobalConfig().Global.FullSetName,
				"serverName", trpc.GlobalConfig().Server.Server,
				"data_id", messagesToSend[len(messagesToSend)-1].vid,
				"subscriber_name", messagesToSend[len(messagesToSend)-1].subscribeName,
			)
			log.ErrorContext(ctx, alarmMsg)
		}
	}

	for {
		select {
		case message := <-notifier.messages:
			messagesToSend = append(messagesToSend, message)
		case <-timer.C:
			do()
			messagesToSend = make([]*errorMessage, 0, cap(messagesToSend))

		}
	}
}
