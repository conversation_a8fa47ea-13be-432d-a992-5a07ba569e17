package worker

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/alarm"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/db"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/extcfg"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/report"
	"git.woa.com/video_media/traceplus"
	"github.com/avast/retry-go/v4"
)

// MessageSendManager 消息发送结构
type MessageSendManager struct {
	notifier      Notifier
	marioNotifier *marioNotifier

	errorNotifier     *errorNotifier
	columnsToPreserve []string
	subscriber        *db.Subscriber
	isShutdown        bool
	ruleFilters       RuleFilters
}

func newMessageSendManager(subscriber *db.Subscriber) (*MessageSendManager, error) {
	notifier, err := GetNotifier(subscriber)
	if err != nil {
		return nil, err
	}
	filters, err := NewRuleFilters(subscriber.FilterRule)
	if err != nil {
		return nil, err
	}
	notifierGroup := &MessageSendManager{
		notifier:          notifier,
		marioNotifier:     newMarioNotifier(),
		columnsToPreserve: subscriber.SubscribedColumns,
		errorNotifier:     NewErrorNotifier(subscriber.Name, subscriber.Admins),
		subscriber:        subscriber,
		ruleFilters:       filters,
	}
	return notifierGroup, nil
}

// Shutdown 关闭
func (n *MessageSendManager) Shutdown() {
	n.isShutdown = true
}

func (n *MessageSendManager) sendSync(ctx context.Context, message *MediaInfo) (string, error) {
	// 执行用户配置的规则
	if !n.ruleFilters.Match(message) {
		return "", MessageFilteredByRule
	}
	ret, err := n.sendMessage(ctx, message)
	if err != nil {
		return ret, err
	}
	return ret, SendSuccess
}

// sendMessage 发送message
func (n *MessageSendManager) sendMessage(ctx context.Context, message *MediaInfo) (string, error) {
	extCfg := extcfg.GetExtCfg()
	notifier := n.notifier
	if len(extCfg.Mario.BaggageKey) > 0 {
		baggageValue := traceplus.ReadBaggage(ctx, extCfg.Mario.BaggageKey)
		if len(baggageValue) > 0 {
			log.DebugContextf(ctx, "BaggageKey found in ctx -> Baggage, "+
				"extCfg.Mario.BaggageKey[%s], baggageValue[%s]", extCfg.Mario.BaggageKey, baggageValue)
			nameHit := extCfg.Mario.CheckSubscriber(n.subscriber.Name)
			if nameHit {
				log.InfoContextf(ctx, "nameHit, replacing all notifiers with marioNotifier, "+
					"n.subscriber.Name[%s], nameHit[%t]", n.subscriber.Name, nameHit)
				notifier = n.marioNotifier
			}
		}
	}

	if n.isShutdown || ctx.Err() != nil {
		log.InfoContext(ctx, "The notifier is shutdown, abort sending")
		return "", SendAbortError
	}

	// 计算当前处理推送消息和当前时间差值，
	handleTimer := time.Now()
	var retrySecond float64
	if message.TimeStamp > 0 { // 有些重推数据，没有时间戳，会导致统计结果失真；故过滤掉此种数据
		retrySecond = float64(handleTimer.Unix() - int64(message.TimeStamp))
	}
	// 等待延迟推送时间
	waitDelayPush(message.TimeStamp, n.subscriber.DelayPushTime)
	// 内容分发的接出，上报ATTa（用于数据监控统计）
	var pushInfo report.PushInfo
	pushInfo.OriginalMsgTimestamps = int64(message.TimeStamp)
	pushInfo.DataID = message.Id
	pushInfo.DataType = message.DataSetId
	pushInfo.ModifyFields = report.GetMapKeys(message.ModifyFieldInfos)
	pushInfo.PrjID, pushInfo.BizID = db.GetPrjBizInfo(n.subscriber.Name)
	pushInfo.MsgSeqID = strconv.FormatInt(message.SequenceId, 10)
	pushInfo.StepNum = 1 // 第一步，接到待推送的消息上报
	if pushInfo.PrjID > 0 && pushInfo.BizID > 0 {
		pushInfo.DoReport(ctx)
	}
	log.InfoContextf(ctx, "subscriber:%s; pushInfo:%+v", n.subscriber.Name, pushInfo)
	ctx = context.WithValue(ctx, "report_info", &pushInfo)

	// 触发推送
	var httpUrl string
	var sendError, retError error
	retError = retry.Do(
		func() error {
			if notifier == nil { // 没有配置回调地址的，可能为nil
				log.ErrorContextf(ctx, "notifier is nil:%s", message.Id)
				return nil
			}
			message.PushTimeStamp = time.Now().UnixNano() / 1e6
			httpUrl, sendError = notifier.Send(ctx, message)
			return sendError
		},
		retry.Attempts(uint(n.subscriber.PushRetry)),                           // 重试次数
		retry.Delay(time.Millisecond*time.Duration(n.subscriber.RetryBackoff)), // 延迟重试
		retry.MaxDelay(time.Millisecond*10000),                                 // 最大延迟时间
		retry.LastErrorOnly(true),
		retry.OnRetry(func(idx uint, err error) { // 每次重试的时候调用方法
			log.ErrorContextf(ctx,
				"Failed to send(%s) message %s, error message is %v; retry time:%d",
				httpUrl, message.Id, sendError, idx)
			// n.errorNotifier.send(&errorMessage{
			// 	vid:        message.Id,
			// 	err:        sendError,
			// 	sequenceId: message.SequenceId,
			// })
		}))
	if retError == nil { // 推送成功
		report.MetReportFunc(message.DataSetId, n.subscriber, 1, 0, 1,
			retrySecond, time.Now().Sub(handleTimer).Seconds())
		return httpUrl, nil
	}

	// 推送告警
	report.MetReportFunc(message.DataSetId, n.subscriber, 1, 1, 0,
		retrySecond, time.Now().Sub(handleTimer).Seconds())
	errorMessage := fmt.Sprintf("[%s:%d]data_id:%s 经过%d次重试后，发送失败了，最后一次错误:%s。"+
		"如果需要重新消费消息，请重置offset", n.subscriber.Name, n.subscriber.Id, message.Id,
		n.subscriber.PushRetry, retError)
	err := alarm.GetInstance().Send(errorMessage, n.subscriber.Admins)
	if err != nil {
		log.ErrorContextf(ctx, "发送微信消息失败了,内容：%s, error:%+v\n", errorMessage, err)
	}
	// 推送失败队列
	err = pushFailedMsg(ctx, &msghub.ErrMessage{
		AppKey:        n.subscriber.AppKey,
		SubscribeName: n.subscriber.Name,
		URL:           n.subscriber.CallbackUrl,
		Err:           errorMessage,
		ID:            message.Id,
		DataSet:       message.DataSetId,
		PushTimeStamp: message.PushTimeStamp,
		MediaInfo:     fmt.Sprintf("%s", message),
	})
	if err != nil {
		log.ErrorContextf(ctx, "pushFailedMsg,errorMessage:%s err:%+v", errorMessage, err)
		return httpUrl, retError
	}
	retError = SendFailedError
	log.InfoContextf(ctx, "pushFailedMsg [%s:%d] success id[%s] pushTimeStamp:[%d], dataSet:[%d]", n.subscriber.Name,
		n.subscriber.Id, message.Id, message.PushTimeStamp, message.DataSetId)
	return httpUrl, retError
}

// Notifier 通知接口
type Notifier interface {
	Send(ctx context.Context, message *MediaInfo) (string, error)
}

// GetNotifier 获取通知实例
func GetNotifier(subscriber *db.Subscriber) (Notifier, error) {
	if subscriber.CallbackUrl != "" {
		return newHttpNotifier(subscriber)
	}
	if subscriber.TafServantName != "" {
		return newRPCNotifier(subscriber.TafServantName, subscriber.Timeout)
	}
	return nil, nil
}

// waitDelayPush 等待延迟推送时间
func waitDelayPush(modifyTime, delayTime int) {
	if modifyTime == 0 || delayTime == 0 {
		return
	}
	for {
		if time.Now().After(time.Unix(int64(modifyTime), 0).Add(time.Second * time.Duration(delayTime))) {
			break
		}
		time.Sleep(time.Millisecond * 20)
	}
	return
}
