// 这个复杂的factory是为了解决反序列化json开销
// 相同的消息体，每个客户会反序列化一次。虽然我们使用了较为高效的反序列化工具，但是效率依然不是很理想，因为json反序列化就很慢
// 本factory通过缓存反序列化对象，来减少反序列化对CPU的消耗
// 同一个消息体的请求到达时，除了第一个请求会真正执行反序列化工作以外，其他请求会等待，直到超时或者缓存成功。这个过程不是原子性不加锁的，所以不排除有两个请求同时在做反序列化工作（概率非常低），最终一致性不会受到影响
// 读写缓存是有锁的

package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/localcache"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	access "git.code.oa.com/trpcprotocol/storage_service/access_layer"
	storage_comm "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	metaData "git.code.oa.com/trpcprotocol/video_media/metadata_api"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/extcfg"
	"github.com/Shopify/sarama"
)

// MediaMessageFactory 媒资消息工厂
type MediaMessageFactory struct {
	extraField   []string
	fuzzyColumns map[string]struct{}
}

// DefaultMediaMessageFactory 默认消息处理工厂
var DefaultMediaMessageFactory = NewMediaMessageFactory([]string{}, []string{})

// NewMediaMessageFactory 新建媒资消息工厂
func NewMediaMessageFactory(extraField []string, columns []string) *MediaMessageFactory {
	fuzzyColumns := map[string]struct{}{}
	// 多语言字段处理：tittle.* 匹配 tittle/tittle.cn/tittle.en等
	for _, v := range columns {
		s := strings.Split(v, separator)
		if !(len(s) == 2 && s[1] == "*") {
			continue
		}
		fuzzyColumns[s[0]] = struct{}{}
	}
	factory := &MediaMessageFactory{extraField: extraField, fuzzyColumns: fuzzyColumns}
	return factory
}

// BuildMessage 构建消息
/*
BuildMessage
*  @Description: 接到的kafka消息是当前租户的所有field信息,这里需要过滤掉用户没有订阅的,同时要把用户跨租户订阅的查回来发下去
基于现有总线模型,该方案可能会有读扩散,同时在并发查询其他租户时,性能会有木桶效应
*  @receiver m
*  @param message
*  @param targetField
*  @return *MediaInfo 此消息将会直接回调给订阅用户
*  @return error
*/
func (m *MediaMessageFactory) BuildMessage(message *sarama.ConsumerMessage, targetField []string) (*MediaInfo, error) {
	mediaInfo, err := NewMediaInfo(message)
	if err != nil {
		return nil, err
	}
	mediaInfo.filterColumns(targetField, m.extraField, m.fuzzyColumns)
	if tenant := extcfg.GetExtCfg().Tenant; tenant != "" {
		ctx := trpc.BackgroundContext()
		if extraFieldInfo, err := m.GetMultiTenantFieldInfo(ctx, mediaInfo.DataSetId, tenant, mediaInfo.Id,
			ClassifyTenantField(ctx, int32(mediaInfo.DataSetId), m.extraField)); err == nil {
			for k, v := range extraFieldInfo {
				mediaInfo.FieldInfos[k] = v
			}
		}
	}
	return mediaInfo, nil
}

// BuildMessageFromMediaInfo 构建字段过滤消息体
func (m *MediaMessageFactory) BuildMessageFromMediaInfo(mediaInfo *MediaInfo, targetField []string) *MediaInfo {
	return mediaInfo.filterColumns(targetField, m.extraField, m.fuzzyColumns)
}

// FieldConfKey 获取fieldconf的key
func FieldConfKey(dateSetID int32, fields string) string {
	return fmt.Sprintf("%d_%s", dateSetID, fields)
}

// ClassifyTenantField 把字段列表中的字段,按租户ID划分开
/*
ClassifyTenantField 可以先从本地缓存取,如果没有从品类库获取,同时可以放入本地缓存
*  @Description: 把字段列表中的字段,按租户ID划分开
*  @param dateSetID
*  @param fields
*  @return map[string][]string key是租户, val是字段列表
*/
func ClassifyTenantField(ctx context.Context, dateSetID int32, fields []string) map[string][]string {
	res := make(map[string][]string)
	for _, f := range fields {
		log.Debugf("dataset:%d, field:%s", dateSetID, f)
		key := FieldConfKey(dateSetID, f)
		fc, err := localcache.GetWithLoad(ctx, key, LoadFieldConf, 600)
		if err != nil {
			log.Errorf("Error loading key:%s, err:%v", key, err)
			continue
		}
		if fieldConf, ok := fc.(FieldConf); ok {
			res[fieldConf.Tenant] = append(res[fieldConf.Tenant], fieldConf.Name)
		}
	}
	return res
}

// FieldConf 字段类型定义
type FieldConf struct {
	DataSetID int32
	Tenant    string
	Name      string
	ValueType int
}

// LoadFieldConf 加载fieldconf
func LoadFieldConf(ctx context.Context, key string) (interface{}, error) {
	log.Debugf("Loading field key:%s", key)
	parts := strings.Split(key, "_")
	if len(parts) < 2 {
		return nil, nil
	}
	dataSetID := parts[0]
	setID, _ := strconv.Atoi(dataSetID)
	fieldName := parts[1]
	proxy := metaData.NewModelAPIClientProxy()
	req := &metaData.ListModelFieldsRequest{
		ModelId:  int32(setID),
		PageInfo: &metaData.PageInfo{Index: 1, Size: 10000}, // 超过1万的时候就坑了
	}
	rsp, err := proxy.ListModelFields(ctx, req, client.WithDisableServiceRouter())
	log.Debugf("ListModelFieldsReq:%v,rsp:%v", req, rsp)
	if err != nil {
		log.Errorf("ListModelFieldsErr:%v", err)
		return nil, err
	}
	var resFieldConf FieldConf
	for _, field := range rsp.Fields {
		fc := FieldConf{
			DataSetID: int32(setID),
			Tenant:    field.TenantId,
			Name:      field.Name,
		}
		if field.Name == fieldName {
			resFieldConf = fc
		}
		localcache.Set(FieldConfKey(int32(setID), field.Name), fc, 600)
	}
	return resFieldConf, nil
}

// StorageSvrTarget 拼接底层服务的路由别名
func StorageSvrTarget(tenant string) string {
	return fmt.Sprintf("polaris://trpc.storage_service.access_layer.%s.storage", tenant)
}

// GetMultiTenantFieldInfo 媒资读取多租户的共享字段,这种字段一定在携带信息中,因为不可以跨租户订阅字段变更,
//    比如现在订阅租户A视频实体的标题,可以携带租户B视频实体的状态,但如果想订阅B的状态变更,只能去租户B那里订阅
/*
  - @receiver m
  - @param tenantFields key是租户ID, value是字段列表
  - @return map[string]fieldInfo key是字段名,值是msghub的FieldInfo结构体
*/
func (m *MediaMessageFactory) GetMultiTenantFieldInfo(ctx context.Context, dateSetID int, tenant, id string,
	tenantFields map[string][]string) (map[string]fieldInfo, error) {
	if len(tenantFields) == 0 {
		return nil, nil
	}
	var locker sync.Mutex
	var hanFuncs []func() error
	res := make(map[string]fieldInfo)
	for k, v := range tenantFields {
		ten, fields := k, v
		if ten == tenant { // 本租户的字段已经再ModifySvr中做了一次getall,这里只取其他租户的
			continue
		}
		fieldInfos := make(map[string]string)
		temFunc := func() error {
			err := GetMediaInfo(ctx, id, StorageSvrTarget(ten), fields, dateSetID, fieldInfos)
			if err != nil {
				return err
			}
			locker.Lock()
			defer locker.Unlock()
			for field, val := range fieldInfos {
				res[field] = fieldInfo{
					Value: val,
				}
			}
			return nil
		}
		hanFuncs = append(hanFuncs, temFunc)
	}
	err := trpc.GoAndWait(hanFuncs...)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GetMediaInfo 拉取媒资信息 总线用的逗号分割, 公共库暂时没有通用方法 target是用于多租户的读取
func GetMediaInfo(ctx context.Context, dataID, target string, fields []string,
	dataSet int, fieldInfos map[string]string) error {
	req := &access.MediaGetRequest{
		AuthInfo: &access.AuthInfo{
			AppId:  "media_message_hub",
			AppKey: "dW7JeXFAmYeYWVQQdGP6",
		},
		FieldNames: fields,
		DataSetId:  int32(dataSet),
		Id:         dataID,
	}
	opt := []client.Option{
		client.WithTimeout(time.Duration(3) * time.Second),
		client.WithDisableServiceRouter(),
		client.WithTarget(target),
	}
	proxy := access.NewMediaInterfaceClientProxy()
	rsp, err := proxy.GetMediaInfo(ctx, req, opt...)
	if err != nil {
		return fmt.Errorf("GetMediaAllInfoErr_%w", err)
	}
	if rsp.RetInfo.ErrCode != 0 {
		return fmt.Errorf("GetMediaAllInfoCodeErr_%d", rsp.RetInfo.ErrCode)
	}
	if len(rsp.FieldInfos) == 0 {
		return fmt.Errorf("GetMediaAllInfoFieldEmpty")
	}
	for _, info := range rsp.FieldInfos {
		switch info.FieldType {
		case storage_comm.EnumFieldType_FieldTypeStr:
			fieldInfos[info.FieldName] = info.StrValue
		case storage_comm.EnumFieldType_FieldTypeIntVec:
			if len(info.VecInt) == 0 {
				continue
			}
			var sbu strings.Builder
			for _, iVal := range info.VecInt {
				fmt.Fprint(&sbu, strconv.Itoa(int(iVal))+",")
			}
			vecStr := sbu.String()
			fieldInfos[info.FieldName] = vecStr[:len(vecStr)-1]
		case storage_comm.EnumFieldType_FieldTypeSet:
			fieldInfos[info.FieldName] = strings.Join(info.VecStr, ",") // 总线用逗号
		case storage_comm.EnumFieldType_FieldTypeMapKV, storage_comm.EnumFieldType_FieldTypeMapKList:
			mapStr, err := json.Marshal(parseStorageMap(info.MapVal))
			if err != nil {
				log.Errorf("map2strErr:%v", err)
				continue
			}
			fieldInfos[info.FieldName] = string(mapStr)
		default:
			log.Errorf("field:%+v,new_type:%v", info.FieldName, info.FieldType)
		}
	}
	log.DebugContextf(ctx, "id:%+v,GetMediaRes1:%+v, err:%+v", dataID, fieldInfos, err)
	return nil
}

// parseStorageMap 底层map解析
func parseStorageMap(sMap map[string]*storage_comm.MapValue) map[string]interface{} {
	res := make(map[string]interface{})
	if len(sMap) == 0 {
		return res
	}
	for k, v := range sMap {
		switch v.Type {
		case storage_comm.EnumFieldType_FieldTypeStr:
			res[k] = v.StrValue
		case storage_comm.EnumFieldType_FieldTypeSet:
			res[k] = v.VecStr
		}
	}
	return res
}
