package worker

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/trpcprotocol/video_media/distribution_filter"
	"git.code.oa.com/video_media/service_protocol/trpc-protocol/VideoMediaInf"
	"git.woa.com/video_media/traceplus/utils"
)

type marioNotifier struct {
	proxy distribution_filter.DistributionFilterClientProxy
}

// Send 发送方法
func (n *marioNotifier) Send(ctx context.Context, message *MediaInfo) (string, error) {
	request := &distribution_filter.DistributionReq{Message: n.translate(message)}
	response, rpcError := n.proxy.Distribution(ctx, request)
	if rpcError != nil {
		return "mario-pipline-push", fmt.Errorf("proxy.Distribution RPC failed, "+
			"request[%s], rpcError.Error[%s]", utils.MarshalJSON(request), rpcError.Error())
	}
	if response.GetCode() != 0 {
		return "mario-pipline-push", fmt.Errorf("proxy.Distribution response invalid, "+
			"request[%s], response.Code[%d], respose.Msg[%s]",
			utils.MarshalJSON(request), response.GetCode(), response.GetMsg())
	}
	return "mario-pipline-push", nil
}

func newMarioNotifier() *marioNotifier {
	notifier := new(marioNotifier)
	notifier.proxy = distribution_filter.NewDistributionFilterClientProxy(
		client.WithNamespace(trpc.GlobalConfig().Global.Namespace),
		client.WithDisableServiceRouter())
	return notifier
}

func (n *marioNotifier) translate(message *MediaInfo) *distribution_filter.MediaInfo {
	mediaInfo := &distribution_filter.MediaInfo{
		FieldInfos:            make(map[string]*distribution_filter.FieldInfo),
		ModifyFieldInfos:      make(map[string]*distribution_filter.ModifyFieldInfo),
		Id:                    message.Id,
		DataSetId:             int32(message.DataSetId),
		Timestamp:             int32(message.TimeStamp),
		ProcessStartTimestamp: message.ProcessStartTimestamp,
		AppId:                 message.AppId,
		Version:               int32(message.Version),
		OperatorName:          message.OperatorName,
		PushTimestamp:         message.PushTimeStamp,
		SequenceId:            message.SequenceId,
	}
	for fKey, fInfo := range message.FieldInfos {
		mediaInfo.FieldInfos[fKey] = &distribution_filter.FieldInfo{
			Id:     int32(fInfo.Id),
			Value:  fInfo.Value,
			MapVal: fInfo.MapVal,
		}
	}
	for mKey, mInfo := range message.ModifyFieldInfos {
		mediaInfo.ModifyFieldInfos[mKey] = &distribution_filter.ModifyFieldInfo{
			Id:        int32(mInfo.Id),
			Old:       mInfo.Old,
			New:       mInfo.New,
			Version:   int32(mInfo.Version),
			OldMapVal: n.translateMapValue(mInfo.OldMapVal),
			NewMapVal: n.translateMapValue(mInfo.NewMapVal),
		}
	}
	return mediaInfo
}

func (n *marioNotifier) translateMapValue(
	source map[string]VideoMediaInf.MapValue) map[string]*common_storage_common.MapValue {
	result := make(map[string]*common_storage_common.MapValue)
	if source == nil {
		return result
	}

	for key, value := range source {
		result[key] = &common_storage_common.MapValue{
			Type:     common_storage_common.EnumFieldType(value.Type),
			StrValue: value.StrValue,
			VecStr:   value.VecStr,
		}
	}
	return result
}
