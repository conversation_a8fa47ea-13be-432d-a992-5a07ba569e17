package worker

import (
	"context"
	"errors"
	"fmt"
	"time"

	_ "git.code.oa.com/trpc-go/trpc-codec/tars" // tars TODO
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/tars-protocol/tafNotifier"
)

type rpcNotifier struct {
	app     tafNotifier.NotifierReceiverProxy
	obj     string
	timeout time.Duration // x * ms
}

// Send 发送rpc消息
func (n *rpcNotifier) Send(ctx context.Context, message *MediaInfo) (string, error) {
	var errorMessage string
	errorCode, err := n.app.Receive(ctx, n.buildNotifyMessage(message), &errorMessage,
		client.WithTarget("polaris://"+n.obj),
		client.WithNamespace("Production"),
		client.WithTimeout(n.timeout),
	)
	if err != nil {
		return "", err
	}
	if errorCode != 0 {
		return "", errors.New(fmt.Sprintf("Send message error, errorCode:%d, msg:%s", errorCode, errorMessage))
	}
	if errorMessage != "" {
		return "", errors.New(errorMessage)
	}
	return "", nil
}

// newRPCNotifier 新建rpc发送者
// RPCNotifier在创建的时候会出错，如果servant有错误的话，taf会直接panic，而不是返回error
func newRPCNotifier(tafServantName string, timeout int) (*rpcNotifier, error) {
	notifier := new(rpcNotifier)
	notifier.obj = fmt.Sprintf(tafServantName)
	notifier.app = tafNotifier.NewNotifierReceiverProxy(notifier.obj)
	notifier.timeout = time.Duration(timeout) * time.Millisecond
	if notifier.timeout > 10*time.Second {
		return nil, errors.New("subscribe notifier.timeout > 10s")
	}
	return notifier, nil
}

func (n *rpcNotifier) buildNotifyMessage(message *MediaInfo) *tafNotifier.MediaMessage {
	messageToBuild := &tafNotifier.MediaMessage{
		Vid:              message.Id,
		Timestamp:        int32(message.TimeStamp),
		DataSetId:        int32(message.DataSetId),
		ModifyFieldInfos: make(map[string]tafNotifier.ModifyFieldInfo),
		FieldInfos:       make(map[string]tafNotifier.FieldInfo),
		OperatorName:     message.OperatorName,
	}

	for fieldName, modifyFieldInfo := range message.ModifyFieldInfos {
		messageToBuild.ModifyFieldInfos[fieldName] = tafNotifier.ModifyFieldInfo{
			Id:  int32(modifyFieldInfo.Id),
			Old: modifyFieldInfo.Old,
			New: modifyFieldInfo.New,
		}
	}

	for fieldName, fieldInfo := range message.FieldInfos {
		messageToBuild.FieldInfos[fieldName] = tafNotifier.FieldInfo{
			Id:    int32(fieldInfo.Id),
			Value: fieldInfo.Value,
		}
	}
	return messageToBuild
}
