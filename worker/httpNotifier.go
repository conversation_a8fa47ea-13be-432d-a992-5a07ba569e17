package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/polaris/polaris-go/api"
	"git.code.oa.com/polaris/polaris-go/pkg/config"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/media_msg_hub/bee_push_plugin"
	"git.code.oa.com/video_media/media_go_commlib/msghub/plugin"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/db"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/polaris"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/report"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/utils"
	"github.com/pkg/errors"
)

// StatusCode_NoPlugin bee插件的返回码, 默认无插件的推送
const (
	StatusCode_NoPlugin = 100
)

// HttpNotifierUrl Http通知抽象方法
type HttpNotifierUrl interface {
	Url() (string, error)
}

type fixedNotifierUrl struct {
	httpUrl string
}

func (u *fixedNotifierUrl) Url() (string, error) {
	return u.httpUrl, nil
}

type polarisNotifierUrl struct {
	scheme  string
	path    string
	request *api.GetOneInstanceRequest
}

var _polarisSDKConsumer api.ConsumerAPI
var once sync.Once

// GetPolarisConsumerAPI TODO
func GetPolarisConsumerAPI() api.ConsumerAPI {
	once.Do(func() {
		configuration := api.NewConfiguration()
		configuration.GetConsumer().GetServiceRouter().SetChain([]string{config.DefaultServiceRouterDstMeta})
		consumer, err := api.NewConsumerAPIByConfig(configuration)
		if err != nil {
			panic(err)
		}
		_polarisSDKConsumer = consumer
	})
	return _polarisSDKConsumer
}

func (p *polarisNotifierUrl) Url() (string, error) {
	instance, err := GetPolarisConsumerAPI().GetOneInstance(p.request)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if len(instance.GetInstances()) == 0 {
		return "", errors.Errorf("No instance found for request %s, %s", p.request.Namespace, p.request.Service)
	}
	node := instance.GetInstances()[0]
	return fmt.Sprintf("%s://%s:%d%s",
		p.scheme, node.GetHost(), node.GetPort(), p.path), nil
}

func newPolarisNotifierUrl(scheme string, namespace string,
	serviceName string, setName string, path string) (*polarisNotifierUrl, error) {
	mapSetNameConf := make(map[string]string)
	if setName != "" {
		mapSetNameConf["internal-enable-set"] = "Y"
		mapSetNameConf["internal-set-name"] = setName
	}
	request := &api.GetOneInstanceRequest{
		GetOneInstanceRequest: model.GetOneInstanceRequest{
			Service:   serviceName,
			Namespace: namespace,
			Metadata:  mapSetNameConf,
		},
	}

	return &polarisNotifierUrl{
		scheme:  scheme,
		path:    path,
		request: request,
	}, nil
}

type zknameNotifierUrl struct {
	scheme     string
	zknameHost string
	path       string
}

func (z *zknameNotifierUrl) Url() (string, error) {
	hostAndPort, e := polaris.GetHostByKey(z.zknameHost)
	if e != nil {
		return "", errors.WithStack(e)
	}
	return z.scheme + "://" + hostAndPort.Ip + ":" + strconv.Itoa(int(hostAndPort.Port)) + z.path, nil
}

// newZknameNotifierUrl 创建一个新的基于zkname的url地址器。创建时候会调用一次zkname，如果zkname有误，直接返回错误
func newZknameNotifierUrl(scheme string, zknameHost string, path string) (*zknameNotifierUrl, error) {
	_, e := polaris.GetHostByKey(zknameHost)
	if e != nil {
		return nil, errors.WithStack(e)
	}
	return &zknameNotifierUrl{scheme: scheme, zknameHost: zknameHost, path: path}, nil
}

type l5NotifierUrl struct {
	scheme string
	mod    int
	cmd    int
	path   string
}

func (l l5NotifierUrl) Url() (string, error) {
	server, err := utils.GetL5API().GetServerBySid(int32(l.mod), int32(l.cmd))
	if err != nil {
		return "", err
	}
	return l.scheme + "://" + server.Ip().String() + ":" + strconv.Itoa(int(server.Port())) + l.path, nil
}

func newL5NotifierUrl(scheme, mod, cmd, path string) (HttpNotifierUrl, error) {
	_mod, err := strconv.Atoi(mod)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	_cmd, err := strconv.Atoi(cmd)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	_, err = utils.GetL5API().GetServerBySid(int32(_mod), int32(_cmd))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := &l5NotifierUrl{
		scheme: scheme,
		mod:    _mod,
		cmd:    _cmd,
		path:   path,
	}

	_, err = result.Url()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

// NewHttpNotifierUrl 新建http通知实例
func NewHttpNotifierUrl(httpUrl string) (HttpNotifierUrl, error) {
	parse, e := url.Parse(httpUrl)
	if e != nil {
		return nil, errors.WithStack(e)
	}
	if strings.HasPrefix(parse.Host, "zkname+") {
		zknameHosts := strings.Split(parse.Host, "+")
		path := parse.Path
		if parse.RawQuery != "" {
			path = path + "?" + parse.RawQuery
		}
		return newZknameNotifierUrl(parse.Scheme, zknameHosts[1], path)

	}
	if strings.HasPrefix(parse.Host, "l5:") {
		l5Hosts := strings.Split(parse.Host, ":")
		path := parse.Path
		if parse.RawQuery != "" {
			path = path + "?" + parse.RawQuery
		}
		return newL5NotifierUrl(parse.Scheme, l5Hosts[1], l5Hosts[2], path)
	}

	if strings.HasPrefix(parse.Host, "polaris+") {
		polarisHost := strings.Split(parse.Host, "+")
		path := parse.Path
		if parse.RawQuery != "" {
			path = path + "?" + parse.RawQuery
		}
		var setName string
		namespace := polarisHost[1]
		serviceName := polarisHost[2]
		if len(polarisHost) == 4 {
			setName = polarisHost[3]
		}
		return newPolarisNotifierUrl(parse.Scheme, namespace, serviceName, setName, path)
	}

	return &fixedNotifierUrl{
		httpUrl: httpUrl,
	}, nil
}

type httpNotifier struct {
	notifierUrl HttpNotifierUrl
	client      http.Client
	timeout     int
	hook        hook
}

type hook interface {
	do(message *MediaInfo) (*bee_push_plugin.Reply, error)
}

func newHttpNotifier(subscriber *db.Subscriber) (*httpNotifier, error) {
	notifierUrl, err := NewHttpNotifierUrl(subscriber.CallbackUrl)
	if err != nil {
		return nil, err
	}

	transport := &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
			DualStack: true,
		}).DialContext,
		MaxIdleConns:          0,
		MaxIdleConnsPerHost:   60,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}
	n := &httpNotifier{
		notifierUrl: notifierUrl,
		client: http.Client{
			Transport: transport,
			Timeout:   time.Millisecond * time.Duration(subscriber.Timeout),
		},
		timeout: subscriber.Timeout,
		hook:    &hookDefault{},
	}

	if len(subscriber.Plugin) > 0 {
		n.hook = &hookImp{
			AppKey:  subscriber.AppKey,
			Service: subscriber.Plugin,
		}
	}
	return n, nil
}

func sendReport(ctx context.Context, pluginStatus int, reqJson string, e error) func() {
	start := time.Now().UnixNano() / 1e6 // 毫秒
	return func() {
		// 非分发插件，不用上报
		if pluginStatus != int(bee_push_plugin.StatusCode_ok) {
			return
		}
		// 推送给下游业务之后的上报
		costTime := time.Now().UnixNano()/1e6 - start
		var pluginRsp plugin.PluginDataFilterRsp
		if err := json.Unmarshal([]byte(reqJson), &pluginRsp); err != nil {
			log.ErrorContextf(ctx, "json unmarshal error: %s", e.Error())
			return
		}
		log.InfoContextf(ctx, "pluginRsp after:%+v", pluginRsp)

		if v := ctx.Value("report_info"); v != nil {
			reportInfo := v.(*report.PushInfo)
			reportInfo.StepNum = 3 // 第三步：推送完下游之后上报
			reportInfo.ReqCostTime = int(costTime)
			reportInfo.DeliveryType = report.DeliveryTypePass
			if e != nil {
				reportInfo.ReqState = "err"
				reportInfo.RspInfo = e.Error()
			} else {
				reportInfo.ReqState = "ok"
			}
			if playEquity, ok := pluginRsp.PlayInfos["all"]; ok {
				if playEquity != nil {
					reportInfo.IsPlay = playEquity.IsPlay
				}
			}
			reportInfo.PushStatus = int(pluginRsp.PushType)
			log.InfoContextf(ctx, "sendReport reportInfo:%+v", reportInfo)
			reportInfo.DoReport(ctx)
		}
	}
}

func (h *httpNotifier) Send(ctx context.Context, message *MediaInfo) (string, error) {
	httpUrl, err := h.notifierUrl.Url()
	if err != nil {
		return "errorUrl", errors.WithStack(err)
	}
	x, err := h.hook.do(message)
	if err != nil {
		return httpUrl, errs.Newf(7777, "hook error msg:%v", err)
	}
	log.InfoContextf(ctx, "req plugin return code:%d return info:%+v", x.Code, x.MediaInfo)
	switch x.Code {
	case int32(bee_push_plugin.StatusCode_ok):
		log.WithContextFields(ctx, "debug_flag", "hook ok")
	case int32(bee_push_plugin.StatusCode_failed):
		log.WithContextFields(ctx, "debug_flag", "hook failed")
		log.InfoContextf(ctx, "msg %s hook failed", message.Id)
		return httpUrl, nil
	case int32(bee_push_plugin.StatusCode_drop):
		log.WithContextFields(ctx, "debug_flag", "hook drop")
		log.InfoContextf(ctx, "msg %s hook drop", message.Id)
		return httpUrl, nil
	case int32(StatusCode_NoPlugin): // 为了与配置了插件的推送区分，新增一个默认状态 StatusCode_NoPlugin
		log.WithContextFields(ctx, "debug_flag", "no hook plugin")
	default:
		return httpUrl, errors.New(fmt.Sprint("Error hook Code is ", x.Code))
	}
	body, httpCode, err := h.post(ctx, int(x.Code), httpUrl, x.MediaInfo)
	if err != nil {
		return httpUrl, err
	}
	return fmt.Sprintf("body(%s)HttpCode(%d)Url(%s)", body, httpCode, httpUrl), nil
}

func (h *httpNotifier) post(ctx context.Context, pluginStatus int, httpUrl string, reqJson string) (string, int,
	error) {
	var err error
	defer sendReport(ctx, pluginStatus, reqJson, err)()
	proxy := thttp.NewClientProxy("trpc.app.http", client.WithSerializationType(codec.SerializationTypeNoop))
	rspHead := &thttp.ClientRspHeader{}
	reqHead := &thttp.ClientReqHeader{}
	var urlInfo *url.URL
	urlInfo, err = url.Parse(httpUrl)
	if err != nil {
		log.ErrorContextf(ctx, "post httpUrl path err:%+v", err)
		return "", 0, errors.WithStack(err)
	}
	reqBody := &codec.Body{Data: []byte(reqJson)}
	rspBody := &codec.Body{}
	var targetAddr, targetType string
	targetAddr = urlInfo.Hostname() + ":" + urlInfo.Port()
	parseIp := net.ParseIP(urlInfo.Hostname())
	if parseIp != nil {
		targetType = "ip://"
	} else {
		targetType = "dns://"
		if urlInfo.Port() == "" {
			reqHead.Host = urlInfo.Hostname()
			targetAddr = urlInfo.Hostname() + ":80"
		}
	}
	target := targetType + targetAddr
	reqHead.AddHeader("Content-Type", "application/json")
	err = proxy.Post(ctx, urlInfo.RequestURI(), reqBody, rspBody,
		client.WithReqHead(reqHead),
		client.WithRspHead(rspHead),
		client.WithTarget(target),
		client.WithTimeout(time.Millisecond*time.Duration(h.timeout)),
	)
	if rspHead.Response == nil && err != nil {
		return "", 0, errors.WithStack(err)
	}
	var bytes []byte
	bytes, err = ioutil.ReadAll(rspHead.Response.Body)
	if err != nil {
		return "", 0, errors.WithStack(err)
	}
	if rspHead.Response.StatusCode != http.StatusOK {
		return "", rspHead.Response.StatusCode, errors.New(fmt.Sprintf("Send message failed, received "+
			"http code: %d, body is %s", rspHead.Response.StatusCode, bytes))
	}
	return string(bytes), rspHead.Response.StatusCode, nil
}

type hookDefault struct{}

func (h *hookDefault) do(message *MediaInfo) (*bee_push_plugin.Reply, error) {
	return &bee_push_plugin.Reply{
		Code:      StatusCode_NoPlugin,
		MediaInfo: fmt.Sprintf("%s", message),
	}, nil
}

type hookImp struct {
	AppKey  string
	Service string
}

func (p *hookImp) do(message *MediaInfo) (*bee_push_plugin.Reply, error) {
	req := &bee_push_plugin.Request{
		AppKey:    p.AppKey,
		MediaInfo: fmt.Sprintf("%s", message),
	}
	proxy := bee_push_plugin.NewPluginClientProxy(
		client.WithServiceName(p.Service),
		client.WithDisableServiceRouter(),
		client.WithTimeout(time.Second*5),
	)
	rsp, err := proxy.Process(trpc.BackgroundContext(), req)
	if err != nil {
		log.Debug("proxy err:", err)
		return nil, errors.WithStack(err)
	}
	log.Infof("hook %s rsp:%s", p.Service, rsp)
	return rsp, nil
}
