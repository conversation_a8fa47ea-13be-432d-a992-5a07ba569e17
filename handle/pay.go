package handle

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/ip_status_update/union"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func typeKnowledgePayHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillKnowledgePayUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeKnowledgePayHandle end. update [%+v]", update)
	}()

	err = PayFirstTimeCal(ctx, planData, mediaInfo, update)
	if err != nil {
		return err
	}

	// 余热期计算
	err = KnowledgeYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}

	return KnowledgeOtherCalAndUpdate(ctx, planData, mediaInfo, update)
}

// KnowledgeOtherCalAndUpdate 知识付费其他更新
func KnowledgeOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	// 片库-余热期三月外处理
	update.YuReEndTime = GetYuReEndTime(update.FirstPlayTime, update.MediaYuReEndTime)
	err := CommonThreeMonthYuReCalV2(ctx, planData, update.YuReEndTime, update.YuReThreeMonthTime, update, true)
	if err != nil {
		return err
	}

	// 长尾期处理
	err = CommonChangWeiCalV2(ctx, planData, update, true)
	if err != nil {
		return err
	}
	// 长尾期结束时间计算
	err = CommonThreeChangWeiEndCalV2(ctx, planData, update, true)
	if err != nil {
		return err
	}

	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

// KnowledgeYuReCal 只是付费余热计算
func KnowledgeYuReCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "KnowledgeYuReCal info start")
	defer log.InfoContextf(ctx, "KnowledgeYuReCal info start")

	yuReThreeEndTime := GetSevenMonthEndTime(update.EndPlayTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeEndTime

	if !model.CheckTime(update.EndPlayTime) {
		log.WarnContextf(ctx, "KnowledgeYuReCal playEndTime:[%s] not illegal", update.EndPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, update.EndPlayTime,
		yuReThreeEndTime, parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// PayFirstTimeCal 知识付费首播时间
func PayFirstTimeCal(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "PayFirstTimeCal info start")
	defer log.InfoContextf(ctx, "PayFirstTimeCal info end")

	firstTime := GetPayFirstTime(update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", firstTime)
	update.FirstPlayTime = firstTime

	endTime, err := GetPayEndTime(ctx, mediaInfo.Id, update)
	if err != nil {
		return err
	}
	update.EndPlayTime = endTime

	if !model.CheckTime(firstTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{}
		log.WarnContextf(ctx, "PayFirstTimeCal firstTime:[%s] not illegal!", firstTime)
		return nil
	}

	if !model.CheckTime(endTime) {
		log.WarnContextf(ctx, "PayFirstTimeCal endPlayTime:[%s] not illegal!", endTime)
	}

	log.InfoContextf(ctx, "firstTime:[%s], endPlayTime:[%s]", firstTime, endTime)
	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, firstTime, endTime,
		parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// FillKnowledgePayUpdateInfo 填充知识付费更新信息，获取必要字段
func FillKnowledgePayUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData,
	update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.MediaEndTimeForVIP = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForVIP)
	update.MediaEndTimeForFree = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForFree)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)
	update.FirstCheckupTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstCheckupTime)
	update.VideoCheckupTime = parse.GetFieldValue(mediaInfo, parse.FieldCheckupTime)
	update.EndTime = getEndTime(parse.GetFieldValue(mediaInfo, parse.FieldEndTime),
		parse.GetFieldValue(mediaInfo, parse.FieldTVEndTime))
	update.PayStatus = parse.GetFieldValue(mediaInfo, parse.FieldPayStatus)

	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	update.IsTrailer = isMovieTrailer(update.Category)

	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// GetPayEndTime 获取知识付费运营结束时间
// 【IP实时状态】知识付费运营期计算逻辑调整：https://tapd.woa.com/MediaContent/prong/stories/view/1010093801883711105
func GetPayEndTime(ctx context.Context, dataID string, update *model.UpdateInfo) (string, error) {
	if update.EndTime == "" {
		return update.MediaEndPlayTime, nil
	}
	if update.PayStatus == parse.ValueMonthOnly || update.PayStatus == parse.ValueMonthSingle {
		return timeAddDay(update.EndTime, 14), nil
	}

	info, err := union.GetInfoFromUnion(ctx, dataID)
	if err != nil {
		return update.MediaEndPlayTime, err
	}
	if !model.CheckTime(info.ProductEndTime) {
		return update.MediaEndPlayTime, nil
	}
	log.InfoContextf(ctx, "endTime:%s, info:%s", update.EndTime, info)
	// 停售时间-2天
	ft := timeAddDay(info.ProductEndTime, -2)
	et := timeAddDay(update.EndTime, 90)

	ett, _ := time.ParseInLocation("2006-01-02 15:04:05", et, time.Local)
	ftt, _ := time.ParseInLocation("2006-01-02 15:04:05", ft, time.Local)
	if ett.Before(ftt) {
		return et, nil
	}
	return ft, nil
}

func fixEndTime(endTime string) string {
	// 完结时间字段存在两种格式,所以需要都进行判断
	if model.CheckTime(endTime) {
		return endTime
	}
	if model.CheckTime2(endTime) {
		return endTime + " 00:00:00"
	}

	return ""
}

// GetPayFirstTime 获取知识付费首播时间
func GetPayFirstTime(update *model.UpdateInfo) string {
	if update.FirstCheckupTime == "" && update.VideoCheckupTime == "" {
		return update.MediaFirstPlayTime
	}

	if update.FirstCheckupTime == "" {
		return update.VideoCheckupTime
	}
	return update.FirstCheckupTime
}
