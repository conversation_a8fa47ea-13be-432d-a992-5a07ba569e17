package handle

import (
	"context"
	"encoding/json"
	"fmt"
	"git.code.oa.com/video_media/ip_status_update/repo"
	"net/http"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/elastic_task_schedule/ets_api"
	"git.code.oa.com/video_media/ip_status_update/config"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	tjob "git.woa.com/trpcprotocol/tjobs/tjobs_core_service"
)

type typeCalValueTime func(valueData *model.ValueData, update *model.UpdateInfo) error

var mapTypeHandle = map[string]typeCalValueTime{
	TypeMovie:       MovieValueTimeCal,
	TypeTV:          TVValueTimeCal,
	TypeVariety:     VarietyValueTimeCal,
	TypeDocumentary: DocumentValueTimeCal,
	TypeAnime:       AnimeAndChildrenValueTimeCal,
	TypeChildren:    AnimeAndChildrenValueTimeCal,
}

// CalValueTimeState 计算价值认定时间和状态逻辑函数
func CalValueTimeState(ctx context.Context, valueData *model.ValueData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "CalValueTimeState start! valueData [%+v]", valueData)
	defer func() {
		log.InfoContextf(ctx, "CalValueTimeState end. valueData [%+v]", valueData)
	}()

	// 获取执行处理函数
	typeFunc, err := GetTypeCalValueTime(ctx, valueData.MediaID, valueData.MediaType)
	if err != nil {
		log.Errorf("[%s] GetTypeCalHandle is err:%+v", valueData.MediaID, err)
		return err
	}

	// 执行处理函数
	typeFunc(valueData, update)
	if err != nil {
		return err
	}

	return dealValueData(ctx, valueData, update)
}

func dealValueData(ctx context.Context, valueData *model.ValueData, update *model.UpdateInfo) error {
	if !update.IsUpdate {
		return nil
	}
	err := model.UpdateMediaInfo(ctx, msghub.MediaInfo{Id: valueData.MediaID}, update, parse.CidType)
	if err != nil {
		log.ErrorContextf(ctx, "[%s] cal value time_state UpdateMediaInfo err [%+v]", valueData.MediaID, err)
		return err
	}

	// 写表，异步写
	InsertIpValueData(context.Background(), valueData)

	// 注册ets
	err = RegisterValueETS(context.Background(), valueData, update)
	if err != nil {
		log.ErrorContextf(context.Background(), "[%s] registerValueETS err [%+v]", valueData.MediaID, err)
		return err
	}
	return nil
}

// InsertIpValueData 插入ipValueData
func InsertIpValueData(ctx context.Context, valueData *model.ValueData) {
	go func() {
		premiereTime := valueData.PremiereTime
		valueEndTime := valueData.ValueEndTime
		if premiereTime == "" && valueEndTime == "" {
			return
		}

		layout := "2006-01-02 15:04:05"
		now := time.Now()
		data := &model.IpValueStatusData{
			DataID:      valueData.MediaID,
			ValueStatus: valueData.ValueTimeState,
			CTime:       &now,
			Mtime:       &now,
		}
		if len(premiereTime) == 19 {
			pt, _ := time.ParseInLocation(layout, premiereTime, time.Local)
			data.PremiereTime = &pt
		}

		if len(valueEndTime) == 19 {
			vt, _ := time.ParseInLocation(layout, valueEndTime, time.Local)
			data.ValueEndTime = &vt
		}
		_ = repo.GetIpStatusRepoClient().InsertIpValueData(ctx, data)
	}()
}

// GetTypeCalValueTime 获取执行函数
func GetTypeCalValueTime(ctx context.Context, mediaID, mediaType string) (func(valueData *model.ValueData,
	update *model.UpdateInfo) error, error) {
	typeFunc, ok := mapTypeHandle[mediaType]
	if !ok {
		log.InfoContextf(ctx, "[%s] GetTypeCalValueTime failed,not correct type", mediaID)
		return nil, errs.New(http.StatusInternalServerError, "not correct type")
	}
	return typeFunc, nil
}

// MovieValueTimeCal 电影价值时间和状态计算
func MovieValueTimeCal(valueData *model.ValueData, update *model.UpdateInfo) error {
	valueEndTime := timeAddDay(valueData.PremiereTime, 365)
	valueData.ValueEndTime = valueEndTime

	return CommonValueTimeStateCal(valueData, update)
}

// TVValueTimeCal 电视剧价值时间和状态计算
func TVValueTimeCal(valueData *model.ValueData, update *model.UpdateInfo) error {
	endTime := getEndTime(valueData.EndTime, valueData.TVEndTime)
	// 获取电视剧出品时间
	productTime, err := GetTVProductTime(context.Background(), valueData.MediaID)
	if err != nil {
		return err
	}
	valueData.ProductTime = productTime
	if len(productTime) >= 4 {
		productTime = productTime[:4]
		if i, err := strconv.Atoi(productTime); err == nil {
			if i >= 2024 {
				return CommonValueTimeCal(valueData.PlayEndTime, valueData.PlayEndTime, valueData, update, 365, 365)
			}
		}
	}

	// 价值认定计算
	return CommonValueTimeCal(valueData.FreeEndTime, endTime, valueData, update, 365, 365)
}

// VarietyValueTimeCal 综艺价值时间和状态计算
func VarietyValueTimeCal(valueData *model.ValueData, update *model.UpdateInfo) error {
	valueEndTime := timeAddDay(valueData.LastPubTime, 365)
	valueData.ValueEndTime = valueEndTime
	return CommonValueTimeStateCal(valueData, update)
}

// AnimeAndChildrenValueTimeCal 动漫和少儿价值时间和状态计算
func AnimeAndChildrenValueTimeCal(valueData *model.ValueData, update *model.UpdateInfo) error {
	return CommonValueTimeCal(valueData.FreeEndTime, valueData.PayEndTime, valueData, update, 365, 372)
}

// DocumentValueTimeCal 纪录片价值时间和状态计算
func DocumentValueTimeCal(valueData *model.ValueData, update *model.UpdateInfo) error {
	return CommonValueTimeCal(valueData.FreeEndTime, valueData.LastUpdateDay, valueData, update, 365, 365)
}

// getEndTime 获取完结时间，优先同步剧完结时间（endtime），为空则取完结时间(end_time)
func getEndTime(endTime, TvEndTime string) string {
	if endTime == "" || endTime == "0000-00-00 00:00:00" {
		endTime = TvEndTime
	}
	return fixEndTime(endTime)
}

// CommonValueTimeCal 通用价值认定状态计算
func CommonValueTimeCal(freeEndTime, endTime string, valueData *model.ValueData, update *model.UpdateInfo, dayNum,
	dayNum1 int) error {
	valueEndTime := CommonValueEndTimeCal(freeEndTime, endTime, valueData.MediaValueEndTime, dayNum, dayNum1)
	valueData.ValueEndTime = valueEndTime
	return CommonValueTimeStateCal(valueData, update)
}

// CommonValueEndTimeCal 公共价值认定期结束时间计算
func CommonValueEndTimeCal(freeEndTime, endTime, mediaValueEndTime string, dayNum, dayNum1 int) string {
	if model.CheckTime(freeEndTime) {
		return timeAddDay(freeEndTime, dayNum)
	}
	endTime = fixEndTime(endTime)
	if model.CheckTime(endTime) {
		return timeAddDay(endTime, dayNum1)
	}
	return mediaValueEndTime
}

// CommonValueTimeStateCal 通用价值时间相关计算
func CommonValueTimeStateCal(valueData *model.ValueData, update *model.UpdateInfo) error {
	valueData.ValueTimeState = valueData.MediaValueTimeState
	if valueData.MediaValueEndTime != valueData.ValueEndTime {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldValueEndTime] = []string{valueData.ValueEndTime}
	}
	premiereTime := valueData.PremiereTime
	valueEndTime := valueData.ValueEndTime
	if !model.CheckTime(premiereTime) || !model.CheckTime(valueEndTime) {
		return nil
	}
	pTime, _ := time.ParseInLocation("2006-01-02 15:04:05", premiereTime, time.Local)
	vTime, _ := time.ParseInLocation("2006-01-02 15:04:05", valueEndTime, time.Local)
	state := valueData.MediaValueTimeState
	if time.Now().Before(pTime) {
		state = parse.ValueStateNotStart
	}
	if time.Now().After(pTime) {
		state = parse.ValueStateStarting
	}
	if time.Now().Before(vTime) {
		state = parse.ValueStateStarting
	}
	if time.Now().After(vTime) {
		state = parse.ValueStateEnd
	}
	if valueData.MediaValueTimeState != state {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldValueTimeState] = []string{state}
	}
	valueData.ValueTimeState = state
	return nil
}

// RegisterValueETS 注册ets，到点触发计算
func RegisterValueETS(ctx context.Context, valueData *model.ValueData, update *model.UpdateInfo) error {
	// 对于ets数据,直接更新计算数据
	if HighThanNowTime(valueData.PremiereTime) {
		err := registerValueETSV2(ctx, valueData, getTimeStamp(valueData.PremiereTime))
		if err != nil {
			return err
		}
	}

	if HighThanNowTime(valueData.ValueTimeState) {
		err := registerValueETSV2(ctx, valueData, getTimeStamp(valueData.ValueTimeState))
		if err != nil {
			return err
		}
	}
	return nil
}

func registerValueETS(ctx context.Context, valueData model.ValueData, timestamp int64) (uint64, error) {
	valueData.PushType = model.ETSType
	pBody, _ := json.Marshal(valueData)
	etsProxy := pb.NewEtsApiClientProxy()
	req := &pb.AddJobRequest{
		JobName:   "ip_status_update_value_cal",
		ParamBody: []byte(pBody),
		JobRouteInfo: &pb.JobRouteInfo{
			ServiceType: pb.ServiceType_TrpcHttpPost,
			Target: &pb.JobRouteInfo_ServiceTarget{
				ServiceTarget: &pb.ServiceTarget{
					Target:            config.GetAccCfg().Namespace.Target,
					TargetServiceName: config.GetAccCfg().Namespace.Target,
					TargetRpcName:     "/recal_value_time",
				}},
			TargetNamespace:   config.GetAccCfg().Namespace.Namespace,
			SerializationType: pb.SerializationType_JSON,
			RetryTimes:        parse.RetryTimes,
			Timeout:           parse.TimeOut,
		},
		JobTrigger: &pb.JobTrigger{
			Begin: &pb.JobTrigger_BeginTimestamp{BeginTimestamp: timestamp},
			End:   &pb.JobTrigger_EndTimestamp{EndTimestamp: timestamp + 1},
			Trigger: &pb.JobTrigger_Interval{
				Interval: 1,
			},
		},
		Owner: config.GetAccCfg().Namespace.Owner,
	}
	log.InfoContextf(ctx, "register ets job, req:[%+v]", req)
	rsp, err := etsProxy.AddJob(GetCtxWithMetaData(ctx), req)
	if err != nil {
		return 0, err
	}
	if rsp.Code != 0 {
		return 0, fmt.Errorf("add ets job err, code:%d", rsp.Code)
	}
	return rsp.JobId, nil
}

// registerValueETSV2 使用tjob升级的接口注册ets
func registerValueETSV2(ctx context.Context, valueData *model.ValueData, timestamp int64) error {
	valueData.PushType = model.ETSType
	pBody, _ := json.Marshal(valueData)
	req := &tjob.AddTaskReq{
		TaskName:  "ip_status_update_value_cal",                            // 任务名 必填
		OutBizNo:  "recal_value_time" + "_" + strconv.Itoa(int(timestamp)), // 任务号码,需要业务保证唯一性 必填
		BizCode:   "recal_value_time",                                      // 接入后由tjob组件分配 必填
		ParamBody: pBody,                                                   // 业务请求编码后数据 必填
		TaskType:  tjob.TaskType_SINGLE,                                    // 任务类型 SINGLE 必填
		BeginTime: timestamp,                                               // 任务开始时间戳,单位:秒 TaskType = SINGLE 必填
		EndTime:   timestamp,                                               // 任务结束时间戳 必须等于BeginTime
	}

	log.InfoContextf(ctx, "register ets job, req:[%+v]", req)
	rsp, err := NewTjobProxy().AddTask(GetCtxWithMetaData(ctx), req)
	if err != nil {
		// 错误码10003为重复添加
		if errs.Code(err) == 10003 {
			return nil
		}
		log.ErrorContextf(ctx, "[%+v] AddETSTask failure err: [%+v],rsp [%+v]", valueData.MediaID, err, rsp)
		return err
	}

	log.InfoContextf(ctx, "[%+v] AddETSTask success. req [%+v], jobID:[%s]", valueData.MediaID, req,
		rsp.Task.TaskId)
	return nil
}

// NewTjobProxy 新建tjobclient
func NewTjobProxy() tjob.TJobCoreClientProxy {
	opts := []client.Option{
		client.WithNamespace("Production"),
		client.WithServiceName("trpc.tjobs.tjobs_core.TJobCore"),
		client.WithProtocol("trpc"),
		client.WithNetwork("tcp"),
		client.WithTimeout(time.Duration(3000) * time.Millisecond),
	}
	return tjob.NewTJobCoreClientProxy(opts...)
}
