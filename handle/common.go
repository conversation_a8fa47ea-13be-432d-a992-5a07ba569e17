package handle

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/video_media/ip_status_update/repo"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/component_plat/common_comm"
	pb "git.code.oa.com/trpcprotocol/elastic_task_schedule/ets_api"
	"git.code.oa.com/video_media/ip_status_update/config"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

const (
	NeedCalFreeTimeFlag    = 1
	NotNeedCalFreeTimeFlag = 0
)

// UpdateInfo IP内容相关更新信息
//type UpdateInfo struct {
//	CID string
//
//	MediaUnScheduleTime     string // MediaUnScheduleTime 未定档预热时间
//	MediaScheduleTime       string // MediaScheduleTime 已定档预热时间
//	MediaPrePareTimeText    string // MediaPrePareTimeText 即将上映时间（文本）
//	MediaPrePareTime        string // MediaPrePareTime 即将上映时间（时间）
//	MediaFirstPlayTime      string
//	MediaEndPlayTime        string
//	MediaYuReThreeMonthTime string
//	MediaYuReEndTime        string
//	MediaThreeYearTailTime  string
//
//	PlanFirstTime string // PlanFirstTime 排播首播时间
//	PlanEndTime   string // PlanEndTime 排播完结时间
//
//	Category            string // Category 品类
//	IsTrailer           bool   // IsTrailer 是否预告片，true为预告片，false为非预告片
//	ProductTime         string // ProductTime 电视剧出品时间
//	EndTime             string // EndTime 电视剧完结时间
//	PArea               string // PArea 制片地区
//	NetPublishTime      string // NetPublishTime 腾讯首播时间
//	LastPubTime         string // LastPubTime 最近一期上线时间
//	AnimeUpdateStatus   string // AnimeUpdateStatus 动漫更新状态
//	HotLevel            string // HotLevel 版权采买等级
//	NatureOfCopyrightID string // NatureOfCopyrightID  版权性质
//	VideoCheckupTime    string // VideoCheckupTime 专辑上架时间
//	PublishDate         string // PublishDate 播放日期
//	SportsColumnType    string // SportsColumnType 体育栏目类型
//	SportsMatchID       string // SportsMatchID 比赛ID
//
//	// 这部分存储计算后待更新数据信息
//	UnScheduleTime     string
//	ScheduleTime       string
//	FirstPlayTime      string
//	EndPlayTime        string // EndPlayTime 运营期结束时间
//	YuReThreeMonthTime string
//	YuReEndTime        string
//	ThreeYearTailTime  string
//
//	UpdateFlag bool
//	FieldMap   map[string][]string
//	IsUpdate   bool
//}

func check4Time(t1, t2, t3, t4 string) bool {
	if !model.CheckTime(t1) || !model.CheckTime(t2) ||
		!model.CheckTime(t3) || !model.CheckTime(t4) {
		return false
	}
	return true
}

// TimeAfterTime 判断时间前后
func TimeAfterTime(t1, t2, t3, t4 string) bool {
	if !check4Time(t1, t2, t3, t4) {
		return false
	}
	at1, _ := time.ParseInLocation("2006-01-02 15:04:05", t1, time.Local)
	at2, _ := time.ParseInLocation("2006-01-02 15:04:05", t2, time.Local)
	at3, _ := time.ParseInLocation("2006-01-02 15:04:05", t3, time.Local)
	at4, _ := time.ParseInLocation("2006-01-02 15:04:05", t4, time.Local)
	if !at1.After(at2) {
		return false
	}
	if at1.After(at3) || at1.After(at4) {
		return true
	}
	return false
}

// TimeBefore 判断两个时间的前后,t1是否在t2之前,true为t1在t2之前，false为t1在t2之后
func TimeBefore(t1, t2 string) bool {
	at1, _ := time.ParseInLocation("2006-01-02 15:04:05", t1, time.Local)
	at2, _ := time.ParseInLocation("2006-01-02 15:04:05", t2, time.Local)
	if !at1.After(at2) {
		return true
	}
	return false
}

// MaxTime 获取靠后的时间
func MaxTime(t1, t2 string, mediaTime string) string {
	if t1 == "" && t2 == "" {
		return mediaTime
	}
	if t1 == "" {
		return t2
	}
	if t2 == "" {
		return t1
	}

	if TimeBefore(t1, t2) {
		return t2
	}
	return t1
}

// HighThanNowTime 判断是否大于当前时间
func HighThanNowTime(t string) bool {
	t1, _ := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	return time.Now().Before(t1)
}

func getTimeStamp(t string) int64 {
	t1, _ := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	return t1.Unix()
}

// GetFirstStartTime 获取首播时间
// 2024-02-02 修改，首播时间取付费和免费谁更前
func GetFirstStartTime(planData *model.PlanData) string {
	// 两者都不为空取更早的值
	if planData.PayStartDate != "" && planData.FreeStartDate != "" {
		return getEarlierTime(planData.PayStartDate, planData.FreeStartDate)
	}
	if planData.PayStartDate != "" {
		return planData.PayStartDate
	}
	if planData.FreeStartDate != "" {
		return planData.FreeStartDate
	}
	return planData.StartDate
}

func getEarlierTime(t1, t2 string) string {
	at1, _ := time.ParseInLocation("2006-01-02 15:04:05", t1, time.Local)
	at2, _ := time.ParseInLocation("2006-01-02 15:04:05", t2, time.Local)
	if at1.After(at2) {
		return at2.Format("2006-01-02 15:04:05")
	}
	return at1.Format("2006-01-02 15:04:05")
}

// GetPlanEndTime 获取排播完结时间
func GetPlanEndTime(planData *model.PlanData) string {
	if planData.FreeEndDate != "" {
		return planData.FreeEndDate
	}
	if planData.PayEndDate != "" {
		return planData.PayEndDate
	}
	return planData.EndDate
}

// CommonGetFirstTime 通用获取排播时间的首播时间
// 优先级：即将上映时间（时间） > 排播字段 > 媒资存储字段
func CommonGetFirstTime(mediaFirstPlayTime, firstStartTime string, update *model.UpdateInfo) string {
	var retFirstTime = mediaFirstPlayTime

	// 优先读取【即将上映时间（时间）】字段
	if model.CheckTime(update.MediaPrePareTime) {
		if update.MediaPrePareTime != mediaFirstPlayTime {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldFirstPlayTime] = []string{update.MediaPrePareTime}
		}
		return update.MediaPrePareTime
	}

	// 如果即将上映时间为空，则走现有的计算逻辑
	// 两个字段不同的时候，优先取排播字段，否则直接取媒资存储字段作为首播时间
	if mediaFirstPlayTime != firstStartTime && model.CheckTime(firstStartTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldFirstPlayTime] = []string{firstStartTime}
		retFirstTime = firstStartTime
	}
	return retFirstTime
}

// RegisterETSCallback 注册ETS回调任务，支持自定义参数和目标RPC接口
func RegisterETSCallback(ctx context.Context, paramBody []byte, targetAPIName string, timestamp int64) (uint64, error) {
	etsProxy := pb.NewEtsApiClientProxy()
	req := &pb.AddJobRequest{
		JobName:   "ip_status_update",
		ParamBody: paramBody,
		JobRouteInfo: &pb.JobRouteInfo{
			ServiceType: pb.ServiceType_TrpcHttpPost,
			Target: &pb.JobRouteInfo_ServiceTarget{
				ServiceTarget: &pb.ServiceTarget{
					Target:            config.GetAccCfg().Namespace.Target,
					TargetServiceName: config.GetAccCfg().Namespace.Target,
					TargetRpcName:     targetAPIName,
				}},
			TargetNamespace:   config.GetAccCfg().Namespace.Namespace,
			SerializationType: pb.SerializationType_JSON,
			RetryTimes:        parse.RetryTimes,
			Timeout:           parse.TimeOut,
		},
		JobTrigger: &pb.JobTrigger{
			Begin: &pb.JobTrigger_BeginTimestamp{BeginTimestamp: timestamp},
			End:   &pb.JobTrigger_EndTimestamp{EndTimestamp: timestamp + 1},
			Trigger: &pb.JobTrigger_Interval{
				Interval: 1,
			},
		},
		Owner: config.GetAccCfg().Namespace.Owner,
	}
	log.InfoContextf(ctx, "register ets job, req:[%+v]", req)
	rsp, err := etsProxy.AddJob(GetCtxWithMetaData(ctx), req)
	if err != nil {
		return 0, err
	}
	if rsp.Code != 0 {
		return 0, fmt.Errorf("add ets job err, code:%d", rsp.Code)
	}
	return rsp.JobId, nil
}

// GetCtxWithMetaData context存储信息
func GetCtxWithMetaData(ctx context.Context) context.Context {
	reqHead := &common_comm.ComponentReqHead{
		AppInfo: &common_comm.AppInfo{
			Appid:  config.GetAccCfg().Namespace.AppID,
			Appkey: config.GetAccCfg().Namespace.AppKey,
		},
	}
	log.Debugf("req Head is %v ", reqHead)
	reqHeadBytes, err := codec.Marshal(0, reqHead)
	if err != nil {
		log.Error(err)
		return nil
	}
	newCtx, msg := codec.WithNewMessage(ctx)
	md := codec.MetaData{
		"component_req_head": reqHeadBytes,
	}
	msg.WithServerMetaData(md)
	return newCtx
}

func timeAddDay(t string, dayNum int) string {
	if len(t) != parse.TimeLen {
		return ""
	}
	t1, _ := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	t2 := t1.AddDate(0, 0, dayNum)
	return t2.Format("2006-01-02 15:04:05")
}

// GetThreeMonthEndTime 获取三个月余热期结束时间
// 2023-02-28变更，三个月余热期结束时间由全品类：运营期结束时间+90天变更为
// 综艺、电视剧、音乐、游戏品类：运营期结束时间+120天
// 动漫、纪录片、电影、少儿、知识付费：运营期结束时间+210天
// 其他品类（维持不变）：运营期结束时间+90天
func GetThreeMonthEndTime(endTime string, mediaTime string) string {
	if model.CheckTime(endTime) {
		return timeAddDay(endTime, parse.MonthDays*3)
	}
	return mediaTime
}

// GetFourMonthEndTime 获取四个月余热期结束时间
func GetFourMonthEndTime(endTime string, mediaTime string) string {
	if model.CheckTime(endTime) {
		return timeAddDay(endTime, parse.MonthDays*4)
	}
	return mediaTime
}

// GetSevenMonthEndTime 获取七个月余热期结束时间
func GetSevenMonthEndTime(endTime string, mediaTime string) string {
	if model.CheckTime(endTime) {
		return timeAddDay(endTime, parse.MonthDays*7)
	}
	return mediaTime
}

// GetYuReEndTime 获取余热期结束时间
func GetYuReEndTime(t string, mediaTime string) string {
	if model.CheckTime(t) {
		return timeAddDay(t, parse.YearDays)
	}
	return mediaTime
}

// GetThreeYearTailTime 获取三年长尾期结束时间
func GetThreeYearTailTime(t string, mediaTime string) string {
	if model.CheckTime(t) {
		return timeAddDay(t, 1095)
	}
	return mediaTime
}

// CheckTimeText 审核即将上映时间（文本）字段
func CheckTimeText(t string) bool {
	if t != "" {
		return true
	}
	return false
}

// CommonTimeCal 公共时间计算函数
func CommonTimeCal(ctx context.Context, planData *model.PlanData, mediaTime, nowTime, endTime,
	fieldTime, valueStatus string, isUpdateStatus bool) (string, model.UpdateInfo, error) {
	log.InfoContextf(ctx, "CommonTimeCal mediaStoreTime:[%s], nowCalTime:[%s], endTime:[%s], fieldTime:[%s], "+
		"valueStatus:[%s], planType:[%+v]", mediaTime, nowTime, endTime, fieldTime, valueStatus, planData)
	var update model.UpdateInfo
	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)
	if planData.PushType != model.ETSType && mediaTime == nowTime {
		if HighThanNowTime(mediaTime) {
			return nowTime, update, nil
		} else if isUpdateStatus {
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{valueStatus}
			update.UpdateFlag = true
		}
	}

	err := RegisterETS(ctx, planData, mediaTime, nowTime, fieldTime, valueStatus, &update, isUpdateStatus)
	if err != nil {
		return "", update, err
	}

	if planData.PushType == model.ETSType && mediaTime == nowTime {
		if !HighThanNowTime(mediaTime) && isUpdateStatus {
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{valueStatus}
			update.UpdateFlag = true
		}
	}

	return nowTime, update, nil
}

// RegisterETS 如果排播时间和媒资DB保存时间不一致，则更新媒资首播时间，如果高于当前时间，注册ETS，如果低于或者等于当前时间，直接更新状态
func RegisterETS(ctx context.Context, planData *model.PlanData, mediaTime, nowTime, fieldTime, valueStatus string,
	update *model.UpdateInfo, isUpdateStatus bool) error {
	// 如果排播时间和媒资DB保存时间不一致，则更新媒资首播时间，如果高于当前时间，注册ETS，如果低于或者等于当前时间，则直接修改状态为运营期，更新首播时间
	if planData.PushType != model.ETSType && mediaTime != nowTime {
		update.FieldMap[fieldTime] = []string{nowTime}
		update.UpdateFlag = true
		if HighThanNowTime(nowTime) {
			planDataCopy := *planData
			planDataCopy.PushType = model.ETSType
			pBody, _ := json.Marshal(planDataCopy)
			_, err := RegisterETSCallback(ctx, pBody, "/ip_rel_time_cal", getTimeStamp(nowTime))
			if err != nil {
				log.ErrorContextf(ctx, "register ets job err [%+v]", err)
				return nil
			}
		} else if isUpdateStatus {
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{valueStatus}
		}
	}
	return nil
}

// UnionUpdateInfo 合并更新请求信息
func UnionUpdateInfo(update *model.UpdateInfo, tmpUpdateInfo model.UpdateInfo) {
	if !tmpUpdateInfo.UpdateFlag {
		return
	}
	if tmpUpdateInfo.UpdateFlag {
		update.UpdateFlag = true
	}
	for field, info := range tmpUpdateInfo.FieldMap {
		update.FieldMap[field] = info
	}
}

// CommonChangWeiCal 长尾计算
func CommonChangWeiCal(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	playEndTime string, update *model.UpdateInfo) (string, error) {
	log.WarnContextf(ctx, "CommonChangWeiCal start")
	defer log.WarnContextf(ctx, "CommonChangWeiCal end")
	log.InfoContextf(ctx, "CommonChangWeiCal cal, playEndTime:[%s]", playEndTime)
	if !model.CheckTime(playEndTime) {
		log.WarnContextf(ctx, "CommonChangWeiCal playEndTime:[%s] not illegal", playEndTime)
		return "", nil
	}
	mediaEndTime := parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	yuReEndTime := GetYuReEndTime(playEndTime, mediaEndTime)
	changWeiThreeEndTime := timeAddDay(yuReEndTime, 1095)
	retTime, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, mediaEndTime, yuReEndTime, changWeiThreeEndTime,
		parse.FieldAfterHeatEndTime, parse.ValueChangWeiOperation, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return retTime, err
}

// CommonChangWeiCalV2 长尾计算
func CommonChangWeiCalV2(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo, isUpdate bool) error {
	yuReEndTime := update.YuReEndTime
	threeYearTailTime := GetThreeYearTailTime(yuReEndTime, update.MediaThreeYearTailTime)
	update.ThreeYearTailTime = threeYearTailTime
	log.WarnContextf(ctx, "CommonChangWeiCal start,yuReEndTime:[%s]", yuReEndTime)
	defer log.WarnContextf(ctx, "CommonChangWeiCal end")
	if !model.CheckTime(yuReEndTime) {
		log.WarnContextf(ctx, "CommonChangWeiCal yuReEndTime:[%s] not illegal", yuReEndTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaYuReEndTime, yuReEndTime,
		threeYearTailTime, parse.FieldAfterHeatEndTime, parse.ValueChangWeiOperation, isUpdate)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// CalEndTime 计算相关完结时间
func CalEndTime(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo, firstTime string,
	update *model.UpdateInfo, calFlag int) error {
	update.MediaEndTimeForVIP = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForVIP)
	update.MediaEndTimeForFree = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForFree)
	log.InfoContextf(ctx, "CalEndTime: mediaEndTimeVip:%s, mediaEndTimeFree:%s, firstTime:%s, FreeStartDate:%s, "+
		"calFlag:%d", update.MediaEndTimeForVIP, update.MediaEndTimeForFree, firstTime, planData.FreeStartDate, calFlag)
	if update.MediaEndTimeForVIP != firstTime {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldEndTimeForVIP] = []string{firstTime}
	}

	if calFlag == NeedCalFreeTimeFlag && update.MediaEndTimeForFree != planData.FreeStartDate {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldEndTimeForFree] = []string{planData.FreeStartDate}
	}
	return nil
}

// CalOtherSomeDate 通用的计算媒资和排播时间合并函数
func CalOtherSomeDate(mediaTime, planData string, field string, update *model.UpdateInfo) {
	if mediaTime != planData {
		update.UpdateFlag = true
		update.FieldMap[field] = []string{planData}
	}
}

// CalOtherSomeDateWithFallback 通用的计算媒资和排播时间合并函数，支持last_pubtime兜底
func CalOtherSomeDateWithFallback(mediaTime, planData, lastPubTime string, field string, update *model.UpdateInfo) {
	var finalTime string

	// 先走现有逻辑
	if planData != "" {
		finalTime = planData
	} else if mediaTime != "" {
		finalTime = mediaTime
	} else {
		// 如果计算为空，则取【最后一期上线日期】作为兜底
		finalTime = FixLastPubTime(lastPubTime)
	}

	// 如果最终计算的时间与媒资存储的时间不同，则更新
	if mediaTime != finalTime && finalTime != "" {
		update.UpdateFlag = true
		update.FieldMap[field] = []string{finalTime}
	}
}

// IsPositiveCategory 通用的正片品类判断函数
func IsPositiveCategory(category string) bool {
	// 动漫正片
	if category == "10994" {
		return true
	}

	// 电影正片
	if _, ok := parse.MoviePositiveMap[category]; ok {
		return true
	}

	// 电视剧正片
	if _, ok := parse.TVPositiveMap[category]; ok {
		return true
	}

	// 综艺正片
	if _, ok := parse.VarietyPositiveMap[category]; ok {
		return true
	}

	// 纪录片正片
	if _, ok := parse.DocumentaryPositiveMap[category]; ok {
		return true
	}

	// 少儿正片
	if _, ok := parse.ChildrenPositiveMap[category]; ok {
		return true
	}

	return false
}

// HandleCategoryChangeToPositiveEvent 处理修改为正片的事件（通用函数）
func HandleCategoryChangeToPositiveEvent(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "HandleCategoryChangeToPositiveEvent start")
	defer log.InfoContextf(ctx, "HandleCategoryChangeToPositiveEvent end")

	// 检查是否有分类字段的变更
	modifyInfo, exists := mediaInfo.ModifyFieldInfos[parse.FieldCategory]
	if !exists {
		return nil
	}

	// 检查新值是否为正片，且旧值非空
	newCategory := modifyInfo.New
	oldCategory := modifyInfo.Old
	if !IsPositiveCategory(newCategory) || oldCategory == "" {
		return nil
	}

	log.InfoContextf(ctx, "detected category change to positive: old[%s] -> new[%s]", oldCategory, newCategory)

	// 获取触发时间（事件发生时间）
	triggerTime := time.Unix(int64(mediaInfo.TimeStamp), 0).Format("2006-01-02 15:04:05")

	// 获取首播时间
	firstPlayTime := CommonGetFirstTime(update.MediaFirstPlayTime, update.PlanFirstTime, update)
	if !model.CheckTime(firstPlayTime) {
		log.WarnContextf(ctx, "first play time is invalid: %s", firstPlayTime)
		// 即使首播时间无效，也要检查是否有其他字段需要更新
		if update.UpdateFlag {
			return CommonDealIpOnlineData(ctx, mediaInfo, update)
		}
		return nil
	}

	log.InfoContextf(ctx, "trigger time: %s, first play time: %s", triggerTime, firstPlayTime)

	// 判断是否满足条件设置为运营期
	if shouldSetToOperatingPeriod(triggerTime, firstPlayTime) {
		log.InfoContextf(ctx, "conditions met, setting to operating period")
		// 设置为运营期状态
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueOperatingPeriod}
	}

	// 无论是否满足运营期条件，只要有字段更新就调用CommonDealIpOnlineData
	if update.UpdateFlag {
		return CommonDealIpOnlineData(ctx, mediaInfo, update)
	}
	return nil
}

// shouldSetToOperatingPeriod 判断是否应该设置为运营期
func shouldSetToOperatingPeriod(triggerTime, firstPlayTime string) bool {
	triggerT, err := time.ParseInLocation("2006-01-02 15:04:05", triggerTime, time.Local)
	if err != nil {
		return false
	}

	firstT, err := time.ParseInLocation("2006-01-02 15:04:05", firstPlayTime, time.Local)
	if err != nil {
		return false
	}

	// 触发时间大于等于首播时间的当天（只需要年月日大于等于，不需要时分秒）
	// 首播时间当天向前推1小时作为边界
	firstPlayDay := time.Date(firstT.Year(), firstT.Month(), firstT.Day(), 0, 0, 0, 0, time.Local)
	boundary := firstPlayDay.Add(-time.Hour) // 前一天23:00:00

	if triggerT.After(boundary) || triggerT.Equal(boundary) {
		return true
	}
	return false
}

// CalOtherTime 计算其他相关时间
func CalOtherTime(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	mediaLastUpdateTime := parse.GetFieldValue(mediaInfo, parse.FieldLastUpdateTime)
	mediaNextUpdateTime := parse.GetFieldValue(mediaInfo, parse.FieldNextUpdateTime)
	update.MediaEndTimeForVIP = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForVIP)
	update.MediaEndTimeForFree = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForFree)

	// 获取最后一期上线日期，用于兜底逻辑
	lastPubTime := parse.GetFieldValue(mediaInfo, parse.FieldCidLastPubtime)

	// 会员结束时间和免费结束时间增加last_pubtime兜底逻辑
	CalOtherSomeDateWithFallback(update.MediaEndTimeForVIP, planData.PayEndDate, lastPubTime,
		parse.FieldEndTimeForVIP, update)
	CalOtherSomeDateWithFallback(update.MediaEndTimeForFree, planData.FreeEndDate, lastPubTime,
		parse.FieldEndTimeForFree, update)
	CalOtherSomeDate(mediaLastUpdateTime, planData.LastUpdate, parse.FieldLastUpdateTime, update)

	// nextTime比较特殊，我们需要注册ETS，我们需要扭转时间，并且对新时间注册定时器
	if mediaNextUpdateTime != planData.NextUpdate {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldNextUpdateTime] = []string{planData.NextUpdate}
		if HighThanNowTime(planData.NextUpdate) {
			planDataCopy := *planData
			planDataCopy.PushType = model.ETSType
			pBody, _ := json.Marshal(planDataCopy)
			_, err := RegisterETSCallback(ctx, pBody, "/ip_rel_time_cal", getTimeStamp(planData.NextUpdate)+2)
			if err != nil {
				log.ErrorContextf(ctx, "register ets job err [%+v]", err)
				return nil
			}
		}
	}
	return nil
}

// CommonThreeMonthYuReCal 公共计算三个月余热期计算时间
func CommonThreeMonthYuReCal(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	yuReEndTime, threeEndTime string, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "CommonThreeMonthYuReCal info start")
	defer log.InfoContextf(ctx, "CommonThreeMonthYuReCal info end")
	if !model.CheckTime(threeEndTime) || !model.CheckTime(yuReEndTime) {
		log.WarnContextf(ctx, "CommonThreeMonthYuReCal threeEndTime:[%s], yuReEndTime:[%s] not illegal!",
			threeEndTime, yuReEndTime)
		return nil
	}
	mediaThreeEndTime := parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	log.InfoContextf(ctx, "CommonThreeMonthYuReCal mediaEndTime:[%s], threeEndTime:[%s], yuReEndTime:[%s]",
		mediaThreeEndTime, threeEndTime, yuReEndTime)
	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, mediaThreeEndTime, threeEndTime, yuReEndTime,
		parse.FieldThreeMonthEndAfterHeatTime, parse.ValueYuReMonthOperation, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// CommonThreeMonthYuReCalV2 公共计算三个月余热期计算时间
func CommonThreeMonthYuReCalV2(ctx context.Context, planData *model.PlanData,
	yuReEndTime, threeEndTime string, update *model.UpdateInfo, isUpdate bool) error {
	log.InfoContextf(ctx, "CommonThreeMonthYuReCal info start")
	defer log.InfoContextf(ctx, "CommonThreeMonthYuReCal info end")
	if !model.CheckTime(threeEndTime) || !model.CheckTime(yuReEndTime) {
		log.WarnContextf(ctx, "CommonThreeMonthYuReCal threeEndTime:[%s], yuReEndTime:[%s] not illegal!",
			threeEndTime, yuReEndTime)
		return nil
	}
	log.InfoContextf(ctx, "CommonThreeMonthYuReCal mediaEndTime:[%s], threeEndTime:[%s], yuReEndTime:[%s]",
		update.MediaYuReThreeMonthTime, threeEndTime, yuReEndTime)
	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaYuReThreeMonthTime, threeEndTime, yuReEndTime,
		parse.FieldThreeMonthEndAfterHeatTime, parse.ValueYuReMonthOperation, isUpdate)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// CommonThreeChangWeiEndCal 公共计算三年长尾期结束时间
func CommonThreeChangWeiEndCal(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	yuReEndTime string, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "CommonThreeChangWeiEndCal info start")
	defer log.InfoContextf(ctx, "CommonThreeChangWeiEndCal info end")
	if !model.CheckTime(yuReEndTime) {
		log.WarnContextf(ctx, "CommonThreeChangWeiEndCal endTime:[%s] not illegal!", yuReEndTime)
		return nil
	}
	mediaThreeEndTime := parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	changWeiThreeEndTime := timeAddDay(yuReEndTime, 1095)

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, mediaThreeEndTime, changWeiThreeEndTime,
		time.Now().AddDate(0, 0, 1).Format("2006-01-02 15:04:05"),
		parse.FieldThreeYearTailTime, parse.ValueChangWeiYearOperation, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// CommonThreeChangWeiEndCalV2 公共计算三年长尾期结束时间
func CommonThreeChangWeiEndCalV2(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo,
	isUpdate bool) error {
	log.InfoContextf(ctx, "CommonThreeChangWeiEndCal info start")
	defer log.InfoContextf(ctx, "CommonThreeChangWeiEndCal info end")
	if !model.CheckTime(update.YuReEndTime) {
		log.WarnContextf(ctx, "CommonThreeChangWeiEndCal endTime:[%s] not illegal!", update.YuReEndTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaThreeYearTailTime, update.ThreeYearTailTime,
		time.Now().AddDate(0, 0, 1).Format("2006-01-02 15:04:05"),
		parse.FieldThreeYearTailTime, parse.ValueChangWeiYearOperation, isUpdate)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// CommonOtherCal 公共其他时间计算
func CommonOtherCal(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo, update *model.UpdateInfo,
	yuReTime string) error {
	// 三个月余热期结束时间计算
	err := CommonThreeMonthYuReCal(ctx, planData, mediaInfo, GetYuReEndTime(update.EndPlayTime, update.MediaYuReEndTime),
		yuReTime, update)
	if err != nil {
		return err
	}
	// 长尾期处理
	yuReEndTime, err := CommonChangWeiCal(ctx, planData, mediaInfo, update.EndPlayTime, update)
	if err != nil {
		return err
	}
	// 长尾期结束时间计算
	err = CommonThreeChangWeiEndCal(ctx, planData, mediaInfo, yuReEndTime, update)
	if err != nil {
		return err
	}
	return nil
}

// CommonYuReOtherCalV2 公共余热相关时间计算
func CommonYuReOtherCalV2(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo, isUpdate bool) error {
	// 片库-余热期三月外处理
	update.YuReEndTime = GetYuReEndTime(update.EndPlayTime, update.MediaYuReEndTime)
	err := CommonThreeMonthYuReCalV2(ctx, planData, update.YuReEndTime, update.YuReThreeMonthTime, update, isUpdate)
	if err != nil {
		return err
	}
	// 长尾期处理
	err = CommonChangWeiCalV2(ctx, planData, update, isUpdate)
	if err != nil {
		return err
	}
	// 长尾期结束时间计算
	err = CommonThreeChangWeiEndCalV2(ctx, planData, update, isUpdate)
	if err != nil {
		return err
	}
	return nil
}

// GetMediaOrTime 获取媒资或首次时间处理
func GetMediaOrTime(mediaTime, time string) string {
	if time == "" {
		return mediaTime
	}
	return time
}

func commScheduleTime(scheduleTime, preHeatTime string, update *model.UpdateInfo) {
	if scheduleTime != preHeatTime {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPreHeatTime] = []string{scheduleTime}
	}
}

func checkUnScheduleStatus(mediaInfo msghub.MediaInfo) bool {
	status := parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	if status == "" || status == parse.ValueUnSchedule {
		return true
	}
	return false
}

func checkScheduleStatus(mediaInfo msghub.MediaInfo) bool {
	status := parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	if status == parse.ValueSchedule {
		return true
	}
	/*if status == "" || status == parse.ValueUnSchedule || status == parse.ValueSchedule {
		return true
	}*/
	return false
}

func checkOldPreValue(newVal, oldVal string) bool {
	if newVal != "" && oldVal != "" {
		return false
	}
	return true
}

// unScheduleTimeHandle 未定档时间处理
func unScheduleTimeHandle(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) (string, bool) {
	newPrePareTimeText, fieldFlag := parse.GetModifyValue(mediaInfo, parse.FieldPrePareTimeText)
	oldPareTimeText := parse.GetModifyOldValue(mediaInfo, parse.FieldPrePareTimeText)
	// 未定档时间只记录字段从空值变为任何值的流水时间
	if newPrePareTimeText == "" && fieldFlag {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPreHeatTimeUn] = []string{}
		if checkUnScheduleStatus(mediaInfo) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{}
			return "", false
		}
	}
	// hollywood_prepare_time 由空变为有值
	if CheckTimeText(newPrePareTimeText) && checkOldPreValue(newPrePareTimeText, oldPareTimeText) {
		curTime := time.Unix(int64(mediaInfo.TimeStamp), 0).Format("2006-01-02 15:04:05")
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPreHeatTimeUn] = []string{curTime}
		return curTime, true
	}
	return "", true
}

// unScheduleTimeHandleV2 未定档时间处理（无状态版本）
func unScheduleTimeHandleV2(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) bool {
	newPrePareTimeText, fieldFlag := parse.GetModifyValue(mediaInfo, parse.FieldPrePareTimeText)
	oldPareTimeText := parse.GetModifyOldValue(mediaInfo, parse.FieldPrePareTimeText)

	update.UnScheduleTime = update.MediaUnScheduleTime
	// 只记录字段从空值变为任何值的流水时间
	if newPrePareTimeText == "" && fieldFlag {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPreHeatTimeUn] = []string{}
	}
	// hollywood_prepare_time 由空变为有值
	if CheckTimeText(newPrePareTimeText) && checkOldPreValue(newPrePareTimeText, oldPareTimeText) {
		curTime := time.Unix(int64(mediaInfo.TimeStamp), 0).Format("2006-01-02 15:04:05")
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPreHeatTimeUn] = []string{curTime}
		update.UnScheduleTime = curTime
	}

	nowPrePareTimeText := update.MediaPrePareTimeText
	if nowPrePareTimeText == "" && checkUnScheduleStatus(mediaInfo) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{}
	}
	return nowPrePareTimeText != ""
}

// scheduleTimeHandle 定档时间计算处理
func scheduleTimeHandle(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) (string, bool) {
	newPrePareTime, fieldFlag := parse.GetModifyValue(mediaInfo, parse.FieldPrePareTimeTime)
	oldPareTime := parse.GetModifyOldValue(mediaInfo, parse.FieldPrePareTimeTime)
	if newPrePareTime == "" && fieldFlag {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPreHeatTime] = []string{}
		if checkScheduleStatus(mediaInfo) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
			return "", false
		}
	}
	if model.CheckTime(newPrePareTime) && checkOldPreValue(newPrePareTime, oldPareTime) {
		curTime := time.Unix(int64(mediaInfo.TimeStamp), 0).Format("2006-01-02 15:04:05")
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPreHeatTime] = []string{curTime}
		return curTime, true
	}
	return "", true
}

// scheduleTimeHandleV2 定档时间计算处理
func scheduleTimeHandleV2(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) bool {
	newPrePareTime, fieldFlag := parse.GetModifyValue(mediaInfo, parse.FieldPrePareTimeTime)
	oldPareTime := parse.GetModifyOldValue(mediaInfo, parse.FieldPrePareTimeTime)

	update.ScheduleTime = update.MediaScheduleTime
	if newPrePareTime == "" && fieldFlag {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPreHeatTime] = []string{}
	}
	if model.CheckTime(newPrePareTime) && checkOldPreValue(newPrePareTime, oldPareTime) {
		curTime := time.Unix(int64(mediaInfo.TimeStamp), 0).Format("2006-01-02 15:04:05")
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPreHeatTime] = []string{curTime}
		update.ScheduleTime = curTime
	}

	nowPrePareTime := update.MediaPrePareTime
	if nowPrePareTime == "" && checkScheduleStatus(mediaInfo) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
		if parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText) == "" {
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{}
		}
	}
	return nowPrePareTime != ""
}

// CommonCheckSegmentTime 通用运营期切断时间的规范检查
func CommonCheckSegmentTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) bool {
	_, premiereTimeFlag := parse.GetModifyValue(mediaInfo, parse.FieldFirstPlayTime)
	_, endTimeFlag := parse.GetModifyValue(mediaInfo, parse.FieldFirstPlayEndTime)
	// 首播时间、运营期结束时间没有变更不进行计算
	if !premiereTimeFlag && !endTimeFlag {
		return false
	}
	firstPlayTime := parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	operationEndTime := parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	// 首播时间、运营期结束时间不符合规范不进行计算
	if !model.CheckTime(firstPlayTime) || !model.CheckTime(operationEndTime) {
		return false
	}
	update.FirstPlayTime = firstPlayTime
	update.EndPlayTime = operationEndTime
	return true
}

// CommonSegmentTimeCal 通用运营期切断时间的计算处理
func CommonSegmentTimeCal(startTime, endTime string, dayNum int, res *[]string) {
	segmentEndTime := timeAddDay(startTime, dayNum)
	if TimeBefore(endTime, segmentEndTime) {
		*res = append(*res, fmt.Sprintf("%s~%s", startTime, endTime))
		return
	}
	*res = append(*res, fmt.Sprintf("%s~%s", startTime, segmentEndTime))
	CommonSegmentTimeCal(timeAddDay(startTime, dayNum+1), endTime, dayNum, res)
}

// processIPOnlineStatus 处理IP在线状态的公共逻辑
func processIPOnlineStatus(update *model.UpdateInfo) {
	update.IPOnlineStatus = update.MediaIPOnlineStatus
	if state, ok := update.FieldMap["ip_online_status"]; ok {
		if len(state) > 0 {
			update.IPOnlineStatus = state[0]
		}
	}
}

// CommonDealIpOnlineData 通用处理ip实时状态
func CommonDealIpOnlineData(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	// 处理IP状态的公共逻辑
	processIPOnlineStatus(update)

	err := model.UpdateMediaInfo(ctx, mediaInfo, update, parse.CidType)
	if err != nil {
		log.ErrorContextf(ctx, "[%s] cal ip_online_status UpdateMediaInfo err [%+v]", mediaInfo.Id, err)
		return err
	}

	// 写表，异步写
	InsertIpOnlineStatusData(context.Background(), mediaInfo.Id, update)
	return nil
}

// CommonDealShortDramaData 专门处理短剧数据更新（IP实时状态+相关时间）
func CommonDealShortDramaData(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	dataID := mediaInfo.Id
	// 使用短剧专用的UpdateMediaInfo更新所有字段（包括IP实时状态和时间字段）
	err := model.UpdateMediaInfo(ctx, mediaInfo, update, parse.ShortDramaType)
	if err != nil {
		log.ErrorContextf(ctx, "[%s] CommonDealShortDramaData UpdateMediaInfo err [%+v]", dataID, err)
		return err
	}

	log.InfoContextf(ctx, "[%s] CommonDealShortDramaData success", dataID)
	return nil
}

// InsertIpOnlineStatusData 插入ip实时状态数据
func InsertIpOnlineStatusData(ctx context.Context, dataID string, update *model.UpdateInfo) {
	go func() {
		layout := "2006-01-02 15:04:05"
		now := time.Now()
		data := &model.IpOnlineStatusData{
			DataID:         dataID,
			IpOnlineStatus: update.IPOnlineStatus,
			CTime:          &now,
			Mtime:          &now,
		}
		if len(update.FirstPlayTime) == 19 {
			pt, _ := time.ParseInLocation(layout, update.FirstPlayTime, time.Local)
			data.PremiereTime = &pt
		}
		if len(update.EndPlayTime) == 19 {
			pt, _ := time.ParseInLocation(layout, update.EndPlayTime, time.Local)
			data.OperationEndTime = &pt
		}
		if len(update.YuReThreeMonthTime) == 19 {
			pt, _ := time.ParseInLocation(layout, update.YuReThreeMonthTime, time.Local)
			data.AfterheatTimeMoretime = &pt
		}
		if len(update.YuReEndTime) == 19 {
			pt, _ := time.ParseInLocation(layout, update.YuReEndTime, time.Local)
			data.AfterheatTime = &pt
		}
		if len(update.ThreeYearTailTime) == 19 {
			pt, _ := time.ParseInLocation(layout, update.ThreeYearTailTime, time.Local)
			data.LongtailTimeMoretime = &pt
		}

		_ = repo.GetIpStatusRepoClient().InsertIpOnlineStatusData(ctx, data)
	}()
}

// IsSinglePointPay 是否单片付费，true为是，false为否
func IsSinglePointPay(payStatus string) bool {
	return payStatus == parse.ValueSinglePay || payStatus == parse.ValueSinglePayPlus
}
