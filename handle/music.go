package handle

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func typeMusicHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillMusicUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeMusicHandle end. update [%+v]", update)
	}()

	// 预热期，未定档更新
	err = MusicUnScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 已定档处理计算
	err = MusicScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	err = MusicFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}
	// 余热期计算
	err = MusicYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}

	return MusicOtherCalAndUpdate(ctx, planData, mediaInfo, update)
}

// FillMusicUpdateInfo 填充音乐更新信息，获取音乐ip计算的必要字段
func FillMusicUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData,
	update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.EndTime = getEndTime(parse.GetFieldValue(mediaInfo, parse.FieldEndTime),
		parse.GetFieldValue(mediaInfo, parse.FieldTVEndTime))
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.MediaEndTimeForVIP = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForVIP)
	update.MediaEndTimeForFree = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForFree)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)
	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	update.IsTrailer = false

	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// MusicOtherCalAndUpdate 音乐其他计算和更新
func MusicOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := CommonYuReOtherCalV2(ctx, planData, update, !update.IsTrailer)
	if err != nil {
		return err
	}

	CalEndTime(ctx, planData, mediaInfo, update.FirstPlayTime, update, NotNeedCalFreeTimeFlag)
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

func musicSchedule(firstPlayTime string, update *model.UpdateInfo) {
	if firstPlayTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	} else if model.CheckTime(firstPlayTime) {
		if HighThanNowTime(firstPlayTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}
}

// MusicScheduleCal 音乐已定档计算
func MusicScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "MusicScheduleCal info start")
	defer log.InfoContextf(ctx, "MusicScheduleCal info end")

	// 获取首播时间
	retFirstTime := CommonGetFirstTime(update.MediaFirstPlayTime, update.PlanFirstTime, update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	flag := scheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTime [%+v]", update.MediaPrePareTime)

	if retFirstTime == "" || HighThanNowTime(retFirstTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	}

	return nil
}

// MusicUnScheduleCal 音乐未定档计算
func MusicUnScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "DocumentaryUnScheduleCal info start")
	defer log.InfoContextf(ctx, "DocumentaryUnScheduleCal info end")

	// 提取流水时间,判断是否需要计算
	flag := unScheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTimeText [%+v]", update.MediaPrePareTimeText)

	if update.MediaPrePareTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
	}

	return nil
}

// MusicYuReCal 音乐余热计算
func MusicYuReCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "MusicYuReCal info start")
	defer log.InfoContextf(ctx, "MusicYuReCal info end")

	yuReThreeEndTime := GetFourMonthEndTime(update.EndPlayTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeEndTime

	if !model.CheckTime(update.EndPlayTime) {
		log.WarnContextf(ctx, "MusicYuReCal playEndTime:[%s] not illegal", update.EndPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, update.EndPlayTime,
		yuReThreeEndTime, parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// MusicFirstTimeCal 音乐首播时间计算
func MusicFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, " MusicFirstTimeCal info start")
	defer log.InfoContextf(ctx, "MusicFirstTimeCal info end")

	endPlayTime := GetMusicPlayEndTime(update.FirstPlayTime, update.MediaEndPlayTime)
	update.EndPlayTime = endPlayTime

	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "Music firstPlayTime:[%s] not illegal", update.FirstPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime,
		endPlayTime, parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// GetMusicPlayEndTime 获取音乐首播结束时间
func GetMusicPlayEndTime(firstTime string, mediaPlanEndTime string) string {
	if model.CheckTime(firstTime) {
		return timeAddDay(firstTime, 30)
	}
	return mediaPlanEndTime
}
