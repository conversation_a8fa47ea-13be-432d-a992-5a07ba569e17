package handle

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func typeDocumentaryHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillDocumentaryUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeChildrenHandle End. update [%+v]", update)
	}()
	// 预热期，未定档更新
	err = DocumentaryUnScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 已定档处理计算
	err = DocumentaryScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	err = DocumentaryFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}
	// 余热期计算
	err = DocumentaryYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}

	// 执行原先的主要计算逻辑
	err = DocumentaryOtherCalAndUpdate(ctx, planData, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "DocumentaryOtherCalAndUpdate End. update [%+v], err [%+v]", update, err)
		return err
	}

	// "修改为正片"的事件处理（放在主逻辑后面，避免影响其他计算逻辑）
	err = HandleCategoryChangeToPositiveEvent(ctx, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "HandleCategoryChangeToPositiveEvent End. update [%+v], err [%+v]", update, err)
		return err
	}
	return nil
}

// FillDocumentaryUpdateInfo 填充纪录片更新信息，获取纪录片计算的必要字段
func FillDocumentaryUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData,
	update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)

	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	// 获取排播完结时间
	update.PlanEndTime = GetPlanEndTime(planData)

	update.IsTrailer = isDocumentaryTrailer(update.Category)
	update.IsPositive = isDocumentaryPositive(update.Category)

	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// DocumentaryOtherCalAndUpdate 电影其他计算和更新
func DocumentaryOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := CommonYuReOtherCalV2(ctx, planData, update, update.IsPositive)
	if err != nil {
		return err
	}
	// 其他四个相关时间计算
	err = CalOtherTime(ctx, planData, mediaInfo, update)
	if err != nil {
		return err
	}
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

// DocumentaryScheduleStatus 电视剧定档状态处理逻辑
func DocumentaryScheduleStatus(firstPlayTime string, update *model.UpdateInfo) {
	if firstPlayTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	} else if model.CheckTime(firstPlayTime) && HighThanNowTime(firstPlayTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	}
}

// DocumentaryScheduleCal 纪录片已定档时间计算
func DocumentaryScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "DocumentaryScheduleCal info start")
	defer log.InfoContextf(ctx, "DocumentaryScheduleCal info end.")

	// 获取首播时间
	retFirstTime := CommonGetFirstTime(update.MediaFirstPlayTime, update.PlanFirstTime, update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	flag := scheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTime [%+v]", update.MediaPrePareTime)

	// 判断媒资分类是否为预告片
	if update.IsTrailer {
		if retFirstTime == "" || HighThanNowTime(retFirstTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}

	return nil
}

// DocumentaryUnScheduleCal 纪录片未定档时间计算
func DocumentaryUnScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "DocumentaryUnScheduleCal info start")
	defer log.InfoContextf(ctx, "DocumentaryUnScheduleCal info end")

	// 提取流水时间,判断是否需要计算
	flag := unScheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTimeText [%+v]", update.MediaPrePareTimeText)

	if update.IsTrailer && update.MediaPrePareTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
	}

	return nil
}

func isDocumentaryTrailer(category string) bool {
	if _, ok := parse.DocumentaryTrailerMap[category]; ok {
		return true
	}
	return false
}

func isDocumentaryPositive(category string) bool {
	if _, ok := parse.DocumentaryPositiveMap[category]; ok {
		return true
	}
	return false
}

// DocumentaryYuReCal 纪录片余热时间计算
func DocumentaryYuReCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "DocumentaryYuReCal info start")
	defer log.InfoContextf(ctx, "DocumentaryYuReCal info end")

	yuReThreeEndTime := GetSevenMonthEndTime(update.EndPlayTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeEndTime

	if !model.CheckTime(update.EndPlayTime) {
		log.WarnContextf(ctx, "DocumentaryYuReCal playEndTime:[%s] not illegal", update.EndPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, update.EndPlayTime,
		yuReThreeEndTime, parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// DocumentaryFirstTimeCal 纪录片首播时间计算
func DocumentaryFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, " DocumentaryFirstTimeCal info start")
	defer log.InfoContextf(ctx, "DocumentaryFirstTimeCal info end")

	endPlayTime := getDocumentEndTime(update.PlanEndTime, update.MediaEndPlayTime)
	update.EndPlayTime = endPlayTime

	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "DocumentaryFirstTimeCal firstPlayTime:[%s] not illegal", update.FirstPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime,
		endPlayTime, parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

func getDocumentEndTime(planEndTime string, mediaPlanEndTime string) string {
	// 动漫、纪录片运营期结束时间取消记录更新状态流水规则。
	if model.CheckTime(planEndTime) {
		return timeAddDay(planEndTime, 14)
	}
	return mediaPlanEndTime
}
