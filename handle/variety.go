package handle

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pbu "git.code.oa.com/trpcprotocol/video_media/universal_mgr"
	"git.code.oa.com/video_media/ip_status_update/config"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	api "git.woa.com/video_media/media_go_commlib/universal_api"
)

func typeVarietyHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillVarietyUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	// 综艺专辑内容结构标识cid_content_structure为旧cid标示的直接过滤，不计算生命周期
	if parse.ValueNewCIDStruct != parse.GetFieldValue(mediaInfo, parse.FieldCidContentStructure) {
		//给巡检用，保持结果一致
		update.IPOnlineStatus = update.MediaIPOnlineStatus
		return nil
	}

	defer func() {
		log.InfoContextf(ctx, "typeVarietyHandle End. update [%+v]", update)
	}()

	// 预热期，未定档更新
	err = VarietyUnScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 已定档处理计算
	err = VarietyScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 首播时间计算
	err = VarietyFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}

	// 余热期计算
	err = VarietyYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}

	// 执行原先的主要计算逻辑
	err = VarietyOtherCalAndUpdate(ctx, planData, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "VarietyOtherCalAndUpdate End. update [%+v], err [%+v]", update, err)
		return err
	}

	// "修改为正片"的事件处理（放在主逻辑后面，避免影响其他计算逻辑）
	err = HandleCategoryChangeToPositiveEvent(ctx, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "HandleCategoryChangeToPositiveEvent End. update [%+v], err [%+v]", update, err)
		return err
	}

	// 新增 新结构CID IP实时状态映射至LID逻辑
	UpdateColumnStatus(ctx, mediaInfo, update.FieldMap)

	return nil
}

// FillVarietyUpdateInfo 填充综艺更新信息，获取综艺计算的必要字段
func FillVarietyUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData,
	update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)
	update.LastPubTime = parse.GetFieldValue(mediaInfo, parse.FieldCidLastPubtime)
	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	update.IsTrailer = isVarietyTrailer(update.Category)
	update.IsPositive = isVarietyPositive(update.Category)
	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// VarietyOtherCalAndUpdate 综艺其他计算和更新
func VarietyOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	// 片库-余热期三月外处理
	update.YuReEndTime = GetYuReEndTime(update.EndPlayTime, update.MediaYuReEndTime)
	err := CommonThreeMonthYuReCalV2(ctx, planData, update.YuReEndTime, update.YuReThreeMonthTime, update,
		update.IsPositive)
	if err != nil {
		return err
	}
	// 长尾期处理
	err = CommonChangWeiCalV2(ctx, planData, update, update.IsPositive)
	if err != nil {
		return err
	}
	// 长尾期结束时间计算
	err = CommonThreeChangWeiEndCalV2(ctx, planData, update, update.IsPositive)
	if err != nil {
		return err
	}
	CalVarietyOtherTime(mediaInfo, update)
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

func varietySchedule(nowFirstStartTime, scheduleTime string, update *model.UpdateInfo) {
	if nowFirstStartTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	} else if model.CheckTime(scheduleTime) {
		if HighThanNowTime(nowFirstStartTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}
}

// VarietyScheduleCal 综艺定档时间计算
func VarietyScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "VarietyScheduleCal info start")
	defer log.InfoContextf(ctx, "VarietyScheduleCal info end.")

	// 获取首播时间
	retFirstTime := CommonGetFirstTime(update.MediaFirstPlayTime, update.MediaPrePareTime, update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	flag := scheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTime [%+v]", update.MediaPrePareTime)

	// 判断媒资分类是否为预告片
	if update.IsTrailer || update.Category == "11862" {
		if retFirstTime == "" || HighThanNowTime(retFirstTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}

	return nil
}

// VarietyUnScheduleCal 综艺未定档时间计算
func VarietyUnScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, " VarietyUnScheduleCal info start")
	defer log.InfoContextf(ctx, "VarietyUnScheduleCal info end.")

	flag := unScheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTimeText [%+v]", update.MediaPrePareTimeText)

	if (update.IsTrailer || update.Category == "11862") && update.MediaPrePareTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
	}
	return nil
}

func checkVarietyPeriod(period string) bool {
	if period == "8319351" || period == "8319469" || period == "8286798" {
		return true
	}
	return false
}

func isVarietyTrailer(category string) bool {
	if _, ok := parse.VarietyTrailerMap[category]; ok {
		return true
	}
	return false
}

func isVarietyPositive(category string) bool {
	if _, ok := parse.VarietyPositiveMap[category]; ok {
		return true
	}
	return false
}

// VarietyYuReCal 综艺余热计算
func VarietyYuReCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "VarietyYuReCal info start")
	defer log.InfoContextf(ctx, "VarietyYuReCal info end")

	yuReThreeEndTime := GetFourMonthEndTime(update.EndPlayTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeEndTime

	if !model.CheckTime(update.EndPlayTime) {
		log.WarnContextf(ctx, "playEndTime:[%s] not illegal!", update.EndPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, update.EndPlayTime,
		yuReThreeEndTime, parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// GetVarietyEndTime 获取综艺结束时间
func GetVarietyEndTime(t string, dayNum int, mediaPlayEndTime string) string {
	if t == "" {
		return mediaPlayEndTime
	}
	// 精确到日（"2022-09-11"）的时间默认拼接" 23:59:59"
	if len(t) == 10 {
		t = t + " 23:59:59"
	}
	t1, _ := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	t2 := t1.AddDate(0, 0, dayNum).Format("2006-01-02 15:04:05")
	return t2
}

// VarietyFirstTimeCal 综艺首播时间计算
func VarietyFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, " VarietyFirstTimeCal info start")
	defer log.InfoContextf(ctx, " VarietyFirstTimeCal info end")

	endPlayTime := GetVarietyEndTime(update.LastPubTime, 14, update.MediaEndPlayTime)
	update.EndPlayTime = endPlayTime
	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "endPlayTime:[%s] not illegal!", endPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime, endPlayTime,
		parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// CalVarietyOtherTime 计算综艺类型的其他时间，最近一次更新时间、会员完结时间、免费完结时间、运营期结束时间、运营期截断时间
func CalVarietyOtherTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) {
	// 取媒资已存字段数据，不一致则按最新的正确值来更新
	update.MediaEndTimeForVIP = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForVIP)
	update.MediaEndTimeForFree = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForFree)
	playEndTime := parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	lastUpdateTime := parse.GetFieldValue(mediaInfo, parse.FieldLastUpdateTime)
	// 计算最新的时间值
	// 取最后一期上线last_pubtime的值
	lastPubTime := parse.GetFieldValue(mediaInfo, parse.FieldCidLastPubtime)
	// 会员完结时间和免费完结时间 都是取last_pubtime值（字段精确到日，默认拼上23:59:59）
	curEndTime := GetVarietyEndTime(lastPubTime, 0, "")
	// 运营期结束时间 取last_pubtime+14天（字段精确到日，默认拼上23:59:59）
	curEndPlayTime := GetVarietyEndTime(lastPubTime, 14, "")
	// 最近一次更新时间：取剧集（期数）更新时间epsode_pubtime
	epsodePubTime := parse.GetFieldValue(mediaInfo, parse.FieldEpsodePubTime)

	if lastPubTime != "" {
		calVarietyOtherTime(update.MediaEndTimeForVIP, curEndTime, update.MediaEndTimeForFree, playEndTime,
			curEndPlayTime, update)
	}
	if epsodePubTime != "" && lastUpdateTime != epsodePubTime {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldLastUpdateTime] = []string{epsodePubTime}
	}
	// 计算运营期截断时间
	if config.GetCommonCfg().SegmentSwitch == "on" {
		calVarietyOperationSegmentTime(mediaInfo, update)
	}
}

func calVarietyOtherTime(endTimeVIP, curEndTime, endTimeFree, playEndTime, curEndPlayTime string,
	update *model.UpdateInfo) {
	if endTimeVIP != curEndTime || endTimeFree != curEndTime || playEndTime != curEndPlayTime {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldEndTimeForFree] = []string{curEndTime}
		update.FieldMap[parse.FieldEndTimeForVIP] = []string{curEndTime}
		update.FieldMap[parse.FieldFirstPlayEndTime] = []string{curEndPlayTime}
	}
}

// UpdateColumnStatus 更新逻辑：如果一个CID是新结构CID（cid_content_structure = 8371548），且该CID的所属栏目有值（column_id不为空），
// 则将该CID的IP实时状态（ip_online_status）映射到该CID的column_id上。
func UpdateColumnStatus(ctx context.Context, mediaInfo msghub.MediaInfo, fieldMap map[string][]string) {
	// 异步更新，避免因cgi接口问题影响主逻辑
	go func() {
		cpCtx := trpc.CloneContext(ctx)
		err := updateColumnStatus(cpCtx, mediaInfo, fieldMap)
		if err != nil {
			log.ErrorContextf(cpCtx, "[%s] update LID ip_update_status err,err -> %+v", mediaInfo.Id, err)
		}
	}()
	return
}

// UpdateColumnStatus 更新逻辑：如果一个CID是新结构CID（cid_content_structure = 8371548），且该CID的所属栏目有值（column_id不为空），
// 则将该CID的IP实时状态（ip_online_status）映射到该CID的column_id上。
func updateColumnStatus(ctx context.Context, mediaInfo msghub.MediaInfo, fieldMap map[string][]string) error {
	// 1. 取column_id的值;
	columnID := parse.GetFieldValue(mediaInfo, parse.FieldColumnID)
	if columnID == "" {
		return nil
	}
	// 2. 取ip_online_state的值;取值逻辑为，如果fieldMap中有更新ip_online_state则取其值，否则则取MediaInfo中的值更新
	ipState := getIPState(mediaInfo, fieldMap)
	if ipState == "" {
		return nil
	}
	// 3. old_cid_related_new_cid等于cid本身的才会更新
	relatedNewCid := parse.GetFieldValue(mediaInfo, parse.FieldOldCidRelatedNewCid)
	if mediaInfo.Id != relatedNewCid {
		return nil
	}
	return updateState(ctx, columnID, ipState)
}

// getIPState 获取要更新的ip_online_status的值
func getIPState(mediaInfo msghub.MediaInfo, fieldMap map[string][]string) string {
	var stateInfo string
	val, ok := fieldMap[parse.FieldIPOnlineStatus]
	if ok && len(val) != 0 {
		stateInfo = val[0]
	} else {
		stateInfo = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	}
	return stateInfo
}

// updateState 更新栏目ID的ip实时状态
func updateState(ctx context.Context, columnID string, ipState string) error {
	// 增加栏目开关，关时请求saas，开时请求cgi
	if config.GetCommonCfg().ColumnSwitch == "on" {
		return updateColumnIPStateInfoByCGI(ctx, columnID, ipState)
	}
	return updateColumnIPStateInfo(ctx, columnID, ipState)
}

// updateColumnIPStateInfo 更新栏目ip实时状态
func updateColumnIPStateInfo(ctx context.Context, columnID string, ipState string) error {
	var reqObj []*pbu.FieldUpdateEntry
	reqObj = append(reqObj, &pbu.FieldUpdateEntry{
		ExternalName: parse.FieldIPOnlineStatus,
		FieldValues:  []string{ipState},
		UpdateType:   pbu.EnumUpdateType_Set, // 更新方式
	})
	_, err := api.GetProxy("column-update").UpdateMediaInfo(ctx, columnID, reqObj)
	if err != nil {
		log.ErrorContextf(ctx, "UpdateColumnIPStateInfo err[%v] columnID[%s] ipState[%+v]", err, columnID, ipState)
		return err
	}
	return nil
}

// updateColumnIPStateInfoByCGI cgi接口更新栏目ip实时状态
func updateColumnIPStateInfoByCGI(ctx context.Context, columnID string, ipState string) error {
	params := url.Values{}
	params.Add("otype", "json")
	params.Add("appid", "mediaplatform")
	params.Add("appkey", "acba0c5b0b76a91b1c7e1d2c2b257d65")
	params.Add("oaname", "ip_status_update")
	params.Add("column_id", columnID)
	params.Add("ip_online_status", ipState)
	resp, err := http.PostForm("http://interface.media.cm.com/api_modify_column", params)
	if err != nil {
		log.ErrorContextf(ctx, "error api_modify_column param %s err:%v,columnID[%s] ipState[%+v]", params.Encode(),
			err, columnID, ipState)
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Errorf("updateColumnIPStateInfoByCGI err,code:%d,columnID[%s] ipState[%+v]", resp.StatusCode,
			columnID, ipState)
		return fmt.Errorf("updateColumnIPStateInfoByCGI err,code:%d,columnID[%s] ipState[%+v]", resp.StatusCode,
			columnID, ipState)
	}
	log.InfoContextf(ctx, "updateColumnIPStateInfoByCGI suc,columnID[%s] ipState[%+v]", columnID, ipState)
	return nil
}

/*
		1、 播出方式broadcast_mode为单播的，不计算
		2、 播出方式broadcast_mode为季播1331635的，记录首播时间premiere_time和运营期结束时间operation_end_time每次的变更值
			a) 每段运营期截段开始时间=首播时间premiere_time
			b) 每段运营期截段结束时间=运营期结束时间operation_end_time
			c) 首播时间变更后的值，不在任何一个截段时间段内的，每变更1次记录1段；首播时间变更后的值在任何一个截段时间段内的，不新增只修改所在段的
	           对应时间，首播时间=上一次结束时间怎么办。
			d) 运营期结束时间修改为未来时间的，重新触发当前段切断修改。修改为过去时间的不修改（是否可告警？）
		3、 播出方式broadcast_mode为全年的，计算运营期结束时间-首播时间，≤182天的不触发计算。＞182天的，第一段为182天，之后每92天为一段（对标动漫）
			a) 第一段运营期截段开始时间=首播时间premiere_time
			b) 第一段运营期截段结束时间=首播时间premiere_time+182天
			c) 第二段运营期截段开始时间=首播时间premiere_time+183天
			d) 第二段运营期截段结束时间=首播时间premiere_time+275天
			e) 若：运营期截段结束时间在运营期结束时间operation_end_time内，则不进行下一阶段计算且填入运营期结束时间。反之正常计算下一截段
			f) 以此类推，直到运营期截段结束时间在运营期结束时间内结束。
			g) 运营期结束时间修改的，重新触发所有切断修改（理论上只会影响最后一段，是否全部重算开发决定）。
*/
func calVarietyOperationSegmentTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) {
	if !needCalVarietySegmentTime(mediaInfo, update) {
		return
	}
	var segmentRes string
	mode := parse.GetFieldValue(mediaInfo, parse.FieldBroadcastMode)
	firstPlayTime := update.FirstPlayTime
	operationEndTime := update.EndPlayTime
	switch mode {
	case parse.SingleBroadcast:
		// 单播的不计算
		return
	case parse.SeasonBroadcast:
		// 目前季播的运营逻辑有问题，先暂时不上
		// segmentRes = seasonCal(mediaInfo)
		return
	case parse.YearBroadcast:
		// 年播处理逻辑
		segmentRes = yearCal(firstPlayTime, operationEndTime)
	default:
	}
	if segmentRes == "" {
		return
	}
	update.UpdateFlag = true
	update.FieldMap[parse.FieldOperationSegmentTime] = []string{segmentRes}
}

func needCalVarietySegmentTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) bool {
	return CommonCheckSegmentTime(mediaInfo, update)
}

// yearCal 年播计算
func yearCal(firstPlayTime, operationEndTime string) string {
	var res []string
	// 先计算第一段
	firstSegmentTime := timeAddDay(firstPlayTime, parse.HalfYearDays)
	if TimeBefore(operationEndTime, firstSegmentTime) {
		return ""
	} else {
		res = append(res, fmt.Sprintf("%s~%s", firstPlayTime, firstSegmentTime))
	}
	CommonSegmentTimeCal(timeAddDay(firstPlayTime, parse.HalfYearDays+1), operationEndTime, parse.ThreeMonthDays, &res)
	return strings.Join(res, ";")
}

// seasonCal 季播计算
func seasonCal(mediaInfo msghub.MediaInfo) string {
	_, premiereTimeFlag := parse.GetModifyValue(mediaInfo, parse.FieldFirstPlayTime)
	_, endTimeFlag := parse.GetModifyValue(mediaInfo, parse.FieldFirstPlayEndTime)
	// 综艺季播的要求是 首播时间、运营期结束时间同时变更不进行计算
	if premiereTimeFlag && endTimeFlag {
		newFirstPlayTime := parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
		newEndTime := parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
		oldEndTime := parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
		segmentSli := strings.Split(parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime), ";")
		if TimeBefore(oldEndTime, newFirstPlayTime) {
			segmentSli = append(segmentSli, fmt.Sprintf("%s~%s", newFirstPlayTime, newEndTime))
		}
		return strings.Join(segmentSli, ";")
	}
	// 首播时间、运营期结束时间没有同时变更的兜底逻辑
	return ""
}

// FixLastPubTime 修正最后一期内容上线日
func FixLastPubTime(t string) string {
	// 最后一期内容上线日存在两种格式,所以需要都进行判断
	if model.CheckTime(t) {
		return t
	}
	if model.CheckTime2(t) {
		return t + " 23:59:59"
	}

	return ""
}
