package handle

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func typeMovieHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillMovieUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeMovieHandle end. update [%+v]", update)
	}()

	// 预热期，未定档更新
	err = MovieUnScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 已定档处理计算
	err = MovieScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 首播处理
	err = MovieFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}

	// 运营期结束时间处理
	err = MovieEndTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}

	// 执行原先的主要计算逻辑
	err = MovieOtherCalAndUpdate(ctx, planData, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "MovieOtherCalAndUpdate End. update [%+v], err [%+v]", update, err)
		return err
	}

	// "修改为正片"的事件处理（放在主逻辑后面，避免影响其他计算逻辑）
	err = HandleCategoryChangeToPositiveEvent(ctx, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "HandleCategoryChangeToPositiveEvent End. update [%+v], err [%+v]", update, err)
		return err
	}
	return nil
}

// FillMovieUpdateInfo 填充电影更新信息，获取电视剧ip计算的必要字段
func FillMovieUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData,
	update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.PayStatus = parse.GetFieldValue(mediaInfo, parse.FieldPayStatus)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)

	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	update.IsTrailer = isMovieTrailer(update.Category)
	update.IsPositive = isMoviePositive(update.Category)
	update.IsSinglePointPay = IsSinglePointPay(update.PayStatus)

	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// MovieOtherCalAndUpdate 电影其他计算和更新
func MovieOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	// 片库-余热期三月外处理
	update.YuReEndTime = GetYuReEndTime(update.FirstPlayTime, update.MediaYuReEndTime)
	err := CommonThreeMonthYuReCalV2(ctx, planData, update.YuReEndTime, update.YuReThreeMonthTime, update,
		update.IsPositive)
	if err != nil {
		return err
	}
	// 长尾期处理
	err = CommonChangWeiCalV2(ctx, planData, update, update.IsPositive)
	if err != nil {
		return err
	}
	// 长尾期结束时间计算
	err = CommonThreeChangWeiEndCalV2(ctx, planData, update, update.IsPositive)
	if err != nil {
		return err
	}
	CalEndTime(ctx, planData, mediaInfo, update.FirstPlayTime, update, NeedCalFreeTimeFlag)
	// 电影品类增加计算单点转包月时间
	calMoviePayVipTime(planData, update)
	// 计算电影运营期截断时间
	calMovieOperationSegmentTime(mediaInfo, update)
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

// MovieScheduleStatus 电影定档状态处理逻辑
func MovieScheduleStatus(firstPlayTime string, update *model.UpdateInfo) {
	if firstPlayTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	} else if model.CheckTime(firstPlayTime) && HighThanNowTime(firstPlayTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	}
	if model.CheckTime(firstPlayTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldFirstPlayTime] = []string{firstPlayTime}
	}
}

// MovieScheduleCal 电影已定档计算
func MovieScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "MovieScheduleCal info start")
	defer log.InfoContextf(ctx, "MovieScheduleCal info end.")

	// 获取首播时间
	retFirstTime := CommonGetFirstTime(update.MediaFirstPlayTime, update.PlanFirstTime, update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	flag := scheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTime [%+v]", update.MediaPrePareTime)

	// 判断媒资分类是否为预告片
	if update.IsTrailer {
		if retFirstTime == "" || HighThanNowTime(retFirstTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}

	return nil
}

// MovieUnScheduleCal 电影未定档计算函数
func MovieUnScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, " MovieUnScheduleCal info start")
	defer log.InfoContextf(ctx, "MovieUnScheduleCal info end.")

	// 提取流水时间,判断是否需要计算
	flag := unScheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTimeText [%+v]", update.MediaPrePareTimeText)

	if update.IsTrailer && update.MediaPrePareTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
	}

	return nil
}

// isMovieTrailer 是否是电影预告片
func isMovieTrailer(category string) bool {
	if _, ok := parse.MovieTrailerMap[category]; ok {
		return true
	}
	return false
}

// isMoviePositive 是否是电影正片
func isMoviePositive(category string) bool {
	if _, ok := parse.MoviePositiveMap[category]; ok {
		return true
	}
	return false
}

// MovieEndTimeCal 运营期结束时间相关计算
func MovieEndTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "MovieEndTimeCal info start")
	defer log.InfoContextf(ctx, "MovieEndTimeCal info end")

	yuReThreeEndTime := GetSevenMonthEndTime(update.EndPlayTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeEndTime

	if !model.CheckTime(update.EndPlayTime) {
		log.WarnContextf(ctx, "MovieEndTimeCal playEndTime:[%s] not illegal!", update.EndPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, update.EndPlayTime,
		yuReThreeEndTime, parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// MovieFirstTimeCal 电影首播时间计算
func MovieFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, " MovieFirstTimeCal info start")
	defer log.InfoContextf(ctx, "MovieFirstTimeCal info end")

	endTime := GetMoviePlayEndTime(update.IsSinglePointPay, update.FirstPlayTime, update.MediaEndPlayTime, planData)
	update.EndPlayTime = endTime

	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "Movie FirstPlayTime:[%s] not illegal!", update.FirstPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime, endTime,
		parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

func checkOperation(update *model.UpdateInfo) {
	if value, ok := update.FieldMap[parse.FieldIPOnlineStatus]; ok {
		if len(value) > 0 {
			if value[0] == parse.ValueOperatingPeriod {
				delete(update.FieldMap, parse.FieldIPOnlineStatus)
			}
		}
	}
	return
}

// GetMoviePlayEndTime 获取电影结束时间
func GetMoviePlayEndTime(isSinglePay bool, firstPlayTime, mediaEndPlayTime string, planData *model.PlanData) string {
	if len(firstPlayTime) != 19 {
		return mediaEndPlayTime
	}
	// 单片付费或者存在单点转包月为首播时间+60
	// tapd:https://tapd.woa.com/10093801/prong/stories/view/1010093801118111776
	if isSinglePay || planData.PayVipTime != "" {
		return timeAddDay(firstPlayTime, 60)
	}
	// 2023-02-28变更，电影品类运营期结束时间由首播时间+90天变为由首播时间+30天
	return timeAddDay(firstPlayTime, 30)
}

// calMoviePayVipTime 计算电影单点转包月时间
func calMoviePayVipTime(planData *model.PlanData, update *model.UpdateInfo) {
	if planData.PayVipTime != "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldPayVipTime] = []string{planData.PayVipTime}
	}
}

// calMovieOperationSegmentTime 计算电影运营期切段时间
func calMovieOperationSegmentTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) {
	firstPlayTime, isModify := parse.GetModifyValue(mediaInfo, parse.FieldFirstPlayTime)
	// 首播时间没有变更，不进行计算
	if !isModify {
		return
	}
	// 首播时间变为空，直接将运营期切段时间清空
	if firstPlayTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldOperationSegmentTime] = []string{}
		return
	}
	// 首播时间变不为空，检查首播时间是否符合规范
	if !model.CheckTime(firstPlayTime) {
		return
	}
	update.UpdateFlag = true
	// 运营策略改动，电影切段时间范围30天改为29天
	update.FieldMap[parse.FieldOperationSegmentTime] = []string{fmt.Sprintf("%s~%s",
		firstPlayTime, timeAddDay(firstPlayTime, parse.MonthDays-1))}
}
