package handle

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func typeGameHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	if isGameColumnCal(parse.GetFieldValue(mediaInfo, parse.FieldSportsColumnType)) {
		return typeGameColumnHandle(ctx, planData, mediaInfo, update)
	}

	err := FillGameUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeChildrenHandle End. update [%+v]", update)
	}()

	// 已定档处理计算
	err = GameScheduleCal(ctx, update)
	if err != nil {
		return err
	}
	err = GameFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}
	// 余热期计算
	err = GameYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}

	return GameOtherCalAndUpdate(ctx, planData, mediaInfo, update)
}

// FillGameUpdateInfo 填充游戏更新信息，获取游戏计算的必要字段
func FillGameUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData,
	update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.MediaEndTimeForVIP = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForVIP)
	update.MediaEndTimeForFree = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForFree)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)
	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	// 获取排播完结时间
	update.PlanEndTime = GetPlanEndTime(planData)

	update.IsTrailer = isGameTrailer(update.Category)

	update.LastPubTime = parse.GetFieldValue(mediaInfo, parse.FieldCidLastPubtime)
	update.VideoCheckupTime = parse.GetFieldValue(mediaInfo, parse.FieldCheckupTime)
	update.PublishDate = parse.GetFieldValue(mediaInfo, parse.FieldPublishDateX)
	update.SportsColumnType = parse.GetFieldValue(mediaInfo, parse.FieldSportsColumnType)

	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// GameOtherCalAndUpdate 游戏其他计算和更新
func GameOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := CommonYuReOtherCalV2(ctx, planData, update, !update.IsTrailer)
	if err != nil {
		return err
	}

	CalEndTime(ctx, planData, mediaInfo, update.FirstPlayTime, update, NotNeedCalFreeTimeFlag)
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

// GameScheduleCal 游戏已定档计算
func GameScheduleCal(ctx context.Context, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "GameScheduleCal info start")
	defer log.InfoContextf(ctx, "GameScheduleCal info end")

	// 获取首播时间
	retFirstTime := GetGamePlayStart(update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	if !model.CheckTime(update.VideoCheckupTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{}
		log.WarnContextf(ctx, "GameScheduleCal scheduleTime:[%s] not illegal", update.VideoCheckupTime)
		return nil
	}

	commScheduleTime(update.VideoCheckupTime, update.MediaPrePareTime, update)

	// 判断媒资分类是否为预告片
	if update.IsTrailer || (update.Category == "11370" && update.SportsColumnType == "8357682") {
		if retFirstTime == "" || HighThanNowTime(retFirstTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}

	return nil
}

func gameFilterCategoryType(category, sportColumnType string) bool {
	if category != "11849" && category != "11370" {
		return false
	}
	if category == "11370" && sportColumnType != "8357682" {
		return false
	}
	return true
}

func gameScheduleStatus(nowFirstPlayTime string, update *model.UpdateInfo) {
	if model.CheckTime(nowFirstPlayTime) && HighThanNowTime(nowFirstPlayTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	}
}

// GameYuReCal 游戏余热计算
func GameYuReCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "GameYuReCal info start")
	defer log.InfoContextf(ctx, "GameYuReCal info end")

	yuReThreeEndTime := GetSevenMonthEndTime(update.EndPlayTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeEndTime

	if !model.CheckTime(update.EndPlayTime) {
		log.WarnContextf(ctx, "GameYuReCal playEndTime:[%s] not illegal", update.EndPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, update.EndPlayTime,
		yuReThreeEndTime, parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, !update.IsTrailer)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// GameFirstTimeCal 游戏首播时间计算
func GameFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, " GameFirstTimeCal info start")
	defer log.InfoContextf(ctx, " GameFirstTimeCal info end")

	endPlayTime := GetGamePlayEnd(update.FirstPlayTime, update.MediaEndPlayTime)
	update.EndPlayTime = endPlayTime

	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "GameFirstTimeCal firstPlayTime:[%s] not illegal", update.FirstPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime,
		endPlayTime, parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, !update.IsTrailer)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// GetGamePlayStart 获取游戏首播时间
// ①取【即将上映时间(时间)】填写的值
// ②取不到值时填入【播出日期】(时分秒默认23:59:59)
func GetGamePlayStart(update *model.UpdateInfo) string {
	if model.CheckTime(update.MediaPrePareTime) {
		return update.MediaPrePareTime
	}

	if len(update.PublishDate) == 10 {
		return update.PublishDate + " 23:59:59"
	}
	return update.MediaFirstPlayTime
}

// GetGamePlayEnd 获取游戏运营期结束时间
func GetGamePlayEnd(t string, mediaPlanEndTime string) string {
	if model.CheckTime(t) {
		return timeAddDay(t, 2)
	}
	return mediaPlanEndTime
}

// isGameColumnCal 是否游戏栏目的特殊计算
func isGameColumnCal(sportsColumnType string) bool {
	if sportsColumnType == parse.ValueProGramme || sportsColumnType == parse.ValueDocumentary {
		return true
	}
	return false
}
