package handle

import (
	"context"
	"testing"

	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func Test_typeSportHandle(t *testing.T) {
	type args struct {
		ctx       context.Context
		planData  *model.PlanData
		mediaInfo msghub.MediaInfo
		update    *model.UpdateInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test sport basic logic",
			args: args{
				ctx: context.Background(),
				planData: &model.PlanData{
					ID:   "test123",
					Type: 1,
				},
				mediaInfo: msghub.MediaInfo{
					Id: "test123",
					FieldInfos: map[string]msghub.FieldInfo{
						"type": {Value: "7"},
					},
				},
				update: &model.UpdateInfo{
					IsUpdate: false, // Skip actual updates
					FieldMap: make(map[string][]string),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Skip integration test that requires external dependencies
			t.Skip("Skipping integration test - requires refactoring for unit testing")
		})
	}
}

func TestGetTimeFromSportsMatch(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"Match_2023-10-05 14:30_ID", "2023-10-05 14:30"},
		{"NoDateHere", ""},
		{"2023-10-05 14:30", "2023-10-05 14:30"},
		{"InvalidDate_2023-13-05 14:30", ""},
		{"Match_2023-10-05 14:30:00_ID", "2023-10-05 14:30"},
	}

	for _, test := range tests {
		result := GetTimeFromSportsMatch(test.input)
		if result != test.expected {
			t.Errorf("For input '%s', expected '%s' but got '%s'", test.input, test.expected, result)
		}
	}
}
