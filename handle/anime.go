package handle

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func typeAnimeHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillAnimeUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeAnimeHandle End. update [%+v]", update)
	}()

	// 执行原先的主要计算逻辑
	err = executeAnimeMainLogic(ctx, planData, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "executeAnimeMainLogic End. update [%+v], err [%+v]", update, err)
		return err
	}

	// "修改为正片"的事件处理（放在主逻辑后面，避免影响其他计算逻辑）
	err = HandleCategoryChangeToPositiveEvent(ctx, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "HandleCategoryChangeToPositiveEvent End. update [%+v], err [%+v]", update, err)
		return err
	}
	return nil
}

// executeAnimeMainLogic 执行动漫的主要计算逻辑
func executeAnimeMainLogic(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	// 预热期，未定档更新
	err := AnimeUnScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 已定档处理计算
	err = AnimeScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 首播时间计算
	err = AnimeFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}
	// 余热期计算
	err = AnimeYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}

	return AnimeOtherCalAndUpdate(ctx, planData, mediaInfo, update)
}

// FillAnimeUpdateInfo 填充动漫更新信息，获取综艺动漫的必要字段
func FillAnimeUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData,
	update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.PayStatus = parse.GetFieldValue(mediaInfo, parse.FieldPayStatus)
	update.EditingVersionComic = parse.GetFieldValue(mediaInfo, parse.FieldEditingVersionComic)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)

	update.AnimeUpdateStatus = parse.GetFieldValue(mediaInfo, parse.FieldAnimeUpdateStatus)
	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	update.PlanEndTime = GetPlanEndTime(planData)
	update.IsTrailer = isAnimeTrailer(update.Category)
	update.IsSinglePointPay = IsSinglePointPay(update.PayStatus)
	update.IsPositive = isAnimePositive(update.Category)

	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// AnimeOtherCalAndUpdate 动漫其他计算和更新
func AnimeOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	// 片库-余热期三月外处理
	update.YuReEndTime = GetYuReEndTime(update.EndPlayTime, update.MediaYuReEndTime)
	err := CommonThreeMonthYuReCalV2(ctx, planData, update.YuReEndTime, update.YuReThreeMonthTime, update,
		update.IsPositive)
	if err != nil {
		return err
	}

	// 长尾期处理
	err = AnimeChangWeiCal(ctx, planData, update)
	if err != nil {
		return err
	}
	// 长尾期结束时间计算
	err = CommonThreeChangWeiEndCalV2(ctx, planData, update, update.IsPositive)
	if err != nil {
		return err
	}
	// 其他四个相关时间计算
	err = CalOtherTime(ctx, planData, mediaInfo, update)
	if err != nil {
		return err
	}
	// 计算运营期截断时间
	calAnimeOperationSegmentTime(mediaInfo, update)
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

// AnimeChangWeiCal 动漫长尾计算
func AnimeChangWeiCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.WarnContextf(ctx, "AnimeChangWeiCal start")
	defer log.WarnContextf(ctx, "AnimeChangWeiCal end")
	// 长尾期三年内处理
	return CommonChangWeiCalV2(ctx, planData, update, update.IsPositive)
}

// AnimeScheduleCal 动漫已定档计算
func AnimeScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "AnimeScheduleCal info start")
	defer log.InfoContextf(ctx, "AnimeScheduleCal info end")

	// 获取首播时间
	retFirstTime := CommonGetFirstTime(update.MediaFirstPlayTime, update.PlanFirstTime, update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	flag := scheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTime [%+v]", update.MediaPrePareTime)

	// 判断媒资分类是否为预告片
	if update.IsTrailer {
		if retFirstTime == "" || HighThanNowTime(retFirstTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	} else {
		AnimePositiveSchedule(update)
	}

	return nil
}

// AnimeTrailerSchedule 动漫预告片定档计算逻辑
func AnimeTrailerSchedule(playTime string, update *model.UpdateInfo) {
	// 已定档预热时间
	if playTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	} else if model.CheckTime(playTime) && HighThanNowTime(playTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	}
}

// AnimePositiveSchedule 动漫正片定档计算逻辑
func AnimePositiveSchedule(update *model.UpdateInfo) {
	if update.AnimeUpdateStatus == parse.ValueStatusComplete && TimeAfterTime(update.ScheduleTime,
		update.UnScheduleTime, update.PlanEndTime, update.EndPlayTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	}
}

// AnimeUnScheduleCal 动漫未定档计算
func AnimeUnScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "AnimeUnScheduleCal info start")
	defer log.InfoContextf(ctx, "AnimeUnScheduleCal info end.")

	flag := unScheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTimeText [%+v]", update.MediaPrePareTimeText)

	if update.IsTrailer {
		if update.MediaPrePareTime == "" {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
		}
	} else {
		animeUnSchedulePositive(update)
	}

	return nil
}

func animeUnSchedulePositive(update *model.UpdateInfo) {
	if update.AnimeUpdateStatus == parse.ValueStatusComplete && TimeAfterTime(update.UnScheduleTime, update.ScheduleTime,
		update.PlanEndTime, update.EndPlayTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
	}
}

func animeUnScheduleTrail(scheduleTime string, update *model.UpdateInfo) {
	// 已定档预热时间
	if scheduleTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
	}
}

func isAnimePositive(category string) bool {
	if category == "10994" {
		return true
	}
	return false
}

func isAnimeTrailer(category string) bool {
	if category == "10996" {
		return true
	}
	return false
}

// AnimeYuReCal 动漫余热计算
func AnimeYuReCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "AnimeYuReCal info start")
	defer log.InfoContextf(ctx, "AnimeYuReCal info end")

	yuReThreeEndTime := GetSevenMonthEndTime(update.EndPlayTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeEndTime

	if !model.CheckTime(update.EndPlayTime) {
		log.WarnContextf(ctx, "AnimeYuReCal playEndTime:[%s] not illegal!", update.EndPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, update.EndPlayTime,
		yuReThreeEndTime, parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, update.IsPositive)

	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

func checkAnimeRelTime(playEndTime, unScheduleTime, scheduleTime string) bool {
	if !model.CheckTime(unScheduleTime) || !model.CheckTime(scheduleTime) {
		return false
	}
	playEnd, _ := time.ParseInLocation("2006-01-02 15:04:05", playEndTime, time.Local)
	unSchedule, _ := time.ParseInLocation("2006-01-02 15:04:05", unScheduleTime, time.Local)
	schedule, _ := time.ParseInLocation("2006-01-02 15:04:05", scheduleTime, time.Local)
	if playEnd.Before(unSchedule) {
		return false
	}
	if playEnd.Before(schedule) {
		return false
	}
	return true
}

// GetAnimeEndTime 获取动漫运营结束时间
func GetAnimeEndTime(planData *model.PlanData, update *model.UpdateInfo) string {
	firstTime := update.FirstPlayTime
	if update.EditingVersionComic == parse.ValueTheater {
		if len(firstTime) != 19 {
			return update.MediaEndPlayTime
		}
		if update.IsSinglePointPay {
			return timeAddDay(update.FirstPlayTime, 60)
		}
		return timeAddDay(update.FirstPlayTime, 30)
	}
	// 获取完结时间
	endTime := GetPlanEndTime(planData)
	if len(endTime) == 19 {
		return timeAddDay(endTime, 14)
	}
	return update.MediaEndPlayTime
}

// AnimeFirstTimeCal 动漫首播时间计算
func AnimeFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, " AnimeFirstTimeCal info start")
	defer log.InfoContextf(ctx, "AnimeFirstTimeCal info end")

	playEndTime := GetAnimeEndTime(planData, update)
	update.EndPlayTime = playEndTime

	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "AnimeFirstTimeCal nowFirstStartTime:[%s] not illegal", update.FirstPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime, playEndTime,
		parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

/*
calAnimeOperationSegmentTime 计算运营期结束时间-首播时间，≤182天的不触发计算。＞182天的，第一段为182天，之后每92天为一段

	a) 第一段运营期截段开始时间=首播时间premiere_time
	b) 第一段运营期截段结束时间=首播时间premiere_time+182天
	c) 第二段运营期截段开始时间=首播时间premiere_time+183天
	d) 第二段运营期截段结束时间=首播时间premiere_time+275天
	e) 若：运营期截段结束时间在运营期结束时间operation_end_time内，则不进行下一阶段计算且填入运营期结束时间。反之正常计算下一截段
	f) 以此类推，直到运营期截段结束时间在运营期结束时间内结束。
	g) 运营期结束时间修改的，重新触发所有切断修改。
*/
func calAnimeOperationSegmentTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) {
	if !needCalAnimeSegmentTime(mediaInfo, update) {
		return
	}
	segmentRes := animeSegmentTimeLogic(update)
	if segmentRes == "" {
		return
	}
	update.UpdateFlag = true
	update.FieldMap[parse.FieldOperationSegmentTime] = []string{segmentRes}
}

func needCalAnimeSegmentTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) bool {
	return CommonCheckSegmentTime(mediaInfo, update)
}

func animeSegmentTimeLogic(update *model.UpdateInfo) string {
	firstPlayTime := update.FirstPlayTime
	operationEndTime := update.EndPlayTime
	var res []string
	// 先计算第一段
	firstSegmentTime := timeAddDay(firstPlayTime, parse.HalfYearDays)
	if TimeBefore(operationEndTime, firstSegmentTime) {
		return ""
	} else {
		res = append(res, fmt.Sprintf("%s~%s", firstPlayTime, firstSegmentTime))
	}
	CommonSegmentTimeCal(timeAddDay(firstPlayTime, parse.HalfYearDays+1), operationEndTime, parse.ThreeMonthDays, &res)
	return strings.Join(res, ";")
}


