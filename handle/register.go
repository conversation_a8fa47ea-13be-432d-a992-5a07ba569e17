package handle

import (
	"context"

	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

const (
	// TypeMovie 电影类型
	TypeMovie = "1"
	// TypeTV 电视剧类型
	TypeTV = "2"
	// TypeVariety 综艺类型
	TypeVariety = "10"
	// TypeAnime 动漫类型
	TypeAnime = "3"
	// TypeChildren 少儿类型
	TypeChildren = "106"
	// TypeDocumentary 纪录片类型
	TypeDocumentary = "9"
	// TypeGame 游戏类型
	TypeGame = "6"
	// TypeSport 体育类型
	TypeSport = "4"
	// TypeMusic 音乐类型
	TypeMusic = "22"
	// TypeKnowledgePay 知识付费类型
	TypeKnowledgePay = "pay"
	// TypeShortDrama 短剧类型（独立处理，不在类型系统中）
	TypeShortDrama = "short_drama"
)

type typeCalHandle func(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error

var mapTypeFilterHandle map[string]typeCalHandle

// GetTypeCalHandle 获取分类处理计算函数
func GetTypeCalHandle(sType string) typeCalHandle {
	if _, ok := mapTypeFilterHandle[sType]; !ok {
		return func(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
			update *model.UpdateInfo) error {
			return nil
		}
	}
	return mapTypeFilterHandle[sType]
}

// RegisterTypeFilter 分类处理注册函数
func RegisterTypeFilter() {
	mapTypeFilterHandle = make(map[string]typeCalHandle)
	mapTypeFilterHandle[TypeMovie] = typeMovieHandle
	mapTypeFilterHandle[TypeTV] = typeTVHandle
	mapTypeFilterHandle[TypeVariety] = typeVarietyHandle
	mapTypeFilterHandle[TypeAnime] = typeAnimeHandle
	mapTypeFilterHandle[TypeChildren] = typeChildrenHandle
	mapTypeFilterHandle[TypeDocumentary] = typeDocumentaryHandle
	mapTypeFilterHandle[TypeGame] = typeGameHandle
	mapTypeFilterHandle[TypeSport] = typeSportHandle
	mapTypeFilterHandle[TypeMusic] = typeMusicHandle
	mapTypeFilterHandle[TypeKnowledgePay] = typeKnowledgePayHandle
}
