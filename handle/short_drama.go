package handle

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

// ShortDramaStatusCal 短剧IP实时状态计算（独立处理函数）短剧即为中视频
func ShortDramaStatusCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "ShortDramaStatusCal start: %v", mediaInfo.Id)
	defer log.InfoContextf(ctx, "ShortDramaStatusCal end: %v", mediaInfo.Id)

	// 检查 update 参数是否为 nil
	if update == nil {
		update = &model.UpdateInfo{}
		update.UpdateFlag = false
		update.FieldMap = make(map[string][]string)
	}

	// 首先判断是否为短剧
	if !IsShortDrama(mediaInfo) {
		log.InfoContextf(ctx, "不是短剧内容，跳过处理")
		return nil
	}

	// 补全当前字段信息（FieldInfos），同时保留变更信息（ModifyFieldInfos）
	currentMediaInfo, err := makeMsgInfo(ctx, mediaInfo.Id, parse.ShortDramaType)
	if err != nil {
		log.ErrorContextf(ctx, "make msgInfo err:%+v, dataID: %v", err, mediaInfo.Id)
		return err
	}
	// 将变更信息合并到完整的媒资信息中
	currentMediaInfo.ModifyFieldInfos = mediaInfo.ModifyFieldInfos
	currentMediaInfo.TimeStamp = mediaInfo.TimeStamp

	// 填充短剧更新信息
	update.MediaIPOnlineStatus = parse.GetFieldValue(currentMediaInfo, parse.FieldIPOnlineStatus)
	update.Category = parse.GetFieldValue(currentMediaInfo, parse.FieldCategory)
	update.TotalAuditState = parse.GetFieldValue(currentMediaInfo, parse.FieldTotalAuditState)
	update.CheckupState = parse.GetFieldValue(currentMediaInfo, parse.FieldCheckUpState)
	update.MediaFirstPlayTime = parse.GetFieldValue(currentMediaInfo, parse.FieldFirstPlayTime)
	update.MediaEndPlayTime = parse.GetFieldValue(currentMediaInfo, parse.FieldFirstPlayEndTime)

	// 调用核心逻辑函数
	shortDramaStatusCalLogic(ctx, update.Category, update.TotalAuditState, update.CheckupState, update, mediaInfo.Id)
	return nil
}

// shortDramaStatusCalLogic 短剧IP实时状态计算核心逻辑
func shortDramaStatusCalLogic(ctx context.Context, category, totalAuditState, checkupState string,
	update *model.UpdateInfo, mediaID string) {
	// 预热期：专辑审核状态=审核通过 && 专辑品类=电视剧-短剧-预告片
	if isShortDramaTrailer(category) && isAuditPassed(totalAuditState) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		log.InfoContextf(ctx, "设置短剧预告片状态为已定档预热期,dataID:%s", mediaID)
		return
	}

	// 短剧正片的状态判断
	log.InfoContextf(ctx, "mediaID:%s, category: %s, checkupState: %s", mediaID, category, checkupState)
	if isShortDramaMain(category) && isOnline(checkupState) {
		// 获取首播时间和运营期结束时间
		premiereTime := update.MediaFirstPlayTime
		operationEndTime := update.MediaEndPlayTime
		currentTime := time.Now().Format("2006-01-02 15:04:05")
		log.InfoContextf(ctx, "mediaID:%s, premiereTime: %s, operationEndTime: %s, currentTime: %s",
			mediaID, premiereTime, operationEndTime, currentTime)

		// 运营期：当前时间大于等于首播时间，且小于运营期结束时间
		if isTimeInOperationPeriod(currentTime, premiereTime, operationEndTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueOperatingPeriod}
			log.InfoContextf(ctx, "设置短剧正片状态为运营期,dataID:%s", mediaID)
			return
		}

		// 片库期：当前时间大于等于运营期结束时间
		if isTimeInLibraryPeriod(currentTime, operationEndTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueYuReOperation}
			log.InfoContextf(ctx, "设置短剧正片状态为片库-次新热期,dataID:%s", mediaID)
			return
		}
	}

	log.InfoContextf(ctx, "未命中任何状态更新逻辑,dataID:%s", mediaID)
}

// isCurrentTimeAfterOrEqual 判断当前时间是否大于等于指定时间
func isCurrentTimeAfterOrEqual(t string) bool {
	targetTime, err := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	if err != nil {
		return false
	}
	return time.Now().After(targetTime) || time.Now().Equal(targetTime)
}

// makeMsgInfo 获取媒资信息
func makeMsgInfo(ctx context.Context, id string, dataSet int) (msghub.MediaInfo, error) {
	// 1.拉取媒资相关字段
	var msgInfo msghub.MediaInfo
	rsp, err := model.GetAllMediaFieldInfos(ctx, []string{id}, dataSet)
	if err != nil {
		return msgInfo, err
	}

	// 2.生成消息总线结构体
	msgInfo, err = model.MakeMediaInfo(rsp, id, dataSet)
	if err != nil {
		return msgInfo, err
	}
	return msgInfo, nil
}

// isShortDramaTrailer 判断是否为短剧预告片
func isShortDramaTrailer(category string) bool {
	return category == parse.ValueCategoryShortDramaTrailer
}

// isShortDramaMain 判断是否为短剧正片
func isShortDramaMain(category string) bool {
	return category == parse.ValueCategoryShortDramaMain
}

// isAuditPassed 判断是否审核通过
func isAuditPassed(auditState string) bool {
	return auditState == parse.ValueTotalAuditStatePassed
}

// isOnline 判断是否已上架
func isOnline(checkupState string) bool {
	return checkupState == parse.ValueCheckupStateOnline
}

// isTimeInOperationPeriod 判断当前时间是否在运营期内
// 当前时间大于等于首播时间，且小于运营期结束时间
func isTimeInOperationPeriod(currentTime, premiereTime, operationEndTime string) bool {
	// 如果首播时间为空，则不在运营期
	if premiereTime == "" {
		return false
	}

	// 如果运营期结束时间为空，只要当前时间大于等于首播时间即可
	if operationEndTime == "" {
		return isCurrentTimeAfterOrEqual(premiereTime)
	}

	// 当前时间大于等于首播时间，且小于运营期结束时间
	return isCurrentTimeAfterOrEqual(premiereTime) && isCurrentTimeBeforeOrEqual(operationEndTime)
}

// isTimeInLibraryPeriod 判断当前时间是否在片库期
// 当前时间大于等于运营期结束时间
func isTimeInLibraryPeriod(currentTime, operationEndTime string) bool {
	// 如果运营期结束时间为空，则不在片库期
	if operationEndTime == "" {
		return false
	}

	// 当前时间大于等于运营期结束时间
	return isCurrentTimeAfterOrEqual(operationEndTime)
}

// isCurrentTimeBeforeOrEqual 判断当前时间是否小于等于指定时间
func isCurrentTimeBeforeOrEqual(t string) bool {
	targetTime, err := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	if err != nil {
		return false
	}
	return time.Now().Before(targetTime) || time.Now().Equal(targetTime)
}

// IsShortDrama 判断是否为短剧
func IsShortDrama(mediaInfo msghub.MediaInfo) bool {
	// 检查内容形式是否为中视频
	contentForm := parse.GetFieldValue(mediaInfo, parse.FieldContentForm)
	if contentForm != parse.ValueContentFormMidVideo {
		return false
	}

	// 检查CP选择付费模式是否在指定范围内
	cpPaymentType := parse.GetFieldValue(mediaInfo, parse.FieldCpMidPaymentType)
	validPaymentTypes := map[string]bool{
		parse.ValueCpPaymentMicroShortDramaVipShare:              true, // 微短剧-会员分账
		parse.ValueCpPaymentMicroShortDramaSinglePay:             true, // 微短剧-单点
		parse.ValueCpPaymentMicroShortDramaFree:                  true, // 微短剧-免费
		parse.ValueCpPaymentMicroShortDramaIncentiveAd:           true, // 微短剧-激励广告
		parse.ValueCpPaymentMicroShortDramaSinglePayIncentiveMix: true, // 微短剧-单点激励广告混合
		parse.ValueCpPaymentMicroShortDramaCard:                  true, // 微短剧-短剧卡
		parse.ValueCpPaymentMicroShortDramaCardSingleMix:         true, // 微短剧-短剧卡单点混合
	}

	return validPaymentTypes[cpPaymentType]
}

// ShortDramaTimeCal 短剧时间计算（已定档预热时间、首播时间、运营期结束时间）
func ShortDramaTimeCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "ShortDramaTimeCal start: %v", mediaInfo.Id)
	defer log.InfoContextf(ctx, "ShortDramaTimeCal end: %v", mediaInfo.Id)

	// 检查 update 参数是否为 nil
	if update == nil {
		update = &model.UpdateInfo{}
		update.UpdateFlag = false
		update.FieldMap = make(map[string][]string)
	}

	// 首先判断是否为短剧
	if !IsShortDrama(mediaInfo) {
		log.InfoContextf(ctx, "不是短剧内容，跳过时间计算处理")
		return nil
	}

	// 补全当前字段信息（FieldInfos），同时保留变更信息（ModifyFieldInfos）
	currentMediaInfo, err := makeMsgInfo(ctx, mediaInfo.Id, parse.ShortDramaType)
	if err != nil {
		log.ErrorContextf(ctx, "make msgInfo err:%+v, dataID: %v", err, mediaInfo.Id)
		return err
	}
	// 将变更信息合并到完整的媒资信息中
	currentMediaInfo.ModifyFieldInfos = mediaInfo.ModifyFieldInfos
	currentMediaInfo.TimeStamp = mediaInfo.TimeStamp

	// 填充短剧更新信息
	update.MediaScheduleTime = parse.GetFieldValue(currentMediaInfo, parse.FieldPreHeatTime)
	update.MediaFirstPlayTime = parse.GetFieldValue(currentMediaInfo, parse.FieldFirstPlayTime)
	update.MediaEndPlayTime = parse.GetFieldValue(currentMediaInfo, parse.FieldFirstPlayEndTime)
	log.InfoContextf(ctx, "currentMediaInfo:%+v", currentMediaInfo)
	log.InfoContextf(ctx, "ShortDramaTimeCal update before: %+v", update)

	// 计算已定档预热时间
	err = calShortDramaScheduleTime(ctx, currentMediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "calShortDramaScheduleTime err:%+v, dataID: %v", err, mediaInfo.Id)
		return err
	}

	// 计算首播时间
	err = calShortDramaFirstPlayTime(ctx, currentMediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "calShortDramaFirstPlayTime err:%+v, dataID: %v", err, mediaInfo.Id)
		return err
	}

	// 计算运营期结束时间
	err = calShortDramaOperationEndTime(ctx, currentMediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "calShortDramaOperationEndTime err:%+v, dataID: %v", err, mediaInfo.Id)
		return err
	}

	return nil
}

// calShortDramaScheduleTime 计算短剧已定档预热时间
func calShortDramaScheduleTime(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	// 检查 update 参数是否为 nil
	if update == nil {
		log.ErrorContextf(ctx, "calShortDramaScheduleTime: update parameter is nil for dataID: %v", mediaInfo.Id)
		return fmt.Errorf("update parameter cannot be nil")
	}

	// 检查专辑审核状态变更
	newAuditState, auditChanged := parse.GetModifyValue(mediaInfo, parse.FieldTotalAuditState)
	currentAuditState := parse.GetFieldValue(mediaInfo, parse.FieldTotalAuditState)

	// 检查专辑品类变更
	newCategory, categoryChanged := parse.GetModifyValue(mediaInfo, parse.FieldCategory)
	currentCategory := parse.GetFieldValue(mediaInfo, parse.FieldCategory)

	// 检查是否满足条件
	shouldSetScheduleTime := false

	// 情况1：专辑审核状态变更为审核通过 && 当前专辑品类为短剧预告片
	if auditChanged && newAuditState == parse.ValueTotalAuditStatePassed &&
		currentCategory == parse.ValueCategoryShortDramaTrailer {
		shouldSetScheduleTime = true
		log.InfoContextf(ctx, "专辑审核状态变更为审核通过，当前品类为短剧预告片，设置已定档预热时间")
	}

	// 情况2：专辑品类变更为短剧预告片 && 当前审核状态为审核通过
	if categoryChanged && newCategory == parse.ValueCategoryShortDramaTrailer &&
		currentAuditState == parse.ValueTotalAuditStatePassed {
		shouldSetScheduleTime = true
		log.InfoContextf(ctx, "专辑品类变更为短剧预告片，当前审核状态为审核通过，设置已定档预热时间")
	}

	if shouldSetScheduleTime {
		// 检查原已定档预热时间是否为空
		if update.MediaScheduleTime == "" {
			currentTime := time.Unix(int64(mediaInfo.TimeStamp), 0).Format("2006-01-02 15:04:05")
			update.UpdateFlag = true
			update.FieldMap[parse.FieldPreHeatTime] = []string{currentTime}
			log.InfoContextf(ctx, "原已定档预热时间为空，设置已定档预热时间为: %s", currentTime)
		} else {
			log.InfoContextf(ctx, "原已定档预热时间不为空(%s)，跳过设置新的已定档预热时间", update.MediaScheduleTime)
		}
	}

	return nil
}

// calShortDramaFirstPlayTime 计算短剧首播时间
func calShortDramaFirstPlayTime(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	// 检查 update 参数是否为 nil
	if update == nil {
		log.ErrorContextf(ctx, "calShortDramaFirstPlayTime: update parameter is nil for dataID: %v", mediaInfo.Id)
		return fmt.Errorf("update parameter cannot be nil")
	}

	// 检查专辑状态变更
	newCheckupState, checkupChanged := parse.GetModifyValue(mediaInfo, parse.FieldCheckUpState)
	currentCheckupState := parse.GetFieldValue(mediaInfo, parse.FieldCheckUpState)

	// 检查专辑品类变更
	newCategory, categoryChanged := parse.GetModifyValue(mediaInfo, parse.FieldCategory)
	currentCategory := parse.GetFieldValue(mediaInfo, parse.FieldCategory)

	// 检查是否满足条件
	shouldSetFirstPlayTime := false

	// 情况1：专辑状态变更为已上架 && 当前专辑品类为短剧正片
	if checkupChanged && newCheckupState == parse.ValueCheckupStateOnline &&
		currentCategory == parse.ValueCategoryShortDramaMain {
		shouldSetFirstPlayTime = true
		log.InfoContextf(ctx, "专辑状态变更为已上架，当前品类为短剧正片，设置首播时间")
	}

	// 情况2：专辑品类变更为短剧正片 && 当前专辑状态为已上架
	if categoryChanged && newCategory == parse.ValueCategoryShortDramaMain &&
		currentCheckupState == parse.ValueCheckupStateOnline {
		shouldSetFirstPlayTime = true
		log.InfoContextf(ctx, "专辑品类变更为短剧正片，当前状态为已上架，设置首播时间")
	}

	if shouldSetFirstPlayTime {
		// 检查原首播时间是否为空
		if update.MediaFirstPlayTime == "" {
			currentTime := time.Unix(int64(mediaInfo.TimeStamp), 0).Format("2006-01-02 15:04:05")
			update.UpdateFlag = true
			update.FieldMap[parse.FieldFirstPlayTime] = []string{currentTime}
			log.InfoContextf(ctx, "原首播时间为空，设置首播时间为: %s", currentTime)
		} else {
			log.InfoContextf(ctx, "原首播时间不为空(%s)，跳过设置新的首播时间", update.MediaFirstPlayTime)
		}
	}

	return nil
}

// calShortDramaOperationEndTime 计算短剧运营期结束时间
func calShortDramaOperationEndTime(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	// 检查 update 参数是否为 nil
	if update == nil {
		log.ErrorContextf(ctx, "calShortDramaOperationEndTime: update parameter is nil for dataID: %v", mediaInfo.Id)
		return fmt.Errorf("update parameter cannot be nil")
	}

	// 检查首播时间是否变更
	newFirstPlayTime, firstPlayChanged := parse.GetModifyValue(mediaInfo, parse.FieldFirstPlayTime)

	// 如果首播时间有变更且为合法时间
	if firstPlayChanged && model.CheckTime(newFirstPlayTime) {
		// 首播时间+30天作为运营期结束时间
		operationEndTime := timeAddDay(newFirstPlayTime, 30)
		if operationEndTime != "" {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldFirstPlayEndTime] = []string{operationEndTime}
			log.InfoContextf(ctx, "dataID: %s, 首播时间变更为: %s，设置运营期结束时间为: %s",
				mediaInfo.Id, newFirstPlayTime, operationEndTime)
		}
	}

	return nil
}
