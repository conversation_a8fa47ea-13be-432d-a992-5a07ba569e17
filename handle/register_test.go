package handle

import (
	"reflect"
	"testing"
)

func TestGetTypeCalHandle(t *testing.T) {
	type args struct {
		sType string
	}
	tests := []struct {
		name string
		args args
		want typeCalHandle
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetTypeCalHandle(tt.args.sType); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("GetTypeCalHandle() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRegisterTypeFilter(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			RegisterTypeFilter()
		})
	}
}
