package handle

import (
	"context"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func typeChildrenHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillChildrenUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeChildrenHandle End. update [%+v]", update)
	}()

	// 预热期，未定档更新
	err = ChildrenUnScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 已定档处理计算
	err = ChildrenScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	err = ChildrenFirstTimeCal(ctx, planData, mediaInfo, update)
	if err != nil {
		return err
	}
	// 余热期计算
	err = ChildrenYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}

	// 执行原先的主要计算逻辑
	err = ChildOtherCalAndUpdate(ctx, planData, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "ChildOtherCalAndUpdate End. update [%+v], err [%+v]", update, err)
		return err
	}

	// "修改为正片"的事件处理（放在主逻辑后面，避免影响其他计算逻辑）
	err = HandleCategoryChangeToPositiveEvent(ctx, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "HandleCategoryChangeToPositiveEvent End. update [%+v], err [%+v]", update, err)
		return err
	}
	return nil
}

// FillChildrenUpdateInfo 填充少儿更新信息，获取少儿计算的必要字段
func FillChildrenUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData, update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)

	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	// 获取排播完结时间
	update.PlanEndTime = GetPlanEndTime(planData)

	update.IsTrailer = isChildrenTrailer(update.Category)
	update.IsPositive = isChildrenPositive(update.Category)
	update.HotLevel = parse.GetFieldValue(mediaInfo, parse.FieldHotLevel)
	update.NatureOfCopyrightID = parse.GetFieldValue(mediaInfo, parse.FieldCidCopyright)

	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// ChildOtherCalAndUpdate 少儿其他计算和更新
func ChildOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := CommonYuReOtherCalV2(ctx, planData, update, update.IsPositive)
	if err != nil {
		return err
	}
	// 其他四个相关时间计算
	err = CalOtherTime(ctx, planData, mediaInfo, update)
	if err != nil {
		return err
	}
	// 计算少儿运营期截断时间
	calChildrenOperationSegmentTime(mediaInfo, update)
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

// ChildrenScheduleStatus 少儿定档状态处理逻辑
func ChildrenScheduleStatus(firstPlayTime string, update *model.UpdateInfo) {
	if firstPlayTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	} else if model.CheckTime(firstPlayTime) && HighThanNowTime(firstPlayTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	}
}

// ChildrenScheduleCal 少儿定档计算
func ChildrenScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "ChildrenScheduleCal info start")
	defer log.InfoContextf(ctx, "ChildrenScheduleCal info end")

	// 获取首播时间
	retFirstTime := CommonGetFirstTime(update.MediaFirstPlayTime, update.PlanFirstTime, update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	flag := scheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTime [%+v]", update.MediaPrePareTime)

	// 判断媒资分类是否为预告片
	if update.IsTrailer {
		if retFirstTime == "" || HighThanNowTime(retFirstTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}

	return nil
}

// ChildrenUnScheduleCal 少儿未定档计算
func ChildrenUnScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "ChildrenUnScheduleCal info start")
	defer log.InfoContextf(ctx, "ChildrenUnScheduleCal info end")

	// 提取流水时间,判断是否需要计算
	flag := unScheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTimeText [%+v]", update.MediaPrePareTimeText)

	if update.IsTrailer && update.MediaPrePareTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
	}

	return nil
}

func isChildrenTrailer(category string) bool {
	if _, ok := parse.ChildrenTrailerMap[category]; ok {
		return true
	}
	return false
}

func isChildrenPositive(category string) bool {
	if _, ok := parse.ChildrenPositiveMap[category]; ok {
		return true
	}
	return false
}

// ChildrenYuReCal 少儿余热计算
func ChildrenYuReCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "ChildrenYuReCal info start")
	defer log.InfoContextf(ctx, "ChildrenYuReCal info end")

	yuReThreeEndTime := GetSevenMonthEndTime(update.EndPlayTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeEndTime

	if !model.CheckTime(update.EndPlayTime) {
		log.WarnContextf(ctx, "ChildrenYuReCal playEndTime:[%s] not illegal", update.EndPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, update.EndPlayTime,
		yuReThreeEndTime, parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// ChildrenFirstTimeCal 少儿首播计算
func ChildrenFirstTimeCal(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "ChildrenFirstTimeCal info start")
	defer log.InfoContextf(ctx, "ChildrenFirstTimeCal info end")

	PlayEndTime := GetChildrenEndTime(update)
	update.EndPlayTime = PlayEndTime

	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "Children firstPlayTime:[%s] not illegal", update.FirstPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime,
		PlayEndTime, parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// GetChildrenEndTime 获取少儿运营结束时间
func GetChildrenEndTime(update *model.UpdateInfo) string {
	return childrenEndTime(update.HotLevel, update.NatureOfCopyrightID, update.PlanEndTime, update.MediaEndPlayTime)
}

func childrenEndTime(hotLevel, copyRight, endTime, mediaPlayEndTime string) string {
	var endPlayTime string
	if hotLevel == parse.ValueHotLevelS || hotLevel == parse.ValueHotLevelSPlus {
		endPlayTime = timeAddDay(endTime, 180)
	} else if hotLevel == parse.ValueHotLevelA {
		if copyRight == parse.ValueTencentMake {
			endPlayTime = timeAddDay(endTime, 180)
		}
		endPlayTime = timeAddDay(endTime, 180)
	} else {
		endPlayTime = timeAddDay(endTime, 14)
	}

	if endPlayTime == "" {
		endPlayTime = mediaPlayEndTime
	}
	return endPlayTime
}

func calChildrenOperationSegmentTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) {
	if !needCalChildrenSegmentTime(mediaInfo, update) {
		return
	}
	segmentRes := childrenSegmentTimeLogic(mediaInfo, update)
	if segmentRes == "" {
		return
	}
	update.UpdateFlag = true
	update.FieldMap[parse.FieldOperationSegmentTime] = []string{segmentRes}
}

func needCalChildrenSegmentTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) bool {
	if ok := CommonCheckSegmentTime(mediaInfo, update); !ok {
		return false
	}
	firstPlayTime := update.FirstPlayTime
	operationEndTime := update.EndPlayTime
	// 少人频道运营期结束时间在首播时间之前不进行计算
	if TimeBefore(operationEndTime, firstPlayTime) {
		return false
	}

	update.FirstPlayTime = firstPlayTime
	update.EndPlayTime = operationEndTime
	// 少人频道，首播时间往前修正的（如7月8日首播修正为7月7日首播）不进行重新计算，往后修正的触发重新计算
	_, premiereTimeFlag := parse.GetModifyValue(mediaInfo, parse.FieldFirstPlayTime)
	if premiereTimeFlag {
		oldFirstPlayTime := parse.GetModifyOldValue(mediaInfo, parse.FieldFirstPlayTime)
		return TimeBefore(oldFirstPlayTime, firstPlayTime)
	}
	return true
}

// childrenSegmentTimeLogic 少儿运营期切段时间计算,
/*  计算逻辑
	1. 运营期结束时间-首播时间，≤180天的不触发计算。＞180天的每180天作为一段进行计算
	2. 第一段运营期截段开始时间=首播时间premiere_time
	3. 第一段运营期截段结束时间=首播时间premiere_time+180天
	4. 第二段运营期截段开始时间=首播时间premiere_time+181天
	5. 第二段运营期截段结束时间=首播时间premiere_time+361天且在运营期结束时间operation_end_time内，
	   如在运营期结束时间内则不进行下一阶段计算且填入运营期结束时间。如在运营期结束时间外，填入首播时间+360天后的时间且进行下一截断填写。以此类推
	6. 首播时间往前修正的（7月8日首播修正为7月7日首播）不进行重新计算，往后修正的触发重新计算
       运营期结束时间修改的，重新触发所有切断修改（理论上只会影响最后一段，是否全部重算开发决定）。
*/
func childrenSegmentTimeLogic(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) string {
	firstPlayTime := update.FirstPlayTime
	operationEndTime := update.EndPlayTime
	firstSegmentTime := timeAddDay(firstPlayTime, parse.MonthDays*6)
	// 运营期结束时间-首播时间小于第一段时间不进行计算，有值的需要置空
	if TimeBefore(operationEndTime, firstSegmentTime) {
		if parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime) != "" {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldOperationSegmentTime] = []string{}
		}
		return ""
	}
	var segmentRes []string
	CommonSegmentTimeCal(firstPlayTime, operationEndTime, parse.MonthDays*6, &segmentRes)
	return strings.Join(segmentRes, ";")
}
