package handle

import (
	"context"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func typeTVHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillTVUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeTVHandle end. update [%+v]", update)
	}()

	// 预热期，未定档更新
	err = TVUnScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 已定档处理计算
	err = TVScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 电视剧首播时间计算
	err = TVFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}
	// 余热期计算
	err = TVYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}

	// 执行原先的主要计算逻辑
	err = TVOtherCalAndUpdate(ctx, planData, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "TVOtherCalAndUpdate End. update [%+v], err [%+v]", update, err)
		return err
	}

	// "修改为正片"的事件处理（放在主逻辑后面，避免影响其他计算逻辑）
	err = HandleCategoryChangeToPositiveEvent(ctx, mediaInfo, update)
	if err != nil {
		log.ErrorContextf(ctx, "HandleCategoryChangeToPositiveEvent End. update [%+v], err [%+v]", update, err)
		return err
	}
	return nil
}

// FillTVUpdateInfo 填充tv更新信息，获取电视剧ip计算的必要字段
func FillTVUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData,
	update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.EndTime = getEndTime(parse.GetFieldValue(mediaInfo, parse.FieldEndTime),
		parse.GetFieldValue(mediaInfo, parse.FieldTVEndTime))
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.PArea = parse.GetFieldValue(mediaInfo, parse.FieldPArea)
	update.NetPublishTime = parse.GetFieldValue(mediaInfo, parse.FieldNetPublishTime)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)
	// 获取电视剧出品时间
	productTime, err := GetTVProductTime(ctx, planData.ID)
	if err != nil {
		return err
	}

	update.ProductTime = productTime
	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	update.IsTrailer = isTVTrailer(update.Category)
	// https://tapd.woa.com/10093801/prong/stories/view/1010093801117973470，新增正片判断。2024-06-13改。
	update.IsPositive = isTVPositive(update.Category)

	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// TVOtherCalAndUpdate 电视剧计算函数
func TVOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	// 片库-余热期三月外处理
	update.YuReEndTime = GetTVYuReEndTime(update.EndTime, update.EndPlayTime, update.ProductTime, update.Category)
	err := CommonThreeMonthYuReCalV2(ctx, planData, update.YuReEndTime, update.YuReThreeMonthTime, update,
		update.IsPositive)
	if err != nil {
		return err
	}
	// 长尾期处理
	err = TVChangWeiCal(ctx, planData, update)
	if err != nil {
		return err
	}

	// 长尾期结束时间计算
	err = CommonThreeChangWeiEndCalV2(ctx, planData, update, update.IsPositive)
	if err != nil {
		return err
	}

	// 电视剧免费完结时间，由【排播表上非会员可看最后一集的时间】，改为【取同步剧完结时间】
	planData.FreeEndDate = update.EndTime
	// 其他四个相关时间计算
	err = CalOtherTime(ctx, planData, mediaInfo, update)
	if err != nil {
		return err
	}
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

// TVChangWeiCal 电视剧长尾期计算
func TVChangWeiCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	// 长尾期三年内处理
	err := CommonChangWeiCalV2(ctx, planData, update, update.IsPositive)
	if err != nil {
		return err
	}

	if !update.IsPositive {
		return nil
	}

	if update.PArea == parse.ValueInnerArea {
		// 出品时间不能为空，为空不计算
		if !model.CheckTime(update.ProductTime) {
			return nil
		}
		if !IsTwoYear(update.ProductTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueChangWeiOperation}
		}
	} else {
		//  腾讯首播时间不能为空，为空不计算
		if update.NetPublishTime == "" {
			return nil
		}
		if !IsTwoYear(update.NetPublishTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueChangWeiOperation}
		}
	}
	return nil
}

// TVScheduleCal 电视剧定档计算
func TVScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "TVScheduleCal info start")
	defer log.InfoContextf(ctx, "TVScheduleCal info end.")

	// 获取首播时间
	retFirstTime := CommonGetFirstTime(update.MediaFirstPlayTime, update.PlanFirstTime, update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	flag := scheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}

	log.InfoContextf(ctx, "MediaPrePareTime [%+v]", update.MediaPrePareTime)

	// 判断媒资分类是否为预告片
	if update.IsTrailer {
		if retFirstTime == "" || HighThanNowTime(retFirstTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}

	return nil
}

// TVUnScheduleCal 电视剧未定档计算
func TVUnScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "TVUnScheduleCal info start")
	defer log.InfoContextf(ctx, "TVUnScheduleCal info end.")

	flag := unScheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTimeText [%+v]", update.MediaPrePareTimeText)

	if update.IsTrailer && update.MediaPrePareTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
	}

	return nil
}

func isTVTrailer(category string) bool {
	if _, ok := parse.TVTrailerMap[category]; ok {
		return true
	}
	return false
}

func isTVPositive(category string) bool {
	if _, ok := parse.TVPositiveMap[category]; ok {
		return true
	}
	return false
}

// TVYuReCal 余热期相关计算
func TVYuReCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "TVYuReCal info start")
	defer log.InfoContextf(ctx, "TVYuReCal info end")

	playEndTime := GetTVPlayEndTime(update.FirstPlayTime, update.EndTime, update.ProductTime, update.MediaEndPlayTime,
		update.Category)
	update.EndPlayTime = playEndTime
	yuReThreeMonthTime := GetFourMonthEndTime(playEndTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeMonthTime

	if !model.CheckTime(playEndTime) {
		log.WarnContextf(ctx, "playEndTime:[%s] not illegal!", playEndTime)
		return nil
	}

	log.InfoContextf(ctx, "TVYuReCal firstPlayTime:[%s], mediaPlayEndTime:[%s], endTime:[%s], playEndTime:[%s]"+
		"yuReThreeMonthTime:[%s]", update.FirstPlayTime, update.MediaEndPlayTime, update.EndTime, playEndTime,
		yuReThreeMonthTime)
	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, playEndTime, yuReThreeMonthTime,
		parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// GetTVPlayEndTime 获取电视剧的运营结束时间
// IP实时状态: 短剧、运营期计算逻辑调整：https://tapd.woa.com/MediaContent/prong/stories/view/1010093801883711105
// https://tapd.woa.com/10093801/prong/stories/view/1010093801116637219,2024-04-17更新
func GetTVPlayEndTime(firstTime, endTime, productTime, mediaPlayEndTime, category string) string {
	if _, ok := parse.TVShortMap[category]; ok {
		return getTVShortPlayEndTime(firstTime, endTime, mediaPlayEndTime)
	}
	return getNoneTVShortPlayEndTime(firstTime, endTime, productTime, mediaPlayEndTime)
}

func getTVShortPlayEndTime(firstTime, endTime, mediaPlayEndTime string) string {
	return commonTVTimeCal(endTime, 7, firstTime, 60, mediaPlayEndTime)
}

func getNoneTVShortPlayEndTime(firstTime, endTime, productTime, mediaPlayEndTime string) string {
	var (
		pTime int
		err   error
	)
	if len(productTime) >= 4 {
		pTime, err = strconv.Atoi(productTime[:4])
		if err != nil {
			return ""
		}
	}

	if pTime <= 2023 {
		return commonTVTimeCal(endTime, 14, firstTime, 60, mediaPlayEndTime)
	}

	return MaxTime(timeAddDay(endTime, 14), timeAddDay(firstTime, 54), mediaPlayEndTime)
}

func commonTVTimeCal(endTime string, endTimeDayNum int, firstTime string, firstTimeDayNum int,
	mediaPlayEndTime string) string {
	if model.CheckTime(endTime) {
		return timeAddDay(endTime, endTimeDayNum)
	}
	if model.CheckTime(firstTime) {
		return timeAddDay(firstTime, firstTimeDayNum)
	}
	return mediaPlayEndTime
}

// GetTVFirstTime 获取电视剧首播时间更新
// 优先级：即将上映时间（时间） > 排播字段 > 媒资存储字段
func GetTVFirstTime(mediaFirstPlayTime, firstStartTime string, update *model.UpdateInfo) string {
	var retFirstTime = mediaFirstPlayTime

	// 优先读取【即将上映时间（时间）】字段
	if model.CheckTime(update.MediaPrePareTime) {
		if update.MediaPrePareTime != mediaFirstPlayTime {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldFirstPlayTime] = []string{update.MediaPrePareTime}
		}
		return update.MediaPrePareTime
	}

	// 如果即将上映时间为空，则走现有的计算逻辑
	// 两个字段不同的时候，优先取排播字段，否则直接取媒资存储字段作为首播时间
	if mediaFirstPlayTime != firstStartTime && model.CheckTime(firstStartTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldFirstPlayTime] = []string{firstStartTime}
		retFirstTime = firstStartTime
	}
	return retFirstTime
}

// TVFirstTimeCal 电视剧首播时间计算
func TVFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "TVFirstTimeCal info start")
	defer log.InfoContextf(ctx, "TVFirstTimeCal info end")

	err := tvFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}

	// 需求单：https://tapd.woa.com/10093801/prong/stories/view/1010093801115235184
	if !update.IsPositive {
		return nil
	}
	productTime := update.ProductTime
	netPublishTime := update.NetPublishTime
	if netPublishTime == "" {
		return nil
	}
	if update.PArea == parse.ValueInnerArea {
		if !model.CheckTime(productTime) {
			return nil
		}
		if IsTwoYear(productTime) && IsTwoYear(netPublishTime) {
			// 判断这个时间，为运营期
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueOperatingPeriod}
		}
	} else {
		if IsTwoYear(netPublishTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueOperatingPeriod}
		}
	}

	return nil
}

func tvFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	playEndTime := GetTVPlayEndTime(update.FirstPlayTime, update.EndTime, update.ProductTime, update.MediaEndPlayTime,
		update.Category)
	update.EndPlayTime = playEndTime

	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "Movie FirstPlayTime:[%s] not illegal!", update.FirstPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime, playEndTime,
		parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, update.IsPositive)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// IsTwoYear 判断是否在近两年时间
func IsTwoYear(t string) bool {
	// 特殊逻辑，为空表示在两年内
	if t == "" {
		return true
	}

	t = fixTime(t)
	t1, _ := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	return time.Now().AddDate(-2, 0, 0).Before(t1)
}

func fixTime(t string) string {
	if len(t) == 19 {
		return t
	}
	if len(t) == 10 {
		return t + " 00:00:00"
	}
	return ""
}

// GetTVProductTime 获取电视剧出品时间
func GetTVProductTime(ctx context.Context, cid string) (string, error) {
	infos, err := model.GetAllMediaFieldInfos(ctx, []string{cid}, parse.CidType)
	if err != nil {
		return "", err
	}
	txt, _ := model.GetIdSetTextFromUniversal(cid, parse.FieldTVPublishTime, infos)
	if len(txt) != 4 {
		return "", nil
	}

	return txt + "-01-01 00:00:00", nil
}

// GetTVYuReEndTime 获取tv余热期结束时间
func GetTVYuReEndTime(endTime, playEndTime, productTime, category string) string {
	yuReEndTime := timeAddDay(playEndTime, parse.YearDays)
	// 完结时间为空，均用运营期完结时间兜底
	if endTime == "" {
		return yuReEndTime
	}
	if _, ok := parse.TVShortMap[category]; ok {
		// 短剧为完结时间+365天
		return timeAddDay(endTime, parse.YearDays)
	}

	if len(productTime) > 4 {
		pTime, err := strconv.Atoi(productTime[:4])
		if err != nil {
			return yuReEndTime
		}
		if pTime <= 2023 {
			return timeAddDay(endTime, parse.YearDays)
		}
	}

	return yuReEndTime
}
