package handle

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func typeSportHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillSportUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeChildrenHandle End. update [%+v]", update)
	}()
	// 已定档处理计算
	err = SportScheduleCal(ctx, update)
	if err != nil {
		return err
	}
	// 首播时间计算
	err = SportFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}
	// 余热期计算
	err = SportYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}
	return SportOtherCalAndUpdate(ctx, planData, mediaInfo, update)
}

// FillSportUpdateInfo 填充体育更新信息，获取体育计算的必要字段
func FillSportUpdateInfo(ctx context.Context, mediaInfo msghub.MediaInfo, planData *model.PlanData,
	update *model.UpdateInfo) error {
	update.MediaUnScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTimeUn)
	update.MediaScheduleTime = parse.GetFieldValue(mediaInfo, parse.FieldPreHeatTime)
	update.MediaPrePareTimeText = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeText)
	update.MediaPrePareTime = parse.GetFieldValue(mediaInfo, parse.FieldPrePareTimeTime)
	update.Category = parse.GetFieldValue(mediaInfo, parse.FieldCategory)
	update.MediaEndPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	update.MediaFirstPlayTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	update.MediaYuReThreeMonthTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeMonthEndAfterHeatTime)
	update.MediaYuReEndTime = parse.GetFieldValue(mediaInfo, parse.FieldAfterHeatEndTime)
	update.MediaThreeYearTailTime = parse.GetFieldValue(mediaInfo, parse.FieldThreeYearTailTime)
	update.MediaIPOnlineStatus = parse.GetFieldValue(mediaInfo, parse.FieldIPOnlineStatus)
	update.MediaEndTimeForVIP = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForVIP)
	update.MediaEndTimeForFree = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForFree)
	update.OperationSegmentTime = parse.GetFieldValue(mediaInfo, parse.FieldOperationSegmentTime)
	// 获取排播首播时间
	update.PlanFirstTime = GetFirstStartTime(planData)
	// 获取排播完结时间
	update.PlanEndTime = GetPlanEndTime(planData)

	update.LastPubTime = parse.GetFieldValue(mediaInfo, parse.FieldCidLastPubtime)
	update.PublishDate = parse.GetFieldValue(mediaInfo, parse.FieldPublishDateX)
	update.VideoCheckupTime = parse.GetFieldValue(mediaInfo, parse.FieldCheckupTime)
	update.SportsColumnType = parse.GetFieldValue(mediaInfo, parse.FieldSportsColumnType)
	update.SportsMatchID, _ = GetMatchIDTime(ctx, mediaInfo.Id)
	update.IsTrailer = false

	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)

	return nil
}

// SportOtherCalAndUpdate 体育其他计算和更新
func SportOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := CommonYuReOtherCalV2(ctx, planData, update, !update.IsTrailer)
	if err != nil {
		return err
	}

	CalEndTime(ctx, planData, mediaInfo, update.FirstPlayTime, update, NotNeedCalFreeTimeFlag)
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

func sportSchedule(firstPlayTime string, update *model.UpdateInfo) {
	if model.CheckTime(firstPlayTime) && HighThanNowTime(firstPlayTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	}
}

// SportScheduleCal 体育已定档计算
func SportScheduleCal(ctx context.Context, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "SportScheduleCal info start")
	defer log.InfoContextf(ctx, "SportScheduleCal info end")

	// 获取首播时间
	retFirstTime := GetSportFirstTime(update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	if !model.CheckTime(update.VideoCheckupTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{}
		log.WarnContextf(ctx, "Sport scheduleTime:[%s] not illegal", update.VideoCheckupTime)
		return nil
	}

	commScheduleTime(update.VideoCheckupTime, update.MediaScheduleTime, update)
	update.ScheduleTime = update.VideoCheckupTime

	if update.SportsColumnType != "" {
		if retFirstTime == "" || HighThanNowTime(retFirstTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}
	return nil
}

// SportYuReCal 体育余热计算
func SportYuReCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "SportYuReCal info start")
	defer log.InfoContextf(ctx, "SportYuReCal info end")

	yuReThreeEndTime := GetThreeMonthEndTime(update.EndPlayTime, update.MediaYuReThreeMonthTime)
	update.YuReThreeMonthTime = yuReThreeEndTime

	if !model.CheckTime(update.EndPlayTime) {
		log.WarnContextf(ctx, "Sport playEndTime:[%s] not illegal!", update.EndPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaEndPlayTime, update.EndPlayTime,
		yuReThreeEndTime, parse.FieldFirstPlayEndTime, parse.ValueYuReOperation, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// SportFirstTimeCal 体育首播时间计算
func SportFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "SportFirstTimeCal info start")
	defer log.InfoContextf(ctx, "SportFirstTimeCal info end")

	endPlayTime := GetSportEndTime(update.FirstPlayTime, update.MediaEndPlayTime)
	update.EndPlayTime = endPlayTime

	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "Sport firstPlayTime:[%s] not illegal", update.FirstPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime,
		endPlayTime, parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// GetSportFirstTime 获取体育首播时间
// ① 【比赛ID】中的时间
// ② 取不到值时填入【即将上映时间(时间)】
// ③ 取不到值时填入 排播表播出时间
// ④ 取不到值时填入【播出日期】
func GetSportFirstTime(update *model.UpdateInfo) (firstTime string) {
	firstTime = GetTimeFromSportsMatch(update.SportsMatchID)
	if firstTime != "" {
		if len(firstTime) == 16 {
			firstTime = firstTime + ":00"
			return
		}
	}

	if model.CheckTime(update.MediaPrePareTime) {
		firstTime = update.MediaPrePareTime
		return
	}

	firstTime = update.PlanFirstTime
	if firstTime != "" {
		return
	}
	if len(update.PublishDate) == 10 {
		firstTime = update.PublishDate + " 23:59:59"
		return
	}

	return update.MediaFirstPlayTime
}

// GetTimeFromSportsMatch 获取比赛id中的时间 SportsMatchID的值举例：【100000:62944010】独行侠 VS 快船 2024-04-22 03:30(100000:62944010)
func GetTimeFromSportsMatch(SportsMatchID string) string {
	// 匹配格式为 YYYY-MM-DD HH:MM 的日期和时间
	// 使用正则表达式查找日期时间字符串
	dateTimeStr := regexp.MustCompile(`\d{4}-\d{2}-\d{2} \d{2}:\d{2}`).FindString(SportsMatchID)
	if dateTimeStr == "" {
		fmt.Println("No date and time found in the string")
		return ""
	}

	// 解析日期时间字符串
	layout := "2006-01-02 15:04" // Go的参考时间格式
	t, err := time.Parse(layout, dateTimeStr)
	if err != nil {
		return ""
	}
	return t.Format(layout)
}

// GetSportEndTime 获取体育运营结束时间
func GetSportEndTime(firstTime string, mediaPlanEndTime string) string {
	if model.CheckTime(firstTime) {
		return timeAddDay(firstTime, 2)
	}
	return mediaPlanEndTime
}

// GetMatchIDTime 获取场次时间
func GetMatchIDTime(ctx context.Context, cid string) (string, error) {
	infos, err := model.GetAllMediaFieldInfos(ctx, []string{cid}, parse.CidType)
	if err != nil {
		return "", err
	}
	txt, _ := model.GetIdSetTextFromUniversal(cid, parse.FieldSportsMatchID, infos)
	return txt, nil
}
