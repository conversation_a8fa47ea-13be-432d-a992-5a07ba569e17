package handle

import (
	"context"
	"testing"
	"time"

	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	"github.com/stretchr/testify/assert"
)

// MockMediaInfo 创建mock的MediaInfo
func createMockMediaInfo(id string, fieldInfos map[string]msghub.FieldInfo, modifyFieldInfos map[string]msghub.ModifyFieldInfo, timestamp int32) msghub.MediaInfo {
	return msghub.MediaInfo{
		Id:               id,
		FieldInfos:       fieldInfos,
		ModifyFieldInfos: modifyFieldInfos,
		TimeStamp:        int(timestamp),
	}
}

// createFieldInfo 创建字段信息
func createFieldInfo(value string) msghub.FieldInfo {
	return msghub.FieldInfo{
		Value: value,
	}
}

// createModifyFieldInfo 创建变更字段信息
func createModifyFieldInfo(old, new string) msghub.ModifyFieldInfo {
	return msghub.ModifyFieldInfo{
		Old: old,
		New: new,
	}
}

// Test_isCurrentTimeAfterOrEqual 测试时间比较函数
func Test_isCurrentTimeAfterOrEqual(t *testing.T) {
	tests := []struct {
		name     string
		timeStr  string
		expected bool
	}{
		{
			name:     "过去时间",
			timeStr:  "2020-01-01 00:00:00",
			expected: true,
		},
		{
			name:     "未来时间",
			timeStr:  "2099-12-31 23:59:59",
			expected: false,
		},
		{
			name:     "无效时间格式",
			timeStr:  "invalid-time",
			expected: false,
		},
		{
			name:     "空字符串",
			timeStr:  "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isCurrentTimeAfterOrEqual(tt.timeStr)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_isShortDramaTrailer 测试预告片判断函数
func Test_isShortDramaTrailer(t *testing.T) {
	tests := []struct {
		name     string
		category string
		expected bool
	}{
		{
			name:     "短剧预告片",
			category: parse.ValueCategoryShortDramaTrailer,
			expected: true,
		},
		{
			name:     "短剧正片",
			category: parse.ValueCategoryShortDramaMain,
			expected: false,
		},
		{
			name:     "其他品类",
			category: "12345",
			expected: false,
		},
		{
			name:     "空字符串",
			category: "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isShortDramaTrailer(tt.category)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_isShortDramaMain 测试正片判断函数
func Test_isShortDramaMain(t *testing.T) {
	tests := []struct {
		name     string
		category string
		expected bool
	}{
		{
			name:     "短剧正片",
			category: parse.ValueCategoryShortDramaMain,
			expected: true,
		},
		{
			name:     "短剧预告片",
			category: parse.ValueCategoryShortDramaTrailer,
			expected: false,
		},
		{
			name:     "其他品类",
			category: "12345",
			expected: false,
		},
		{
			name:     "空字符串",
			category: "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isShortDramaMain(tt.category)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_isAuditPassed 测试审核通过判断函数
func Test_isAuditPassed(t *testing.T) {
	tests := []struct {
		name       string
		auditState string
		expected   bool
	}{
		{
			name:       "审核通过",
			auditState: parse.ValueTotalAuditStatePassed,
			expected:   true,
		},
		{
			name:       "审核未通过",
			auditState: "102",
			expected:   false,
		},
		{
			name:       "空字符串",
			auditState: "",
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isAuditPassed(tt.auditState)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_isOnline 测试上架状态判断函数
func Test_isOnline(t *testing.T) {
	tests := []struct {
		name         string
		checkupState string
		expected     bool
	}{
		{
			name:         "已上架",
			checkupState: parse.ValueCheckupStateOnline,
			expected:     true,
		},
		{
			name:         "未上架",
			checkupState: "1",
			expected:     false,
		},
		{
			name:         "空字符串",
			checkupState: "",
			expected:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isOnline(tt.checkupState)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_IsShortDrama 测试短剧判断函数
func Test_IsShortDrama(t *testing.T) {
	tests := []struct {
		name      string
		mediaInfo msghub.MediaInfo
		expected  bool
	}{
		{
			name: "短剧内容-会员分账",
			mediaInfo: createMockMediaInfo("test1", map[string]msghub.FieldInfo{
				parse.FieldContentForm:      createFieldInfo(parse.ValueContentFormMidVideo),
				parse.FieldCpMidPaymentType: createFieldInfo(parse.ValueCpPaymentMicroShortDramaVipShare),
			}, nil, 0),
			expected: true,
		},
		{
			name: "短剧内容-单点",
			mediaInfo: createMockMediaInfo("test2", map[string]msghub.FieldInfo{
				parse.FieldContentForm:      createFieldInfo(parse.ValueContentFormMidVideo),
				parse.FieldCpMidPaymentType: createFieldInfo(parse.ValueCpPaymentMicroShortDramaSinglePay),
			}, nil, 0),
			expected: true,
		},
		{
			name: "短剧内容-免费",
			mediaInfo: createMockMediaInfo("test3", map[string]msghub.FieldInfo{
				parse.FieldContentForm:      createFieldInfo(parse.ValueContentFormMidVideo),
				parse.FieldCpMidPaymentType: createFieldInfo(parse.ValueCpPaymentMicroShortDramaFree),
			}, nil, 0),
			expected: true,
		},
		{
			name: "非中视频内容",
			mediaInfo: createMockMediaInfo("test4", map[string]msghub.FieldInfo{
				parse.FieldContentForm:      createFieldInfo("other"),
				parse.FieldCpMidPaymentType: createFieldInfo(parse.ValueCpPaymentMicroShortDramaVipShare),
			}, nil, 0),
			expected: false,
		},
		{
			name: "非短剧付费模式",
			mediaInfo: createMockMediaInfo("test5", map[string]msghub.FieldInfo{
				parse.FieldContentForm:      createFieldInfo(parse.ValueContentFormMidVideo),
				parse.FieldCpMidPaymentType: createFieldInfo("other"),
			}, nil, 0),
			expected: false,
		},
		{
			name:      "缺少字段信息",
			mediaInfo: createMockMediaInfo("test6", map[string]msghub.FieldInfo{}, nil, 0),
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsShortDrama(tt.mediaInfo)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_calShortDramaScheduleTime 测试已定档预热时间计算
func Test_calShortDramaScheduleTime(t *testing.T) {
	ctx := context.Background()
	currentTime := int32(time.Now().Unix())

	tests := []struct {
		name             string
		mediaInfo        msghub.MediaInfo
		expectedUpdate   bool
		expectedLogCount int
	}{
		{
			name: "审核状态变更为通过且当前品类为预告片",
			mediaInfo: createMockMediaInfo("test1",
				map[string]msghub.FieldInfo{
					parse.FieldCategory: createFieldInfo(parse.ValueCategoryShortDramaTrailer),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldTotalAuditState: createModifyFieldInfo("100", parse.ValueTotalAuditStatePassed),
				},
				currentTime),
			expectedUpdate: true,
		},
		{
			name: "品类变更为预告片且当前审核状态为通过",
			mediaInfo: createMockMediaInfo("test2",
				map[string]msghub.FieldInfo{
					parse.FieldTotalAuditState: createFieldInfo(parse.ValueTotalAuditStatePassed),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldCategory: createModifyFieldInfo("other", parse.ValueCategoryShortDramaTrailer),
				},
				currentTime),
			expectedUpdate: true,
		},
		{
			name: "审核状态变更但品类不对",
			mediaInfo: createMockMediaInfo("test3",
				map[string]msghub.FieldInfo{
					parse.FieldCategory: createFieldInfo("other"),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldTotalAuditState: createModifyFieldInfo("100", parse.ValueTotalAuditStatePassed),
				},
				currentTime),
			expectedUpdate: false,
		},
		{
			name: "品类变更但审核状态不对",
			mediaInfo: createMockMediaInfo("test4",
				map[string]msghub.FieldInfo{
					parse.FieldTotalAuditState: createFieldInfo("100"),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldCategory: createModifyFieldInfo("other", parse.ValueCategoryShortDramaTrailer),
				},
				currentTime),
			expectedUpdate: false,
		},
		{
			name: "无变更",
			mediaInfo: createMockMediaInfo("test5",
				map[string]msghub.FieldInfo{
					parse.FieldCategory:        createFieldInfo(parse.ValueCategoryShortDramaTrailer),
					parse.FieldTotalAuditState: createFieldInfo(parse.ValueTotalAuditStatePassed),
				},
				nil,
				currentTime),
			expectedUpdate: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			update := &model.UpdateInfo{
				FieldMap: make(map[string][]string),
			}

			err := calShortDramaScheduleTime(ctx, tt.mediaInfo, update)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedUpdate, update.UpdateFlag)

			if tt.expectedUpdate {
				assert.NotEmpty(t, update.FieldMap[parse.FieldPreHeatTime])
			}
		})
	}
}

// Test_calShortDramaScheduleTime_WithExistingTime 测试已定档预热时间计算（原时间不为空的情况）
func Test_calShortDramaScheduleTime_WithExistingTime(t *testing.T) {
	ctx := context.Background()
	currentTime := int32(time.Now().Unix())
	existingTime := "2024-01-01 10:00:00"

	tests := []struct {
		name                   string
		mediaInfo              msghub.MediaInfo
		existingScheduleTime   string
		expectedUpdate         bool
		expectedSkipLog        bool
	}{
		{
			name: "审核状态变更为通过且当前品类为预告片，但原已定档预热时间不为空",
			mediaInfo: createMockMediaInfo("test1",
				map[string]msghub.FieldInfo{
					parse.FieldCategory: createFieldInfo(parse.ValueCategoryShortDramaTrailer),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldTotalAuditState: createModifyFieldInfo("100", parse.ValueTotalAuditStatePassed),
				},
				currentTime),
			existingScheduleTime: existingTime,
			expectedUpdate:       false,
			expectedSkipLog:      true,
		},
		{
			name: "审核状态变更为通过且当前品类为预告片，原已定档预热时间为空",
			mediaInfo: createMockMediaInfo("test2",
				map[string]msghub.FieldInfo{
					parse.FieldCategory: createFieldInfo(parse.ValueCategoryShortDramaTrailer),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldTotalAuditState: createModifyFieldInfo("100", parse.ValueTotalAuditStatePassed),
				},
				currentTime),
			existingScheduleTime: "",
			expectedUpdate:       true,
			expectedSkipLog:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			update := &model.UpdateInfo{
				FieldMap:          make(map[string][]string),
				MediaScheduleTime: tt.existingScheduleTime,
			}

			err := calShortDramaScheduleTime(ctx, tt.mediaInfo, update)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedUpdate, update.UpdateFlag)

			if tt.expectedUpdate {
				assert.NotEmpty(t, update.FieldMap[parse.FieldPreHeatTime])
			} else if tt.expectedSkipLog {
				assert.Empty(t, update.FieldMap[parse.FieldPreHeatTime])
			}
		})
	}
}

// Test_calShortDramaFirstPlayTime 测试首播时间计算
func Test_calShortDramaFirstPlayTime(t *testing.T) {
	ctx := context.Background()
	currentTime := int32(time.Now().Unix())

	tests := []struct {
		name           string
		mediaInfo      msghub.MediaInfo
		expectedUpdate bool
	}{
		{
			name: "专辑状态变更为已上架且当前品类为正片",
			mediaInfo: createMockMediaInfo("test1",
				map[string]msghub.FieldInfo{
					parse.FieldCategory: createFieldInfo(parse.ValueCategoryShortDramaMain),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldCheckUpState: createModifyFieldInfo("1", parse.ValueCheckupStateOnline),
				},
				currentTime),
			expectedUpdate: true,
		},
		{
			name: "品类变更为正片且当前状态为已上架",
			mediaInfo: createMockMediaInfo("test2",
				map[string]msghub.FieldInfo{
					parse.FieldCheckUpState: createFieldInfo(parse.ValueCheckupStateOnline),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldCategory: createModifyFieldInfo("other", parse.ValueCategoryShortDramaMain),
				},
				currentTime),
			expectedUpdate: true,
		},
		{
			name: "状态变更但品类不对",
			mediaInfo: createMockMediaInfo("test3",
				map[string]msghub.FieldInfo{
					parse.FieldCategory: createFieldInfo("other"),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldCheckUpState: createModifyFieldInfo("1", parse.ValueCheckupStateOnline),
				},
				currentTime),
			expectedUpdate: false,
		},
		{
			name: "无变更",
			mediaInfo: createMockMediaInfo("test4",
				map[string]msghub.FieldInfo{
					parse.FieldCategory:     createFieldInfo(parse.ValueCategoryShortDramaMain),
					parse.FieldCheckUpState: createFieldInfo(parse.ValueCheckupStateOnline),
				},
				nil,
				currentTime),
			expectedUpdate: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			update := &model.UpdateInfo{
				FieldMap: make(map[string][]string),
			}

			err := calShortDramaFirstPlayTime(ctx, tt.mediaInfo, update)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedUpdate, update.UpdateFlag)

			if tt.expectedUpdate {
				assert.NotEmpty(t, update.FieldMap[parse.FieldFirstPlayTime])
			}
		})
	}
}

// Test_calShortDramaFirstPlayTime_WithExistingTime 测试首播时间计算（原时间不为空的情况）
func Test_calShortDramaFirstPlayTime_WithExistingTime(t *testing.T) {
	ctx := context.Background()
	currentTime := int32(time.Now().Unix())
	existingTime := "2024-01-01 10:00:00"

	tests := []struct {
		name                    string
		mediaInfo               msghub.MediaInfo
		existingFirstPlayTime   string
		expectedUpdate          bool
		expectedSkipLog         bool
	}{
		{
			name: "专辑状态变更为已上架且当前品类为正片，但原首播时间不为空",
			mediaInfo: createMockMediaInfo("test1",
				map[string]msghub.FieldInfo{
					parse.FieldCategory: createFieldInfo(parse.ValueCategoryShortDramaMain),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldCheckUpState: createModifyFieldInfo("1", parse.ValueCheckupStateOnline),
				},
				currentTime),
			existingFirstPlayTime: existingTime,
			expectedUpdate:        false,
			expectedSkipLog:       true,
		},
		{
			name: "专辑状态变更为已上架且当前品类为正片，原首播时间为空",
			mediaInfo: createMockMediaInfo("test2",
				map[string]msghub.FieldInfo{
					parse.FieldCategory: createFieldInfo(parse.ValueCategoryShortDramaMain),
				},
				map[string]msghub.ModifyFieldInfo{
					parse.FieldCheckUpState: createModifyFieldInfo("1", parse.ValueCheckupStateOnline),
				},
				currentTime),
			existingFirstPlayTime: "",
			expectedUpdate:        true,
			expectedSkipLog:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			update := &model.UpdateInfo{
				FieldMap:             make(map[string][]string),
				MediaFirstPlayTime:   tt.existingFirstPlayTime,
			}

			err := calShortDramaFirstPlayTime(ctx, tt.mediaInfo, update)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedUpdate, update.UpdateFlag)

			if tt.expectedUpdate {
				assert.NotEmpty(t, update.FieldMap[parse.FieldFirstPlayTime])
			} else if tt.expectedSkipLog {
				assert.Empty(t, update.FieldMap[parse.FieldFirstPlayTime])
			}
		})
	}
}

// Test_calShortDramaOperationEndTime 测试运营期结束时间计算
func Test_calShortDramaOperationEndTime(t *testing.T) {
	ctx := context.Background()
	validTime := "2024-01-01 10:00:00"
	invalidTime := "invalid-time"

	tests := []struct {
		name           string
		mediaInfo      msghub.MediaInfo
		expectedUpdate bool
	}{
		{
			name: "首播时间变更为有效时间",
			mediaInfo: createMockMediaInfo("test1",
				nil,
				map[string]msghub.ModifyFieldInfo{
					parse.FieldFirstPlayTime: createModifyFieldInfo("", validTime),
				},
				0),
			expectedUpdate: true,
		},
		{
			name: "首播时间变更为无效时间",
			mediaInfo: createMockMediaInfo("test2",
				nil,
				map[string]msghub.ModifyFieldInfo{
					parse.FieldFirstPlayTime: createModifyFieldInfo("", invalidTime),
				},
				0),
			expectedUpdate: false,
		},
		{
			name: "首播时间无变更",
			mediaInfo: createMockMediaInfo("test3",
				map[string]msghub.FieldInfo{
					parse.FieldFirstPlayTime: createFieldInfo(validTime),
				},
				nil,
				0),
			expectedUpdate: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			update := &model.UpdateInfo{
				FieldMap: make(map[string][]string),
			}

			err := calShortDramaOperationEndTime(ctx, tt.mediaInfo, update)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedUpdate, update.UpdateFlag)

			if tt.expectedUpdate {
				assert.NotEmpty(t, update.FieldMap[parse.FieldFirstPlayEndTime])
			}
		})
	}
}

// Test_ShortDramaStatusCal 测试短剧状态计算主函数
func Test_ShortDramaStatusCal(t *testing.T) {
	// 由于这个函数依赖makeMsgInfo，我们先跳过集成测试
	t.Run("integration test", func(t *testing.T) {
		t.Skip("Skipping integration test - requires mocking makeMsgInfo function")
	})
}

// Test_shortDramaStatusCalLogic 测试短剧状态计算核心逻辑
func Test_shortDramaStatusCalLogic(t *testing.T) {
	ctx := context.Background()

	// 获取当前时间和相关时间点
	now := time.Now()
	pastTime := now.Add(-2 * time.Hour).Format("2006-01-02 15:04:05")
	futureTime := now.Add(2 * time.Hour).Format("2006-01-02 15:04:05")
	farFutureTime := now.Add(24 * time.Hour).Format("2006-01-02 15:04:05")

	tests := []struct {
		name                string
		category            string
		totalAuditState     string
		checkupState        string
		mediaFirstPlayTime  string
		mediaEndPlayTime    string
		mediaID             string
		expectedUpdate      bool
		expectedStatus      string
	}{
		{
			name:            "预热期：专辑审核状态=审核通过 && 专辑品类=短剧预告片",
			category:        parse.ValueCategoryShortDramaTrailer,
			totalAuditState: parse.ValueTotalAuditStatePassed,
			checkupState:    "1", // 不影响预告片逻辑
			mediaID:         "test1",
			expectedUpdate:  true,
			expectedStatus:  parse.ValueSchedule,
		},
		{
			name:               "运营期：专辑状态=已上架 && 专辑品类=短剧正片 && 在运营期内",
			category:           parse.ValueCategoryShortDramaMain,
			totalAuditState:    "100", // 不影响正片逻辑
			checkupState:       parse.ValueCheckupStateOnline,
			mediaFirstPlayTime: pastTime,    // 首播时间在过去
			mediaEndPlayTime:   futureTime,  // 运营期结束时间在未来
			mediaID:            "test2",
			expectedUpdate:     true,
			expectedStatus:     parse.ValueOperatingPeriod,
		},
		{
			name:               "片库期：专辑状态=已上架 && 专辑品类=短剧正片 && 超过运营期",
			category:           parse.ValueCategoryShortDramaMain,
			totalAuditState:    "100",
			checkupState:       parse.ValueCheckupStateOnline,
			mediaFirstPlayTime: pastTime,    // 首播时间在过去
			mediaEndPlayTime:   pastTime,    // 运营期结束时间也在过去
			mediaID:            "test3",
			expectedUpdate:     true,
			expectedStatus:     parse.ValueYuReOperation,
		},
		{
			name:               "不满足条件：短剧正片但首播时间未到",
			category:           parse.ValueCategoryShortDramaMain,
			totalAuditState:    parse.ValueTotalAuditStatePassed,
			checkupState:       parse.ValueCheckupStateOnline,
			mediaFirstPlayTime: futureTime,     // 首播时间在未来
			mediaEndPlayTime:   farFutureTime,  // 运营期结束时间在更远的未来
			mediaID:            "test4",
			expectedUpdate:     false,
			expectedStatus:     "",
		},
		{
			name:            "不满足条件：短剧预告片但审核未通过",
			category:        parse.ValueCategoryShortDramaTrailer,
			totalAuditState: "100", // 未通过
			checkupState:    "1",
			mediaID:         "test5",
			expectedUpdate:  false,
			expectedStatus:  "",
		},
		{
			name:            "不满足条件：短剧正片但未上架",
			category:        parse.ValueCategoryShortDramaMain,
			totalAuditState: parse.ValueTotalAuditStatePassed,
			checkupState:    "1", // 未上架
			mediaID:         "test6",
			expectedUpdate:  false,
			expectedStatus:  "",
		},
		{
			name:            "不满足条件：非短剧品类",
			category:        "123130250", // 其他品类
			totalAuditState: parse.ValueTotalAuditStatePassed,
			checkupState:    parse.ValueCheckupStateOnline,
			mediaID:         "test7",
			expectedUpdate:  false,
			expectedStatus:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			update := &model.UpdateInfo{
				FieldMap:           make(map[string][]string),
				MediaFirstPlayTime: tt.mediaFirstPlayTime,
				MediaEndPlayTime:   tt.mediaEndPlayTime,
			}

			shortDramaStatusCalLogic(ctx, tt.category, tt.totalAuditState, tt.checkupState, update, tt.mediaID)
			assert.Equal(t, tt.expectedUpdate, update.UpdateFlag)

			if tt.expectedUpdate {
				assert.NotEmpty(t, update.FieldMap[parse.FieldIPOnlineStatus])
				assert.Equal(t, tt.expectedStatus, update.FieldMap[parse.FieldIPOnlineStatus][0])
			} else {
				assert.Empty(t, update.FieldMap[parse.FieldIPOnlineStatus])
			}
		})
	}
}

// Test_ShortDramaTimeCal 测试短剧时间计算主函数
func Test_ShortDramaTimeCal(t *testing.T) {
	// 由于这个函数依赖makeMsgInfo，我们先跳过集成测试
	t.Run("integration test", func(t *testing.T) {
		t.Skip("Skipping integration test - requires mocking makeMsgInfo function")
	})
}

// 基准测试
func BenchmarkIsShortDrama(b *testing.B) {
	mediaInfo := createMockMediaInfo("benchmark", map[string]msghub.FieldInfo{
		parse.FieldContentForm:      createFieldInfo(parse.ValueContentFormMidVideo),
		parse.FieldCpMidPaymentType: createFieldInfo(parse.ValueCpPaymentMicroShortDramaVipShare),
	}, nil, 0)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		IsShortDrama(mediaInfo)
	}
}

func BenchmarkIsCurrentTimeAfterOrEqual(b *testing.B) {
	timeStr := "2020-01-01 00:00:00"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		isCurrentTimeAfterOrEqual(timeStr)
	}
}

// Test_isTimeInOperationPeriod 测试运营期时间判断
func Test_isTimeInOperationPeriod(t *testing.T) {
	now := time.Now()
	pastTime := now.Add(-2 * time.Hour).Format("2006-01-02 15:04:05")
	futureTime := now.Add(2 * time.Hour).Format("2006-01-02 15:04:05")
	currentTime := now.Format("2006-01-02 15:04:05")

	tests := []struct {
		name             string
		currentTime      string
		premiereTime     string
		operationEndTime string
		expected         bool
	}{
		{
			name:             "在运营期内：当前时间在首播时间之后，运营期结束时间之前",
			currentTime:      currentTime,
			premiereTime:     pastTime,
			operationEndTime: futureTime,
			expected:         true,
		},
		{
			name:             "不在运营期：首播时间为空",
			currentTime:      currentTime,
			premiereTime:     "",
			operationEndTime: futureTime,
			expected:         false,
		},
		{
			name:             "不在运营期：当前时间在首播时间之前",
			currentTime:      currentTime,
			premiereTime:     futureTime,
			operationEndTime: futureTime,
			expected:         false,
		},
		{
			name:             "不在运营期：当前时间在运营期结束时间之后",
			currentTime:      currentTime,
			premiereTime:     pastTime,
			operationEndTime: pastTime,
			expected:         false,
		},
		{
			name:             "在运营期内：运营期结束时间为空，当前时间在首播时间之后",
			currentTime:      currentTime,
			premiereTime:     pastTime,
			operationEndTime: "",
			expected:         true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isTimeInOperationPeriod(tt.currentTime, tt.premiereTime, tt.operationEndTime)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_isTimeInLibraryPeriod 测试片库期时间判断
func Test_isTimeInLibraryPeriod(t *testing.T) {
	now := time.Now()
	pastTime := now.Add(-2 * time.Hour).Format("2006-01-02 15:04:05")
	futureTime := now.Add(2 * time.Hour).Format("2006-01-02 15:04:05")
	currentTime := now.Format("2006-01-02 15:04:05")

	tests := []struct {
		name             string
		currentTime      string
		operationEndTime string
		expected         bool
	}{
		{
			name:             "在片库期：当前时间在运营期结束时间之后",
			currentTime:      currentTime,
			operationEndTime: pastTime,
			expected:         true,
		},
		{
			name:             "不在片库期：运营期结束时间为空",
			currentTime:      currentTime,
			operationEndTime: "",
			expected:         false,
		},
		{
			name:             "不在片库期：当前时间在运营期结束时间之前",
			currentTime:      currentTime,
			operationEndTime: futureTime,
			expected:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isTimeInLibraryPeriod(tt.currentTime, tt.operationEndTime)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_isCurrentTimeBeforeOrEqual 测试当前时间是否小于等于指定时间
func Test_isCurrentTimeBeforeOrEqual(t *testing.T) {
	now := time.Now()
	pastTime := now.Add(-2 * time.Hour).Format("2006-01-02 15:04:05")
	futureTime := now.Add(2 * time.Hour).Format("2006-01-02 15:04:05")

	tests := []struct {
		name     string
		timeStr  string
		expected bool
	}{
		{
			name:     "当前时间小于指定时间",
			timeStr:  futureTime,
			expected: true,
		},
		{
			name:     "当前时间大于指定时间",
			timeStr:  pastTime,
			expected: false,
		},
		{
			name:     "无效时间格式",
			timeStr:  "invalid-time",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isCurrentTimeBeforeOrEqual(tt.timeStr)
			assert.Equal(t, tt.expected, result)
		})
	}
}
