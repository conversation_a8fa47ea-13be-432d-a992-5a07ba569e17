package handle

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

// typeGameColumnHandle 游戏品类根据栏目类型进行不同的计算 https://tapd.woa.com/10093801/prong/stories/view/1010093801116200929
func typeGameColumnHandle(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := FillGameUpdateInfo(ctx, mediaInfo, planData, update)
	if err != nil {
		return err
	}

	defer func() {
		log.InfoContextf(ctx, "typeChildrenHandle End. update [%+v]", update)
	}()

	// 预热期，未定档更新
	err = GameColumnUnScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}
	// 已定档处理计算
	err = GameColumnScheduleCal(ctx, mediaInfo, update)
	if err != nil {
		return err
	}

	err = GameColumnFirstTimeCal(ctx, planData, update)
	if err != nil {
		return err
	}
	// 余热期计算
	err = GameYuReCal(ctx, planData, update)
	if err != nil {
		return err
	}

	return GameColumnOtherCalAndUpdate(ctx, planData, mediaInfo, update)
}

// GameColumnUnScheduleCal 游戏栏目未定档计算
func GameColumnUnScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "GameUnScheduleCal info start")
	defer log.InfoContextf(ctx, "GameUnScheduleCal info end")

	// 提取流水时间,判断是否需要计算
	flag := unScheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTimeText [%+v]", update.MediaPrePareTimeText)

	if update.IsTrailer && update.MediaPrePareTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueUnSchedule}
	}

	return nil
}

// GameColumnScheduleCal 游戏栏目定档时间计算
func GameColumnScheduleCal(ctx context.Context, mediaInfo msghub.MediaInfo, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "GameColumnScheduleCal info start")
	defer log.InfoContextf(ctx, "GameColumnScheduleCal info end")

	// 获取首播时间
	retFirstTime := CommonGetFirstTime(update.MediaFirstPlayTime, update.MediaPrePareTime, update)
	log.InfoContextf(ctx, "retFirstTime [%+v]", retFirstTime)
	update.FirstPlayTime = retFirstTime

	flag := scheduleTimeHandleV2(mediaInfo, update)
	if !flag {
		return nil
	}
	log.InfoContextf(ctx, "MediaPrePareTime [%+v]", update.MediaPrePareTime)

	// 判断媒资分类是否为预告片
	if update.IsTrailer {
		if retFirstTime == "" || HighThanNowTime(retFirstTime) {
			update.UpdateFlag = true
			update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
		}
	}

	return nil
}

// GameColumnFirstTimeCal 游戏栏目首播计算
func GameColumnFirstTimeCal(ctx context.Context, planData *model.PlanData, update *model.UpdateInfo) error {
	log.InfoContextf(ctx, "GameColumnFirstTimeCal info start")
	defer log.InfoContextf(ctx, "GameColumnFirstTimeCal info end")

	endPlayTime := GetGameColumnEndTime(update.LastPubTime, 14, update.MediaEndPlayTime)
	update.EndPlayTime = endPlayTime

	// 首播时间为空直接返回
	if !model.CheckTime(update.FirstPlayTime) {
		log.WarnContextf(ctx, "GameColumnFirstTimeCal firstPlayTime:[%s] not illegal", update.FirstPlayTime)
		return nil
	}

	_, tmpUpdateInfo, err := CommonTimeCal(ctx, planData, update.MediaFirstPlayTime, update.FirstPlayTime,
		endPlayTime, parse.FieldFirstPlayTime, parse.ValueOperatingPeriod, true)
	UnionUpdateInfo(update, tmpUpdateInfo)
	return err
}

// GameColumnOtherCalAndUpdate 游戏栏目其他计算和更新
func GameColumnOtherCalAndUpdate(ctx context.Context, planData *model.PlanData, mediaInfo msghub.MediaInfo,
	update *model.UpdateInfo) error {
	err := CommonYuReOtherCalV2(ctx, planData, update, !update.IsTrailer)
	if err != nil {
		return err
	}
	CalGameColumnOtherTime(mediaInfo, update)
	return CommonDealIpOnlineData(ctx, mediaInfo, update)
}

// CalGameColumnOtherTime 计算游戏栏目其他时间
func CalGameColumnOtherTime(mediaInfo msghub.MediaInfo, update *model.UpdateInfo) {
	// 取媒资已存字段数据，不一致则按最新的正确值来更新
	endTimeVIP := parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForVIP)
	endTimeFree := parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForFree)
	playEndTime := parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	// 计算最新的时间值
	// 取最后一期上线last_pubtime的值
	lastPubTime := parse.GetFieldValue(mediaInfo, parse.FieldCidLastPubtime)
	// 会员完结时间和免费完结时间 都是取last_pubtime值（字段精确到日，默认拼上23:59:59）
	// 对齐综艺
	curEndTime := GetVarietyEndTime(lastPubTime, 0, "")
	// 运营期结束时间 取last_pubtime+14天（字段精确到日，默认拼上23:59:59）
	curEndPlayTime := GetVarietyEndTime(lastPubTime, 14, "")
	if lastPubTime != "" {
		calVarietyOtherTime(endTimeVIP, curEndTime, endTimeFree, playEndTime, curEndPlayTime, update)
	}
}

// GetGameColumnEndTime 获取游戏栏目运营期结束时间
func GetGameColumnEndTime(t string, dayNum int, mediaPlanEndTime string) string {
	if t == "" {
		return mediaPlanEndTime
	}
	// 精确到日（"2022-09-11"）的时间默认拼接" 23:59:59"
	if len(t) == 10 {
		t = t + " 23:59:59"
	}
	t1, _ := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	t2 := t1.AddDate(0, 0, dayNum).Format("2006-01-02 15:04:05")
	return t2
}

// GameColumnScheduleStatus 游戏栏目定档状态处理逻辑
func GameColumnScheduleStatus(firstPlayTime string, update *model.UpdateInfo) {
	// 首播时间为空，或者首播时间不为空且首播时间大于当前时间则计算为可预热定档
	if firstPlayTime == "" {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	} else if model.CheckTime(firstPlayTime) && HighThanNowTime(firstPlayTime) {
		update.UpdateFlag = true
		update.FieldMap[parse.FieldIPOnlineStatus] = []string{parse.ValueSchedule}
	}
}

func isGameTrailer(category string) bool {
	if category == "" {
		return false
	}
	if _, ok := parse.GameTrailerMap[category]; ok {
		return true
	}
	return false
}
