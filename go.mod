module git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor

go 1.23.0

require (
	git.code.oa.com/atta/attaapi-go/v2 v2.0.3
	git.code.oa.com/pcg-csd/trpc-ext/trpc-task/tasksdk v1.20.1
	git.code.oa.com/trpc-go/trpc-config-tconf v0.1.10
	git.code.oa.com/trpc-go/trpc-database/kafka v0.3.5
	git.code.oa.com/trpc-go/trpc-database/mysql v0.3.0
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.13
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.5
	git.code.oa.com/trpc-go/trpc-filter/validation v0.1.3
	git.code.oa.com/trpc-go/trpc-go v0.20.0
	git.code.oa.com/trpc-go/trpc-log-atta v0.2.2
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.5.20
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.5.26
	git.code.oa.com/trpc-go/trpc-opentracing-tjg v0.2.0
	git.code.oa.com/trpc-go/trpc-selector-cl5 v0.2.0
	git.code.oa.com/trpcprotocol/hydra/tms v1.4.4
	git.code.oa.com/video_media/media_go_commlib/msghub v0.2.15
	git.code.oa.com/video_media/media_go_commlib/utils v0.3.5
	github.com/agiledragon/gomonkey/v2 v2.12.0
	git.woa.com/galileo/trpc-go-galileo v0.19.2
	git.woa.com/goom/mocker v0.3.8
	git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc v0.6.3
	git.woa.com/video_media/traceplus v0.1.21
	github.com/IBM/sarama v1.45.2
	github.com/avast/retry-go/v4 v4.6.1
	github.com/bmizerany/assert v0.0.0-20160611221934-b7ed37b82869
	github.com/golang/mock v1.6.0
	go.uber.org/automaxprocs v1.6.0
	gopkg.in/yaml.v2 v2.4.0
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	git.code.oa.com/atta/attaapi-go v1.0.8 // indirect
	git.code.oa.com/atta/attaapi_go v1.6.5 // indirect
	git.code.oa.com/devsec/protoc-gen-secv v0.3.4 // indirect
	git.code.oa.com/going/l5 v0.2.1 // indirect
	git.code.oa.com/open-wuji/go-sdk v1.5.2 // indirect
	git.code.oa.com/polaris/polaris-go v0.12.15 // indirect
	git.code.oa.com/rainbow/golang-sdk v0.6.2 // indirect
	git.code.oa.com/rainbow/proto v1.123.0 // indirect
	git.code.oa.com/tjg/tjg-opentracing-go v0.7.1 // indirect
	git.code.oa.com/tjg/tjg_go_api v1.9.1 // indirect
	git.code.oa.com/trpc-go/trpc-selector-dsn v0.2.1 // indirect
	git.code.oa.com/trpc-go/trpc-utils v0.2.2 // indirect
	git.code.oa.com/trpcprotocol/base/trending v1.2.45 // indirect
	git.code.oa.com/trpcprotocol/contentproduct/databus v1.5.34 // indirect
	git.code.oa.com/trpcprotocol/contentproduct/datahub v1.1.73 // indirect
	git.code.oa.com/trpcprotocol/hydra/imagedownload v1.1.16 // indirect
	git.code.oa.com/trpcprotocol/infinite/videoclassify v1.1.21 // indirect
	git.code.oa.com/trpcprotocol/infinite/videotag v1.1.5 // indirect
	git.code.oa.com/trpcprotocol/pcg_csd/taskschedule_manage v1.2.82 // indirect
	git.code.oa.com/trpcprotocol/storage_service/access_layer v1.1.30 // indirect
	git.code.oa.com/trpcprotocol/storage_service/adaptor_layer v1.1.31 // indirect
	git.code.oa.com/trpcprotocol/storage_service/common_storage_common v1.1.40 // indirect
	git.code.oa.com/trpcprotocol/tconf/tconfserver v1.1.2 // indirect
	git.code.oa.com/trpcprotocol/video_media/category_web_backend v1.1.56 // indirect
	git.code.oa.com/video_media/media_go_commlib/dbcache v1.0.6 // indirect
	git.code.oa.com/video_media/service_protocol/trpc-protocol/VideoMediaInf v0.0.9 // indirect
	git.code.oa.com/video_media/storage_service/common v0.3.9 // indirect
	git.code.oa.com/video_media/storage_service/config_cache v1.0.29 // indirect
	git.woa.com/galileo/eco/go/sdk/base v0.20.0 // indirect
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/opentelemetry/opentelemetry-go-ecosystem v0.6.3 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.8 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.2.5 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.7 // indirect
	git.woa.com/tpstelemetry/cgroups v0.2.3 // indirect
	git.woa.com/tpstelemetry/cgroups/cgroupsv2 v0.2.3 // indirect
	git.woa.com/tpstelemetry/tpstelemetry-protocol v0.0.2-0.20230403124315-f383964b6bcc // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	git.woa.com/trpc-go/tnet v0.1.1 // indirect
	git.woa.com/trpc/trpc-robust/go-sdk v0.0.1 // indirect
	git.woa.com/trpc/trpc-robust/proto/pb/go/trpc-robust v0.0.0-20241120021538-8dfb323d8c12 // indirect
	git.woa.com/trpcprotocol/hydra/objectdownload v1.1.14 // indirect
	git.woa.com/trpcprotocol/hydra/objectexport v1.1.14 // indirect
	github.com/BurntSushi/toml v1.5.0 // indirect
	github.com/Shopify/sarama v1.29.1 // indirect
	github.com/alphadose/haxmap v1.4.1 // indirect
	github.com/andybalholm/brotli v1.2.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bytedance/sonic v1.13.3 // indirect
	github.com/bytedance/sonic/loader v0.3.0 // indirect
	github.com/cenkalti/backoff/v4 v4.3.0 // indirect
	github.com/cenkalti/backoff/v5 v5.0.2 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/coreos/go-semver v0.3.1 // indirect
	github.com/coreos/go-systemd/v22 v22.5.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/deckarep/golang-set v1.8.0 // indirect
	github.com/eapache/go-resiliency v1.7.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/ebitengine/purego v0.8.4 // indirect
	github.com/fsnotify/fsnotify v1.9.0 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/go-logr/logr v1.4.3 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-playground/form/v4 v4.2.1 // indirect
	github.com/go-sql-driver/mysql v1.9.3 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v1.0.0 // indirect
	github.com/google/flatbuffers v25.2.10+incompatible // indirect
	github.com/google/pprof v0.0.0-20250630185457-6e76a2b096b5 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/grpc-ecosystem/go-grpc-prometheus v1.2.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway v1.16.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.27.1 // indirect
	github.com/guillermo/go.procmeminfo v0.0.0-20131127224636-be4355a9fb0e // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jinzhu/copier v0.4.0 // indirect
	github.com/jinzhu/gorm v1.9.16 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jmoiron/sqlx v1.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/juju/ratelimit v1.0.2 // indirect
	github.com/jxskiss/base62 v1.1.0 // indirect
	github.com/kelindar/bitmap v1.5.3 // indirect
	github.com/kelindar/simd v1.1.2 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/klauspost/cpuid/v2 v2.2.11 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/lestrrat-go/strftime v1.1.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20250317134145-8bc96cf8fc35 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mozillazg/go-pinyin v0.20.0 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/nanmu42/limitio v1.0.0 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/panjf2000/ants/v2 v2.11.3 // indirect
	github.com/pierrec/lz4 v2.6.0+incompatible // indirect
	github.com/pierrec/lz4/v4 v4.1.22 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20240221224432-82ca36839d55 // indirect
	github.com/prometheus/client_golang v1.22.0 // indirect
	github.com/prometheus/client_model v0.6.2 // indirect
	github.com/prometheus/common v0.65.0 // indirect
	github.com/prometheus/procfs v0.17.0 // indirect
	github.com/qianbin/directcache v0.9.7 // indirect
	github.com/r3labs/sse/v2 v2.10.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20250401214520-65e299d6c5c9 // indirect
	github.com/remeh/sizedwaitgroup v1.0.0 // indirect
	github.com/robfig/cron v1.2.0 // indirect
	github.com/rogpeppe/go-internal v1.13.1 // indirect
	github.com/shirou/gopsutil/v3 v3.24.5 // indirect
	github.com/shirou/gopsutil/v4 v4.25.6 // indirect
	github.com/shopspring/decimal v1.4.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/cast v1.9.2 // indirect
	github.com/tklauser/go-sysconf v0.3.15 // indirect
	github.com/tklauser/numcpus v0.10.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/upbit/goalbatch v0.1.0 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.63.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	go.etcd.io/etcd/api/v3 v3.6.2 // indirect
	go.etcd.io/etcd/client/pkg/v3 v3.6.2 // indirect
	go.etcd.io/etcd/client/v3 v3.6.2 // indirect
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/host v0.62.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/runtime v0.62.0 // indirect
	go.opentelemetry.io/contrib/zpages v0.62.0 // indirect
	go.opentelemetry.io/otel v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetricgrpc v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetrichttp v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.37.0 // indirect
	go.opentelemetry.io/otel/metric v1.37.0 // indirect
	go.opentelemetry.io/otel/sdk v1.37.0 // indirect
	go.opentelemetry.io/otel/sdk/metric v1.37.0 // indirect
	go.opentelemetry.io/otel/trace v1.37.0 // indirect
	go.opentelemetry.io/proto/otlp v1.7.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/arch v0.19.0 // indirect
	golang.org/x/crypto v0.40.0 // indirect
	golang.org/x/exp v0.0.0-20250711185948-6ae5c78190dc // indirect
	golang.org/x/net v0.42.0 // indirect
	golang.org/x/sync v0.16.0 // indirect
	golang.org/x/sys v0.34.0 // indirect
	golang.org/x/text v0.27.0 // indirect
	golang.org/x/time v0.12.0 // indirect
	google.golang.org/genproto v0.0.0-20250707201910-8d1bb00bc6a7 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250707201910-8d1bb00bc6a7 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250707201910-8d1bb00bc6a7 // indirect
	google.golang.org/grpc v1.73.0 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/cenkalti/backoff.v1 v1.1.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
