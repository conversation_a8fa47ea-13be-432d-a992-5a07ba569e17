module git.code.oa.com/video_media/storage_service/access_layer

go 1.18

require (
	git.code.oa.com/pcgmonitor/trpc_report_api_go v0.3.13
	git.code.oa.com/trpc-go/trpc-config-tconf v0.1.8
	git.code.oa.com/trpc-go/trpc-database/kafka v0.2.6
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.4
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.2
	git.code.oa.com/trpc-go/trpc-filter/validation v0.1.3
	git.code.oa.com/trpc-go/trpc-go v0.11.1
	git.code.oa.com/trpc-go/trpc-log-atta v0.1.13
	git.code.oa.com/trpc-go/trpc-metrics-m007 v0.4.9
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.1.7
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.3.2
	git.code.oa.com/trpc-go/trpc-selector-cl5 v0.2.0
	git.code.oa.com/trpcprotocol/storage_service/access_layer v1.1.30
	git.code.oa.com/trpcprotocol/storage_service/adaptor_layer v1.1.31
	git.code.oa.com/trpcprotocol/storage_service/common_storage_common v1.1.40
	git.code.oa.com/trpcprotocol/video_media/metadata_api v1.1.33
	git.code.oa.com/video_media/storage_service/auth v0.1.0
	git.code.oa.com/video_media/storage_service/common v0.3.8
	git.code.oa.com/video_media/storage_service/config_cache v1.0.29
	git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/otelcarriers/otelkafka v0.3.1
	git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc v0.3.1
	git.woa.com/polaris/polaris-go/v2 v2.5.10
	git.woa.com/video_media/domain_utils/lib v0.0.0-20230320115558-fc43b8ee602d
	git.woa.com/video_media/metadata/metadata-sdk v0.1.13
	git.woa.com/video_media/traceplus v0.1.17
	github.com/Shopify/sarama v1.29.1
	github.com/agiledragon/gomonkey/v2 v2.9.0
	github.com/bwmarrin/snowflake v0.3.0
	github.com/cch123/gogctuner v0.0.0-20220714084524-712d934a61a3
	github.com/golang/mock v1.6.0
	github.com/prashantv/gostub v1.1.0
	github.com/samber/lo v1.33.0
	github.com/stretchr/testify v1.8.1
	go.uber.org/automaxprocs v1.5.1
	go.uber.org/multierr v1.8.0
	google.golang.org/protobuf v1.30.0
)

require (
	git.code.oa.com/atta/attaapi-go v1.0.8 // indirect
	git.code.oa.com/atta/attaapi_go v1.6.5 // indirect
	git.code.oa.com/devsec/protoc-gen-secv v0.3.4 // indirect
	git.code.oa.com/going/l5 v0.2.1 // indirect
	git.code.oa.com/polaris/polaris-go v0.11.6 // indirect
	git.code.oa.com/rainbow/golang-sdk v0.4.12 // indirect
	git.code.oa.com/rainbow/proto v1.74.0 // indirect
	git.code.oa.com/tpstelemetry/cgroups v0.2.1 // indirect
	git.code.oa.com/tpstelemetry/cgroups/cgroupsv2 v0.2.1 // indirect
	git.code.oa.com/tpstelemetry/tpstelemetry-protocol v0.0.0-20210614022014-4c430301b7b3 // indirect
	git.code.oa.com/trpc-go/trpc-config-rainbow v0.1.25 // indirect
	git.code.oa.com/trpc-go/trpc-selector-dsn v0.1.3 // indirect
	git.code.oa.com/trpcprotocol/tconf/tconfserver v1.1.2 // indirect
	git.code.oa.com/trpcprotocol/video_media/api_gateway v1.1.7 // indirect
	git.code.oa.com/trpcprotocol/video_media/common_domain_head v1.1.11 // indirect
	git.code.oa.com/trpcprotocol/video_media/metadata_api_types v1.1.12 // indirect
	git.code.oa.com/trpcprotocol/video_media/value_mgr v1.1.41 // indirect
	git.code.oa.com/video_media/service_protocol/trpc-protocol/VideoMediaInf v0.0.9 // indirect
	git.woa.com/atta/attaapi-go/v3 v3.0.2 // indirect
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/open-wuji/sdk/go v1.6.5 // indirect
	git.woa.com/opentelemetry/opentelemetry-go-ecosystem v0.3.1 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.3 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.3 // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	git.woa.com/trpc-go/tnet v0.0.6 // indirect
	github.com/BurntSushi/toml v0.4.1 // indirect
	github.com/andybalholm/brotli v1.0.4 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cenkalti/backoff/v4 v4.1.3 // indirect
	github.com/cespare/xxhash/v2 v2.1.2 // indirect
	github.com/coreos/go-semver v0.3.0 // indirect
	github.com/coreos/go-systemd/v22 v22.3.2 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/eapache/go-resiliency v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20180814174437-776d5712da21 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/fsnotify/fsnotify v1.5.1 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/go-logr/logr v1.2.3 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/form v3.1.4+incompatible // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/flatbuffers v2.0.6+incompatible // indirect
	github.com/google/uuid v1.2.0 // indirect
	github.com/grpc-ecosystem/go-grpc-prometheus v1.2.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway v1.16.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.7.0 // indirect
	github.com/guillermo/go.procmeminfo v0.0.0-20131127224636-be4355a9fb0e // indirect
	github.com/hashicorp/errwrap v1.0.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-uuid v1.0.2 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.0.0 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.2 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jinzhu/copier v0.3.5 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/juju/ratelimit v1.0.1 // indirect
	github.com/klauspost/compress v1.13.6 // indirect
	github.com/lestrrat-go/strftime v1.0.6 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.2-0.20181231171920-c182affec369 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.4.1 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mozillazg/go-pinyin v0.18.0 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/panjf2000/ants/v2 v2.4.7 // indirect
	github.com/pierrec/lz4 v2.6.0+incompatible // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/prometheus/client_golang v1.14.0 // indirect
	github.com/prometheus/client_model v0.3.0 // indirect
	github.com/prometheus/common v0.37.0 // indirect
	github.com/prometheus/procfs v0.8.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/remeh/sizedwaitgroup v1.0.0 // indirect
	github.com/shirou/gopsutil v3.21.11+incompatible // indirect
	github.com/shirou/gopsutil/v3 v3.22.2 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/cast v1.5.0 // indirect
	github.com/tklauser/go-sysconf v0.3.10 // indirect
	github.com/tklauser/numcpus v0.4.0 // indirect
	github.com/upbit/goalbatch v0.1.0 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.31.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.0.2 // indirect
	github.com/xdg-go/stringprep v1.0.2 // indirect
	github.com/yusufpapurcu/wmi v1.2.2 // indirect
	go.etcd.io/etcd/api/v3 v3.5.2 // indirect
	go.etcd.io/etcd/client/pkg/v3 v3.5.2 // indirect
	go.etcd.io/etcd/client/v3 v3.5.2 // indirect
	go.opentelemetry.io/otel v1.9.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/internal/retry v1.7.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlpmetric v0.30.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetricgrpc v0.30.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetrichttp v0.30.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.7.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.7.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.7.0 // indirect
	go.opentelemetry.io/otel/metric v0.30.0 // indirect
	go.opentelemetry.io/otel/sdk v1.7.0 // indirect
	go.opentelemetry.io/otel/sdk/export/metric v0.28.0 // indirect
	go.opentelemetry.io/otel/sdk/metric v0.30.0 // indirect
	go.opentelemetry.io/otel/trace v1.9.0 // indirect
	go.opentelemetry.io/proto/otlp v0.16.0 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	golang.org/x/crypto v0.0.0-20210921155107-089bfa567519 // indirect
	golang.org/x/exp v0.0.0-20220303212507-bbda1eaf7a17 // indirect
	golang.org/x/net v0.0.0-20220722155237-a158d28d115b // indirect
	golang.org/x/sync v0.1.0 // indirect
	golang.org/x/sys v0.0.0-20220909162455-aba9fc2a8ff2 // indirect
	golang.org/x/text v0.3.7 // indirect
	google.golang.org/genproto v0.0.0-20220211171837-173942840c17 // indirect
	google.golang.org/grpc v1.46.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
