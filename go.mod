module git.code.oa.com/video_media/cover_msghub_cb/cover_standard_series_flag

go 1.13

require (
	bou.ke/monkey v1.0.2
	git.code.oa.com/polaris/polaris-go v0.7.6 // indirect
	git.code.oa.com/trpc-go/trpc-codec/tars v1.3.3 // indirect
	git.code.oa.com/trpc-go/trpc-config-tconf v0.1.8
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.2
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.2
	git.code.oa.com/trpc-go/trpc-go v0.9.4
	git.code.oa.com/trpc-go/trpc-log-atta v0.1.8
	git.code.oa.com/trpc-go/trpc-metrics-m007 v0.2.4
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.2.0
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.2.2
	git.code.oa.com/trpc-go/trpc-opentracing-tjg v0.1.8
	git.code.oa.com/trpcprotocol/storage_service/common_storage_common v1.1.7
	git.code.oa.com/video_media/media_go_commlib/dataaccess v0.0.18
	git.code.oa.com/video_media/media_go_commlib/msghub v0.2.9
	github.com/google/go-cmp v0.5.8 // indirect
	github.com/google/uuid v1.2.0 // indirect
	github.com/mitchellh/mapstructure v1.4.1 // indirect
	github.com/stretchr/testify v1.8.0 // indirect
	go.uber.org/automaxprocs v1.3.0
	go.uber.org/zap v1.17.0 // indirect
	golang.org/x/net v0.0.0-20220127200216-cd36cc0744dd // indirect
	golang.org/x/sys v0.0.0-20220909162455-aba9fc2a8ff2 // indirect
	google.golang.org/genproto v0.0.0-20220211171837-173942840c17 // indirect
	google.golang.org/grpc v1.46.2 // indirect
	google.golang.org/protobuf v1.28.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)
