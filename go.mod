module git.code.oa.com/video_media/storage_service/adaptor_layer

go 1.18

require (
	git.code.oa.com/tpstelemetry/tps-sdk-go/instrumentation/trpctelemetry v0.4.20
	git.code.oa.com/trpc-go/trpc-config-tconf v0.1.8
	git.code.oa.com/trpc-go/trpc-database/es v0.1.2
	git.code.oa.com/trpc-go/trpc-database/mysql v0.1.9
	git.code.oa.com/trpc-go/trpc-database/redis v0.1.9
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.3
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.2
	git.code.oa.com/trpc-go/trpc-filter/validation v0.1.3
	git.code.oa.com/trpc-go/trpc-go v0.10.0
	git.code.oa.com/trpc-go/trpc-log-atta v0.1.9
	git.code.oa.com/trpc-go/trpc-metrics-m007 v0.4.6
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.1.7
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.3.2
	git.code.oa.com/trpc-go/trpc-opentracing-tjg v0.2.0
	git.code.oa.com/trpc-go/trpc-selector-cl5 v0.2.0
	git.code.oa.com/trpcprotocol/storage_service/adaptor_layer v1.1.31
	git.code.oa.com/trpcprotocol/storage_service/common_storage_common v1.1.40
	git.code.oa.com/trpcprotocol/unionplus/common v1.1.24
	git.code.oa.com/v/data_platform/unionplus/lib/trpc_go_union_client v1.0.1
	git.code.oa.com/video_media/media_go_commlib/utils v0.2.9
	git.code.oa.com/video_media/storage_service/common v0.3.9
	git.code.oa.com/video_media/storage_service/config_cache v1.0.28
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-redsync/redsync/v4 v4.4.1
	github.com/golang/mock v1.6.0
	github.com/google/go-cmp v0.5.7 // indirect
	github.com/olivere/elastic/v7 v7.0.31
	github.com/prashantv/gostub v1.0.0
	go.uber.org/automaxprocs v1.3.0
)

require (
	git.code.oa.com/trpc-go/trpc-database/hbase v0.1.0
	git.code.oa.com/trpc-go/trpc-database/kafka v0.2.6
	git.code.oa.com/trpcprotocol/storage_service/access_layer v1.1.30
	git.code.oa.com/video_media/storage_service/antlr_es_dsl v0.0.3
	github.com/DATA-DOG/go-sqlmock v1.5.2
	github.com/Shopify/sarama v1.29.1
	github.com/bitly/go-simplejson v0.5.0
	github.com/cch123/gogctuner v0.0.0-20220714084524-712d934a61a3
	github.com/stretchr/testify v1.8.1
	github.com/tsuna/gohbase v0.0.0-20201125011725-348991136365
	google.golang.org/protobuf v1.30.0
)

require (
	git.code.oa.com/atta/attaapi-go v1.0.8 // indirect
	git.code.oa.com/atta/attaapi-go/v2 v2.0.1 // indirect
	git.code.oa.com/atta/attaapi_go v1.6.5 // indirect
	git.code.oa.com/devsec/protoc-gen-secv v0.3.4 // indirect
	git.code.oa.com/going/l5 v0.2.1 // indirect
	git.code.oa.com/pcgmonitor/trpc_report_api_go v0.3.13 // indirect
	git.code.oa.com/polaris/polaris-go v0.11.6 // indirect
	git.code.oa.com/tjg/tjg-opentracing-go v0.7.1 // indirect
	git.code.oa.com/tjg/tjg_go_api v1.9.1 // indirect
	git.code.oa.com/tpstelemetry/cgroups v0.1.1 // indirect
	git.code.oa.com/tpstelemetry/tps-sdk-go v0.4.20 // indirect
	git.code.oa.com/tpstelemetry/tpstelemetry-protocol v0.0.0-20210614022014-4c430301b7b3 // indirect
	git.code.oa.com/trpc-go/trpc-selector-dsn v0.1.3 // indirect
	git.code.oa.com/trpc-go/trpc-utils v0.1.0 // indirect
	git.code.oa.com/trpcprotocol/component_plat/common_comm v1.1.10 // indirect
	git.code.oa.com/trpcprotocol/tconf/tconfserver v1.1.2 // indirect
	git.code.oa.com/trpcprotocol/unionplus/unionplus_access_access_rpc v1.1.6 // indirect
	git.code.oa.com/video_media/service_protocol/trpc-protocol/VideoMediaInf v0.0.9 // indirect
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/open-wuji/sdk/go v1.6.5 // indirect
	git.woa.com/polaris/polaris-go/v2 v2.5.10 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.3 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.3 // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	github.com/BurntSushi/toml v0.4.1 // indirect
	github.com/andybalholm/brotli v1.0.4 // indirect
	github.com/antlr/antlr4/runtime/Go/antlr v1.4.10 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.1.2 // indirect
	github.com/coreos/go-semver v0.2.0 // indirect
	github.com/coreos/go-systemd/v22 v22.0.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/eapache/go-resiliency v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20180814174437-776d5712da21 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/fsnotify/fsnotify v1.5.1 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/form v3.1.4+incompatible // indirect
	github.com/go-sql-driver/mysql v1.5.0 // indirect
	github.com/go-zookeeper/zk v1.0.2 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gomodule/redigo v1.8.3 // indirect
	github.com/google/flatbuffers v2.0.0+incompatible // indirect
	github.com/google/uuid v1.1.2 // indirect
	github.com/grpc-ecosystem/go-grpc-prometheus v1.2.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway v1.16.0 // indirect
	github.com/guillermo/go.procmeminfo v0.0.0-20131127224636-be4355a9fb0e // indirect
	github.com/hanjm/etcd v0.7.1-0.20220722090715-9fe76006ef28 // indirect
	github.com/hashicorp/errwrap v1.0.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-uuid v1.0.2 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.0.0 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.2 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jmoiron/sqlx v1.3.4 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/juju/ratelimit v1.0.1 // indirect
	github.com/klauspost/compress v1.13.6 // indirect
	github.com/lestrrat-go/strftime v1.0.5 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.1 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.3.3 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mozillazg/go-pinyin v0.18.0 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/olivere/elastic v6.2.37+incompatible // indirect
	github.com/olivere/elastic/v6 v6.2.1 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/panjf2000/ants/v2 v2.4.7 // indirect
	github.com/pierrec/lz4 v2.6.0+incompatible // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.7.1 // indirect
	github.com/prometheus/client_model v0.2.0 // indirect
	github.com/prometheus/common v0.10.0 // indirect
	github.com/prometheus/procfs v0.1.3 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/rogpeppe/go-internal v1.9.0 // indirect
	github.com/shirou/gopsutil v3.21.11+incompatible // indirect
	github.com/sirupsen/logrus v1.8.1 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/cast v1.4.1 // indirect
	github.com/tklauser/go-sysconf v0.3.10 // indirect
	github.com/tklauser/numcpus v0.4.0 // indirect
	github.com/upbit/goalbatch v0.1.0 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.31.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.0.2 // indirect
	github.com/xdg-go/stringprep v1.0.2 // indirect
	github.com/yusufpapurcu/wmi v1.2.2 // indirect
	go.opentelemetry.io/otel v0.19.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp v0.19.0 // indirect
	go.opentelemetry.io/otel/exporters/stdout v0.19.0 // indirect
	go.opentelemetry.io/otel/metric v0.19.0 // indirect
	go.opentelemetry.io/otel/sdk v0.19.0 // indirect
	go.opentelemetry.io/otel/sdk/export/metric v0.19.0 // indirect
	go.opentelemetry.io/otel/sdk/metric v0.19.0 // indirect
	go.opentelemetry.io/otel/trace v0.19.0 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.7.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	golang.org/x/crypto v0.0.0-20210921155107-089bfa567519 // indirect
	golang.org/x/lint v0.0.0-20210508222113-6edffad5e616 // indirect
	golang.org/x/mod v0.6.0-dev.0.20220419223038-86c51ed26bb4 // indirect
	golang.org/x/net v0.0.0-20220722155237-a158d28d115b // indirect
	golang.org/x/sync v0.1.0 // indirect
	golang.org/x/sys v0.0.0-20220722155257-8c9f86f7a55f // indirect
	golang.org/x/text v0.3.7 // indirect
	golang.org/x/tools v0.1.12 // indirect
	google.golang.org/genproto v0.0.0-20211129164237-f09f9a12af12 // indirect
	google.golang.org/grpc v1.46.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	honnef.co/go/tools v0.0.1-2020.1.3 // indirect
	modernc.org/b v1.0.0 // indirect
)
