module git.code.oa.com/video_media/video_msghub_cb/sync_media_formal_test_data

go 1.12

replace git.code.oa.com/trpcprotocol/video_msghub_cb/sync_media_formal_test_data => ./stub/git.code.oa.com/trpcprotocol/video_msghub_cb/sync_media_formal_test_data

require (
	bou.ke/monkey v1.0.2
	git.code.oa.com/open-wuji/go-sdk v1.5.2
	git.code.oa.com/trpc-go/trpc-config-tconf v0.1.8
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.3
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.2
	git.code.oa.com/trpc-go/trpc-go v0.18.5
	git.code.oa.com/trpc-go/trpc-log-atta v0.1.12
	git.code.oa.com/trpc-go/trpc-metrics-m007 v0.4.2
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.2.2
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.5.24
	git.code.oa.com/trpc-go/trpc-opentracing-tjg v0.1.8
	git.code.oa.com/trpc-go/trpc-selector-cl5 v0.2.0
	git.code.oa.com/trpcprotocol/storage_service/common_storage_common v1.1.3
	git.code.oa.com/trpcprotocol/video_msghub_cb/sync_media_formal_test_data v0.0.0-00010101000000-000000000000
	git.code.oa.com/video_media/media_go_commlib/dataaccess v0.0.3
	git.code.oa.com/video_media/media_go_commlib/msghub v0.0.4
	git.code.oa.com/video_media/media_go_commlib/xhttp v0.1.7
	git.code.oa.com/vlib/go/trpc_plugins/video_component_tjg v1.0.0
	github.com/agiledragon/gomonkey v2.0.2+incompatible
	github.com/bitly/go-simplejson v0.5.0 // indirect
	github.com/gin-gonic/gin v1.7.1
	github.com/golang/mock v1.6.0
	github.com/sirupsen/logrus v1.7.0
	go.uber.org/automaxprocs v1.5.4-0.20240213192314-8553d3bb2149
)
