environments:  {
  name:  STAGING_ENV
  app_platform_123:  {
    env_id:  "test"
  }
}
ru_deploy:  {
  product_name:  "video_media"
  app_name:  "media_plat"
  ru_name:  "access_layer"
  receivers:  "zolachen"
  receivers:  "simonxcliu"
  receivers:  "lanceduan"
  receivers:  "lucasqian"
  receivers:  "leefwang"
  receivers:  "johnlu"
  receivers:  "jimmyin"
  receivers:  "calenjiang"
  receivers:  "joshuozhang"
  receivers:  "nelsontang"
  receivers:  "mooyang"
  receivers:  "robertxie"
  receivers:  "jixingguan"
  receivers:  "danielschen"
  workflows:  {
    env:  STAGING_ENV
    trigger:  {
      event:  NEW_ARTIFACT
    }
    jobs:  {
      job_name:  "staging_update"
      job_type:  UPDATE
    }
    notification_granularity:  NORMAL
  }
  workflows:  {
    env:  PRODUCTION_ENV
    access_approve:  {
      approvers:  "lucasqian"
      approvers:  "calenjiang"
      approvers:  "lanceduan"
      approvers:  "zolachen"
    }
    trigger:  {
      event:  NEW_ARTIFACT
    }
    jobs:  {
      job_name:  "production_all"
      job_type:  UPDATE
    }
    notification_granularity:  NORMAL
  }
  platform_123:  {
    server_id:  "storage_service.access_layer"
    xac_ref:  "https://git.woa.com/depot/tencent2:://prod/canal/cad/template/vepc/xac123.py@master"
    env_strategies:  {
      env:  STAGING_ENV
      deploy_policy:  "test_policy_with_ngtest"
    }
    env_strategies:  {
      env:  PRODUCTION_ENV
      deploy_policy:  "formal_policy_with_galileo_007"
      emergency_policy:  "formal_emergency_policy"
      auto_deploy_request:  true
    }
  }
}
