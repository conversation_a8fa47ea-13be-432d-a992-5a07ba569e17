package common

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"github.com/Shopify/sarama"
)

// KafkaConsumerCfg kafka消费者配置
type KafkaConsumerCfg struct {
	// Address kafka集群接入点
	Address string `yaml:"address"`
	// GroupId 消费者groupId，以','分隔
	GroupId string `yaml:"groupId"`
	// Topics 消费主题
	Topics []string `yaml:"topics"`
	// OffSetType 消费偏移类型 oldest/newest
	OffSetType string `yaml:"offsetType"`
	// BatchNum 批量消费数据(1为不批量消费)
	BatchNum int `yaml:"batchNum"`
}

// ConsumerClient kafka消费者结构
type ConsumerClient struct {
	KafkaConsumerCfg
	ready chan bool
	// consumeFunc 消费方法
	consumeFunc func(session sarama.ConsumerGroupSession, messages []*sarama.ConsumerMessage) (bool, error)
}

// check 检查消费者参数
func (config *KafkaConsumerCfg) check() bool {
	if config.Address == "" || config.GroupId == "" {
		return false
	}

	if config.OffSetType == "" {
		// 默认offset使用newest
		config.OffSetType = "newest"
	}

	if config.BatchNum <= 0 {
		// 如果入参小于等于0，直接设置为默认值1
		config.BatchNum = 1
	}

	if config.OffSetType != "oldest" && config.OffSetType != "newest" {
		return false
	}
	return true
}

// NewKafkaConsumer 创建消费者组对象
func NewKafkaConsumer(config KafkaConsumerCfg,
	handler func(sarama.ConsumerGroupSession, []*sarama.ConsumerMessage) (bool, error),
) *ConsumerClient {
	if handler == nil || !config.check() {
		log.Errorf("Invalid init config:%+v.", config)
		return nil
	}

	c := &ConsumerClient{
		KafkaConsumerCfg: config,
		ready:            make(chan bool),
		consumeFunc:      handler,
	}
	if c.initConsumerGroup() != nil {
		return nil
	}
	return c
}

// initConsumerGroup 初始化消费者组
func (c *ConsumerClient) initConsumerGroup() error {
	log.Info("Begin kafka consumer init...")

	config := newConsumerConfig(c.KafkaConsumerCfg)
	consumerGroup, err := sarama.NewConsumerGroup(strings.Split(c.Address, ","), c.GroupId, config)
	if err != nil {
		log.Errorf("Fail to create consumer group, err is %s.", err)
		return err
	}

	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer func() {
			wg.Done()
		}()
		for {
			log.Info("Begin consume...")
			if err := consumerGroup.Consume(context.Background(), c.Topics, c); err != nil {
				// 消费失败重连
				log.Errorf("Error from consumer:%+v.", err)
			}
			c.ready = make(chan bool)
		}
	}()
	<-c.ready
	log.Info("Sarama consumer up and running!")
	// 保证在系统退出时，通道里面的消息被消费
	return nil
}

// newConsumerConfig 创建消费者配置
func newConsumerConfig(cfg KafkaConsumerCfg) *sarama.Config {
	config := sarama.NewConfig()
	config.Version = sarama.V1_1_1_0
	config.ClientID = cfg.GroupId
	config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRange // 分区分配策略
	if "oldest" == cfg.OffSetType {
		config.Consumer.Offsets.Initial = sarama.OffsetOldest
	} else {
		config.Consumer.Offsets.Initial = sarama.OffsetNewest
	}
	config.ChannelBufferSize = 2 // channel长度
	config.Consumer.Return.Errors = true
	config.Consumer.Fetch.Default = 524288                // 单次消费拉取请求中，单个分区最大返回消息大小。一次拉取请求可能返回多个分区的数据，这里限定单个分区的最大数据大小
	config.Consumer.Fetch.Max = 1048576                   // 单次消费拉取请求中，单个分区最大返回消息大小。一次拉取请求可能返回多个分区的数据，这里限定单个分区的最大数据大小
	config.Consumer.MaxWaitTime = 1000 * time.Millisecond // 单次消费拉取请求最长等待时间。最长等待时间仅在没有最新数据时才会等待。此值应当设置较大点，减少空请求对服务端QPS的消耗。
	sarama.MaxResponseSize = 1048576                      // 单次消费拉取请求，最大拉取包大小。
	// 根据经验，这里设置100K-1M比较合适。设置太大，消费吞吐力上不去（从服务端拉数据和业务处理数据差不多变串行了），设置太小，则sdk会发送大量请求到服务端，kafka服务端TPS很容易触达上限，导致整体吞吐能力下降
	// 不论值大与小，kafka服务端如果有数据返回，一定会返回一个完整的数据包。（比如一条消息大小为1K，设置max为900，则也会返回这一条1K大消息的完整包）
	config.Consumer.Offsets.CommitInterval = 3 * time.Second // 定时多久一次提交消费进度

	config.Metadata.Full = false                            // 禁止拉取所有元数据
	config.Metadata.Retry.Max = 1                           // 元数据更新重次次数
	config.Metadata.Retry.Backoff = 1000 * time.Millisecond // 元数据更新等待时间
	return config
}

// Setup is run at the beginning of a new session, before ConsumeClaim
func (c *ConsumerClient) Setup(session sarama.ConsumerGroupSession) error {
	// todo:根据offsetType初始化消费起始offset
	// Mark the consumer as ready
	close(c.ready)
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (c *ConsumerClient) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

func (c *ConsumerClient) batchConsumeFunc(session sarama.ConsumerGroupSession,
	msgArray []*sarama.ConsumerMessage,
) error {
	isBreak, err := c.consumeFunc(session, msgArray)
	if isBreak {
		log.Errorf("fail to consume, err is %s.", err)
		return fmt.Errorf("fail to consume, err is %s", err)
	}
	// 直接提交最后一个
	session.MarkMessage(msgArray[len(msgArray)-1], "")
	return nil
}

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages().
func (c *ConsumerClient) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	// NOTE:
	// Do not move the code below to a goroutine.
	// The `ConsumeClaim` itself is called within a goroutine, see:
	// https://github.com/Shopify/sarama/blob/master/consumer_group.go#L27-L29
	// 具体消费消息
	log.Infof("Begin to ConsumeClaim, consumer is %+v, partition:%d.", c, claim.Partition())
	ticker := time.NewTicker(5 * time.Second)
	messageArray := make([]*sarama.ConsumerMessage, 0)
	for {
		select {
		case message, ok := <-claim.Messages():
			if !ok {
				// channel为空 sleep 50ms
				log.Errorf("Fail to get message claim, partition is %d", claim.Partition())
				time.Sleep(50 * time.Millisecond)
				return nil
			}
			log.Debugf("Message of consumer:%s is topic:%s, key:%s, partition:%d, offset:%d.", c.GroupId, message.Topic,
				string(message.Key), message.Partition, message.Offset)
			messageArray = append(messageArray, message)
			if len(messageArray) >= c.BatchNum {
				if err := c.batchConsumeFunc(session, messageArray); err != nil {
					return err
				}
				// 批量消费完需要清空 messageArray
				messageArray = messageArray[0:0]
			}
		case <-ticker.C:
			// 超时触发批量消费
			if len(messageArray) > 0 {
				if err := c.batchConsumeFunc(session, messageArray); err != nil {
					return err
				}
				// 批量消费完需要清空 messageArray
				messageArray = messageArray[0:0]
			}
		}
	}
}
