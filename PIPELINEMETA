# PIPELINEMETA
# Format: Pbtxt
# proto-file: depot/tencent2::prod/canal/metadata/pipelinemeta.proto
# proto-message: Pipelinemeta
# metadata generated by metax.
# template version: 1.0
# source template: https://git.woa.com/cli-market/t2-plugins/blob/master/resources/metadata/PIPELINEMETA.vepc

ru_id: "video_media.media_plat.access_layer"

ru_package: {

  # 打包的触发条件
  schedule:  {
    # 每周一上午十点十五分自动打包
    cron:  "0 15 10 ? * 1"
    # 代码合入之后总是自动打包
    release_unit_code_change: true
  }

  # 发布在 123 平台的制品
  platform_123_artifact: {
    # 基于 trpc_go 的编译模式 
    trpc_go_build: {
      build_image: TLINUX3_GO_1_18
      runtime_image: TRPC_GO_RUNTIME_0_2_2
    }
  }

}
