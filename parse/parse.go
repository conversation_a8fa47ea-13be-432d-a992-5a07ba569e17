// Package parse declares parse
package parse

import "git.code.oa.com/video_media/media_go_commlib/msghub"

// 属性项定义
const (

	// FieldPreHeatTimeUn 未定档预热时间
	FieldPreHeatTimeUn = "preheat_time_unscheduled"
	// FieldPreHeatTime 已定档预热时间
	FieldPreHeatTime = "preheat_time_scheduled"
	// FieldFirstPlayTime 首播时间字段
	FieldFirstPlayTime = "premiere_time"
	// FieldFirstPlayEndTime 运营期结束时间
	FieldFirstPlayEndTime = "operation_end_time"
	// FieldAfterHeatEndTime 余热期结束时间
	FieldAfterHeatEndTime = "afterheat_time"
	// FieldIPOnlineStatus ip实时状态
	FieldIPOnlineStatus = "ip_online_status"
	// FieldType 大分类字段
	FieldType = "type"
	// FieldCategory 品类字段
	FieldCategory = "category_value"
	// FieldCheckUpState 专辑状态
	FieldCheckUpState = "checkup_state"
	// FieldPositiveContent 正片标识
	FieldPositiveContent = "positive_content"
	// FieldIsXintu 是否星途知识付费类型
	FieldIsXintu = "whether_or_not_xintu"
	// FieldContentForm 内容形式
	FieldContentForm = "content_form"
	// FieldCpMidPaymentType CP选择付费模式
	FieldCpMidPaymentType = "cp_mid_payment_type"
	// FieldTotalAuditState 专辑审核状态
	FieldTotalAuditState = "total_audit_state"
	// FieldFirstCheckupTime 首正片时间
	FieldFirstCheckupTime = "first_checkup_time"
	// FieldPublishDateX 播出日期时间
	FieldPublishDateX = "publish_date"
	// FieldCheckupTime 专辑上架时间
	FieldCheckupTime = "video_checkup_time"
	// FieldAnimeUpdateStatus 动漫更新状态
	FieldAnimeUpdateStatus = "anime_update_status"
	// FieldHotLevel 版权采买级别
	FieldHotLevel = "hot_level"
	// FieldCidCopyright 专辑版权性质
	FieldCidCopyright = "nature_of_the_copyright_id"
	// FieldMatchID 场次字段
	FieldMatchID = "race_match_id"
	// FieldEpsodePubTime 剧集期数更新时间
	FieldEpsodePubTime = "epsode_pubtime"
	// FieldPrePareTimeText 即将上映时间（文本）
	FieldPrePareTimeText = "hollywood_prepare_time"
	// FieldPrePareTimeTime 即将上映时间（时间）
	FieldPrePareTimeTime = "going_to_pubtime"
	// FieldPeriod 期数
	FieldPeriod = "period"
	// FieldSportsColumnType 体育栏目类型
	FieldSportsColumnType = "sports_column_type"
	// FieldEndTime (同步剧)完结时间
	FieldEndTime = "endtime"
	// FieldTVEndTime 电视剧完结时间（全网）
	FieldTVEndTime = "end_time"
	// FieldTVPublishTime 电视剧出品时间
	FieldTVPublishTime = "publish_time_drama"
	// FieldLastUpdateTime 上一次更新时间
	FieldLastUpdateTime = "last_update_time"
	// FieldNextUpdateTime 下架次更新时间
	FieldNextUpdateTime = "update_time_next"
	// FieldEndTimeForVIP 会员完结时间
	FieldEndTimeForVIP = "end_time_for_vip"
	// FieldEndTimeForFree 免费完结时间
	FieldEndTimeForFree = "end_time_for_free"
	// FieldThreeMonthEndAfterHeatTime 三个月余热期结束时间
	FieldThreeMonthEndAfterHeatTime = "afterheat_time_moretime"
	// FieldThreeYearTailTime 三年长尾期结束时间
	FieldThreeYearTailTime = "longtail_time_moretime"
	// FieldCidContentStructure 综艺专辑内容结构标识
	FieldCidContentStructure = "cid_content_structure"
	// FieldCidLastPubtime 最后一期上线时间
	FieldCidLastPubtime = "last_pubtime"
	// FieldColumnID 栏目ID
	FieldColumnID = "column_id"
	// FieldPayVipTime 单点转包月时间
	FieldPayVipTime = "pay_vip_time"
	// FieldOperationSegmentTime 运营期切段时间
	FieldOperationSegmentTime = "operation_time_segment"
	// FieldBroadcastMode 播出方式
	FieldBroadcastMode = "broadcast_mode"
	// FieldPayStatus 付费状态
	FieldPayStatus = "pay_status"
	// FieldValueEndTime 价值认定期结束时间
	FieldValueEndTime = "value_endtime"
	// FieldValueTimeState 价值认定期状态
	FieldValueTimeState = "value_time_state"
	// FieldOldCidRelatedNewCid 旧结构专辑关联新结构专辑
	FieldOldCidRelatedNewCid = "old_cid_related_new_cid"
	// FieldPArea 制片地区
	FieldPArea = "p_area"
	// FieldNetPublishTime 腾讯首播时间
	FieldNetPublishTime = "net_publish_time"
	// OperationProtectionFields 运营保护字段
	OperationProtectionFields = "operation_protection_fields"
	// FieldSportsMatchID 比赛ID
	FieldSportsMatchID = "sports_match_id"
	// FieldEditingVersionComic 动漫剪辑版本
	FieldEditingVersionComic = "editing_version_comic"
)

// 属性值定义
const (
	// ValueIs 是否库-是
	ValueIs = "1543606"
	// ValueNotIs 是否库-否
	ValueNotIs = "1543607"
	// ValueUnSchedule IP实时状态-可预热未定档
	ValueUnSchedule = "8377973"
	// ValueSchedule IP实时状态-可预热定档
	ValueSchedule = "8377974"
	// ValueOperatingPeriod IP实时状态-运营期
	ValueOperatingPeriod = "8389611"
	// ValueYuReOperation 片库-余热期三月内
	ValueYuReOperation = "8389612"
	// ValueYuReMonthOperation 片库-余热期三月外
	ValueYuReMonthOperation = "8396614"
	// ValueChangWeiOperation 片库-长尾期三年内
	ValueChangWeiOperation = "8389613"
	// ValueChangWeiYearOperation 片库-长尾期三年外
	ValueChangWeiYearOperation = "8396615"
	// ValueStatusComplete 更新状态-已完结
	ValueStatusComplete = "1568245"
	// ValueHotLevelA 版权采买等级A
	ValueHotLevelA = "224426"
	// ValueHotLevelB 版权采买等级B
	ValueHotLevelB = "224427"
	// ValueHotLevelS 版权采买等级S
	ValueHotLevelS = "224425"
	// ValueHotLevelSPlus 版权采买等级S+
	ValueHotLevelSPlus = "1525777"
	// ValueTencentMake 版权性质 - 腾讯自制
	ValueTencentMake = "1584566"
	// ValueTypeSports 体育大分类
	ValueTypeSports = "4"
	// ValueTypeGame 游戏大分类
	ValueTypeGame = "6"
	// ValueOldCIDStruct 旧CID结构
	ValueOldCIDStruct = "8371549"
	// ValueNewCIDStruct 新CID结构
	ValueNewCIDStruct = "8371548"
	// ValueTypeMovie 电影大分类
	ValueTypeMovie = 1
	// ValueMonthOnly 包月only
	ValueMonthOnly = "6"
	// ValueMonthSingle 包月单点(非会员可以单片购买/用券支付观看; 会员可以免费观看)
	ValueMonthSingle = "5"
	// ValueInnerArea 制片地区为内地
	ValueInnerArea = "153505"
	// ValueTheater 剧场版动漫剪辑版本
	ValueTheater = "1342751"
	// ValueSinglePay 单片付费(单片购买观看)
	ValueSinglePay = "7"
	// ValueSinglePayPlus 单片付费plus
	ValueSinglePayPlus = "9"
	// ValueContentFormMidVideo 内容形式-中视频
	ValueContentFormMidVideo = "8368972"
	// ValueCpPaymentMicroShortDramaVipShare 微短剧-会员分账
	ValueCpPaymentMicroShortDramaVipShare = "123131290"
	// ValueCpPaymentMicroShortDramaSinglePay 微短剧-单点
	ValueCpPaymentMicroShortDramaSinglePay = "123131291"
	// ValueCpPaymentMicroShortDramaFree 微短剧-免费
	ValueCpPaymentMicroShortDramaFree = "123131759"
	// ValueCpPaymentMicroShortDramaIncentiveAd 微短剧-激励广告
	ValueCpPaymentMicroShortDramaIncentiveAd = "123131760"
	// ValueCpPaymentMicroShortDramaSinglePayIncentiveMix 微短剧-单点激励广告混合
	ValueCpPaymentMicroShortDramaSinglePayIncentiveMix = "123132082"
	// ValueCpPaymentMicroShortDramaCard 微短剧-短剧卡
	ValueCpPaymentMicroShortDramaCard = "123133454"
	// ValueCpPaymentMicroShortDramaCardSingleMix 微短剧-短剧卡单点混合
	ValueCpPaymentMicroShortDramaCardSingleMix = "123133455"
	// ValueTotalAuditStatePassed 专辑审核状态-审核通过
	ValueTotalAuditStatePassed = "101"
	// ValueCheckupStateOnline 专辑状态-已上架
	ValueCheckupStateOnline = "4"
	// ValueCategoryShortDramaTrailer 电视剧-短剧-预告片
	ValueCategoryShortDramaTrailer = "11814"
	// ValueCategoryShortDramaMain 电视剧-短剧-正片
	ValueCategoryShortDramaMain = "11811"
)

// 相关类型定义
const (
	// CidType 专辑定义
	CidType = 2003
	// LidType 栏目定义
	LidType = 2005
	// ShortDramaType 短剧定义
	ShortDramaType = 104
	// TimeLen 时间长度
	TimeLen = 19
	// RetryTimes 重试次数
	RetryTimes = 50
	// TimeOut 超时时间
	TimeOut = 3000
)

// 播放方式定义
const (
	// SingleBroadcast 单播
	SingleBroadcast = "8299094"
	// SeasonBroadcast 季播
	SeasonBroadcast = "1331635"
	// YearBroadcast 年播
	YearBroadcast = "1331636"
)

// 价值认定状态
const (
	// ValueStateNotStart 价值认定未开始
	ValueStateNotStart = "123129409"
	// ValueStateStarting 价值认定中
	ValueStateStarting = "123129410"
	// ValueStateEnd 价值认定结束
	ValueStateEnd = "123129411"
)

// 栏目类型（sports_column_type）
const (
	// ValueProGramme 节目
	ValueProGramme = "8288340"
	// ValueDocumentary 纪录片
	ValueDocumentary = "8288341"
	// ValueGame 赛事类
	ValueGame = "8357682"
)

// 日期天数定义
const (
	// OneDay 一天
	OneDay = 1
	// MonthDays 一月的天数
	MonthDays = 30
	// ThreeMonthDays 三月的天数
	ThreeMonthDays = 92
	// HalfYearDays 半年的天数
	HalfYearDays = 182
	// YearDays 一年的天数
	YearDays = 365
)

// 排播状态定义
const (
	// Configured 已配置
	Configured = 2
)

// GetFieldValue 字段值获取
func GetFieldValue(info msghub.MediaInfo, field string) string {
	if _, ok := info.FieldInfos[field]; !ok {
		return ""
	}
	return info.FieldInfos[field].Value
}

// GetModifyValue 获取变更字段值
func GetModifyValue(info msghub.MediaInfo, field string) (string, bool) {
	if _, ok := info.ModifyFieldInfos[field]; !ok {
		return "", false
	}
	return info.ModifyFieldInfos[field].New, true
}

// GetModifyOldValue 获取变更前字段值
func GetModifyOldValue(info msghub.MediaInfo, field string) string {
	if _, ok := info.ModifyFieldInfos[field]; !ok {
		return ""
	}
	return info.ModifyFieldInfos[field].Old
}
