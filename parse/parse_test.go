package parse

import (
	"testing"

	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func TestGetFieldValue(t *testing.T) {
	type args struct {
		info  msghub.MediaInfo
		field string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				info: msghub.MediaInfo{
					FieldInfos: map[string]msghub.FieldInfo{
						"123": msghub.FieldInfo{
							Id:     0,
							Value:  "123",
							MapVal: nil,
						},
					},
				},
				field: "123",
			},
			want: "123",
		},
		{
			name: "test",
			args: args{
				info: msghub.MediaInfo{
					FieldInfos: map[string]msghub.FieldInfo{
						"123": msghub.FieldInfo{
							Id:     0,
							Value:  "123",
							MapVal: nil,
						},
					},
				},
				field: "1",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetFieldValue(tt.args.info, tt.args.field); got != tt.want {
				t.Errorf("GetFieldValue() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetModifyValue(t *testing.T) {
	type args struct {
		info  msghub.MediaInfo
		field string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				info: msghub.MediaInfo{
					ModifyFieldInfos: map[string]msghub.ModifyFieldInfo{
						"213": msghub.ModifyFieldInfo{
							New: "1234",
						},
					},
				},
				field: "213",
			},
			want: "1234",
		},
		{
			name: "test",
			args: args{
				info: msghub.MediaInfo{
					ModifyFieldInfos: map[string]msghub.ModifyFieldInfo{
						"213": msghub.ModifyFieldInfo{
							New: "1234",
						},
					},
				},
				field: "13",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got, _ := GetModifyValue(tt.args.info, tt.args.field); got != tt.want {
				t.Errorf("GetModifyValue() = %v, want %v", got, tt.want)
			}
		})
	}
}
