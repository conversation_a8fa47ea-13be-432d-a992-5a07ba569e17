package parse

// MovieTrailerMap 电影品类预告片
var MovieTrailerMap map[string]string

// MoviePositiveMap 电影品类正片
var MoviePositiveMap map[string]string

// ChildrenTrailerMap 少儿品类预告片
var ChildrenTrailerMap map[string]string

// ChildrenPositiveMap 少儿品类正片
var ChildrenPositiveMap map[string]string

// VarietyTrailerMap 综艺品类预告片
var VarietyTrailerMap map[string]string

// VarietyPositiveMap 综艺品类正片
var VarietyPositiveMap map[string]string

// GameTrailerMap 游戏品类预告片
var GameTrailerMap map[string]string

// TVShortMap tv短剧
var TVShortMap map[string]string

// TVPositiveMap tv正片
var TVPositiveMap map[string]string

// TVTrailerMap tv预告片
var TVTrailerMap map[string]string

// DocumentaryTrailerMap 纪录片品类预告片
var DocumentaryTrailerMap map[string]string

// DocumentaryPositiveMap 纪录片品类正片
var DocumentaryPositiveMap map[string]string

// InitMap 初始化map
func InitMap() {
	MovieTrailerMap = map[string]string{
		"10142": "电影-电影-预告片",
		"10151": "电影-微电影-预告片",
		"10417": "电影-原创短片-预告片",
		"10979": "电影-网络电影-预告片",
	}
	MoviePositiveMap = map[string]string{
		"10139": "电影-电影-正片",
		"10150": "电影-微电影-正片",
		"10416": "电影-原创短片-正片",
		"10890": "电电影-网络电影-正片",
		"11866": "电影-电影-幕后纪录片",
	}
	ChildrenTrailerMap = map[string]string{
		"11241": "少儿-儿童音乐-预告片",
		"11246": "少儿-动画-预告片",
		"11251": "少儿-玩具-预告片",
		"11256": "少儿-综艺-预告片",
		"11260": "少儿-资讯-预告片",
	}
	ChildrenPositiveMap = map[string]string{
		"11239": "少儿-儿童音乐-正片",
		"11244": "少儿-动画-正片",
		"11249": "少儿-玩具-正片",
		"11254": "少儿-综艺-正片",
		"11258": "少儿-资讯-正片",
	}
	VarietyTrailerMap = map[string]string{
		"10004": "综艺-栏目-预告片",
		"10005": "综艺-颁奖礼-预告片",
		"10006": "综艺-晚会盛典-预告片",
		"10473": "综艺-综艺演出-预告片",
	}
	VarietyPositiveMap = map[string]string{
		"10001": "综艺-栏目-正片",
		"10002": "综艺-颁奖礼-正片",
		"10003": "综艺-晚会盛典-正片",
		"10472": "综艺-综艺演出-正片",
		"11862": "综艺-加更正片",
	}
	GameTrailerMap = map[string]string{
		"11849": "游戏-栏目-预告片",
	}
	TVShortMap = map[string]string{
		"11811": "电视剧-短剧-正片",
		"11812": "电视剧-短剧-竖屏短剧",
		"11813": "电视剧-短剧-互动剧",
		"11814": "电视剧-短剧-预告片",
		"11815": "电视剧-短剧-片花",
		"11816": "电视剧-短剧-花絮",
		"11817": "电视剧-短剧-其他",
	}
	TVPositiveMap = map[string]string{
		"10470": "电视剧-正片",
		"10471": "电视剧-正片",
		"11811": "电视剧-正片",
		"11812": "电视剧-正片",
		"11813": "电视剧-正片",
	}
	TVTrailerMap = map[string]string{
		"10479": "电视剧-预告片",
		"10475": "电视剧-预告片",
		"11814": "电视剧-预告片",
	}
	DocumentaryTrailerMap = map[string]string{
		"10721": "纪录片-预告片",
		"10724": "纪录片-预告片",
		"11754": "纪录片-预告片",
	}
	DocumentaryPositiveMap = map[string]string{
		"10720": "纪录片-正片",
		"10723": "纪录片-正片",
		"10726": "纪录片-正片",
	}
}
