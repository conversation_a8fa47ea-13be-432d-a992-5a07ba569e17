package service

var (
	calValueStatusErrCh    = make(chan valueStatusInfo, 100)
	calIPOnlineStatusErrCh = make(chan ipOnlineStatusInfo, 500)
)

type valueStatusInfo struct {
	cid      string
	err      error
	retryNum int // num 控制重试次数
}

type ipOnlineStatusInfo struct {
	cid      string
	err      error
	retryNum int // num 控制重试次数
}

// InitRetry 初始化重试
func InitRetry() {
	DoCalValueStatusErr()
	DoCalIpOnlineStatusErr()
}

// DoCalValueStatusErr 处理CalValueStatus的错误
func DoCalValueStatusErr() {
	go func() {
		for errInfo := range calValueStatusErrCh {
			info := errInfo
			if info.err == nil {
				continue
			}

			retryCalValueStatus(info)
		}
	}()
}

// DoCalIpOnlineStatusErr 处理CalIpOnlineStatus的错误
func DoCalIpOnlineStatusErr() {
	go func() {
		for errInfo := range calIPOnlineStatusErrCh {
			info := errInfo
			if info.err == nil {
				continue
			}

			retryCalIPOnlineStatus(info)
		}
	}()
}
