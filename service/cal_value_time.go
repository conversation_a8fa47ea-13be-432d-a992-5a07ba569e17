package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/handle"
	"git.code.oa.com/video_media/ip_status_update/model"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/ip_status_update/repo"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	media_utils "git.code.oa.com/video_media/media_go_commlib/utils"
)

const TypeDocumentary = "9"

// HandleCalIPValueTime 计算ip实时状态回调函数
func HandleCalIPValueTime(w http.ResponseWriter, r *http.Request) error {
	var mediaInfo msghub.MediaInfo
	if err := media_utils.DecodeRequest(r, &mediaInfo); err != nil {
		// 反序列化消息总线数据失败，直接跳过
		w.WriteHeader(http.StatusOK)
		log.Errorf("Fail to decode request, err is %s.", err)
		return nil
	}
	ctx := r.Context()
	log.InfoContextf(ctx, "mediaInfo ID:%s, modifyInfos:%+v, fieldInfos:%+v", mediaInfo.Id,
		mediaInfo.ModifyFieldInfos, mediaInfo.FieldInfos)
	valueData, err := fillValueData(ctx, mediaInfo)
	if err != nil {
		return err
	}

	update := &model.UpdateInfo{}
	update.IsUpdate = true
	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)
	err = handle.CalValueTimeState(ctx, valueData, update)
	if err != nil {
		log.Errorf("CalValueTimeState err:%+v", err)
		w.WriteHeader(http.StatusInternalServerError)
		return errs.New(http.StatusInternalServerError, err.Error())
	}
	w.WriteHeader(http.StatusOK)
	return nil
}

// HandleReCalValueTime 接收ets数据或者，数据清洗重算
func HandleReCalValueTime(w http.ResponseWriter, r *http.Request) error {
	ctx := r.Context()
	log.InfoContextf(ctx, "test ets callback reqInfo:[%+v]", r.Body)
	var req model.ValueData
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		w.WriteHeader(http.StatusOK)
		log.ErrorContextf(ctx, "Fail to decode request, err is %s.", err)
		return nil
	}
	log.InfoContextf(ctx, "reCal ValueTime reqInfo:%+v", req)
	// 拉取媒资相关数据数据，构建消息总线结构
	mediaInfo, err := makeMsgInfo(ctx, req.MediaID, parse.CidType)
	if err != nil {
		log.ErrorContextf(ctx, "make msgInfo err:%+v", err)
		w.WriteHeader(http.StatusInternalServerError)
		return errs.New(http.StatusInternalServerError, err.Error())
	}
	valueData, err := fillValueData(ctx, mediaInfo)
	if err != nil {
		return err
	}
	valueData.PushType = req.PushType

	update := &model.UpdateInfo{}
	update.IsUpdate = true
	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)
	err = handle.CalValueTimeState(ctx, valueData, update)
	if err != nil {
		log.ErrorContextf(ctx, "recal TimeState err:%+v", err)
		w.WriteHeader(http.StatusInternalServerError)
		return errs.New(http.StatusInternalServerError, err.Error())
	}
	w.WriteHeader(http.StatusOK)
	return nil
}

// fillValueData 填充价值认定计算相关的数据
func fillValueData(ctx context.Context, mediaInfo msghub.MediaInfo) (*model.ValueData, error) {
	var valueData = model.ValueData{}
	valueData.MediaID = mediaInfo.Id
	valueData.MediaType = parse.GetFieldValue(mediaInfo, parse.FieldType)
	valueData.FreeEndTime = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForFree)
	valueData.PayEndTime = parse.GetFieldValue(mediaInfo, parse.FieldEndTimeForVIP)
	valueData.EndTime = parse.GetFieldValue(mediaInfo, parse.FieldEndTime)
	valueData.TVEndTime = parse.GetFieldValue(mediaInfo, parse.FieldTVEndTime)
	valueData.PlayEndTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayEndTime)
	valueData.PremiereTime = parse.GetFieldValue(mediaInfo, parse.FieldFirstPlayTime)
	valueData.LastPubTime = FixLastPubTime(parse.GetFieldValue(mediaInfo, parse.FieldCidLastPubtime))
	valueData.MediaValueEndTime = parse.GetFieldValue(mediaInfo, parse.FieldValueEndTime)
	valueData.MediaValueTimeState = parse.GetFieldValue(mediaInfo, parse.FieldValueTimeState)

	if valueData.MediaType == TypeDocumentary {
		// 获取排播数据
		nowPlanData, err := model.GetPlanData(ctx, model.PlanData{
			ID:   valueData.MediaID,
			Type: getPushType(mediaInfo),
		})
		if err != nil {
			log.ErrorContextf(ctx, "[%s] getPlanData err: %+v", mediaInfo.Id, err)
			return nil, err
		}
		valueData.LastUpdateDay = handle.GetPlanEndTime(nowPlanData)
	}
	return &valueData, nil
}

// FixLastPubTime 修正最后一期内容上线日
func FixLastPubTime(t string) string {
	// 最后一期内容上线日存在两种格式,所以需要都进行判断
	if model.CheckTime(t) {
		return t
	}
	if model.CheckTime2(t) {
		return t + " 23:59:59"
	}

	return ""
}

// TimerReCalValueStatus 定时检查价值认定计算
func TimerReCalValueStatus(ctx context.Context, _, _ string, _ int32) error {
	cids, err := repo.GetIpStatusRepoClient().GetNeedReCalValueData(ctx)
	if err != nil {
		return err
	}

	for _, cid := range cids {
		id := cid
		err = reCalValueStatus(ctx, id)
		if err != nil {
			log.ErrorContextf(ctx, "reCalValueStatus err [%v], data [%+v]", err, id)
			go func() {
				calValueStatusErrCh <- valueStatusInfo{
					cid:      id,
					err:      err,
					retryNum: 3,
				}
			}()
			// 失败的id加入到失败队列，继续计算。
			continue
		}
	}

	return nil
}

func reCalValueStatus(ctx context.Context, cid string) error {
	mediaInfo, err := makeMsgInfo(ctx, cid, parse.CidType)
	if err != nil {
		log.ErrorContextf(ctx, "make msgInfo err [%+v]", err)
		return err
	}
	valueData, err := fillValueData(ctx, mediaInfo)
	if err != nil {
		return err
	}

	update := &model.UpdateInfo{}
	update.IsUpdate = true
	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)
	err = handle.CalValueTimeState(ctx, valueData, update)
	if err != nil {
		log.ErrorContextf(ctx, "recal TimeState err [%+v]", err)
		return err
	}
	return nil
}

func retryCalValueStatus(info valueStatusInfo) {
	ctx := context.Background()
	err := reCalValueStatus(ctx, info.cid)
	info.err = err
	info.retryNum--
	if err == nil {
		return
	}

	if err != nil && info.retryNum > 0 {
		time.Sleep(100 * time.Millisecond)
		calValueStatusErrCh <- info
		return
	}

	if err != nil {
		// 重试多次失败，告警出来
		_ = SendMsg(info)
		return
	}
	return
}

// HandleCheckIPValueStatus 检查ip价值认定状态
func HandleCheckIPValueStatus(w http.ResponseWriter, r *http.Request) error {
	res := &checkResponse{
		Code: 0,
		Msg:  "suc",
	}
	ctx := r.Context()
	var req model.ValueData
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		res.Code = http.StatusBadRequest
		res.Msg = "json Decode fail"
		log.ErrorContextf(ctx, "Fail to decode request, err is %s.", err)
		return err
	}

	res.Cid = req.MediaID
	mediaInfo, err := makeMsgInfo(ctx, req.MediaID, parse.CidType)
	if err != nil {
		res.Code = http.StatusInternalServerError
		res.Msg = "StatusInternalServerError"
		log.ErrorContextf(ctx, "make msgInfo err:%+v", err)
		w.WriteHeader(http.StatusInternalServerError)
		return errs.New(http.StatusInternalServerError, err.Error())
	}

	valueData, err := fillValueData(ctx, mediaInfo)
	if err != nil {
		res.Code = http.StatusInternalServerError
		res.Msg = "StatusInternalServerError"
		return errs.New(http.StatusInternalServerError, err.Error())
	}

	update := &model.UpdateInfo{}
	// 不更新
	update.IsUpdate = false
	update.UpdateFlag = false
	update.FieldMap = make(map[string][]string, 0)
	err = handle.CalValueTimeState(ctx, valueData, update)
	if err != nil {
		res.Code = http.StatusInternalServerError
		res.Msg = "StatusInternalServerError"
		return errs.New(http.StatusInternalServerError, err.Error())
	}

	res.IpValueStatus = update.GetCalculateValue(parse.FieldValueTimeState, valueData.ValueTimeState)
	b, _ := json.Marshal(res)
	w.Write(b)
	w.WriteHeader(http.StatusOK)
	return nil
}

const qwUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=83cef7fa-cd2b-43c9-bc81-4c2b8432da38"

type msgBody struct {
	MsgType string `json:"msgtype"`
	Text    struct {
		Content string   `json:"content"`
		MList   []string `json:"mentioned_list"`
	} `json:"text"`
}

// SendMsg 发送通知消息
func SendMsg(notifyInfo interface{}) error {
	var notifyContent string
	switch v := notifyInfo.(type) {
	case ipOnlineStatusInfo:
		notifyContent = fmt.Sprintf("[%s]IP实时计算重试多次仍然失败，请关注并检查数据，错误信息[%+v]", v.cid, v.err)
	case valueStatusInfo:
		notifyContent = fmt.Sprintf("[%s]价值认定计算重试多次仍然失败，请关注并检查数据，错误信息[%+v]", v.cid, v.err)
	default:
		return nil
	}

	body := &msgBody{
		MsgType: "text",
	}

	body.Text.Content = notifyContent
	body.Text.MList = []string{"danielschen"}
	b, err := codec.Marshal(codec.SerializationTypeJSON, body)
	if err != nil {
		return err
	}
	if _, err = http.Post(qwUrl, "application/json", bytes.NewReader(b)); err != nil {
		return err
	}
	return nil
}
