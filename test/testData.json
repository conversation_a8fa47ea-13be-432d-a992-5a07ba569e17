[{"CaseName": "Test_outer_circulation_monitor-video_media_notify", "Type": "", "CaseDesc": "测试HandleCoverMsg（可重入，为了方便通过流水线；当有新功能加入时，最好构造合法的case来进行接口测试）", "AuthorInfo": "mooyang", "CostTimeMS": 5000, "TestSuite": "suitName_1", "ServiceName": "trpc.crawler_schedule.outer_circulation_monitor.http", "RequestJson": {"id": "p003642lkv5", "dataSetId": 2001, "timeStamp": 1569247774, "operatorName": "mootest", "modifyFieldInfos": {"testfield": {"id": 800005, "old": "0", "new": "4"}}}, "CheckList": [], "CustomizeParam": {"Url": "/cover_info_update", "Method": "POST", "Header": null}}]