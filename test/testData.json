[{"CaseName": "Test_cover_videos_auto_operation-video_checkup", "Type": "", "CaseDesc": "一个不经过主逻辑的case（可重入，为了方便通过流水线；当有新功能加入时，最好构造合法的case来进行接口测试）", "AuthorInfo": "mooyang", "CostTimeMS": 5000, "TestSuite": "suitName_1", "RequestJson": {"id": "p003642lkv5", "dataSetId": 2001, "timeStamp": 1569247774, "operatorName": "mootest", "modifyFieldInfos": {"testfield": {"id": 800005, "old": "0", "new": "4"}}}, "CheckList": [], "CustomizeParam": {"Url": "/video_msghub_cb/cover_videos_auto_operation", "Method": "POST", "Header": null}}]