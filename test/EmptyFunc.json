[{"CaseName": "caseName1", "Type": "", "CaseDesc": "AccessLayer", "CostTimeMS": 0, "TestSuite": "suitName1", "ServiceName": "trpc.storage_service.access_layer.MediaInterface", "MethodName": "EmptyFunc", "ProtoFile": "", "ReqBody": "trpc.storage_service.access_layer.MediaInterface.EmptyReq", "RspBody": "trpc.storage_service.access_layer.MediaInterface.EmptyRsp", "Protocol": "trpc", "Head": "", "RequestJson": {}, "CheckList": []}]