[{"CaseName": "caseName1", "Type": "", "CaseDesc": "Adaptor<PERSON>ayer", "CostTimeMS": 0, "TestSuite": "suitName1", "ServiceName": "trpc.storage_service.adaptor_layer.DataAdaptor", "MethodName": "EmptyFunc", "ProtoFile": "", "ReqBody": "trpc.storage_service.adaptor_layer.DataAdaptor.EmptyReq", "RspBody": "trpc.storage_service.adaptor_layer.DataAdaptor.EmptyRsp", "Protocol": "trpc", "Head": "", "RequestJson": {}, "CheckList": []}]