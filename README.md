# 标题

[![BK Pipelines Status](https://api.bkdevops.qq.com/process/api/external/pipelines/projects/video-media-backend/p-f7cd345390ec4c1f9381bc67db6d924c/badge?X-DEVOPS-PROJECT-ID=video-media-backend)](http://api.devops.oa.com/process/api-html/user/builds/projects/video-media-backend/pipelines/p-f7cd345390ec4c1f9381bc67db6d924c/latestFinished?X-DEVOPS-PROJECT-ID=video-media-backend) [![VEPC Score](https://pbaccess.video.qq.com/trpc.vepc_tools.vepc_banner.http/score?vappid=70420569&vsecret=6db9bf8c97010b57dd401166223ccd57b8d009c257261d53&origin=1&path=video_media%2Fcover_msghub_cb%2Fcover_standard_series_flag)](https://pbaccess.video.qq.com/trpc.vepc_tools.vepc_banner.http/detail?vappid=70420569&vsecret=6db9bf8c97010b57dd401166223ccd57b8d009c257261d53&origin=1&path=video_media%2Fcover_msghub_cb%2Fcover_standard_series_flag)


## 目录

[TOC]

## 背景

### 计算专辑是否标准剧集程序重构（798字段）

当前程序为扫表计算，计算结果直接写DB；
需要改为接数据总线，计算结果写新存储服务；

旧代码路径：mediaplat/local/module/recal_series_flag.cpp 

### 代码逻辑
订阅了专辑的长视频列表long_video_list，专辑type；

计算专辑是否标准剧集：
1. 针对电视剧(2)、动漫(3)频道下的上架专辑进行计算
2. 获取专辑下的正片视频列表
3. 对每个正片视频计算：获取视频标题
4. 更新“是否标准剧集（798）”字段为是 ：所有视频标题需满足“title_num”格式，或“番外num”格式

注意：不考虑对已经在专辑内的视频变更标题的情况，一般运营起好标题才会加入专辑；若考虑这种情况，还需要订阅视频title的变更，这样消息量会较大

## 接口

如果是对外提供服务，那么在这里*详细*描述你的接口，并且给出一个示例。

### 子章节(可选)

## API/SDK

如果是对外提供一个API/SDK的公共库，那么在这里详细描述你的API/SDK，并且给出一个示例。

### 子章节(可选)

## 架构

这个章节图文并茂的描述你的架构。

## 运维

这个章节需要描述当你服务出问题时的一些操作，比如：

### 服务降级
### 问题定位
### 监控查看
### 日志查看
### More

## 引用

假如你的项目参考或引用了其他项目资料，请列出来。

- [视频后台研发手册](https://git.code.oa.com/videobase/videonavi)
- More

## 更多章节(可选)
