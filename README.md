
# distribution_server

该项目是媒资分发中负责和订阅用户交互的一环。该系统从kafka中读取上游ModificationReceiverService整理好的消息，然后分发到各个订阅者手中。系统每一个用户订阅一个kafka的consumerGroup，所以整体的逻辑是隔离的

## 项目介绍     README

该项目是媒资分发中负责和订阅用户交互的一环，kafka消费模型和虫洞
因为虫洞系统对消费进行了鉴权，就是说每一个consumeGroup都必须在虫洞系统中注册。每当有一个新的订阅者，我们都需要手动取虫洞平台注册一个新消费者（测试和线上各有一个消费者）并且审核通过


## 快速上手     Getting Started

使用者如何快速上手使用本组织/项目，比如API列表和运维信息等。

### API

如果是对外提供服务API或公共库API，那么在这里**详细**描述你的接口，并且给出一个示例。

### 运维

这个章节需要描述当你服务出问题时的一些操作，比如：

- 服务降级
- 问题定位
- 监控查看
- 日志查看

## 常见问题     FAQ

本组织/项目的常见通用问题和官方解答

## 行为准则    Code Of Conduct

本组织/项目在代码协作方面需遵循的责任、范围、软件许可证、冲突解决等章程

## 如何加入    How To Join

本组织/项目有明确的如何加入和贡献的文字说明

## 团队介绍    Members

本组织/项目的角色分工、人名和联络方式、官方交流/沟通渠道

## 参考    References

假如你的项目参考或引用了其他项目资料，请列出来。

- [视频后台研发手册](https://git.code.oa.com/videobase/videonavi)

