# 标题

[![BK Pipelines Status](https://api.bkdevops.qq.com/process/api/external/pipelines/projects/video-media-backend/p-eb39a11136fb48a7a9657f4d2450480a/badge?X-DEVOPS-PROJECT-ID=video-media-backend)](http://api.devops.oa.com/process/api-html/user/builds/projects/video-media-backend/pipelines/p-eb39a11136fb48a7a9657f4d2450480a/latestFinished?X-DEVOPS-PROJECT-ID=video-media-backend)  [![VEPC Score](https://pbaccess.video.qq.com/trpc.vepc_tools.vepc_banner.http/score?vappid=70420569&vsecret=6db9bf8c97010b57dd401166223ccd57b8d009c257261d53&origin=1&path=video_media%2Fvideo_msghub_cb%2Fsync_media_formal_test_data)](https://pbaccess.video.qq.com/trpc.vepc_tools.vepc_banner.http/detail?vappid=70420569&vsecret=6db9bf8c97010b57dd401166223ccd57b8d009c257261d53&origin=1&path=video_media%2Fvideo_msghub_cb%2Fsync_media_formal_test_data)

一行简单的描述。(短描述)

## 项目介绍     README

本组织/项目的整体说明概览

## 快速上手     Getting Started

使用者如何快速上手使用本组织/项目，比如API列表和运维信息等。

### API

如果是对外提供服务API或公共库API，那么在这里**详细**描述你的接口，并且给出一个示例。

### 运维

这个章节需要描述当你服务出问题时的一些操作，比如：

- 服务降级
- 问题定位
- 监控查看
- 日志查看

## 常见问题     FAQ

本组织/项目的常见通用问题和官方解答

## 行为准则    Code Of Conduct

本组织/项目在代码协作方面需遵循的责任、范围、软件许可证、冲突解决等章程

## 如何加入    How To Join

本组织/项目有明确的如何加入和贡献的文字说明

## 团队介绍    Members

本组织/项目的角色分工、人名和联络方式、官方交流/沟通渠道

## 参考    References

假如你的项目参考或引用了其他项目资料，请列出来。

- [视频后台研发手册](https://git.code.oa.com/videobase/videonavi)

