# 数据接入层服务

[![BK Pipelines Status](https://api.bkdevops.qq.com/process/api/external/pipelines/projects/video-media-backend/p-8c7d22588e6e4747b1c25007bd5a6547/badge?X-DEVOPS-PROJECT-ID=video-media-backend)](http://api.devops.oa.com/process/api-html/user/builds/projects/video-media-backend/pipelines/p-8c7d22588e6e4747b1c25007bd5a6547/latestFinished?X-DEVOPS-PROJECT-ID=video-media-backend)  [![VEPC Score](https://pbaccess.video.qq.com/trpc.vepc_tools.vepc_banner.http/score?vappid=70420569&vsecret=6db9bf8c97010b57dd401166223ccd57b8d009c257261d53&origin=1&path=video_media%2Fstorage_service%2Faccess_layer)](https://pbaccess.video.qq.com/trpc.vepc_tools.vepc_banner.http/detail?vappid=70420569&vsecret=6db9bf8c97010b57dd401166223ccd57b8d009c257261d53&origin=1&path=video_media%2Fstorage_service%2Faccess_layer)

媒资统一存储接口接入层服务

## 项目介绍     README

* 为业务提供查询、修改底层数据的key，value统一接口，业务无需关注底层存储具体实现。
* 支持mysql，tidb等多种数据源，且支持动态配置。
* 具备鉴权、频控功能。
* 数据变更时通知下游业务。

## 快速上手     Getting Started

可参考项目中的使用样例：[https://git.woa.com/video_media/media_go_commlib/dataaccess](https://git.woa.com/video_media/media_go_commlib/dataaccess)

### API

```
// 数据接入层接口
service MediaInterface
{
    rpc GetMediaInfo(MediaGetRequest) returns (MediaGetResponse) {}
    rpc BatchGetMediaInfo(BatchGetRequest) returns (BatchGetResponse) {}
    rpc GetAllMediaInfo(MediaGetAllRequest) returns (MediaGetAllResponse) {}
    rpc UpdateMediaInfo(MediaUpdateRequest) returns (MediaUpdateResponse) {}
    rpc SearchMediaInfo(MediaSearchRequest) returns (MediaSearchResponse) {}
}
```

[服务协议rick链接](https://trpc.rick.woa.com/rick/pb/detail?detail=1&id=11706)

### 运维

-
问题定位：[业务定位手册](https://doc.weixin.qq.com/doc/w3_AUgAZAb-ACs0wSrKhh0TXSOyqJXxx?scode=AJEAIQdfAAoZcq61rxAUgAZAb-ACs)
-
监控查看：[伽利略监控](https://j.woa.com/service/service-analysis/client?platform=PCG-123&module_name=storage_service.access_layer&_from=PCG-123&env=2)
- 日志查看：[鹰眼日志](http://log2.oa.com/mylog/logQuery/lid/103098)

## 参考    References

- [底层存储服务支持多模存储设计](https://doc.weixin.qq.com/doc/w3_AHIAlQatACgsxz9uAQmSkGPYKfGp4?scode=AJEAIQdfAAoFD1nBgpAHIAlQatACg)

