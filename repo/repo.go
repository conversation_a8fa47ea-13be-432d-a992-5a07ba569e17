// Package repo 依赖层
package repo

import (
	"context"

	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
)

// EventMetaReader 事件总线元数据读取借口
type EventMetaReader interface {
	GetEventBridge(ctx context.Context, eventBridgeID int64) *metadata_access.EventBridge // 根据事件总线ID获取事件总线定义
	GetAllEventBridges(ctx context.Context) []*metadata_access.EventBridge                // 获取所有的事件总线定义
	GetEventSources(ctx context.Context,
		eventBridgeID int64) []*metadata_access.EventSource // 根据事件总线ID获取事件总线下事件源定义，为空表示GetAll
	GetEventType(ctx context.Context, eventBridgeID int64) []*metadata_access.EventType
	GetEventSubscribes(ctx context.Context, eventBridgeID int64,
		eventType string) []*metadata_access.EventSubscribe // eventBridgeID不允许为空，eventType为空或者 * 代表获取bridge下所有的订阅
	GetEventSubscribeByAppID(ctx context.Context, appID string) *metadata_access.EventSubscribe
	GetEventSubscribeByID(ctx context.Context, id int64) *metadata_access.EventSubscribe
}
