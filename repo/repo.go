// Package repo declares repo
package repo

import (
	"gorm.io/gorm"

	trpcgorm "git.code.oa.com/trpc-go/trpc-database/gorm"
)

// IpCheck ip接口
type IpCheck interface {
	IpOnlineStatus
	IpValueStatus
}

var client = &IpCheckImp{}

// NewIpStatusCheckImp 新建ip实时状态检查db实体
func NewIpStatusCheckImp() IpCheck {
	engine, err := trpcgorm.NewClientProxy("trpc.mysql.ip_status_check")
	if err != nil {
		panic(err)
	}
	client.db = engine
	return &IpCheckImp{db: engine}
}

// GetIpStatusRepoClient 获取client
func GetIpStatusRepoClient() *IpCheckImp {
	return client
}

// IpCheckImp 实例
type IpCheckImp struct {
	db *gorm.DB
}
