package repo

import (
	"context"
	"time"

	"gorm.io/gorm/clause"

	"git.code.oa.com/video_media/ip_status_update/model"
)

// IpValueStatus ip价值认定接口
type IpValueStatus interface {
	InsertIpValueData(ctx context.Context, record *model.IpValueStatusData) error
	GetNeedReCalValueData(ctx context.Context) ([]string, error)
}

// InsertIpValueData 插入ip价值认定数据
func (d *IpCheckImp) InsertIpValueData(ctx context.Context, record *model.IpValueStatusData) error {
	return d.db.WithContext(ctx).Table("t_value_status_check").Model(record).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "c_data_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"c_premiere_time", "c_value_endtime", "c_value_status"}),
	}).Create(record).Error
}

// GetNeedReCalValueData 获取需要重算价值认定的数据
func (d *IpCheckImp) GetNeedReCalValueData(ctx context.Context) ([]string, error) {
	now := time.Now()
	tenMinutesAgo := time.Now().Add(-10 * time.Minute)
	var datas []model.IpValueStatusData
	query := d.db.WithContext(ctx).Table("t_value_status_check").
		Where("c_premiere_time BETWEEN ? AND ?", tenMinutesAgo, now).
		Or("c_value_endtime BETWEEN ? AND ?", tenMinutesAgo, now)
	if err := query.Find(&datas).Error; err != nil {
		return []string{}, err
	}

	var cids []string
	for _, v := range datas {
		cids = append(cids, v.DataID)
	}
	return cids, nil
}
