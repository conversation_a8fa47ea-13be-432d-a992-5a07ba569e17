// Package repo 无极-repo实现
package repo

import (
	"context"
	"fmt"

	"git.code.oa.com/open-wuji/go-sdk/wujiclient"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	conf "git.code.oa.com/video_media/media_go_commlib/mediapkg/config"
	"git.woa.com/trpcprotocol/media_event_hub/common_errcode"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
)

// wuJiBaseConfig 无极配置信息
type wuJiBaseConfig struct {
	AppID     string `yaml:"appID"`
	SchemaID  string `yaml:"schemaID"`
	SchemaKey string `yaml:"schemaKey"`
}

// wuJiMetaConfig 元数据无极配置
type wuJiMetaConfig struct {
	WuJiConfigEventBridge         wuJiBaseConfig `yaml:"wuJiConfigEventBridge"`
	WuJiConfigEventSource         wuJiBaseConfig `yaml:"wuJiConfigEventSource"`
	WuJiConfigEventType           wuJiBaseConfig `yaml:"wuJiConfigEventType"`
	WuJiConfigEventSubscribe      wuJiBaseConfig `yaml:"wuJiConfigEventSubscribe"`
	WuJiConfigEventSubscribeAppID wuJiBaseConfig `yaml:"wuJiConfigEventSubscribeAppID"`
	WuJiConfigEventTarget         wuJiBaseConfig `yaml:"wuJiConfigEventTarget"`
	WuJiConfigEventSubscribeRule  wuJiBaseConfig `yaml:"wuJiConfigEventSubscribeRule"`
}

// NewEventMetaReader 初始化无极元数据读取包
var NewEventMetaReader = func() (EventMetaReader, error) {
	if err := conf.InitCfg("rainbow1", "eda_meta_config.yaml", &wuJiMetaConfig{}); err != nil {
		return nil, errs.New(int(common_errcode.ErrCode_INIT_META_DATA), fmt.Sprintf("init config err: %+v", err))
	}
	metaCfg := conf.GetCfg("rainbow1", "eda_meta_config.yaml").(*wuJiMetaConfig)
	var err error
	eventBridgeFilter, err = initWuJi([]string{"id"}, metaCfg.WuJiConfigEventBridge, eventBridge{})
	if err != nil {
		return nil, err
	}
	eventSourceFilter, err = initWuJi([]string{"bridge_id", "status"}, metaCfg.WuJiConfigEventSource, eventSource{})
	if err != nil {
		return nil, err
	}
	eventTypeFilter, err = initWuJi([]string{"bridge_id"}, metaCfg.WuJiConfigEventType, eventType{})
	if err != nil {
		return nil, err
	}
	eventSubscribeFilter, err = initWuJi([]string{"status", "bridge_id"}, metaCfg.WuJiConfigEventSubscribe,
		eventSubscribe{})
	if err != nil {
		return nil, err
	}
	eventSubscribeAppFilter, err = initWuJi([]string{"status", "app_id"}, metaCfg.WuJiConfigEventSubscribeAppID,
		eventSubscribe{})
	if err != nil {
		return nil, err
	}
	eventSubscribeIDFilter, err = initWuJi([]string{"status", "id"}, metaCfg.WuJiConfigEventSubscribeAppID,
		eventSubscribe{})
	if err != nil {
		return nil, err
	}
	eventSubscribeRuleFilter, err = initWuJi([]string{"status", "subscribe_id"}, metaCfg.WuJiConfigEventSubscribeRule,
		eventSubscribeRule{})
	if err != nil {
		return nil, err
	}
	eventTargetFilter, err = initWuJi([]string{"status", "subscribe_id"}, metaCfg.WuJiConfigEventTarget,
		eventTarget{})
	if err != nil {
		return nil, err
	}
	return &eventMetaWuJiReader{}, nil
}

var (
	eventBridgeFilter        wujiclient.FilterInterface
	eventSourceFilter        wujiclient.FilterInterface
	eventTypeFilter          wujiclient.FilterInterface
	eventSubscribeFilter     wujiclient.FilterInterface
	eventSubscribeAppFilter  wujiclient.FilterInterface
	eventSubscribeIDFilter   wujiclient.FilterInterface
	eventSubscribeRuleFilter wujiclient.FilterInterface
	eventTargetFilter        wujiclient.FilterInterface
)

func initWuJi(fields []string, c wuJiBaseConfig, template interface{}) (wujiclient.FilterInterface, error) {
	f, err := wujiclient.NewClientWithFilter(fields, "", template,
		wujiclient.WithAppID(c.AppID),
		wujiclient.WithSchemaID(c.SchemaID),
		wujiclient.WithSchemaKey(c.SchemaKey),
		wujiclient.EnableFilter(),
		wujiclient.EnsureInitLatestData(),
		wujiclient.WithExtraParam("datetime", "timestamp"))
	if err != nil {
		return nil, errs.New(int(common_errcode.ErrCode_INIT_META_DATA), fmt.Sprintf("init WuJi err: %+v", err))
	}
	return f, nil
}

type eventMetaWuJiReader struct {
}

func (e eventMetaWuJiReader) GetEventSubscribeByAppID(ctx context.Context,
	appID string) *metadata_access.EventSubscribe {
	tmp := eventSubscribeAppFilter.GetWhere(fmt.Sprintf("status=1&app_id=%s", appID))
	if tmp == nil {
		return nil
	}
	for _, item := range tmp {
		if item == nil {
			continue
		}
		pbModel := item.(*eventSubscribe).convertToPB()
		pbModel.Rules = e.getEventSubscribeRules(ctx, pbModel.Id)
		pbModel.Targets = e.getEventTargets(ctx, pbModel.Id)
		return pbModel
	}
	return nil
}

func (e eventMetaWuJiReader) GetEventSubscribeByID(ctx context.Context, id int64) *metadata_access.EventSubscribe {
	tmp := eventSubscribeIDFilter.GetWhere(fmt.Sprintf("status=1&id=%d", id))
	if tmp == nil {
		return nil
	}
	for _, item := range tmp {
		if item == nil {
			continue
		}
		pbModel := item.(*eventSubscribe).convertToPB()
		pbModel.Rules = e.getEventSubscribeRules(ctx, pbModel.Id)
		pbModel.Targets = e.getEventTargets(ctx, pbModel.Id)
		return pbModel
	}
	return nil
}

func (e eventMetaWuJiReader) GetEventSubscribes(ctx context.Context, eventBridgeID int64,
	eventType string) []*metadata_access.EventSubscribe {
	// GetAll
	if eventBridgeID == 0 {
		all := eventSubscribeFilter.GetALL()
		subscribes := all.([]*eventSubscribe)
		var result []*metadata_access.EventSubscribe
		for _, subscribe := range subscribes {
			if subscribes == nil {
				continue
			}
			item := subscribe.convertToPB()
			if item.Status == metadata_access.Status_DISABLE {
				continue
			}
			item.Rules = e.getEventSubscribeRules(ctx, item.Id)
			item.Targets = e.getEventTargets(ctx, item.Id)
			result = append(result, item)
		}
		return result
	}

	// GetOne
	tmp := eventSubscribeFilter.GetWhere(fmt.Sprintf("status=1&bridge_id=%d", eventBridgeID))
	var result []*metadata_access.EventSubscribe
	for _, item := range tmp {
		if item == nil {
			continue
		}
		pbModel := item.(*eventSubscribe).convertToPB()
		pbModel.Rules = e.getEventSubscribeRules(ctx, pbModel.Id)
		if eventType != "" && eventType != "*" {
			if _, ok := pbModel.Rules[eventType]; !ok {
				continue
			}
		}
		pbModel.Targets = e.getEventTargets(ctx, pbModel.Id)
		result = append(result, pbModel)
	}
	return result
}

func (e eventMetaWuJiReader) getEventSubscribeRules(ctx context.Context,
	subscribeID int64) map[string]*metadata_access.EventSubscribeRule {
	// step1 根据订阅ID获取所有的订阅规则
	tmp := eventSubscribeRuleFilter.GetWhere(fmt.Sprintf("status=1&subscribe_id=%d", subscribeID))
	if tmp == nil {
		return nil
	}
	var rules []*metadata_access.EventSubscribeRule
	for _, item := range tmp {
		if item == nil {
			continue
		}
		rules = append(rules, item.(*eventSubscribeRule).convertToPB())
	}
	result := make(map[string]*metadata_access.EventSubscribeRule)
	for _, rule := range rules {
		result[rule.WatchType] = rule
	}
	return result
}

func (e eventMetaWuJiReader) getEventTargets(ctx context.Context, subscribeID int64) []*metadata_access.EventTarget {
	tmp := eventTargetFilter.GetWhere(fmt.Sprintf("status=1&subscribe_id=%d", subscribeID))
	var result []*metadata_access.EventTarget
	for _, item := range tmp {
		if item == nil {
			continue
		}
		result = append(result, item.(*eventTarget).convertToPB())
	}
	return result
}

func (e eventMetaWuJiReader) GetEventType(ctx context.Context, eventBridgeID int64) []*metadata_access.EventType {
	var result []*metadata_access.EventType
	if eventBridgeID == 0 {
		tmp := eventTypeFilter.GetALL()
		types := tmp.([]*eventType)
		for _, item := range types {
			result = append(result, item.convertToPB())
		}
	} else {
		tmp := eventTypeFilter.GetWhere(fmt.Sprintf("bridge_id=%d", eventBridgeID))
		if tmp == nil {
			return nil
		}
		for _, item := range tmp {
			if item == nil {
				continue
			}
			result = append(result, item.(*eventType).convertToPB())
		}
	}
	return result
}

func (e eventMetaWuJiReader) GetEventBridge(ctx context.Context,
	eventBridgeID int64) *metadata_access.EventBridge {
	tmp := eventBridgeFilter.Get(fmt.Sprintf("id=%d", eventBridgeID))
	if tmp == nil {
		return nil
	}
	return tmp.(*eventBridge).convertToPB()
}

func (e eventMetaWuJiReader) GetAllEventBridges(ctx context.Context) []*metadata_access.EventBridge {
	tmp := eventBridgeFilter.GetALL()
	if tmp == nil {
		return nil
	}
	bridges := tmp.([]*eventBridge)
	var result []*metadata_access.EventBridge
	for _, bridge := range bridges {
		result = append(result, bridge.convertToPB())
	}
	return result
}

func (e eventMetaWuJiReader) GetEventSources(ctx context.Context,
	eventBridgeID int64) []*metadata_access.EventSource {
	var result []*metadata_access.EventSource
	if eventBridgeID == 0 {
		tmp := eventSourceFilter.GetALL()
		sources := tmp.([]*eventSource)
		for _, source := range sources {
			item := source.convertToPB()
			if item.Status == metadata_access.Status_DISABLE {
				continue
			}
			result = append(result, item)
		}
	} else {
		tmp := eventSourceFilter.GetWhere(fmt.Sprintf("bridge_id=%d&status=1", eventBridgeID))
		if tmp == nil {
			return nil
		}
		for _, item := range tmp {
			if item == nil {
				continue
			}
			result = append(result, item.(*eventSource).convertToPB())
		}
	}
	return result
}
