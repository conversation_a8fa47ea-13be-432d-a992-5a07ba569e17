package repo

import (
	"encoding/json"
	"time"

	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
)

type eventBridge struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	Desc       string `json:"desc"`
	Topic      string `json:"topic"`
	Consumer   string `json:"consumer"`
	Owners     string `json:"owners"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
	Operator   string `json:"operator"`
}

func (e *eventBridge) convertToPB() *metadata_access.EventBridge {
	var owners []string
	var consumer metadata_access.ConsumerGroup
	json.Unmarshal([]byte(e.Consumer), &consumer)
	json.Unmarshal([]byte(e.Owners), &owners)
	return &metadata_access.EventBridge{
		Id:         e.ID,
		Name:       e.Name,
		Desc:       e.Desc,
		Consumer:   &consumer,
		Owners:     owners,
		CreateTime: time.UnixMilli(e.CreateTime).Format(time.DateTime),
		UpdateTime: time.UnixMilli(e.UpdateTime).Format(time.DateTime),
		Operator:   e.Operator,
	}
}

type eventSource struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	Desc       string `json:"desc"`
	BridgeID   int64  `json:"bridge_id"`
	Status     int    `json:"status"`
	Owners     string `json:"owners"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
	Operator   string `json:"operator"`
}

func (e *eventSource) convertToPB() *metadata_access.EventSource {
	var owners []string
	json.Unmarshal([]byte(e.Owners), &owners)
	return &metadata_access.EventSource{
		Id:         e.ID,
		Name:       e.Name,
		Desc:       e.Desc,
		BridgeId:   e.BridgeID,
		Status:     metadata_access.Status(e.Status),
		Owners:     owners,
		CreateTime: time.UnixMilli(e.CreateTime).Format(time.DateTime),
		UpdateTime: time.UnixMilli(e.UpdateTime).Format(time.DateTime),
		Operator:   e.Operator,
	}
}

type eventType struct {
	ID           int64  `json:"id"`
	BridgeID     int64  `json:"bridge_id"`
	SourceIDList string `json:"source_id_list"`
	Type         string `json:"type"`
	Name         string `json:"name"`
	Desc         string `json:"desc"`
	CreateTime   int64  `json:"create_time"`
	UpdateTime   int64  `json:"update_time"`
	Operator     string `json:"operator"`
	DataSchema   string `json:"data_schema"`
}

func (e *eventType) convertToPB() *metadata_access.EventType {
	var sourceIDList []int64
	json.Unmarshal([]byte(e.SourceIDList), &sourceIDList)
	return &metadata_access.EventType{
		Id:           e.ID,
		Name:         e.Name,
		Desc:         e.Desc,
		SourceIdList: sourceIDList,
		Type:         e.Type,
		BridgeId:     e.BridgeID,
		CreateTime:   time.UnixMilli(e.CreateTime).Format(time.DateTime),
		UpdateTime:   time.UnixMilli(e.UpdateTime).Format(time.DateTime),
		Operator:     e.Operator,
		DataSchema:   e.DataSchema,
	}
}

type eventSubscribe struct {
	ID             int64  `json:"id"`
	Name           string `json:"name"`
	Desc           string `json:"desc"`
	Status         int    `json:"status"`
	BridgeID       int64  `json:"bridge_id"`
	ProcessorTopic string `json:"processor_topic"`
	AppID          string `json:"app_id"`
	SecretKey      string `json:"secret_key"`
	Owners         string `json:"owners"`
	Operator       string `json:"operator"`
	CreateTime     int64  `json:"create_time"`
	UpdateTime     int64  `json:"update_time"`
}

func (e *eventSubscribe) convertToPB() *metadata_access.EventSubscribe {
	var producer metadata_access.Producer
	var owners []string
	unmarshal(e.ProcessorTopic, &producer)
	unmarshal(e.Owners, &owners)
	return &metadata_access.EventSubscribe{
		Id:             e.ID,
		Name:           e.Name,
		Desc:           e.Desc,
		Status:         metadata_access.Status(e.Status),
		BridgeId:       e.BridgeID,
		ProcessorTopic: &producer,
		AppId:          e.AppID,
		SecretKey:      e.SecretKey,
		Owners:         owners,
		Operator:       e.Operator,
		CreateTime:     time.Unix(e.CreateTime, 0).Format(time.DateTime),
		UpdateTime:     time.Unix(e.UpdateTime, 0).Format(time.DateTime),
	}
}

type eventSubscribeRule struct {
	ID               int64  `json:"id"`
	SubscribeID      int64  `json:"subscribe_id"`
	WatchType        string `json:"watch_type"`
	MatchRule        string `json:"match_rule"`
	CutJsonpath      string `json:"cut_jsonpath"`
	ExtDataMatchRule string `json:"ext_data_match_rule"`
	Status           int    `json:"status"`
	CreateTime       int64  `json:"create_time"`
	UpdateTime       int64  `json:"update_time"`
}

func (e *eventSubscribeRule) convertToPB() *metadata_access.EventSubscribeRule {
	var matchRule metadata_access.MatchRuleGroupsConfig
	cuts := map[string]string{}
	var extGetRule []*metadata_access.ExtDataConfig
	unmarshal(e.MatchRule, &matchRule)
	unmarshal(e.CutJsonpath, &cuts)
	unmarshal(e.ExtDataMatchRule, &extGetRule)
	return &metadata_access.EventSubscribeRule{
		Id:               e.ID,
		SubscribeId:      e.SubscribeID,
		WatchType:        e.WatchType,
		MatchRule:        &matchRule,
		CutJsonpath:      cuts,
		ExtDataMatchRule: extGetRule,
		Status:           metadata_access.Status(e.Status),
		CreateTime:       time.Unix(e.CreateTime, 0).Format(time.DateTime),
		UpdateTime:       time.Unix(e.UpdateTime, 0).Format(time.DateTime),
	}
}

type eventTarget struct {
	ID              int64  `json:"id"`
	SubscribeID     int64  `json:"subscribe_id"`
	WatchTypes      string `json:"watch_types"`
	Type            int    `json:"type"`
	Target          string `json:"target"`
	BackoffStrategy int    `json:"backoff_strategy"`
	FixedBackoffGap int64  `json:"fixed_backoff_gap"`
	IsBlock         int    `json:"is_block"`
	RetryTimes      int64  `json:"retry_times"`
	EnableDlq       int    `json:"enable_dlq"`
	DlqProducer     string `json:"dlq_producer"`
	DlqConsumer     string `json:"dlq_consumer"`
	Plugin          string `json:"plugin"`
	Delay           int64  `json:"delay"`
	Timeout         int64  `json:"timeout"`
	ConsumerGroup   string `json:"consumer_group"`
	Status          int    `json:"status"`
	CreateTime      int64  `json:"create_time"`
	UpdateTime      int64  `json:"update_time"`
}

func (e *eventTarget) convertToPB() *metadata_access.EventTarget {
	var dlqConsumer metadata_access.ConsumerGroup
	var plugin metadata_access.EventTargetPlugin
	var watchTypes []string
	unmarshal(e.WatchTypes, &watchTypes)
	unmarshal(e.DlqConsumer, &dlqConsumer)
	unmarshal(e.Plugin, &plugin)
	return &metadata_access.EventTarget{
		Id:              e.ID,
		SubscribeId:     e.SubscribeID,
		WatchTypes:      watchTypes,
		Type:            metadata_access.EventTargetType(e.Type),
		BackoffStrategy: metadata_access.EventTargetRetryStrategy(e.BackoffStrategy),
		FixedBackoffGap: e.FixedBackoffGap,
		IsBlock:         e.IsBlock == 1,
		RetryTimes:      e.RetryTimes,
		EnableDlq:       e.EnableDlq == 1,
		DlqConsumer:     &dlqConsumer,
		Plugin:          &plugin,
		Delay:           e.Delay,
		Timeout:         e.Timeout,
		ConsumerGroup:   e.ConsumerGroup,
		CreateTime:      time.Unix(e.CreateTime, 0).Format(time.DateTime),
		UpdateTime:      time.Unix(e.UpdateTime, 0).Format(time.DateTime),
		Status:          metadata_access.Status(e.Status),
		Target:          e.Target,
	}
}

func unmarshal(str string, m any) {
	json.Unmarshal([]byte(str), &m)
}
