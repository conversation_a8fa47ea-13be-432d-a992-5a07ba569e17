package repo

import (
	"context"
	"time"

	"gorm.io/gorm/clause"

	"git.code.oa.com/video_media/ip_status_update/model"
)

// IpOnlineStatus ip实时状态接口
type IpOnlineStatus interface {
	InsertIpOnlineStatusData(ctx context.Context, record *model.IpOnlineStatusData) error
	GetNeedReCalIpOnlineStatusData(ctx context.Context) ([]string, error)
}

// InsertIpOnlineStatusData 插入ip实时状态数据
func (d *IpCheckImp) InsertIpOnlineStatusData(ctx context.Context, record *model.IpOnlineStatusData) error {
	return d.db.WithContext(ctx).Table("t_ip_online_status_check").Model(record).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "c_data_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"c_premiere_time", "c_operation_end_time",
			"c_afterheat_time_moretime", "c_afterheat_time", "c_longtail_time_moretime", "c_ip_online_status"}),
	}).Create(record).Error
}

// GetNeedReCalIpOnlineStatusData 获取需要重算ip实时状态的数据
func (d *IpCheckImp) GetNeedReCalIpOnlineStatusData(ctx context.Context) ([]string, error) {
	now := time.Now()
	tenMinutesAgo := time.Now().Add(-10 * time.Minute)
	var datas []model.IpOnlineStatusData
	query := d.db.WithContext(ctx).Table("t_ip_online_status_check").
		Where("c_premiere_time BETWEEN ? AND ?", tenMinutesAgo, now).
		Or("c_operation_end_time BETWEEN ? AND ?", tenMinutesAgo, now).
		Or("c_afterheat_time_moretime BETWEEN ? AND ?", tenMinutesAgo, now).
		Or("c_afterheat_time BETWEEN ? AND ?", tenMinutesAgo, now).
		Or("c_longtail_time_moretime BETWEEN ? AND ?", tenMinutesAgo, now)
	if err := query.Find(&datas).Error; err != nil {
		return []string{}, err
	}

	var cids []string
	for _, v := range datas {
		cids = append(cids, v.DataID)
	}
	return cids, nil
}
