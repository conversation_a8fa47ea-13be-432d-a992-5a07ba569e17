syntax = "proto3";

package trpc.storage_service.common;
option go_package="git.code.oa.com/trpcprotocol/storage_service/common_storage_common";

// 字段类型存储
enum EnumFieldType
{
    // 占位使用
    InValidType = 0;
    // 字符类型
    FieldTypeStr = 1;
    // 整型向量
    FieldTypeIntVec = 2;
    // Set类型，支持排序操作
    FieldTypeSet = 3;
    // Map类型
    FieldTypeMap = 4;
}

// 字段更新方式
enum EnumUpdateType
{
    // 占位使用
    UpInValidType = 0;
    // 覆盖写入
    UpdateTypeSet = 1;
    // 删除写入
    UpdateTypeDel = 2;
    // 追加写入
    UpdateTypeAppend = 3;
    // 插入写入(Set,Map类型支持)
    UpdateTypeInsert = 4;
    // 重排序(Set,Map类型支持)
    UpdateTypeReorder = 5;
    // 元素顺序反转(Set,Map类型支持)
    UpdateTypeReverse = 6;
}

// 媒资接口返回码
enum EnumMediaErrorCode
{
    // 操作成功
    RetSuccess = 0;
    // 用户不存在
    RetNoApp = 1;
    // 用户鉴权失败
    RetNoAuth = 2;
    // 用户请求超载
    RetOverApplyReq = 3;
    // 用户字段配置不存在
    RetNoAppField = 4;
    // 无字段操作权限
    RetNoPermission = 5;
    // 字段不存在
    RetFieldInfoNotExist = 6;
    // 路由配置不存在
    RetNoRouteCfg = 7;
    // 数据操作非法
    RetInvalidOpType = 8;
    // 数据连接对象失败
    RetNoRouteStoreItem = 9;
    // 设备信息不存在
    RetNoDevCfg = 10;
    // 连接数据源失败
    RetFailConnectDataSource = 11;
    // 数据库查询字段失败
    RetFailSelect = 12;
    // 更新选项型字段失败(内部)
    RetFailUpdateVec = 13;
    // 更新全部字段失败(内部)
    RetFailUpdateAll = 14;
    // 更新字段操作失败
    RetFailUpdate = 15;
    // 内部错误
    RetInnerErr = 16;
    // 调用数据适配层服务失败
    RetCallAdaptorErr = 17;
    // 没有操作权限
    RetNoOpRight = 18;
    // 非法的数据集id
    RetInvalidDataSet = 19;
}

// 返回信息
message CommRetInfo
{
    // 返回错误码
    EnumMediaErrorCode errCode = 1;
    // 返回错误信息
    string errMsg = 2;
    // 失败错误列表（key:fieldName）
    map<string, EnumMediaErrorCode> failList = 3;
}

// MapValue map类型值
message MapValue
{
    // 值类型
    EnumFieldType type = 1;
    // 字符串类型值
    string strValue = 2;
    // 列表类型值
    repeated string vecStr = 3;
}

// 字段信息
message FieldInfo
{
    // 字段名
    string fieldName = 1;
    // 内部字段名
    string innerFieldName = 2;
    // 字段id
    uint32 fieldId = 3;
    // 字段类型
    EnumFieldType fieldType = 4;
    // 字符串字段值
    string strValue = 5;
    // 整型向量
    repeated uint32 vecInt = 6;
    // 列表字段值
    repeated string vecStr = 7;
    // map字段值
    map<string,MapValue> mapVal = 8;
}

// 媒资字段信息
message UpdateFieldInfo
{
    // 字段信息
    FieldInfo fieldInfo = 1;
    // 更新方式
    EnumUpdateType updateType = 2;
    // 插入位置(仅UpdateTypeInsert,UpdateTypeReorder支持)
    int32 pos = 3;
}

// 变更字段信息
message ModifyFieldInfo
{
    // 字段名
    string fieldName = 1;
    // 字段id
    uint32 fieldId = 2;
    // 字段类型
    EnumFieldType fieldType = 3;
    // 字符串字段原值
    string oldStrValue = 4;
    // 字符串字段新值
    string newStrValue = 5;
    // 整型向量原值
    repeated uint32 oldVecInt = 6;
    // 整型向量新值
    repeated uint32 newVecInt = 7;
    // 列表字段原值
    repeated string oldVecStr = 8;
    // 列表字段新值
    repeated string newVecStr = 9;
    // map字段原值
    map<string,MapValue> oldMapVal = 10;
    // map字段新值
    map<string,MapValue> newMapVal = 11;
}