2020-11-06 14:54:19.010	INFO	server/service.go:106	process:65255, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 14:55:10.992	ERROR	dataaccess@v0.0.1/data_get.go:181	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist
2020-11-06 14:55:10.992	ERROR	dataaccess@v0.0.1/data_get.go:181	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist
2020-11-06 14:55:10.992	ERROR	dataaccess@v0.0.1/data_get.go:181	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist
2020-11-06 14:55:10.992	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist]
2020-11-06 15:18:41.373	INFO	server/service.go:106	process:67555, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 15:18:47.747	ERROR	dataaccess/data_get.go:182	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist
2020-11-06 15:18:47.747	ERROR	dataaccess/data_get.go:182	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist
2020-11-06 15:18:47.747	ERROR	dataaccess/data_get.go:182	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist
2020-11-06 15:18:47.747	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist]
2020-11-06 15:23:18.557	INFO	server/service.go:106	process:68210, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 15:23:25.697	ERROR	dataaccess/data_get.go:182	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:62163->************:10048: i/o timeout, cost:2.000437909s
2020-11-06 15:23:25.697	ERROR	dataaccess/data_get.go:182	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:62164->*************:10099: i/o timeout, cost:2.000306243s
2020-11-06 15:23:25.697	ERROR	dataaccess/data_get.go:182	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:62165->*************:10033: i/o timeout, cost:2.00045634s
2020-11-06 15:23:25.697	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:62164->*************:10099: i/o timeout, cost:2.000306243s v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:62163->************:10048: i/o timeout, cost:2.000437909s y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:62165->*************:10033: i/o timeout, cost:2.00045634s]
2020-11-06 15:31:55.237	INFO	server/service.go:106	process:69421, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 15:32:10.936	ERROR	dataaccess/data_get.go:183	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64433->*************:10027: i/o timeout, cost:2.00483803s
2020-11-06 15:32:10.936	ERROR	dataaccess/data_get.go:183	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64431->*************:10027: i/o timeout, cost:2.004838024s
2020-11-06 15:32:10.936	ERROR	dataaccess/data_get.go:183	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64432->*************:10027: i/o timeout, cost:2.004844845s
2020-11-06 15:32:10.938	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64431->*************:10027: i/o timeout, cost:2.004838024s v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64433->*************:10027: i/o timeout, cost:2.00483803s y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64432->*************:10027: i/o timeout, cost:2.004844845s]
2020-11-06 15:33:47.620	INFO	server/service.go:106	process:70083, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 15:34:14.960	ERROR	dataaccess/data_get.go:183	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64944->*************:10099: i/o timeout, cost:2.000605125s
2020-11-06 15:34:14.960	ERROR	dataaccess/data_get.go:183	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64945->*************:10027: i/o timeout, cost:2.000610611s
2020-11-06 15:34:14.960	ERROR	dataaccess/data_get.go:183	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64943->************:10048: i/o timeout, cost:2.000584767s
2020-11-06 15:34:14.962	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64945->*************:10027: i/o timeout, cost:2.000610611s v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64944->*************:10099: i/o timeout, cost:2.000605125s y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:64943->************:10048: i/o timeout, cost:2.000584767s]
2020-11-06 15:34:55.498	INFO	server/service.go:106	process:70210, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 15:35:13.157	ERROR	dataaccess/data_get.go:183	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65198->*************:10027: i/o timeout, cost:2.001541391s
2020-11-06 15:35:13.159	ERROR	dataaccess/data_get.go:183	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65199->*************:10033: i/o timeout, cost:2.003040986s
2020-11-06 15:35:13.159	ERROR	dataaccess/data_get.go:183	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65200->*************:10033: i/o timeout, cost:2.002830703s
2020-11-06 15:35:13.159	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65200->*************:10033: i/o timeout, cost:2.002830703s v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65198->*************:10027: i/o timeout, cost:2.001541391s y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65199->*************:10033: i/o timeout, cost:2.003040986s]
2020-11-06 15:36:29.473	INFO	server/service.go:106	process:70432, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 15:36:34.176	ERROR	dataaccess/data_get.go:183	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist
2020-11-06 15:36:34.176	ERROR	dataaccess/data_get.go:183	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist
2020-11-06 15:36:34.176	ERROR	dataaccess/data_get.go:183	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist
2020-11-06 15:36:34.176	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:131, msg:client: selector polaris not exist]
2020-11-06 15:37:37.245	INFO	server/service.go:106	process:70757, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 15:37:48.837	ERROR	dataaccess/data_get.go:183	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:49701->************:10048: i/o timeout, cost:2.000230443s
2020-11-06 15:37:48.837	ERROR	dataaccess/data_get.go:183	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:49699->*************:10099: i/o timeout, cost:2.000202884s
2020-11-06 15:37:48.841	ERROR	dataaccess/data_get.go:183	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:49700->*************:10033: i/o timeout, cost:2.003860436s
2020-11-06 15:37:48.841	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:49700->*************:10033: i/o timeout, cost:2.003860436s v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:49699->*************:10099: i/o timeout, cost:2.000202884s y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:49701->************:10048: i/o timeout, cost:2.000230443s]
2020-11-06 16:19:23.812	INFO	server/service.go:106	process:74075, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 16:19:31.746	ERROR	dataaccess/data_get.go:184	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:59925->*************:10027: i/o timeout, cost:2.000617323s
2020-11-06 16:19:31.750	ERROR	dataaccess/data_get.go:184	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:59926->*************:10027: i/o timeout, cost:2.004142898s
2020-11-06 16:19:31.750	ERROR	dataaccess/data_get.go:184	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:59924->*************:10099: i/o timeout, cost:2.004627484s
2020-11-06 16:19:31.750	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:59926->*************:10027: i/o timeout, cost:2.004142898s v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:59924->*************:10099: i/o timeout, cost:2.004627484s y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:59925->*************:10027: i/o timeout, cost:2.000617323s]
2020-11-06 16:40:46.708	INFO	server/service.go:106	process:75891, xhttp service:trpc.video_msghub_cb.cover_standard_series_flag.Greeter launch success, address:127.0.0.1:8000, serving ...
2020-11-06 16:40:53.700	ERROR	dataaccess@v0.0.1/data_get.go:181	u0012vdwfp0 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65368->************:10048: i/o timeout, cost:2.000374417s
2020-11-06 16:40:53.705	ERROR	dataaccess@v0.0.1/data_get.go:181	y001299q7f3 fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65369->*************:10033: i/o timeout, cost:2.00546965s
2020-11-06 16:40:53.706	ERROR	dataaccess@v0.0.1/data_get.go:181	v0012a4wp8a fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65370->*************:10099: i/o timeout, cost:2.005460622s
2020-11-06 16:40:53.707	ERROR	handler/handler.go:169	DataBatchGet VID info failed:%+vmap[u0012vdwfp0:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65368->************:10048: i/o timeout, cost:2.000374417s v0012a4wp8a:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65370->*************:10099: i/o timeout, cost:2.005460622s y001299q7f3:fail to call accessLayer, method:GetMediaInfo, ret:0, err:type:framework, code:101, msg:tcp client transport ReadFrame: read tcp4 **************:65369->*************:10033: i/o timeout, cost:2.00546965s]
