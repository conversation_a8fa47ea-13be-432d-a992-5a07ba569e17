package main

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/trpcprotocol/unionplus/common"
	"git.code.oa.com/v/data_platform/unionplus/lib/trpc_go_union_client/unionplus"
	mockUnion "git.code.oa.com/v/data_platform/unionplus/lib/trpc_go_union_client/unionplus/mock"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/union"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"

	"github.com/golang/mock/gomock"
	"github.com/prashantv/gostub"
)

// CacheInterface cache接口，用于测试中的模拟
type CacheInterface interface {
	GetRouteCfg(datasourceID int32) *item.RouteCfg
	GetDevCfg(devName string) *item.DevCfg
}

// MockCache 模拟Cache实现
type MockCache struct {
	routeData map[int32]*item.RouteCfg
	devData   map[string]*item.DevCfg
}

func NewMockCache() *MockCache {
	return &MockCache{
		routeData: make(map[int32]*item.RouteCfg),
		devData:   make(map[string]*item.DevCfg),
	}
}

func (m *MockCache) SetRouteData(dataSourceID int32, cfg *item.RouteCfg) {
	m.routeData[dataSourceID] = cfg
}

func (m *MockCache) SetDevData(devName string, cfg *item.DevCfg) {
	m.devData[devName] = cfg
}

func (m *MockCache) GetRouteCfg(datasourceID int32) *item.RouteCfg {
	return m.routeData[datasourceID]
}

func (m *MockCache) GetDevCfg(devName string) *item.DevCfg {
	return m.devData[devName]
}

// TestableDataAdaptorServiceImpl 可测试的数据适配器服务实现，支持注入cache接口
type TestableDataAdaptorServiceImpl struct {
	*dataAdaptorServiceImpl
	cacheInterface CacheInterface
}

// NewTestableDataAdaptorServiceImpl 创建可测试的数据适配器服务
func NewTestableDataAdaptorServiceImpl(cache CacheInterface) *TestableDataAdaptorServiceImpl {
	return &TestableDataAdaptorServiceImpl{
		dataAdaptorServiceImpl: &dataAdaptorServiceImpl{},
		cacheInterface:         cache,
	}
}

// SetFieldInfos 重写SetFieldInfos方法以使用注入的cache接口
func (s *TestableDataAdaptorServiceImpl) SetFieldInfos(ctx context.Context, req *adaptor.SetFieldInfosRequest,
	rsp *adaptor.SetFieldInfosResponse,
) (opErr error) {
	rsp.RetInfo = logic.NewRetInfo()
	// 初始化map
	rsp.RetInfo.FailList = make(map[string]protocol.EnumMediaErrorCode)

	if len(req.GetFieldInfos()) == 0 {
		return nil
	}

	// 使用注入的cache接口获取路由配置
	routeCfg := s.cacheInterface.GetRouteCfg(req.GetDataSourceId())
	if routeCfg == nil {
		const noRoute = int(protocol.EnumMediaErrorCode_RetNoRouteCfg)
		return errs.New(noRoute, "can not find route config")
	}

	// 创建存储对象
	var obj inf.StoreObj
	obj, opErr = dao.NewStoreObjWithRouteCfg(ctx, routeCfg)
	if opErr != nil {
		log.Errorf("Fail to create dataItem[dataSourceId:%d], err is %s.", req.DataSourceId, opErr)
		return
	}

	// 设置路由规则
	obj.RouteRuleFunc(routeCfg.RouteFunc, req.GetId(), routeCfg.DevSetStr)

	// 获取设备配置并连接
	devCfg := s.cacheInterface.GetDevCfg(obj.GetKey())
	if devCfg == nil {
		const noRoute = int(protocol.EnumMediaErrorCode_RetNoRouteStoreItem)
		return errs.New(noRoute, "fail to route storage object")
	}

	if err := obj.GetConn(devCfg.ConnectInfo, devCfg.AuthInfo, devCfg.Options); err != nil {
		const failConnect = int(protocol.EnumMediaErrorCode_RetFailConnectDataSource)
		return errs.New(failConnect, "fail to connect dataSource")
	}

	modifyInfo, err := obj.SetFieldInfos(req.GetId(), req.GetBaseFieldIds(), req.GetFieldInfos(), rsp)
	if err != nil {
		log.Errorf("Fail to call setFieldInfos, err is %s.", err)
		// 更新失败场景仍有modifyInfo，触发检查补发消息流程
		logic.AddTimeoutCheckTask(ctx, req.GetDataSourceId(), req.GetId(), req.GetExtraInfo(), modifyInfo)
		return errs.New(int(protocol.EnumMediaErrorCode_RetFailUpdate), "fail to call SetFieldInfos")
	}
	// 填充变更事件列表
	rsp.BaseInfo, rsp.ModifyInfos = modifyInfo.GetBaseInfo(), modifyInfo.GetInfos()
	log.Debugf("Update baseInfo:%+v, modifyInfo:%+v.", rsp.GetBaseInfo(), rsp.GetModifyInfos())
	return
}

func Test_dataAdaptorServiceImpl_SetFieldInfos(t *testing.T) {
	type args struct {
		in0 context.Context
		req *adaptor.SetFieldInfosRequest
		rsp *adaptor.SetFieldInfosResponse
	}

	// 创建模拟cache
	mockCache := NewMockCache()
	mockCache.SetRouteData(1, &item.RouteCfg{
		DataSourceID: 1,
		DataType:     uint32(inf.RedisType),
		RouteFunc:    inf.NoneFunction,
		DevSetStr:    "testRedis",
	})
	mockCache.SetRouteData(2, &item.RouteCfg{
		DataSourceID: 2,
		DataType:     uint32(inf.UnionType),
		RouteFunc:    inf.NoneFunction,
		DevSetStr:    "testUnion",
	})
	mockCache.SetDevData("testRedis", &item.DevCfg{})
	mockCache.SetDevData("testUnion", &item.DevCfg{
		ConnectInfo: "view=2001",
		AuthInfo:    "testApp:testKey",
	})

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试set接口失败",
			args: args{
				req: &adaptor.SetFieldInfosRequest{
					DataSourceId: 1,
					Id:           "testID",
					FieldInfos: []*protocol.UpdateFieldInfo{{
						FieldInfo: &protocol.FieldInfo{
							FieldName: "title",
							FieldId:   9,
							FieldType: protocol.EnumFieldType_FieldTypeStr,
							StrValue:  "测试标题",
						},
						UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
					}},
				},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewTestableDataAdaptorServiceImpl(mockCache)
			if err := s.SetFieldInfos(tt.args.in0, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("SetFieldInfos() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_dataAdaptorServiceImpl_GetFieldInfos(t *testing.T) {
	type args struct {
		in0 context.Context
		req *adaptor.GetFieldInfosRequest
		rsp *adaptor.GetFieldInfosResponse
	}

	// mock cache
	mockCache := NewMockCache()
	mockCache.SetRouteData(1, &item.RouteCfg{
		DataSourceID: 1,
		DataType:     uint32(inf.RedisType),
		RouteFunc:    inf.NoneFunction,
		DevSetStr:    "testRedis",
	})
	mockCache.SetRouteData(2, &item.RouteCfg{
		DataSourceID: 2,
		DataType:     uint32(inf.UnionType),
		RouteFunc:    inf.NoneFunction,
		DevSetStr:    "testUnion",
	})
	mockCache.SetDevData("testRedis", &item.DevCfg{})
	mockCache.SetDevData("testUnion", &item.DevCfg{
		ConnectInfo: "view=2001",
		AuthInfo:    "testApp:testKey",
	})

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试get接口失败",
			args: args{
				req: &adaptor.GetFieldInfosRequest{
					DataSourceId: 1,
					Id:           "testID1",
					FieldIds:     []uint32{9},
				},
				rsp: &adaptor.GetFieldInfosResponse{
					FailList: make(map[uint32]protocol.EnumMediaErrorCode),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &dataAdaptorServiceImpl{}
			if err := s.GetFieldInfos(tt.args.in0, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("GetFieldInfos() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_dataAdaptorServiceImpl_InsertFieldInfos(t *testing.T) {
	type args struct {
		in0 context.Context
		req *adaptor.InsertFieldInfosRequest
		rsp *adaptor.InsertFieldInfosResponse
	}
	// mock cache
	mockCache := NewMockCache()
	mockCache.SetRouteData(1, &item.RouteCfg{
		DataSourceID: 1,
		DataType:     uint32(inf.RedisType),
		RouteFunc:    inf.NoneFunction,
		DevSetStr:    "testRedis",
	})
	mockCache.SetRouteData(2, &item.RouteCfg{
		DataSourceID: 2,
		DataType:     uint32(inf.UnionType),
		RouteFunc:    inf.NoneFunction,
		DevSetStr:    "testUnion",
	})
	mockCache.SetDevData("testRedis", &item.DevCfg{})
	mockCache.SetDevData("testUnion", &item.DevCfg{
		ConnectInfo: "view=2001",
		AuthInfo:    "testApp:testKey",
	})

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试insert接口",
			args: args{
				req: &adaptor.InsertFieldInfosRequest{
					DataSourceId: 1,
					Id:           "testID1",
					FieldInfos: []*protocol.UpdateFieldInfo{{
						FieldInfo: &protocol.FieldInfo{
							FieldName: "title",
							FieldId:   9,
							FieldType: protocol.EnumFieldType_FieldTypeStr,
							StrValue:  "测试标题",
						},
						UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
					}},
				},
				rsp: &adaptor.InsertFieldInfosResponse{
					RetInfo: logic.NewRetInfo(),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &dataAdaptorServiceImpl{}
			if err := s.InsertFieldInfos(tt.args.in0, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("InsertFieldInfos() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_dataAdaptorServiceImpl_BatchGetFieldInfos(t *testing.T) {
	type args struct {
		ctx context.Context
		req *adaptor.BatchGetFieldsRequest
		rsp *adaptor.BatchGetFieldsResponse
	}
	// 创建模拟cache
	mockCache := NewMockCache()
	mockCache.SetRouteData(1, &item.RouteCfg{
		DataSourceID: 1,
		DataType:     uint32(inf.RedisType),
		RouteFunc:    inf.NoneFunction,
		DevSetStr:    "testRedis",
	})
	mockCache.SetRouteData(2, &item.RouteCfg{
		DataSourceID: 2,
		DataType:     uint32(inf.UnionType),
		RouteFunc:    inf.NoneFunction,
		DevSetStr:    "testUnion",
	})
	mockCache.SetDevData("testRedis", &item.DevCfg{})
	mockCache.SetDevData("testUnion", &item.DevCfg{
		ConnectInfo: "view=2001",
		AuthInfo:    "testApp:testKey",
	})

	// mock union proxy
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	proxy := mockUnion.NewMockClient(ctrl)
	stub := gostub.Stub(&union.NewUnionPlusProxy, func(appID, appKey string, opts ...client.Option) unionplus.Client {
		return proxy
	})
	defer stub.Reset()
	proxy.EXPECT().GetRaw(gomock.Any(), gomock.Any()).Return(&common.QueryRsp{
		Data: &common.Table{
			Rows: []*common.DataSet{{
				Key:     "testVid",
				Columns: map[string]*common.Value{"titleX": {Val: &common.Value_Strval{Strval: []byte("测试标题")}}},
			}},
		},
	}, nil).AnyTimes()

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试批量读接口",
			args: args{
				ctx: context.Background(),
				req: &adaptor.BatchGetFieldsRequest{
					DataSourceID: 2,
					Id:           []string{"testVid"},
					FieldNames:   []string{"titleX"},
					DataSetID:    2001,
				},
				rsp: &adaptor.BatchGetFieldsResponse{
					DocInfos: make(map[string]*protocol.DocInfo),
					FailList: make(map[string]string),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewTestableDataAdaptorServiceImpl(mockCache)
			if err := s.BatchGetFieldInfos(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("BatchGetFieldInfos() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_transBatchReqToInterfaceReq(t *testing.T) {
	type args struct {
		req *adaptor.BatchGetFieldsRequest
	}
	tests := []struct {
		name  string
		args  args
		want  []string
		want1 map[string][]string
	}{
		{
			name: "测试接口转换-1",
			args: args{&adaptor.BatchGetFieldsRequest{
				FieldNames: []string{"mapField.key1", "mapField.key2"},
			}},
			want:  []string{"mapField"},
			want1: map[string][]string{"mapField": {"key1", "key2"}},
		}, {
			name: "测试接口转换-2",
			args: args{&adaptor.BatchGetFieldsRequest{
				FieldNames: []string{"*"},
			}},
			want:  []string{"*"},
			want1: map[string][]string{},
		}, {
			name: "测试接口转换-3",
			args: args{&adaptor.BatchGetFieldsRequest{
				FieldNames: []string{"title", "desc"},
			}},
			want:  []string{"title", "desc"},
			want1: map[string][]string{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := transBatchReqToInterfaceReq(tt.args.req)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("transBatchReqToInterfaceReq() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("transBatchReqToInterfaceReq() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

// BatchGetFieldInfos 重写BatchGetFieldInfos方法以使用注入的cache接口
func (s *TestableDataAdaptorServiceImpl) BatchGetFieldInfos(ctx context.Context, req *adaptor.BatchGetFieldsRequest,
	rsp *adaptor.BatchGetFieldsResponse,
) error {
	rsp.DocInfos, rsp.FailList = make(map[string]*protocol.DocInfo), make(map[string]string)
	if len(req.Id) == 0 || len(req.FieldNames) == 0 {
		// 空操作直接跳过
		return nil
	}

	// 使用注入的cache接口获取路由配置
	routeCfg := s.cacheInterface.GetRouteCfg(req.DataSourceID)
	if routeCfg == nil {
		const noRoute = int(protocol.EnumMediaErrorCode_RetNoRouteCfg)
		return errs.New(noRoute, "can not find route config")
	}

	// 创建存储对象列表（简化版，不进行实际的分组路由）
	obj, err := dao.NewStoreObjWithRouteCfg(ctx, routeCfg)
	if err != nil {
		const noRoute = int(protocol.EnumMediaErrorCode_RetNoRouteCfg)
		return errs.New(noRoute, "route data_type invalid")
	}

	// 设置路由规则（使用第一个ID）
	obj.RouteRuleFunc(routeCfg.RouteFunc, req.Id[0], routeCfg.DevSetStr)

	// 获取设备配置并连接
	devCfg := s.cacheInterface.GetDevCfg(obj.GetKey())
	if devCfg == nil {
		const noRoute = int(protocol.EnumMediaErrorCode_RetNoRouteStoreItem)
		return errs.New(noRoute, "fail to route storage object")
	}

	if err := obj.GetConn(devCfg.ConnectInfo, devCfg.AuthInfo, devCfg.Options); err != nil {
		const failConnect = int(protocol.EnumMediaErrorCode_RetFailConnectDataSource)
		return errs.New(failConnect, "fail to connect dataSource")
	}

	fields, extra := transBatchReqToInterfaceReq(req)
	bRsp, err := obj.BatchGetFields(req.DataSetID, req.GetId(), fields, extra)
	if err != nil {
		log.Errorf("Fail to batch getFields, err is %v.", err)
		rsp.ErrCode = protocol.EnumMediaErrorCode_RetCallAdaptorErr
		return err
	}

	// 合并结果
	if bRsp != nil {
		for _, doc := range bRsp.DocInfos {
			rsp.DocInfos[doc.GetId()] = doc
		}
		for id, msg := range bRsp.FailList {
			rsp.FailList[id] = msg
		}
	}
	return nil
}
