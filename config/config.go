// Package config 数据适配层服务配置包
package config

import (
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
)

// cfg 数据导入服务yaml配置对象
var cfg = tool.NewTConfConfig("service.yaml")

// Redis 分布式锁配置
type Redis struct {
	// Addr redis配置
	Addr string `yaml:"addr"`
	// User 用户
	User string `yaml:"user"`
	// Password 密码
	Password string `yaml:"password"`
}

// TimeoutCheckTask 超时检查任务配置
type TimeoutCheckTask struct {
	// Enable 是否开启
	Enable bool `yaml:"enable"`
	// Retry 重试次数
	Retry int `yaml:"retry"`
	// Topic 消息队列topic
	Topic string `yaml:"topic"`
}

// AdaptorCfg 数据适配层配置
type AdaptorCfg struct {
	// CheckTask 超时检查任务配置
	CheckTask TimeoutCheckTask `yaml:"checkTask"`
	// Lock 分布式锁配置
	Lock Redis `yaml:"lock"`
	// CacheConfig 缓存配置
	CacheConfig cache.CacheCfg `yaml:"cache"`
}

// LoadYaml 加载yaml配置
func LoadYaml() error {
	return cfg.LoadYaml(&AdaptorCfg{})
}

// GetConfig 获取配置对象
func GetConfig() *AdaptorCfg {
	adaptorCfg, _ := cfg.Load().(*AdaptorCfg)
	if adaptorCfg == nil {
		// 如果为空则返回一个空配置
		return &AdaptorCfg{}
	}
	return adaptorCfg
}

// GetCacheCfg 获取缓存db配置
func GetCacheCfg() cache.CacheCfg {
	return GetConfig().CacheConfig
}
