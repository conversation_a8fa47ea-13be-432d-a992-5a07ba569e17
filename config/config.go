// Package config load conf from rainbow
package config

import (
	"encoding/json"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"

	"gopkg.in/yaml.v2"
)

// Config 配置结构体
type Config struct {
	// DaoAuth 数据访问接口的配置信息
	DaoAuth map[int64]map[string]AccessInfo `yaml:"dao_auth"` // key为事件总线ID
	// ConsumerRetry 消费者重试配置
	ConsumerRetry map[int64]RetryConfig `yaml:"consumer_retry"` // key为事件总线ID
}

const DefaultConfBusID = 0

// RetryConfig 重试配置
type RetryConfig struct {
	// RetryCount 重试次数
	RetryCount int `yaml:"retry_count"`
	// RetryIntervalMs 重试间隔时间（毫秒）
	RetryIntervalMs int `yaml:"retry_interval_ms"`
}

// AccessInfo 数据访问配置信息
type AccessInfo struct {
	// AppID 数据服务的鉴权ID
	AppID string `yaml:"app_id"`
	// AppKey 数据服务的鉴权密钥
	AppKey string `yaml:"app_key"`
	// SetName 数据服务set名
	SetName string `yaml:"set_name"`
	// Namespace 数据服务环境名
	Namespace string `yaml:"namespace"`
	// RetryCount 外部服务调用重试次数
	RetryCount int `yaml:"retry_count"`
	// RetryIntervalMs 外部服务调用重试间隔时间（毫秒）
	RetryIntervalMs int `yaml:"retry_interval_ms"`
}

// New 新建配置
func New() (*Config, error) {
	name := "service.yaml"
	conf, err := config.Load(name, config.WithCodec("yaml"), config.WithProvider("tconf"))
	if err != nil {
		return nil, fmt.Errorf("load tconf fail, name:[%s], err:[%v]", name, err)
	}
	c := &Config{}
	if err = yaml.Unmarshal(conf.Bytes(), c); err != nil {
		return nil, err
	}
	str, _ := json.Marshal(c)
	log.InfoContextf(trpc.BackgroundContext(), "load conf succ[%s]", str)
	return c, nil
}

// GetAccess 获取指定事件总线和存储类型的权限信息
// busID: 事件总线ID
// storageType: 存储类型，取common_event.pb中EnumViewType对应的英文名
// 优先使用指定事件总线的配置，若不存在则回退到公共配置（0号总线）
func (c *Config) GetAccess(busID int64, storageType string) (*AccessInfo, error) {
	// 按优先级查找配置：先查指定总线，再查公共总线
	candidateIDs := []int64{busID, DefaultConfBusID}
	for _, id := range candidateIDs {
		if access := c.getAccessFromBus(id, storageType); access != nil {
			return access, nil
		}
	}
	return nil, fmt.Errorf("access configuration not found for busID:%d, storageType:%s", busID, storageType)
}

// getAccessFromBus 从指定总线获取访问配置
func (c *Config) getAccessFromBus(busID int64, storageType string) *AccessInfo {
	if busCfg, exist := c.DaoAuth[busID]; exist {
		if access, found := busCfg[storageType]; found {
			return &access
		}
	}
	return nil
}

// GetRetryConfig 获取指定事件总线的重试配置
// busID: 事件总线ID
// 优先使用指定事件总线的配置，若不存在则回退到公共配置（0号总线）
func (c *Config) GetRetryConfig(busID int64) *RetryConfig {
	// 按优先级查找配置：先查指定总线，再查公共总线
	candidateIDs := []int64{busID, DefaultConfBusID}
	for _, id := range candidateIDs {
		if retryConfig, exists := c.ConsumerRetry[id]; exists {
			return &retryConfig
		}
	}
	return nil
}
