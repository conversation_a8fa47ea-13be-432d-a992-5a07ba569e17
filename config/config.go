// Package config 服务配置
package config

import (
	"context"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	"git.woa.com/video_media/domain_utils/lib"
)

var (
	// cfg 数据导入服务yaml配置对象
	cfg = tool.NewTConfConfig("service.yaml")
	// baseCfg 服务基础信息
	baseCfg BaseInfo
)

// KafkaCfg kafka生产者配置信息
type KafkaCfg struct {
	// RetryMQAddr 重试队列连接串
	RetryMQAddr string `yaml:"retry_mq_addr"`
	// Timeout 超时时间(单位s)
	Timeout int `yaml:"timeout"`
	// SetName 配置的set名称(只有指定set加载重试队列)
	SetName string `yaml:"set_name"`
}

// BaseInfo 服务基础信息
type BaseInfo struct {
	// SetName 服务set名字
	SetName string
	// TenantID 租户ID
	TenantID string
	// LocalIp 本机ip
	LocalIp string
	// NameSpace 命名空间
	NameSpace string
	// SampleRate 采样率
	SampleRate float64
}

// ValidationConfig 字段值数据校验配置
type ValidationConfig struct {
	Enabled bool `yaml:"enabled"`
}

// LimitCfg 频控配置
type LimitCfg struct {
	// ExpireMin 过期时间（单位：分钟）
	ExpireMin int `yaml:"expire_min"`
}

// Report 上报配置
type Report struct {
	// SampleRate 采样率
	SampleRate float64 `yaml:"sample_rate"`
}

// AccessCfg 数据接入层配置
type AccessCfg struct {
	// CacheConfig 缓存配置
	CacheConfig cache.CacheCfg `yaml:"cache"`
	// Kafka kafka生产者配置
	Kafka KafkaCfg `yaml:"kafka"`
	// ValidationConfig 字段值数据校验配置
	ValidationConfig ValidationConfig `yaml:"validation"`
	// Limit 频控配置
	Limit LimitCfg `yaml:"limit"`
	// Report 上报配置
	Report Report `yaml:"report"`
	// CipherKey 加密密钥
	CipherKey string `yaml:"cipher_key"`
}

func loadAccessConfig() *AccessCfg {
	c, ok := cfg.Load().(*AccessCfg)
	if ok {
		return c
	}
	return new(AccessCfg)
}

// GetDBCfg 获取缓存db配置
func GetDBCfg() cache.CacheCfg {
	return loadAccessConfig().CacheConfig
}

// GetKafkaCfg 获取kafka配置
func GetKafkaCfg() KafkaCfg {
	return loadAccessConfig().Kafka
}

// GetLimitCfg 获取频控配置
func GetLimitCfg() LimitCfg {
	return loadAccessConfig().Limit
}

// GetCipherKey 获取加密密钥
func GetCipherKey() string {
	return loadAccessConfig().CipherKey
}

// getTenantIDFromSetName 解析set名获取租户ID
func getTenantIDFromSetName(setName string) string {
	// 判断set格式是否符合规则，为了跟过去的set名区分，新的set名以saas开头
	if !strings.HasPrefix(setName, "saas") {
		return ""
	}

	sets := strings.Split(setName, ".")
	if len(sets) != 3 {
		return ""
	}
	tenantID := sets[1]
	if !strings.HasPrefix(tenantID, "ten") {
		// 租户id不是ten开头的，说明不是租户id
		return ""
	}
	// set名格式：saas{业务名称}.{租户id}.{地域}
	return tenantID
}

// InitConfig 初始化服务配置
func InitConfig() error {
	if err := cfg.LoadYaml(&AccessCfg{}); err != nil {
		return err
	}
	// 加载服务基础信息
	globalInfo := trpc.GlobalConfig().Global
	baseCfg = BaseInfo{
		SetName:    globalInfo.FullSetName,
		TenantID:   getTenantIDFromSetName(globalInfo.FullSetName),
		LocalIp:    globalInfo.LocalIP,
		NameSpace:  globalInfo.Namespace,
		SampleRate: loadAccessConfig().Report.SampleRate,
	}
	log.Infof("BaseCfg is %+v", baseCfg)
	return nil
}

// GetSetName 获取服务Set名字
func GetSetName() string {
	return baseCfg.SetName
}

// GetTenantID 获取租户ID
func GetTenantID(ctx context.Context) string {
	// 优先使用api网关透传的租户ID
	tenantID, _, _ := lib.GetTenantAccessInfo(ctx)
	if tenantID != "" {
		return tenantID
	}
	// 使用set解析出的租户ID
	return baseCfg.TenantID
}

// GetLocalIp 获取本地ip
func GetLocalIp() string {
	return baseCfg.LocalIp
}

// GetNameSpace 获取命名空间
func GetNameSpace() string {
	return baseCfg.NameSpace
}

// ValidationEnabled 规则校验是否开启
func ValidationEnabled() bool {
	return loadAccessConfig().ValidationConfig.Enabled
}

// ReportSampleRate 获取上报采样率
func ReportSampleRate() float64 {
	return baseCfg.SampleRate
}
