package config

import (
	"fmt"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/config"
	_ "git.code.oa.com/trpc-go/trpc-go/config"
	"git.woa.com/goom/mocker"
)

func TestGetAccCfg(t *testing.T) {
	tests := []struct {
		name string
		want AccConfig
	}{
		// TODO: Add test cases.
		{
			name: "test",
			want: AccCfg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetAccCfg(); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rrorf("GetAccCfg() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInitConfig(t *testing.T) {
	mock := mocker.Create()
	mock.Func(config.GetYAML).Return(nil)
	InitConfig()
	mock.Reset()
	mock = mocker.Create()
	mock.Func(config.GetYAML).Return(fmt.Errorf("123"))
	InitConfig()
	mock.Reset()
}
