package config

import (
	"encoding/json"
	"testing"

	"gopkg.in/yaml.v2"
)

// validateAccessInfo 验证AccessInfo字段是否匹配
func validateAccessInfo(t *testing.T, result, expected *AccessInfo) {
	if result == nil {
		t.<PERSON><PERSON>("GetAccess() returned nil, expected %v", expected)
		return
	}
	if result.AppID != expected.AppID {
		t.<PERSON>("AppID = %v, want %v", result.AppID, expected.AppID)
	}
	if result.AppKey != expected.AppKey {
		t.<PERSON>rf("AppKey = %v, want %v", result.AppKey, expected.AppKey)
	}
	if result.SetName != expected.SetName {
		t.Errorf("SetName = %v, want %v", result.SetName, expected.SetName)
	}
	if result.Namespace != expected.Namespace {
		t.Errorf("Namespace = %v, want %v", result.Namespace, expected.Namespace)
	}
	if result.RetryCount != expected.RetryCount {
		t.<PERSON><PERSON><PERSON>("RetryCount = %v, want %v", result.RetryCount, expected.RetryCount)
	}
	if result.RetryIntervalMs != expected.RetryIntervalMs {
		t.<PERSON>("RetryIntervalMs = %v, want %v", result.RetryIntervalMs, expected.RetryIntervalMs)
	}
}

func TestConfig_GetAccess(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		busID       int64
		storageType string
		expected    *AccessInfo
		wantErr     bool
	}{
		{
			name: "正常获取指定总线配置",
			config: &Config{DaoAuth: map[int64]map[string]AccessInfo{
				1: {"UNIVERSAL_VIEW": {AppID: "test_app_1", AppKey: "test_key_1", SetName: "test_set_1", Namespace: "test_namespace_1", RetryCount: 3, RetryIntervalMs: 1000}}}},
			busID: 1, storageType: "UNIVERSAL_VIEW",
			expected: &AccessInfo{AppID: "test_app_1", AppKey: "test_key_1", SetName: "test_set_1", Namespace: "test_namespace_1", RetryCount: 3, RetryIntervalMs: 1000},
		},
		{
			name: "回退到公共配置",
			config: &Config{DaoAuth: map[int64]map[string]AccessInfo{
				0: {"UNION_VIEW": {AppID: "common_app", AppKey: "common_key", SetName: "common_set"}},
				1: {"UNIVERSAL_VIEW": {AppID: "specific_app"}}}},
			busID: 1, storageType: "UNION_VIEW",
			expected: &AccessInfo{AppID: "common_app", AppKey: "common_key", SetName: "common_set"},
		},
		{
			name: "优先使用指定总线配置",
			config: &Config{DaoAuth: map[int64]map[string]AccessInfo{
				0: {"UNIVERSAL_VIEW": {AppID: "common_app", AppKey: "common_key"}},
				2: {"UNIVERSAL_VIEW": {AppID: "specific_app", AppKey: "specific_key"}}}},
			busID: 2, storageType: "UNIVERSAL_VIEW",
			expected: &AccessInfo{AppID: "specific_app", AppKey: "specific_key"},
		},
		{
			name: "零值配置",
			config: &Config{DaoAuth: map[int64]map[string]AccessInfo{
				0: {"UNIVERSAL_VIEW": {AppID: "", AppKey: "", RetryCount: 0, RetryIntervalMs: 0}}}},
			busID: 0, storageType: "UNIVERSAL_VIEW",
			expected: &AccessInfo{AppID: "", AppKey: "", RetryCount: 0, RetryIntervalMs: 0},
		},
		{
			name: "总线不存在", config: &Config{DaoAuth: map[int64]map[string]AccessInfo{1: {"UNIVERSAL_VIEW": {AppID: "test_app"}}}},
			busID: 999, storageType: "UNIVERSAL_VIEW", wantErr: true,
		},
		{
			name: "存储类型不存在", config: &Config{DaoAuth: map[int64]map[string]AccessInfo{1: {"UNIVERSAL_VIEW": {AppID: "test_app"}}}},
			busID: 1, storageType: "INVALID_VIEW", wantErr: true,
		},
		{
			name: "空配置", config: &Config{DaoAuth: map[int64]map[string]AccessInfo{}},
			busID: 1, storageType: "UNIVERSAL_VIEW", wantErr: true,
		},
		{
			name: "配置为nil", config: &Config{DaoAuth: nil},
			busID: 1, storageType: "UNIVERSAL_VIEW", wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.config.GetAccess(tt.busID, tt.storageType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccess() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && tt.expected != nil {
				validateAccessInfo(t, result, tt.expected)
			}
		})
	}
}

// 测试AccessInfo结构体的JSON序列化
func TestAccessInfo_JSON(t *testing.T) {
	tests := []struct {
		name       string
		accessInfo AccessInfo
		expectJSON string
	}{
		{
			name: "完整配置JSON序列化",
			accessInfo: AccessInfo{
				AppID:           "test_app",
				AppKey:          "test_key",
				SetName:         "test_set",
				Namespace:       "test_namespace",
				RetryCount:      5,
				RetryIntervalMs: 2000,
			},
			expectJSON: `{"AppID":"test_app","AppKey":"test_key","SetName":"test_set","Namespace":"test_namespace","RetryCount":5,"RetryIntervalMs":2000}`,
		},
		{
			name: "最小配置JSON序列化",
			accessInfo: AccessInfo{
				AppID:  "minimal_app",
				AppKey: "minimal_key",
			},
			expectJSON: `{"AppID":"minimal_app","AppKey":"minimal_key","SetName":"","Namespace":"","RetryCount":0,"RetryIntervalMs":0}`,
		},
		{
			name:       "零值JSON序列化",
			accessInfo: AccessInfo{},
			expectJSON: `{"AppID":"","AppKey":"","SetName":"","Namespace":"","RetryCount":0,"RetryIntervalMs":0}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试JSON序列化
			jsonData, err := json.Marshal(tt.accessInfo)
			if err != nil {
				t.Errorf("JSON Marshal failed: %v", err)
				return
			}

			if string(jsonData) != tt.expectJSON {
				t.Errorf("JSON Marshal = %s, expected %s", string(jsonData), tt.expectJSON)
			}

			// 测试JSON反序列化
			var unmarshaled AccessInfo
			err = json.Unmarshal(jsonData, &unmarshaled)
			if err != nil {
				t.Errorf("JSON Unmarshal failed: %v", err)
				return
			}

			if unmarshaled != tt.accessInfo {
				t.Errorf("JSON round-trip failed: got %v, expected %v", unmarshaled, tt.accessInfo)
			}
		})
	}
}

// 测试Config结构体的YAML序列化
func TestConfig_YAML(t *testing.T) {
	tests := []struct {
		name       string
		config     Config
		expectYAML string
	}{
		{
			name: "基础配置YAML序列化",
			config: Config{
				DaoAuth: map[int64]map[string]AccessInfo{
					0: {
						"UNIVERSAL_VIEW": AccessInfo{
							AppID:   "common_app",
							AppKey:  "common_key",
							SetName: "common_set",
						},
					},
					1: {
						"UNION_VIEW": AccessInfo{
							AppID:           "specific_app",
							AppKey:          "specific_key",
							RetryCount:      3,
							RetryIntervalMs: 1000,
						},
					},
				},
			},
		},
		{
			name: "空配置YAML序列化",
			config: Config{
				DaoAuth: map[int64]map[string]AccessInfo{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试YAML序列化
			yamlData, err := yaml.Marshal(tt.config)
			if err != nil {
				t.Errorf("YAML Marshal failed: %v", err)
				return
			}

			// 测试YAML反序列化
			var unmarshaled Config
			err = yaml.Unmarshal(yamlData, &unmarshaled)
			if err != nil {
				t.Errorf("YAML Unmarshal failed: %v", err)
				return
			}

			// 验证反序列化结果
			if len(unmarshaled.DaoAuth) != len(tt.config.DaoAuth) {
				t.Errorf("YAML round-trip failed: DaoAuth length mismatch")
				return
			}

			for busID, busConfig := range tt.config.DaoAuth {
				unmarshaledBusConfig, exists := unmarshaled.DaoAuth[busID]
				if !exists {
					t.Errorf("YAML round-trip failed: busID %d missing", busID)
					continue
				}

				for storageType, accessInfo := range busConfig {
					unmarshaledAccessInfo, exists := unmarshaledBusConfig[storageType]
					if !exists {
						t.Errorf("YAML round-trip failed: storageType %s missing for busID %d", storageType, busID)
						continue
					}

					if unmarshaledAccessInfo != accessInfo {
						t.Errorf("YAML round-trip failed: AccessInfo mismatch for busID %d, storageType %s", busID, storageType)
					}
				}
			}
		})
	}
}

// 测试Config结构体的边界情况
func TestConfig_EdgeCases(t *testing.T) {
	tests := []struct {
		name   string
		config *Config
	}{
		{
			name:   "Config为nil",
			config: nil,
		},
		{
			name: "DaoAuth为nil",
			config: &Config{
				DaoAuth: nil,
			},
		},
		{
			name: "DaoAuth为空map",
			config: &Config{
				DaoAuth: map[int64]map[string]AccessInfo{},
			},
		},
		{
			name: "包含空的busConfig",
			config: &Config{
				DaoAuth: map[int64]map[string]AccessInfo{
					1: {},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试对于nil config的处理
			if tt.config == nil {
				// 这种情况下不应该调用方法，因为会panic
				return
			}

			// 测试GetAccess方法在边界情况下的行为
			_, err := tt.config.GetAccess(1, "UNIVERSAL_VIEW")
			if err == nil {
				t.Errorf("GetAccess() should return error for edge case: %s", tt.name)
			}
		})
	}
}

// 测试访问信息的完整性验证
func TestAccessInfo_Validation(t *testing.T) {
	tests := []struct {
		name       string
		accessInfo AccessInfo
		isValid    bool
	}{
		{
			name: "完整有效的访问信息",
			accessInfo: AccessInfo{
				AppID:           "valid_app",
				AppKey:          "valid_key",
				SetName:         "valid_set",
				Namespace:       "valid_namespace",
				RetryCount:      3,
				RetryIntervalMs: 1000,
			},
			isValid: true,
		},
		{
			name: "最小有效配置",
			accessInfo: AccessInfo{
				AppID:  "app",
				AppKey: "key",
			},
			isValid: true,
		},
		{
			name: "缺少AppID",
			accessInfo: AccessInfo{
				AppKey: "key",
			},
			isValid: false,
		},
		{
			name: "缺少AppKey",
			accessInfo: AccessInfo{
				AppID: "app",
			},
			isValid: false,
		},
		{
			name:       "完全空的配置",
			accessInfo: AccessInfo{},
			isValid:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 简单的验证逻辑：AppID和AppKey不能为空
			isValid := tt.accessInfo.AppID != "" && tt.accessInfo.AppKey != ""

			if isValid != tt.isValid {
				t.Errorf("AccessInfo validation = %v, expected %v", isValid, tt.isValid)
			}
		})
	}
}

// 测试多层次配置的优先级
func TestConfig_ConfigPriority(t *testing.T) {
	config := &Config{
		DaoAuth: map[int64]map[string]AccessInfo{
			0: {
				"UNIVERSAL_VIEW": AccessInfo{
					AppID:   "common_universal_app",
					AppKey:  "common_universal_key",
					SetName: "common_set",
				},
				"UNION_VIEW": AccessInfo{
					AppID:  "common_union_app",
					AppKey: "common_union_key",
				},
			},
			1: {
				"UNIVERSAL_VIEW": AccessInfo{
					AppID:  "specific_universal_app",
					AppKey: "specific_universal_key",
				},
				// 注意：1号总线没有UNION_VIEW配置
			},
			2: {
				"UNION_VIEW": AccessInfo{
					AppID:  "specific_union_app",
					AppKey: "specific_union_key",
				},
				// 注意：2号总线没有UNIVERSAL_VIEW配置
			},
		},
	}

	tests := []struct {
		name           string
		busID          int64
		storageType    string
		expectedAppID  string
		expectedAppKey string
		wantErr        bool
		description    string
	}{
		{
			name:           "1号总线优先使用专用配置",
			busID:          1,
			storageType:    "UNIVERSAL_VIEW",
			expectedAppID:  "specific_universal_app",
			expectedAppKey: "specific_universal_key",
			wantErr:        false,
			description:    "应该使用1号总线的专用配置，而不是公共配置",
		},
		{
			name:           "1号总线回退到公共配置",
			busID:          1,
			storageType:    "UNION_VIEW",
			expectedAppID:  "common_union_app",
			expectedAppKey: "common_union_key",
			wantErr:        false,
			description:    "1号总线没有UNION_VIEW配置，应该回退到0号总线的公共配置",
		},
		{
			name:           "2号总线回退到公共配置",
			busID:          2,
			storageType:    "UNIVERSAL_VIEW",
			expectedAppID:  "common_universal_app",
			expectedAppKey: "common_universal_key",
			wantErr:        false,
			description:    "2号总线没有UNIVERSAL_VIEW配置，应该回退到0号总线的公共配置",
		},
		{
			name:        "不存在的总线和存储类型",
			busID:       999,
			storageType: "INVALID_VIEW",
			wantErr:     true,
			description: "既不存在专用配置也不存在公共配置",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := config.GetAccess(tt.busID, tt.storageType)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccess() error = %v, wantErr %v (%s)", err, tt.wantErr, tt.description)
				return
			}

			if !tt.wantErr {
				if result.AppID != tt.expectedAppID {
					t.Errorf("GetAccess() AppID = %v, expected %v (%s)", result.AppID, tt.expectedAppID, tt.description)
				}
				if result.AppKey != tt.expectedAppKey {
					t.Errorf("GetAccess() AppKey = %v, expected %v (%s)", result.AppKey, tt.expectedAppKey, tt.description)
				}
			}
		})
	}
}

// 测试RetryConfig结构体
func TestConfig_GetRetryConfig(t *testing.T) {
	tests := []struct {
		name      string
		config    *Config
		busID     int64
		expected  *RetryConfig
		expectNil bool
	}{
		{
			name: "获取指定总线的重试配置",
			config: &Config{
				ConsumerRetry: map[int64]RetryConfig{
					1: {RetryCount: 5, RetryIntervalMs: 200},
				},
			},
			busID:    1,
			expected: &RetryConfig{RetryCount: 5, RetryIntervalMs: 200},
		},
		{
			name: "回退到公共配置",
			config: &Config{
				ConsumerRetry: map[int64]RetryConfig{
					0: {RetryCount: 3, RetryIntervalMs: 100},
					2: {RetryCount: 10, RetryIntervalMs: 500},
				},
			},
			busID:    1, // 1号总线不存在，应该回退到0号总线
			expected: &RetryConfig{RetryCount: 3, RetryIntervalMs: 100},
		},
		{
			name: "优先使用指定总线配置",
			config: &Config{
				ConsumerRetry: map[int64]RetryConfig{
					0: {RetryCount: 3, RetryIntervalMs: 100},
					2: {RetryCount: 8, RetryIntervalMs: 300},
				},
			},
			busID:    2,
			expected: &RetryConfig{RetryCount: 8, RetryIntervalMs: 300},
		},
		{
			name: "零值配置",
			config: &Config{
				ConsumerRetry: map[int64]RetryConfig{
					0: {RetryCount: 0, RetryIntervalMs: 0},
				},
			},
			busID:    0,
			expected: &RetryConfig{RetryCount: 0, RetryIntervalMs: 0},
		},
		{
			name: "配置不存在",
			config: &Config{
				ConsumerRetry: map[int64]RetryConfig{
					1: {RetryCount: 5, RetryIntervalMs: 200},
				},
			},
			busID:     999,
			expectNil: true,
		},
		{
			name: "空配置",
			config: &Config{
				ConsumerRetry: map[int64]RetryConfig{},
			},
			busID:     1,
			expectNil: true,
		},
		{
			name: "ConsumerRetry为nil",
			config: &Config{
				ConsumerRetry: nil,
			},
			busID:     1,
			expectNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.config.GetRetryConfig(tt.busID)

			if tt.expectNil {
				if result != nil {
					t.Errorf("GetRetryConfig() = %v, expected nil", result)
				}
				return
			}

			if result == nil {
				t.Errorf("GetRetryConfig() returned nil, expected %v", tt.expected)
				return
			}

			if result.RetryCount != tt.expected.RetryCount {
				t.Errorf("RetryCount = %v, want %v", result.RetryCount, tt.expected.RetryCount)
			}

			if result.RetryIntervalMs != tt.expected.RetryIntervalMs {
				t.Errorf("RetryIntervalMs = %v, want %v", result.RetryIntervalMs, tt.expected.RetryIntervalMs)
			}
		})
	}
}

// 测试RetryConfig的JSON序列化
func TestRetryConfig_JSON(t *testing.T) {
	tests := []struct {
		name        string
		retryConfig RetryConfig
		expectJSON  string
	}{
		{
			name: "完整配置JSON序列化",
			retryConfig: RetryConfig{
				RetryCount:      5,
				RetryIntervalMs: 200,
			},
			expectJSON: `{"RetryCount":5,"RetryIntervalMs":200}`,
		},
		{
			name:        "零值JSON序列化",
			retryConfig: RetryConfig{},
			expectJSON:  `{"RetryCount":0,"RetryIntervalMs":0}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试JSON序列化
			jsonData, err := json.Marshal(tt.retryConfig)
			if err != nil {
				t.Errorf("JSON Marshal failed: %v", err)
				return
			}

			if string(jsonData) != tt.expectJSON {
				t.Errorf("JSON Marshal = %s, expected %s", string(jsonData), tt.expectJSON)
			}

			// 测试JSON反序列化
			var unmarshaled RetryConfig
			err = json.Unmarshal(jsonData, &unmarshaled)
			if err != nil {
				t.Errorf("JSON Unmarshal failed: %v", err)
				return
			}

			if unmarshaled != tt.retryConfig {
				t.Errorf("JSON round-trip failed: got %v, expected %v", unmarshaled, tt.retryConfig)
			}
		})
	}
}
