package config

import "testing"

func Test_getTenantIDFromSetName(t *testing.T) {
	type args struct {
		setName string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "测试video.sz.*",
			args: args{setName: "video.sz.*"},
			want: "",
		}, {
			name: "测试saastxsp.tenant1.1",
			args: args{setName: "saastxsp.tenant1.1"},
			want: "tenant1",
		}, {
			name: "非法的set名",
			args: args{setName: "invalidtestName"},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getTenantIDFromSetName(tt.args.setName); got != tt.want {
				t.<PERSON>rf("getTenantIDFromSetName() = %v, want %v", got, tt.want)
			}
		})
	}
}
