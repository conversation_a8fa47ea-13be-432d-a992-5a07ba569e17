package config

import (
	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// UniAccess 统一接入平台访问鉴权凭证
type UniAccess struct {
	// UniAppID 接入平台更新appid
	UniAppID string `yaml:"uniAppID"`
	// UniAppKey 接入平台更新appley
	UniAppKey string `yaml:"uniAppKey"`
	// CanUpdate
	CanUpdate bool `yaml:"canUpdate"`
}

// ETSNamespace ETS组件的环境
type ETSNamespace struct {
	// NameSpace 环境(Production/Development)
	Namespace string `yaml:"Namespace"`
	// AppID ETS的鉴权ID
	AppID string `yaml:"AppId"`
	// AppKey ETS的鉴权Key
	AppKey string `yaml:"AppKey"`
	// Target ETS的目标服务地址
	Target string `yaml:"Target"`
	// Owner 服务调用人
	Owner string `yaml:"Owner"`
}

// Common 通用配置
type Common struct {
	// SegmentSwitch 计算截断时间开关
	SegmentSwitch string `yaml:"Switch"`
	// ColumnSwitch 栏目开关
	ColumnSwitch string `yaml:"ColumnSwitch"`
}

// AccConfig 访问鉴权参数结构体
type AccConfig struct {
	// UniAccGet 接入平台获取字段服务鉴权参数
	UniAccGet UniAccess `yaml:"uniAccessGet"`
	// UniAccUpdate 接入平台更新字段服务鉴权参数
	UniAccUpdate UniAccess `yaml:"uniAccessUpdate"`
	// UniAccColumnUpdate 接入平台栏目更新服务鉴权参数
	UniAccColumnUpdate UniAccess `yaml:"uniAccessColumnUpdate"`
	// UniAccMidCoveGet 接入平台中视频专辑读取服务鉴权参数
	UniAccMidCoveGet UniAccess `yaml:"uniAccessMidCoverGet"`
	// UniAccMidCoveUpdate 接入平台中视频专辑更新服务鉴权参数
	UniAccMidCoveUpdate UniAccess `yaml:"uniAccessMidCoverUpdate"`
	// Namespace 命名空间
	Namespace ETSNamespace `yaml:"etsNamespace"`
	// CommonConf 通用配置
	CommonConf Common `yaml:"common"`
}

// AccCfg 鉴权结构体
var AccCfg AccConfig

// GetAccCfg 获取访问鉴权配置
func GetAccCfg() AccConfig {
	return AccCfg
}

// InitConfig 初始化配置
func InitConfig() error {
	if err := config.GetYAML("get_config_auth.yaml", &AccCfg); err != nil {
		log.Errorf("read acc config failed:", err)
		return err
	}
	log.Infof("acc config is %+v", AccCfg)
	return nil
}

// GetColumnUpdateCfg 获取栏目更新配置
func GetColumnUpdateCfg() (projID, dimID int32, appID, appKey string) {
	return 1, 1, GetAccCfg().UniAccColumnUpdate.UniAppID, GetAccCfg().UniAccColumnUpdate.UniAppKey
}

// GetCommonCfg 获取通用配置
func GetCommonCfg() Common {
	return GetAccCfg().CommonConf
}
