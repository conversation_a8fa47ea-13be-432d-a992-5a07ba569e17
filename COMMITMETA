# COMMITMETA
# Format: Pbtxt
# proto-file: tencent2/prod/canal/metadata/commitmeta.proto
# proto-message: Commitmeta
# metadata generated by metax.
# template version: 1.0
# source template: https://git.woa.com/cli-market/t2-plugins/blob/master/resources/metadata/COMMITMETA.vepc

rule_sets: {

  # 指定在 COMMITMETA 文件所在目录下哪些文件的修改会触发此 RuleSet
  # 如果不配置则默认所有该目录下的修改都会触发
  # 需符合 glob 匹配规则
  # included_paths: ""
  # excluded_paths: ""

  # 执行编译
  build_executor { 
    # 开启 Go 进行编译，如需关闭，注释掉该结构即可
    go_build {
      image: TLINUX3_GO_1_18
    }
  }

  # 执行测试
  test_executor { 
    # 开启 Go 进行测试，如需关闭，注释掉该结构即可
    go_test {
      image: TLINUX3_GO_1_18
    }
  }

  # 开启代码静态扫描
  static_analyzer { 
    # 覆盖率配置
    test_coverage { 
      # 增量覆盖率
      changelist_threshold: 10
      changelist_min_lines_of_code: 10
    }
  }

  # 开启代码安全扫描
  security_analyzer {}
  
  # 开启代码依赖分析
  dependency_analyzer {
    # 是否阻塞合并请求
    continue_on_error: true
  }
}
