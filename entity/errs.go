// Package entity 媒体事件中心处理器实体定义
package entity

import "git.code.oa.com/trpc-go/trpc-go/errs"

// 错误码定义
const (
	// 参数相关错误码 1000-1099
	ErrCodeEmptyParam = 1001 // 必需参数为空

	// 配置相关错误码 1100-1199
	ErrCodeConfigFail      = 1101 // 配置解析失败
	ErrCodeUnsupportedType = 1102 // 不支持的数据存储类型

	// 连接相关错误码 1200-1299
	ErrCodeConnectFail = 1201 // 数据存储连接初始化失败

	// 数据访问相关错误码 1300-1399
	ErrCodeDataQueryFail = 1301 // 数据查询失败
	ErrCodeDataEmpty     = 1302 // 数据为空
	ErrCodeBatchFail     = 1303 // 批量处理失败
	ErrCodeRetryFail     = 1304 // 重试失败
	ErrCodeDataBlock     = 1305 // 数据阻塞
)

// 错误消息映射
var errorMsgMap = map[int]string{
	// 参数相关错误
	ErrCodeEmptyParam: "必需参数为空",

	// 配置相关错误
	ErrCodeConfigFail:      "配置解析失败",
	ErrCodeUnsupportedType: "不支持的数据存储类型",

	// 连接相关错误
	ErrCodeConnectFail: "数据存储连接初始化失败",

	// 数据访问相关错误
	ErrCodeDataQueryFail: "数据查询失败",
	ErrCodeDataEmpty:     "数据为空或被阻塞",
	ErrCodeBatchFail:     "批量处理失败",
	ErrCodeRetryFail:     "重试失败",
}

// GetErrMsg 获取错误信息
func GetErrMsg(code int, err error) string {
	// 获取基础错误信息
	baseMsg, exists := errorMsgMap[code]
	if !exists {
		baseMsg = "未知错误"
	}

	// 如果没有错误详情或者是已知错误码，直接返回基础消息（已知错误码不暴露详细信息）
	if err == nil || exists {
		return baseMsg
	}

	// 对于未知错误码，添加详细错误信息
	return baseMsg + "，错误详情：" + err.Error()
}

// NewError 创建带错误码的错误
func NewError(code int, err error) error {
	return errs.New(code, GetErrMsg(code, err))
}

// NewErrorf 创建带错误码的错误（带格式化消息）
func NewErrorf(code int, format string, args ...interface{}) error {
	return errs.Newf(code, format, args...)
}
