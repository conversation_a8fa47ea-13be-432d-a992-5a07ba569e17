// Package report 上报ATTA
package report

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"git.code.oa.com/atta/attaapi-go/v2"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

var (
	// ATTaApi 阿塔api对象
	ATTaApi attaapi.AttaApi
	Once    sync.Once
)

const (
	ArticleTblATTaID    = "***********"
	ArticleTblATTaToken = "**********"
	AccountTblATTaID    = "0a300074006"
	AccountTblATTaToken = "**********"
	TopicTblATTaID      = "***********"
	TopicTblATTaToken   = "**********"
	CrawlTraceATTaID    = "08e00075018"
	CrawlTraceATTaToken = "**********"
	CompetitorATTaID    = "08e00076159"
	CompetitorATTaToken = "**********"
)

// ReportInfo 抓取结果上报接口
type ReportInfo interface {
	DoReport()
}

// ReportCompetitor 上报抓取的竞品信息
type ReportCompetitor struct {
	CrawlTime    string `json:"crawl_time"`      // 抓取时间
	Source       string `json:"source"`          // 抓取来源
	BizDataID    string `json:"compete_uniq_id"` // 数据ID（抓取方自己定义的ID）
	Title        string `json:"title"`           // 剧名
	Desc         string `json:"desc"`            // 简介
	VideoType    string `json:"video_type"`      // 剧类型（抓取自己定义的字符串）
	ImageUrl     string `json:"image_url"`       // 封面图
	PlayUrl      string `json:"url"`             // 播放页URL
	CommentCount int    `json:"comment_count"`   // 评论量
	LikeCount    int    `json:"like_count"`      // 点赞量
	SubCount     int    `json:"sub_count"`       // 预约量
	PublishText  string `json:"publish_text"`    // 排播信息
}

// DoReport 封装 竞品信息上报流水接口
func (info *ReportCompetitor) DoReport(ctx context.Context) {
	Once.Do(func() {
		// 初始化阿塔api对象
		if ret := ATTaApi.InitTCP(); ret != attaapi.AttaReportCodeSuccess {
			log.ErrorContextf(ctx, fmt.Sprintf("Fail to init ATTA api, ret:%d.", ret))
		}
	})
	if info.Title == "" { // 标题是必上报的
		log.ErrorContextf(ctx, fmt.Sprintf("report title is empty, info:%+v.", info))
		return
	}

	if ret := ATTaApi.SendFields(CompetitorATTaID, CompetitorATTaToken, []string{
		info.CrawlTime,
		info.Source,
		info.BizDataID,
		strings.Replace(info.Title, "|", " ", -1), // 默认的转义设置不能转义竖线，故手动替换
		strings.Replace(info.Desc, "|", " ", -1),
		info.VideoType,
		info.ImageUrl,
		info.PlayUrl,
		strconv.Itoa(info.CommentCount),
		strconv.Itoa(info.LikeCount),
		strconv.Itoa(info.SubCount),
		strings.Replace(info.PublishText, "|", " ", -1),
	}, true); ret != attaapi.AttaReportCodeSuccess {
		log.ErrorContextf(ctx, "Fail to report info to ATTa, ret:%d", ret)
	}
}

// ReportArticleInfo 上报抓取实体信息
type ReportArticleInfo struct {
	MsgID         string            `json:"raw_id"`      // MsgID 消息ID
	AccountID     string            `json:"mid"`         // AccountID 账号id
	PubTime       string            `json:"pub_time"`    // PubTime 发布时间
	StaticInfo    EntityStaticInfo  `json:"static_ext"`  // StaticInfo 静态数据
	DynamicInfo   EntityDynamicInfo `json:"dynamic_ext"` // DynamicInfo 动态数据字段
	Source        string            `json:"source"`      // Source 抓取平台
	ErrorMsg      string            `json:"error"`       // ErrorMsg 错误提示
	Transmit      string            `json:"transmit"`
	CrawType      int               // CrawType 抓取类型：1 IP官号；2 IP话题；3 CP信息
	CoverIDs      string            // CoverIDs 关联的专辑
	IPName        string            // IPName IP名
	PriorityFlag  int               // PriorityFlag 是否优先剧集
	EnvFlag       int               // EnvFlag 标识正式环境/测试环境
	ConfirmStatus string            // ConfirmStatus 确认状态
	CorrectKey    string            // CorrectKey 纠正 key
	OrderID       int               // OrderID 关联的抓取订单ID
}

// EntityStaticInfo 实体的静态数据
type EntityStaticInfo struct {
	Title          string `json:"title"`          // Title 发文标题
	Nick           string `json:"author"`         // Nick 作者昵称
	Copywriter     string `json:"abstract"`       // Copywriter 发文文案
	ArticleType    string `json:"article_type"`   // ArticleType 发文类型
	EntityID       string `json:"compete_doc_id"` // EntityID 视频/图文ID
	EntityURL      string `json:"url"`            // EntityURL 视频/图文对应的URL
	CpURL          string `json:"cp_url"`         // CpURL CP的URL
	Transmit       string `json:"transmit"`       // Transmit 用户透传的信息（我们用此字段作为唯一key）
	CrawlSource    string `json:"crawl_source"`   // CrawlSource 抓取源
	CrawlUpdatedAt string `json:"crawl_time"`     // CrawlUpdatedAt 抓取时间
}

// EntityDynamicInfo 实体的动态数据
type EntityDynamicInfo struct {
	PlayCount    int `json:"play_count"`    // PlayCount 播放量
	LikeCount    int `json:"like_count"`    // LikeCount 点赞量
	ShareCount   int `json:"share_count"`   // ShareCount 分享量
	CommentCount int `json:"comment_count"` // CommentCount 评论量
}

// DoReport 封装发文信息上报流水接口
func (info *ReportArticleInfo) DoReport(ctx context.Context) {
	Once.Do(func() {
		// 初始化阿塔api对象
		if ret := ATTaApi.InitTCP(); ret != attaapi.AttaReportCodeSuccess {
			log.ErrorContextf(ctx, fmt.Sprintf("Fail to init ATTA api, ret:%d.", ret))
		}
	})
	_, _, info.OrderID, _ = model.ExtractCrawID(info.StaticInfo.Transmit)
	if info.StaticInfo.Title == "" { // 标题是必上报的
		log.ErrorContextf(ctx, fmt.Sprintf("report title is empty, info:%+v.", info))
		return
	}
	log.InfoContextf(ctx, "ReportArticleInfo:%+v", *info)

	if ret := ATTaApi.SendFields(ArticleTblATTaID, ArticleTblATTaToken, []string{
		strings.Replace(info.StaticInfo.Title, "|", " ", -1), // 默认的转义设置不能转义竖线，故手动替换
		strings.Replace(info.StaticInfo.Copywriter, "|", " ", -1),
		info.AccountID,
		info.StaticInfo.ArticleType,
		info.StaticInfo.EntityID,
		info.StaticInfo.EntityURL,
		strconv.Itoa(info.DynamicInfo.PlayCount),
		strconv.Itoa(info.DynamicInfo.LikeCount),
		strconv.Itoa(info.DynamicInfo.ShareCount),
		info.PubTime,
		info.StaticInfo.CrawlSource,
		info.MsgID,
		strconv.Itoa(info.DynamicInfo.CommentCount),
		info.StaticInfo.CrawlUpdatedAt,
		strconv.Itoa(info.CrawType),
		info.CoverIDs,
		info.IPName,
		strconv.Itoa(info.PriorityFlag),
		strings.Replace(info.StaticInfo.Nick, "|", " ", -1),
		info.CoverIDs,
		strconv.Itoa(info.EnvFlag),
		info.ConfirmStatus,
		info.CorrectKey,
		strconv.Itoa(info.OrderID),
	}, true); ret != attaapi.AttaReportCodeSuccess {
		log.ErrorContextf(ctx, "Fail to report info to ATTa, ret:%d", ret)
	}
}

// ReportAccountInfo 上报抓取CP账号信息
type ReportAccountInfo struct {
	MsgID            string        // MsgID 消息ID
	StaticInfo       CPStaticInfo  `json:"static_ext"`  // StaticInfo 静态数据
	DynamicInfo      CPDynamicInfo `json:"dynamic_ext"` // DynamicInfo 动态数据字段
	CrawlerState     int           // CrawlerState 抓取状态
	CrawlerStartTime string        // CrawlerStartTime 开始抓取时间
	AccountID        string        `json:"mid"` // AccountID 账号id
	AccountType      string        // AccountType 账号类型
	AccountPlatform  string        `json:"source"` // AccountPlatform 账号平台
	ErrorMsg         string        `json:"error"`  // ErrorMsg 错误提示
	CrawType         int           // CrawType 抓取类型：1 IP官号；2 IP话题；3 CP信息
	CoverIDs         string        // CoverIDs 关联的专辑
	IPName           string        // IPName IP名
	PriorityFlag     int           // PriorityFlag 是否优先剧集
	OrderID          int           // OrderID 关联的抓取订单ID
}

// CPStaticInfo CP的静态数据
type CPStaticInfo struct {
	AccountURL     string `json:"url"`              // AccountURL 账号对应url
	Nick           string `json:"name"`             // Nick 昵称
	CrawlUpdatedAt string `json:"crawl_updated_at"` // CrawlUpdatedAt 抓取时间
	CrawlSource    string `json:"crawl_source"`     // CrawlSource 抓取源
	AccountDesc    string `json:"introduction"`     // AccountDesc 账号简介
	Transmit       string `json:"transmit"`         // Transmit 用户透传的信息（我们用此字段作为唯一key）
}

// CPDynamicInfo CP的动态数据
type CPDynamicInfo struct {
	FansCount    int `json:"fans_count"`    // FansCount 粉丝量
	FollowCount  int `json:"follow_count"`  // FollowCount 关注量
	VideoCount   int `json:"video_count"`   // VideoCount 视频数
	LikeCount    int `json:"like_count"`    // LikeCount 点赞量
	ArticleCount int `json:"article_count"` // ArticleCount 文章数
}

// DoReport 封装上报流水接口
func (info *ReportAccountInfo) DoReport(ctx context.Context) {
	Once.Do(func() {
		if ret := ATTaApi.InitTCP(); ret != attaapi.AttaReportCodeSuccess {
			log.ErrorContextf(ctx, fmt.Sprintf("Fail to init ATTA api, ret:%d.", ret))
		}
	})
	_, _, info.OrderID, _ = model.ExtractCrawID(info.StaticInfo.Transmit)
	log.InfoContextf(ctx, "ReportAccountInfo:%+v", *info)

	if ret := ATTaApi.SendFields(AccountTblATTaID, AccountTblATTaToken, []string{
		info.AccountID,
		info.AccountType,
		info.AccountPlatform,
		info.StaticInfo.AccountURL,
		strings.Replace(info.StaticInfo.Nick, "|", " ", -1),
		strconv.Itoa(info.CrawlerState),
		info.CrawlerStartTime,
		strconv.Itoa(info.DynamicInfo.FansCount),
		strconv.Itoa(info.DynamicInfo.FollowCount),
		strconv.Itoa(info.DynamicInfo.VideoCount),
		info.StaticInfo.CrawlUpdatedAt,
		info.StaticInfo.CrawlSource,
		strings.Replace(info.StaticInfo.AccountDesc, "|", " ", -1),
		info.MsgID,
		strconv.Itoa(info.DynamicInfo.LikeCount),
		strconv.Itoa(info.DynamicInfo.ArticleCount),
		strconv.Itoa(info.CrawType),
		info.CoverIDs,
		info.IPName,
		strconv.Itoa(info.PriorityFlag),
		info.CoverIDs,
		strconv.Itoa(info.OrderID),
	}, true); ret != attaapi.AttaReportCodeSuccess {
		log.ErrorContextf(ctx, "Fail to report info to ATTa, ret:%d", ret)
	}
}

// ReportTopicFieldInfo 上报话题属性信息
type ReportTopicFieldInfo struct {
	CrawlSource    string // CrawlSource 抓取渠道
	TopicName      string // TopicName 话题名
	PlayCount      int    `json:"error"` // PlayCount 话题播放量
	ViewCount      int    // ViewCount 话题浏览量
	ReadCount      int    // ReadCount 阅读次数
	TalkCount      int    // TalkCount 讨论次数
	OriginalNum    int    // OriginalNum 原创人数
	RelateVideoNum int    // RelateVideoNum 参与视频量
	LinkTxVideo    int    // LinkTxVideo 是否挂链跳转腾讯视频
	Transmit       string `json:"transmit"` // Transmit 用户透传的信息（我们用此字段作为唯一key）
	RetCrawlStatus int    // RetCrawlStatus 爬虫侧返回的抓取状态
	CoverIDs       string // CoverIDs 关联的专辑IDs
	EnvFlag        int    // EnvFlag 标识正式环境/测试环境
	ConfirmStatus  string // ConfirmStatus 确认状态
	CorrectKey     string // CorrectKey 纠正 key
	OrderID        int    // OrderID 关联的抓取订单ID
}

// DoReport 封装上报流水接口
func (info *ReportTopicFieldInfo) DoReport(ctx context.Context) {
	Once.Do(func() {
		if ret := ATTaApi.InitTCP(); ret != attaapi.AttaReportCodeSuccess {
			log.ErrorContextf(ctx, fmt.Sprintf("Fail to init ATTA api, ret:%d.", ret))
		}
	})
	_, _, info.OrderID, _ = model.ExtractCrawID(info.Transmit)
	log.InfoContextf(ctx, "ReportTopicFieldInfo:%+v", *info)
	if info.CrawlSource == "" || info.TopicName == "" { // 抓取源和话题名是必须报的
		log.ErrorContextf(ctx, fmt.Sprintf("report CrawlSource or TopicName is empty, info:%+v.", info))
		return
	}

	if ret := ATTaApi.SendFields(TopicTblATTaID, TopicTblATTaToken, []string{
		info.CrawlSource,
		info.TopicName,
		strconv.Itoa(info.PlayCount),
		strconv.Itoa(info.ViewCount),
		strconv.Itoa(info.ReadCount),
		strconv.Itoa(info.TalkCount),
		strconv.Itoa(info.OriginalNum),
		strconv.Itoa(info.RelateVideoNum),
		strconv.Itoa(info.LinkTxVideo),
		info.CoverIDs,
		strconv.Itoa(info.EnvFlag),
		info.ConfirmStatus,
		info.CorrectKey,
		strconv.Itoa(info.OrderID),
	}, false); ret != attaapi.AttaReportCodeSuccess {
		log.ErrorContextf(ctx, "Fail to report info to ATTa, ret:%d", ret)
	}
}

// ReportCrawlTraceInfo 上报抓取链路信息（用于监控抓取链路质量）
type ReportCrawlTraceInfo struct {
	CrawlType   int    // CrawlType 抓取类型
	CrawlSource string // CrawlSource 抓取渠道
	CrawlKey    string // CrawlKey 抓取Key
	CoverIDs    string // CoverIDs 关联的专辑
	ReqInfo     string // ReqInfo 请求接口信息
	RspInfo     string // RspInfo 抓取接口返回信息
	MsgType     int    // MsgType 消息类型（1触发抓取上报，2抓取返回发文信息）
	Transmit    string `json:"transmit"` // Transmit 用户透传的信息（我们用此字段作为唯一key）
	RspCode     int    // RspCode 抓取接口返回错误码
	OrderID     int    // OrderID 关联的抓取订单ID
}

// DoReport 封装上报流水接口
func (info *ReportCrawlTraceInfo) DoReport(ctx context.Context) {
	Once.Do(func() {
		if ret := ATTaApi.InitTCP(); ret != attaapi.AttaReportCodeSuccess {
			log.ErrorContextf(ctx, fmt.Sprintf("Fail to init ATTA api, ret:%d.", ret))
		}
	})
	_, _, info.OrderID, _ = model.ExtractCrawID(info.Transmit)
	log.InfoContextf(ctx, "ReportCrawlTraceInfo:%+v", *info)

	if ret := ATTaApi.SendFields(CrawlTraceATTaID, CrawlTraceATTaToken, []string{
		strconv.Itoa(info.CrawlType),
		info.CrawlSource,
		info.CrawlKey,
		info.CoverIDs,
		info.ReqInfo,
		info.RspInfo,
		strconv.Itoa(info.MsgType),
		info.Transmit,
		strconv.Itoa(info.RspCode),
		info.CoverIDs,
		strconv.Itoa(info.OrderID),
	}, true); ret != attaapi.AttaReportCodeSuccess {
		log.ErrorContextf(ctx, "Fail to report info to ATTa, ret:%d", ret)
	}
}
