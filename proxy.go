package common

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/storage_service/access_layer"
	"git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	storeComm "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
)

// defaultOptions 默认trpc初始化选项
var defaultOptions = []client.Option{client.WithProtocol("trpc"), client.WithNetwork("tcp4")}

// AccessLayerProxy 底层数据接入层proxy
type AccessLayerProxy struct {
	// client 底层存储接入层服务client对象
	client access_layer.MediaInterfaceClientProxy
	// ID 存储id(vid, cid, lid...)
	ID string
	// DataSetID 数据集id
	DataSetID int32
	// DataSourceID 数据源id
	DataSourceID int32
	// appID 用户id
	appID string
	// appKey 用户key
	appKey string
	// operatorName 操作人
	operatorName string
	// getFields 读取字段列表
	getFields []string
	// UpdateFields 更新字段列表
	UpdateFields []*storeComm.UpdateFieldInfo
}

// NewAccessLayerProxy 创建数据接入层服务proxy对象
func NewAccessLayerProxy(appID, appKey, id string, dataSetID int32) *AccessLayerProxy {
	return &AccessLayerProxy{
		client:    access_layer.NewMediaInterfaceClientProxy(defaultOptions...),
		ID:        id,
		DataSetID: dataSetID,
		appID:     appID,
		appKey:    appKey,
		// 默认操作人使用appID
		operatorName: appID,
	}
}

// SetOperatorName 设置操作人
func (a *AccessLayerProxy) SetOperatorName(operatorName string) *AccessLayerProxy {
	a.operatorName = operatorName
	return a
}

// SetSetUpdateField 添加set型字段set操作
func (a *AccessLayerProxy) SetSetUpdateField(fieldName string, fieldValue []string) *AccessLayerProxy {
	a.UpdateFields = append(a.UpdateFields, &storeComm.UpdateFieldInfo{
		FieldInfo: &storeComm.FieldInfo{
			FieldName: fieldName,
			FieldType: storeComm.EnumFieldType_FieldTypeSet,
			VecStr:    fieldValue,
		},
		UpdateType: storeComm.EnumUpdateType_UpdateTypeSet,
	})
	return a
}

// SetVecUpdateField 添加选项型字段set操作
func (a *AccessLayerProxy) SetVecUpdateField(fieldName string, fieldVal []uint32) *AccessLayerProxy {
	a.UpdateFields = append(a.UpdateFields, &storeComm.UpdateFieldInfo{
		FieldInfo: &storeComm.FieldInfo{
			FieldName: fieldName,
			FieldType: storeComm.EnumFieldType_FieldTypeIntVec,
			VecInt:    fieldVal,
		},
		UpdateType: storeComm.EnumUpdateType_UpdateTypeSet,
	})
	return a
}

// SetStrUpdateField 添加文本字段set操作
func (a *AccessLayerProxy) SetStrUpdateField(fieldName, fieldVal string) *AccessLayerProxy {
	a.UpdateFields = append(a.UpdateFields, &storeComm.UpdateFieldInfo{
		FieldInfo: &storeComm.FieldInfo{
			FieldName: fieldName,
			FieldType: storeComm.EnumFieldType_FieldTypeStr,
			StrValue:  fieldVal,
		},
		UpdateType: storeComm.EnumUpdateType_UpdateTypeSet,
	})
	return a
}

// AddGetFields 添加get接口字段列表
func (a *AccessLayerProxy) AddGetFields(fields []string) *AccessLayerProxy {
	a.getFields = append(a.getFields, fields...)
	return a
}

// SetDataSourceID 设置数据源ID
func (a *AccessLayerProxy) SetDataSourceID(id int32) *AccessLayerProxy {
	a.DataSourceID = id
	return a
}

// AddUpdateField 增加更新字段
func (a *AccessLayerProxy) AddUpdateField(fields ...*storeComm.UpdateFieldInfo) *AccessLayerProxy {
	a.UpdateFields = append(a.UpdateFields, fields...)
	return a
}

// AddUpdateFieldFromMap 从字段map中添加更新字段
func (a *AccessLayerProxy) AddUpdateFieldFromMap(fields map[string]*storeComm.UpdateFieldInfo) *AccessLayerProxy {
	for _, field := range fields {
		a.UpdateFields = append(a.UpdateFields, field)
	}
	return a
}

// GetStrFieldVal 获取指定字符串字段值
func GetStrFieldVal(rsp *access_layer.MediaGetResponse, fieldName string) string {
	field, ok := rsp.GetFieldInfos()[fieldName]
	if !ok {
		return ""
	}
	return field.StrValue
}

// GetVecIntFieldVal 获取指定选项型字段值
func GetVecIntFieldVal(rsp *access_layer.MediaGetResponse, fieldName string) []uint32 {
	field, ok := rsp.GetFieldInfos()[fieldName]
	if !ok {
		return nil
	}
	return field.VecInt
}

// GetVecStrFieldVal 获取指定列表型字段值
func GetVecStrFieldVal(rsp *access_layer.MediaGetResponse, fieldName string) []string {
	field, ok := rsp.GetFieldInfos()[fieldName]
	if !ok {
		return nil
	}
	return field.VecStr
}

// GetMediaInfo 数据接入层Get接口
func (a *AccessLayerProxy) GetMediaInfo(ctx context.Context, opts ...client.Option) (
	rsp *access_layer.MediaGetResponse, err error,
) {
	if len(a.ID) == 0 || len(a.getFields) == 0 {
		return &access_layer.MediaGetResponse{
			RetInfo:    &storeComm.CommRetInfo{FailList: make(map[string]storeComm.EnumMediaErrorCode)},
			FieldInfos: make(map[string]*storeComm.FieldInfo),
		}, nil
	}

	rsp, err = a.client.GetMediaInfo(ctx, &access_layer.MediaGetRequest{
		AuthInfo: &access_layer.AuthInfo{
			AppId:  a.appID,
			AppKey: a.appKey,
		},
		DataSetId:  a.DataSetID,
		Id:         a.ID,
		FieldNames: a.getFields,
	}, opts...)
	if err == nil && rsp.GetRetInfo().GetErrCode() != storeComm.EnumMediaErrorCode_RetSuccess {
		err = fmt.Errorf("retInfo is %s", rsp.GetRetInfo())
	}
	return
}

// GetAllMediaInfo 数据接入层GetAll接口
func (a *AccessLayerProxy) GetAllMediaInfo(ctx context.Context, opts ...client.Option) (
	rsp *access_layer.MediaGetAllResponse, err error,
) {
	if len(a.ID) == 0 {
		return &access_layer.MediaGetAllResponse{
			RetInfo:    &storeComm.CommRetInfo{FailList: make(map[string]storeComm.EnumMediaErrorCode)},
			FieldInfos: make(map[string]*storeComm.FieldInfo),
		}, nil
	}

	rsp, err = a.client.GetAllMediaInfo(ctx, &access_layer.MediaGetAllRequest{
		AuthInfo: &access_layer.AuthInfo{
			AppId:  a.appID,
			AppKey: a.appKey,
		},
		DataSetId: a.DataSetID,
		Id:        a.ID,
	}, opts...)
	if err == nil && rsp.GetRetInfo().GetErrCode() != storeComm.EnumMediaErrorCode_RetSuccess {
		err = fmt.Errorf("retInfo is %s", rsp.GetRetInfo())
	}
	return
}

// UpdateMediaInfo  数据接入层Update接口
func (a *AccessLayerProxy) UpdateMediaInfo(ctx context.Context, opts ...client.Option) (
	*access_layer.MediaUpdateResponse, error,
) {
	if len(a.ID) == 0 || len(a.UpdateFields) == 0 {
		return &access_layer.MediaUpdateResponse{RetInfo: &storeComm.CommRetInfo{
			FailList: make(map[string]storeComm.EnumMediaErrorCode),
		}}, nil
	}

	rsp, err := a.client.UpdateMediaInfo(ctx, &access_layer.MediaUpdateRequest{
		AuthInfo: &access_layer.AuthInfo{
			AppId:  a.appID,
			AppKey: a.appKey,
		},
		DataSetId:        a.DataSetID,
		Id:               a.ID,
		UpdateFieldInfos: a.UpdateFields,
		OperatorName:     a.operatorName,
		DataSourceId:     a.DataSourceID,
	}, opts...)
	if err != nil {
		return nil, err
	} else if rsp.GetRetInfo().GetErrCode() != storeComm.EnumMediaErrorCode_RetSuccess {
		return nil, fmt.Errorf("retInfo is %s", rsp.GetRetInfo())
	}
	return rsp, nil
}

// AdaptorLayerProxy 底层数据适配层proxy
type AdaptorLayerProxy struct {
	// client 底层存储数据适配层服务client对象
	client adaptor_layer.DataAdaptorClientProxy
	// IDs 批量ID
	IDs []string
	// DataSourceID 数据源id
	DataSourceID int32
	// DataSetID 数据集id
	DataSetID int32
	// fieldIDs 读字段id列表
	fieldIDs []uint32
	// fieldNames 读字段名列表
	fieldNames []string
	// fieldKeys 读字段key列表
	fieldKeys map[uint32]*adaptor_layer.FieldKey
	// updateFields 更新字段列表
	updateFields []*storeComm.UpdateFieldInfo
	// baseInfo 基础字段信息
	baseInfo []*storeComm.FieldInfo
	// extraInfo 附加信息
	extraInfo string
}

// NewAdaptorLayerProxy 创建数据适配层服务proxy对象
func NewAdaptorLayerProxy(dataSourceID int32, ids []string) *AdaptorLayerProxy {
	if len(ids) == 0 {
		ids = []string{""}
	}
	return &AdaptorLayerProxy{
		client:       adaptor_layer.NewDataAdaptorClientProxy(defaultOptions...),
		IDs:          ids,
		DataSourceID: dataSourceID,
	}
}

// SetDataSetID 设置数据集ID
func (a *AdaptorLayerProxy) SetDataSetID(dataSetID int32) *AdaptorLayerProxy {
	a.DataSetID = dataSetID
	return a
}

// AddGetFields 增加get接口字段id
func (a *AdaptorLayerProxy) AddGetFields(fieldIDs ...uint32) *AdaptorLayerProxy {
	a.fieldIDs = append(a.fieldIDs, fieldIDs...)
	return a
}

// AddGetFieldNames 添加get字段列表
func (a *AdaptorLayerProxy) AddGetFieldNames(fieldNames ...string) *AdaptorLayerProxy {
	a.fieldNames = append(a.fieldNames, fieldNames...)
	return a
}

// SetFieldKeys 设置get接口字段key
func (a *AdaptorLayerProxy) SetFieldKeys(keys map[uint32]*adaptor_layer.FieldKey) *AdaptorLayerProxy {
	a.fieldKeys = keys
	return a
}

// AddBaseInfo 增加update接口基础字段
func (a *AdaptorLayerProxy) AddBaseInfo(baseInfos ...*storeComm.FieldInfo) *AdaptorLayerProxy {
	a.baseInfo = append(a.baseInfo, baseInfos...)
	return a
}

// SetExtraInfo 设置附加信息
func (a *AdaptorLayerProxy) SetExtraInfo(extraInfo string) *AdaptorLayerProxy {
	a.extraInfo = extraInfo
	return a
}

// AddUpdateFields 增加update接口更新字段
func (a *AdaptorLayerProxy) AddUpdateFields(fields ...*storeComm.UpdateFieldInfo) *AdaptorLayerProxy {
	a.updateFields = append(a.updateFields, fields...)
	return a
}

// BatchGetMediaInfos 数据适配层服务批量get接口
func (a *AdaptorLayerProxy) BatchGetMediaInfos(ctx context.Context, opts ...client.Option) (
	*adaptor_layer.BatchGetFieldsResponse, error,
) {
	return a.client.BatchGetFieldInfos(ctx, &adaptor_layer.BatchGetFieldsRequest{
		DataSourceID: a.DataSourceID,
		Id:           a.IDs,
		FieldNames:   a.fieldNames,
		DataSetID:    a.DataSetID,
	}, opts...)
}

// SetMediaInfos 数据适配层服务set接口
func (a *AdaptorLayerProxy) SetMediaInfos(ctx context.Context, opts ...client.Option) (
	*adaptor_layer.SetFieldInfosResponse, error,
) {
	return a.client.SetFieldInfos(ctx, &adaptor_layer.SetFieldInfosRequest{
		DataSourceId: a.DataSourceID,
		Id:           a.IDs[0],
		FieldInfos:   a.updateFields,
		BaseFieldIds: a.baseInfo,
		ExtraInfo:    a.extraInfo,
	}, opts...)
}

// InsertMediaInfos 数据适配层insert接口
func (a *AdaptorLayerProxy) InsertMediaInfos(ctx context.Context, version int64, opts ...client.Option) (
	*adaptor_layer.InsertFieldInfosResponse, error,
) {
	return a.client.InsertFieldInfos(ctx, &adaptor_layer.InsertFieldInfosRequest{
		DataSourceId: a.DataSourceID,
		Id:           a.IDs[0],
		FieldInfos:   a.updateFields,
		Version:      version,
	}, opts...)
}
