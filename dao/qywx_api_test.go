package dao

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"github.com/agiledragon/gomonkey/v2"
)

func TestSendQyWxMsg(t *testing.T) {
	proxy := thttp.NewClientProxy("qyapi.weixin.qq.com") //先按正常的写法，把对象new出来
	out := []gomonkey.OutputCell{                        // mock返回值
		{Values: gomonkey.Params{nil}},
	}
	patch := gomonkey.ApplyMethodSeq(reflect.TypeOf(proxy), "Post", out) // mock对象的方法
	defer patch.Reset()

	type args struct {
		ctx     context.Context
		content string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "Test_addCoverVideosList",
			args:    args{trpc.BackgroundContext(), "TestSendQyWxMsg"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SendQyWxMsg(trpc.BackgroundContext(), "", tt.args.content); (err != nil) != tt.wantErr {
				t.Errorf("SendQyWxMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

}
