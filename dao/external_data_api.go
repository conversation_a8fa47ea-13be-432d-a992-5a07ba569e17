package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/media_go_commlib/utils"
)

// PlanInfo 排播计划信息
type PlanInfo struct {
	Id        int    `json:"id"`
	Name      string `json:"name"`
	Category  string `json:"category"`  // 品类
	Level     string `json:"level"`     // IP的「等级」
	StartTime string `json:"startTime"` // 开始时间
	EndTime   string `json:"endTime"`   // 结束时间
	State     int    `json:"state"`
	Platforms string `json:"platforms"` // 网络平台
}

// PlanAPIResult 排播接口返回结构
type PlanAPIResult struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		PlanInfos []PlanInfo `json:"planInfos"`
		TotalSize int        `json:"totalSize"`
	} `json:"data"`
}

// isValidPlanData 判断是否合法的排播数据
// 合法数据：
// 1、电视剧、电影、综艺频道
// 2、A级以上
// 3、当前时间属于 [开播日-7天, 完结日+150天]
func isValidPlanData(planInfo *PlanInfo, categoryNames []string, nowFunc func() time.Time) bool {
	// 校验开始时间脏数据
	if isInvalidStartTime(planInfo.StartTime) {
		return false
	}

	startTime, err1 := time.Parse("2006-01-02", planInfo.StartTime)
	if err1 != nil { // 开始时间非法，则不抓取了
		return false
	}

	currentTime := nowFunc() // 使用注入的时间函数

	// 检查基本条件：品类和等级
	if !utils.Contain(categoryNames, planInfo.Category) {
		return false
	}
	if !(strings.Contains(planInfo.Level, "S") || strings.Contains(planInfo.Level, "A") ||
		strings.Contains(planInfo.Level, "S+")) {
		return false
	}

	// 检查开始时间条件：允许开播前7天（含边界）
	if currentTime.Before(startTime.AddDate(0, 0, -7)) {
		return false
	}

	// 处理结束时间，包括脏数据的情况
	endTime, err2 := time.Parse("2006-01-02", planInfo.EndTime)
	if err2 != nil || isInvalidEndTime(planInfo.EndTime) {
		// 如果结束时间是非法值或脏数据，且其它条件满足，则认为符合条件
		return true
	}

	// 结束时间有效时，检查时间范围：当前时间应该在 [开播日-7天, 完结日+150天] 范围内（含右侧边界）
	return !currentTime.After(endTime.AddDate(0, 0, 150))
}

// isInvalidEndTime 判断是否为无效的结束时间（脏数据）
func isInvalidEndTime(endTime string) bool {
	if endTime == "" {
		return true
	}
	// 检查是否为类似 "0001-01-01" 这种明显的脏数据
	if endTime == "0001-01-01" || endTime == "0000-00-00" || endTime == "1900-01-01" {
		return true
	}
	return false
}

// isInvalidStartTime 判断是否为无效的开始时间（脏数据）
func isInvalidStartTime(start string) bool {
	if start == "" {
		return true
	}
	// 明显的脏数据
	if start == "0001-01-01" || start == "0000-00-00" || start == "1900-01-01" {
		return true
	}
	return false
}

// GetAllPlanInfo 获取所有竞品排播信息
func GetAllPlanInfo(ctx context.Context, categoryNames []string) ([]*CoverInfo, error) {
	var coverInfos []*CoverInfo
	opts := []client.Option{
		client.WithServiceName("trpc.video_play.pre_play_plan.PrePlayPlan"),
	}
	proxy := thttp.NewClientProxy("trpc.video_play.pre_play_plan.PrePlayPlan", opts...)

	maxPageNo := 1000 // 最大翻页页号
	pageSize := 100   // 页大小
	for i := 1; i <= maxPageNo; i++ {
		rsp := &PlanAPIResult{}
		url := fmt.Sprintf("/api/prePlayPlan/getPlanList?category=&pageSize=%d&pageIdx=%d", pageSize, i)
		err := proxy.Get(ctx, url, rsp)
		if err != nil {
			log.ErrorContextf(ctx, "call http  err: %s", err.Error())
			return nil, err
		}
		if len(rsp.Data.PlanInfos) == 0 {
			break
		}
		for _, p := range rsp.Data.PlanInfos {
			log.InfoContextf(ctx, "planInfo: %+v", p)
			if !isValidPlanData(&p, categoryNames, time.Now) {
				continue
			}
			log.InfoContextf(ctx, " valid planInfo: %+v; %+v", p, categoryNames)
			ext, _ := json.Marshal(p)
			coverInfos = append(coverInfos, &CoverInfo{
				CoverType: model.TransTypeName(p.Category),
				Title:     tranTitle(p.Name),
				CoverID:   strconv.Itoa(p.Id), // 把自增ID作为竞品IP的专辑ID
				Ext:       string(ext),
			})
		}
		if len(rsp.Data.PlanInfos) < pageSize { // 当前返回一页数据量小于页大小，则认为是最后一页
			break
		}
	}
	return coverInfos, nil
}

// 去除标题中的（）以及括号中的内容
func tranTitle(src string) string {
	r := regexp.MustCompile("（.*）")
	return r.ReplaceAllString(src, "")
}
