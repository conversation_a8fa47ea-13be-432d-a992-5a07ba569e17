package redis

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-database/redis/mockredis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"

	"github.com/golang/mock/gomock"
	"github.com/prashantv/gostub"
)

// CacheInterface 缓存接口，用于测试中的模拟
type CacheInterface interface {
	GetFieldInfoByID(fieldID uint32, datasetID int32) *item.FieldInfo
	GetFieldInfoByName(fieldName string, datasetID int32) *item.FieldInfo
}

// MockCache 模拟缓存实现
type MockCache struct {
	dataByID   map[string]*item.FieldInfo
	dataByName map[string]*item.FieldInfo
}

func NewMockCache() *MockCache {
	return &MockCache{
		dataByID:   make(map[string]*item.FieldInfo),
		dataByName: make(map[string]*item.FieldInfo),
	}
}

func (m *MockCache) SetMockData(fieldID uint32, datasetID int32, fieldInfo *item.FieldInfo) {
	keyByID := fmt.Sprintf("%d_%d", fieldID, datasetID)
	keyByName := fmt.Sprintf("%s_%d", fieldInfo.FieldName, datasetID)
	m.dataByID[keyByID] = fieldInfo
	m.dataByName[keyByName] = fieldInfo
}

func (m *MockCache) GetFieldInfoByID(fieldID uint32, datasetID int32) *item.FieldInfo {
	key := fmt.Sprintf("%d_%d", fieldID, datasetID)
	return m.dataByID[key]
}

func (m *MockCache) GetFieldInfoByName(fieldName string, datasetID int32) *item.FieldInfo {
	key := fmt.Sprintf("%s_%d", fieldName, datasetID)
	return m.dataByName[key]
}

// LockInterface 锁接口，用于测试中的模拟
type LockInterface interface {
	Lock() error
	Unlock() (bool, error)
}

// MockLock 模拟锁实现
type MockLock struct{}

func (m *MockLock) Lock() error {
	return nil
}

func (m *MockLock) Unlock() (bool, error) {
	return true, nil
}

// TestableRedisObj 可测试的Redis对象，允许注入锁获取函数和缓存接口
type TestableRedisObj struct {
	*RedisObj
	getMutexFunc   func(name string) LockInterface
	cacheInterface CacheInterface
}

// SetFieldInfos 重写SetFieldInfos方法以使用可注入的锁
func (tr *TestableRedisObj) SetFieldInfos(id string, baseFieldIDs []*protocol.FieldInfo, fieldList []*protocol.UpdateFieldInfo,
	rsp *adaptor.SetFieldInfosResponse,
) (*model.ModifyInfo, error) {
	// 初始化redis对象内部数据
	tr.cur, tr.infos, tr.fieldList = make(map[uint32]string), make(map[uint32]*protocol.ModifyFieldInfo), fieldList
	_, lockFields := logic.ClassifyFieldsByType(fieldList)
	if len(lockFields) > 0 {
		// 存在需要加锁的字段操作，则以id为粒度加锁，key为{datasourceID}_{id}
		var mutex LockInterface
		if tr.getMutexFunc != nil {
			mutex = tr.getMutexFunc(fmt.Sprintf("%d_%s", tr.DatasourceID, id))
		} else {
			// 如果没有注入锁函数，使用默认的MockLock
			mutex = &MockLock{}
		}

		if err := mutex.Lock(); err != nil {
			return nil, err
		}
		defer mutex.Unlock()
	}

	if err := tr.getFieldOldVal(id, baseFieldIDs); err != nil {
		// 读取旧值失败，统一批量修改failList
		logic.BatchHandleFailList(fieldList, rsp.RetInfo.FailList, protocol.EnumMediaErrorCode_RetFailSelect)
		return nil, err
	}
	if err := tr.updateFieldVal(id, rsp.RetInfo.FailList); err != nil {
		// 更新新值失败，统一批量修改failList
		logic.BatchHandleFailList(fieldList, rsp.RetInfo.FailList, protocol.EnumMediaErrorCode_RetFailUpdate)
		return nil, err
	}
	// 生成数据变更消息
	return model.BuildModifyInfo(baseFieldIDs, tr.infos, tr.RedisObj), nil
}

// addFieldInfo 重写addFieldInfo以使用注入的缓存
func (tr *TestableRedisObj) addFieldInfo(docInfo *protocol.DocInfo, fieldID uint32, datasetID int32, val string) {
	var f *item.FieldInfo
	if tr.cacheInterface != nil {
		f = tr.cacheInterface.GetFieldInfoByID(fieldID, datasetID)
	} else {
		f = cache.GetFieldInfoByID(fieldID, datasetID)
	}
	if f == nil {
		return
	}

	// 根据字段类型处理数据转换
	var fieldInfo *protocol.FieldInfo
	switch protocol.EnumFieldType(f.FieldType) {
	case protocol.EnumFieldType_FieldTypeStr:
		fieldInfo = &protocol.FieldInfo{
			FieldId:        f.FieldID,
			FieldName:      f.FieldName,
			InnerFieldName: f.InnerFieldName,
			FieldType:      protocol.EnumFieldType(f.FieldType),
			StrValue:       val,
		}
	case protocol.EnumFieldType_FieldTypeIntVec:
		// 解析整数向量，格式为"12+23+567"
		var intVec []uint32
		if val != "" {
			parts := strings.Split(val, "+")
			for _, part := range parts {
				if intVal, err := strconv.ParseUint(part, 10, 32); err == nil {
					intVec = append(intVec, uint32(intVal))
				}
			}
		}
		fieldInfo = &protocol.FieldInfo{
			FieldId:        f.FieldID,
			FieldName:      f.FieldName,
			InnerFieldName: f.InnerFieldName,
			FieldType:      protocol.EnumFieldType(f.FieldType),
			VecInt:         intVec,
		}
	default:
		// 默认作为字符串处理
		fieldInfo = &protocol.FieldInfo{
			FieldId:        f.FieldID,
			FieldName:      f.FieldName,
			InnerFieldName: f.InnerFieldName,
			FieldType:      protocol.EnumFieldType(f.FieldType),
			StrValue:       val,
		}
	}

	docInfo.Fields[f.FieldName] = fieldInfo
}

// newBatchRsp 重写newBatchRsp以使用注入的缓存
func (tr *TestableRedisObj) newBatchRsp(id string, datasetID int32, fieldIDs []uint32) *model.BatchGetResponse {
	docInfo := &protocol.DocInfo{
		Id:       id,
		Fields:   make(map[string]*protocol.FieldInfo),
		FailList: make(map[string]*protocol.FailInfo),
	}
	rsp := &model.BatchGetResponse{
		DocInfos: []*protocol.DocInfo{docInfo},
		FailList: make(map[string]string),
	}

	if !tr.isGetAll {
		// 处理get接口结果数据
		for i, fieldID := range fieldIDs {
			tr.addFieldInfo(docInfo, fieldID, datasetID, bytesToString(tr.data[i]))
		}
		return rsp
	}
	// 处理getAll接口结果数据
	for i := 0; i < len(tr.data); i += 2 {
		val1, val2 := bytesToString(tr.data[i]), bytesToString(tr.data[i+1])
		fieldID, err := strconv.Atoi(val1)
		if err != nil {
			log.Errorf("Invalid fieldID:%s, err is %v.", val1, err)
			continue
		}
		tr.addFieldInfo(docInfo, uint32(fieldID), datasetID, val2)
	}
	return rsp
}

// newFieldKey 重写字段key生成逻辑，使用注入的缓存
func (tr *TestableRedisObj) newFieldKey(datasetID int32, fields []string, extra map[string][]string) *inf.FieldKey {
	var fieldKey inf.FieldKey
	for _, field := range fields {
		if field == "*" {
			// 处理getaAll场景请求
			fieldKey.FieldIDs = []uint32{logic.GetAllFieldID}
			break
		}

		var f *item.FieldInfo
		if tr.cacheInterface != nil {
			f = tr.cacheInterface.GetFieldInfoByName(field, datasetID)
		} else {
			f = cache.GetFieldInfoByName(field, datasetID)
		}
		if f == nil {
			continue
		}
		fieldKey.FieldIDs = append(fieldKey.FieldIDs, f.FieldID)
	}
	return &fieldKey
}

// BatchGetFields 重写BatchGetFields方法以使用注入的缓存
func (tr *TestableRedisObj) BatchGetFields(datasetID int32, ids, fields []string,
	extra map[string][]string,
) (*model.BatchGetResponse, error) {
	fieldKey := tr.newFieldKey(datasetID, fields, extra)
	fieldIDs := fieldKey.FieldIDs
	tr.isGetAll = logic.IsGetAllRequest(fieldIDs)
	// redis数据源批量读只用于兼容旧的读接口，目前只支持读取第一个id
	id := ids[0]
	if err := tr.getFieldVal(tr.Ctx, tr.proxy, tr.newQueryArgs(id, fieldIDs)); err != nil {
		return nil, err
	}

	if len(tr.data) == 0 {
		// 数据为空直接跳过
		return nil, nil
	}
	return tr.newBatchRsp(id, datasetID, fieldIDs), nil
}

func TestRedisObj_SetFieldInfos(t *testing.T) {
	type fields struct {
		getInfo      getInfo
		updateInfo   updateInfo
		proxy        redis.Client
		Name         string
		DatasourceID int32
	}
	type args struct {
		id           string
		baseFieldIDs []*protocol.FieldInfo
		fieldList    []*protocol.UpdateFieldInfo
		rsp          *adaptor.SetFieldInfosResponse
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// mock redis proxy
	proxy := mockredis.NewMockClient(ctrl)
	stub := gostub.Stub(&redis.NewClientProxy, func(name string, opts ...client.Option) redis.Client {
		return proxy
	})
	defer stub.Reset()
	proxy.EXPECT().Do(gomock.Any(), "HMGET", "testID1", uint32(9)).Return([]interface{}{nil}, nil).
		AnyTimes()
	proxy.EXPECT().Do(gomock.Any(), "HMSET", "testID1", uint32(9), "测试标题").
		Return("OK", nil).AnyTimes()
	proxy.EXPECT().Do(gomock.Any(), "HMGET", "testID1", uint32(7)).Return([]interface{}{nil}, nil).
		AnyTimes()
	proxy.EXPECT().Do(gomock.Any(), "HMSET", "testID1", uint32(7), "dockID1").
		Return("OK", nil).AnyTimes()

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *model.ModifyInfo
		wantErr bool
	}{
		{
			name:   "测试redis存储set接口-1",
			fields: fields{proxy: proxy},
			args: args{
				id: "testID1",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "title",
						FieldId:   9,
						FieldType: protocol.EnumFieldType_FieldTypeStr,
						StrValue:  "测试标题",
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName:   "title",
					FieldId:     9,
					FieldType:   protocol.EnumFieldType_FieldTypeStr,
					OldStrValue: "",
					NewStrValue: "测试标题",
				}},
			},
			wantErr: false,
		}, {
			name:   "测试redis存储set接口-2",
			fields: fields{proxy: proxy},
			args: args{
				id: "testID1",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "dokiids",
						FieldId:   7,
						FieldType: protocol.EnumFieldType_FieldTypeSet,
						VecStr:    []string{"dockID1"},
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeAppend,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName: "dokiids",
					FieldId:   7,
					FieldType: protocol.EnumFieldType_FieldTypeSet,
					OldVecStr: nil,
					NewVecStr: []string{"dockID1"},
				}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			redisObj := &RedisObj{
				getInfo:      tt.fields.getInfo,
				updateInfo:   tt.fields.updateInfo,
				proxy:        tt.fields.proxy,
				Name:         tt.fields.Name,
				DatasourceID: tt.fields.DatasourceID,
			}

			// 创建可测试的RedisObj，注入模拟锁
			r := &TestableRedisObj{
				RedisObj: redisObj,
				getMutexFunc: func(name string) LockInterface {
					return &MockLock{}
				},
			}

			got, err := r.SetFieldInfos(tt.args.id, tt.args.baseFieldIDs, tt.args.fieldList, tt.args.rsp)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetFieldInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetFieldInfos() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedisObj_GetFieldInfos(t *testing.T) {
	type fields struct {
		getInfo      getInfo
		updateInfo   updateInfo
		proxy        redis.Client
		Name         string
		DatasourceID int32
	}
	type args struct {
		id       []string
		fieldKey *inf.FieldKey
		rsp      *adaptor.GetFieldInfosResponse
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// mock redis proxy
	proxy := mockredis.NewMockClient(ctrl)
	stub := gostub.Stub(&redis.NewClientProxy, func(name string, opts ...client.Option) redis.Client {
		return proxy
	})
	defer stub.Reset()
	proxy.EXPECT().Do(gomock.Any(), "HMGET", "testID1", uint32(9)).Return([]interface{}{[]byte("测试标题")}, nil).AnyTimes()
	proxy.EXPECT().Do(gomock.Any(), "HGETALL", "testID1").Return([]interface{}{[]byte("9"), []byte("测试标题")}, nil).AnyTimes()
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "测试redis存储get接口",
			fields: fields{proxy: proxy},
			args: args{
				id: []string{"testID1"},
				fieldKey: &inf.FieldKey{
					FieldIDs: []uint32{9},
				},
				rsp: &adaptor.GetFieldInfosResponse{FailList: make(map[uint32]protocol.EnumMediaErrorCode)},
			},
			wantErr: false,
		}, {
			name:   "测试redis存储getAll接口",
			fields: fields{proxy: proxy},
			args: args{
				id: []string{"testID1"},
				fieldKey: &inf.FieldKey{
					FieldIDs: []uint32{0},
				},
				rsp: &adaptor.GetFieldInfosResponse{FailList: make(map[uint32]protocol.EnumMediaErrorCode)},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RedisObj{
				getInfo:      tt.fields.getInfo,
				updateInfo:   tt.fields.updateInfo,
				proxy:        tt.fields.proxy,
				Name:         tt.fields.Name,
				DatasourceID: tt.fields.DatasourceID,
			}
			if err := r.GetFieldInfos(tt.args.id, tt.args.fieldKey, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("GetFieldInfos() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedisObj_BatchGetFields(t *testing.T) {
	type fields struct {
		getInfo      getInfo
		updateInfo   updateInfo
		Ctx          context.Context
		proxy        redis.Client
		Name         string
		DatasourceID int32
	}
	type args struct {
		datasetID int32
		ids       []string
		fields    []string
		extra     map[string][]string
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// mock redis proxy
	proxy := mockredis.NewMockClient(ctrl)
	stub := gostub.Stub(&redis.NewClientProxy, func(name string, opts ...client.Option) redis.Client {
		return proxy
	})
	defer stub.Reset()
	proxy.EXPECT().Do(gomock.Any(), "HMGET", gomock.Any()).Return([]interface{}{[]byte("测试标题")}, nil).AnyTimes()
	proxy.EXPECT().Do(gomock.Any(), "HGETALL", gomock.Any()).Return([]interface{}{
		[]byte("80045"),
		[]byte("12+23+567"),
	}, nil).AnyTimes()

	// 创建模拟缓存并设置测试数据
	mockCache := NewMockCache()
	mockCache.SetMockData(9, 2001, &item.FieldInfo{
		FieldID:        9,
		FieldName:      "title",
		InnerFieldName: "title",
		FieldType:      int32(protocol.EnumFieldType_FieldTypeStr),
	})
	mockCache.SetMockData(80045, 2001, &item.FieldInfo{
		FieldID:        80045,
		FieldName:      "vcats",
		InnerFieldName: "vcats",
		FieldType:      int32(protocol.EnumFieldType_FieldTypeIntVec),
	})

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *model.BatchGetResponse
		wantErr bool
	}{
		{
			name:   "测试批量get",
			fields: fields{proxy: proxy},
			args: args{
				datasetID: 2001,
				ids:       []string{"testID1"},
				fields:    []string{"title"},
			},
			want: &model.BatchGetResponse{
				DocInfos: []*protocol.DocInfo{{
					Id: "testID1",
					Fields: map[string]*protocol.FieldInfo{"title": {
						FieldName:      "title",
						InnerFieldName: "title",
						FieldId:        9,
						FieldType:      protocol.EnumFieldType_FieldTypeStr,
						StrValue:       "测试标题",
					}},
					FailList: make(map[string]*protocol.FailInfo),
				}},
				FailList: make(map[string]string),
			},
			wantErr: false,
		}, {
			name:   "测试批量getAll",
			fields: fields{proxy: proxy},
			args: args{
				datasetID: 2001,
				ids:       []string{"testID1"},
				fields:    []string{"*"},
			},
			want: &model.BatchGetResponse{
				DocInfos: []*protocol.DocInfo{{
					Id: "testID1",
					Fields: map[string]*protocol.FieldInfo{"vcats": {
						FieldName:      "vcats",
						InnerFieldName: "vcats",
						FieldId:        80045,
						FieldType:      protocol.EnumFieldType_FieldTypeIntVec,
						VecInt:         []uint32{12, 23, 567},
					}},
					FailList: make(map[string]*protocol.FailInfo),
				}},
				FailList: make(map[string]string),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			redisObj := &RedisObj{
				getInfo:      tt.fields.getInfo,
				updateInfo:   tt.fields.updateInfo,
				Ctx:          tt.fields.Ctx,
				proxy:        tt.fields.proxy,
				Name:         tt.fields.Name,
				DatasourceID: tt.fields.DatasourceID,
			}

			// 创建可测试的RedisObj，注入模拟缓存
			r := &TestableRedisObj{
				RedisObj:       redisObj,
				cacheInterface: mockCache,
			}

			got, err := r.BatchGetFields(tt.args.datasetID, tt.args.ids, tt.args.fields, tt.args.extra)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetFields() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetFields() got = %v, want %v", got, tt.want)
			}
		})
	}
}
