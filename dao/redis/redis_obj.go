// Package redis 数据适配层服务redis类型存储相关逻辑
package redis

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"
	"unsafe"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/config"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"

	redislib "github.com/go-redis/redis/v8"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v8"
)

var (
	// once 单例模式初始化对象
	once sync.Once
	// redSync 分布式锁对象
	redSync *redsync.Redsync
)

// failLockCounter 加锁失败上报失败
var failLockCounter = metrics.Counter("RedisFailLock")

type updateInfo struct {
	// fieldList 更新字段列表
	fieldList []*protocol.UpdateFieldInfo
	// cur 当前数据
	cur map[uint32]string
	// infos 变更消息队列，key为fieldID
	infos map[uint32]*protocol.ModifyFieldInfo
}

type getInfo struct {
	// isGetAll 是否是getAll操作
	isGetAll bool
	// data get接口结果
	data [][]byte
}

// RedisObj redis存储对象
type RedisObj struct {
	getInfo
	updateInfo
	// Ctx 上下文
	Ctx context.Context
	// proxy redis客户端
	proxy redis.Client
	// Name redis集群名
	Name string
	// DatasourceID 数据源ID
	DatasourceID int32
}

// RouteRuleFunc 实现路由接口
func (r *RedisObj) RouteRuleFunc(key, _, rule string) string {
	// redis场景目前只需要支持直连配置
	switch key {
	case inf.NoneFunction:
		r.Name = rule
	}
	return r.Name
}

// GetConn 实现连接接口
func (r *RedisObj) GetConn(connInfo, authInfo, _ string) error {
	log.Debugf("connInfo is %s, authInfo is %s", connInfo, authInfo)
	r.proxy = redis.NewClientProxy("trpc.storage_service.adaptor_layer.redis", client.WithTarget(connInfo),
		client.WithPassword(authInfo))
	return nil
}

// GetKey 返回设备信息表key值
func (r *RedisObj) GetKey() string {
	return r.Name
}

// InsertFieldInfos 统一插入接口，可以指定版本号
func (r *RedisObj) InsertFieldInfos(_ string, _ int64, _ []*protocol.UpdateFieldInfo) (*model.ModifyInfo, error) {
	// todo:redis先不支持insert语义
	return nil, fmt.Errorf("not support")
}

// getFieldOldVal 获取当前值
func (r *RedisObj) getFieldOldVal(id string, base []*protocol.FieldInfo) error {
	// 获取旧值(base+fieldList)
	args := []interface{}{id}
	for _, f := range base {
		args = append(args, f.FieldId)
	}
	for _, f := range r.fieldList {
		args = append(args, f.FieldInfo.FieldId)
	}

	if err := r.getFieldVal(r.Ctx, r.proxy, args); err != nil {
		return err
	}
	for i := 1; i < len(args); i++ {
		// args一定是uint32
		r.cur[args[i].(uint32)] = string(r.data[i-1])
	}
	return nil
}

// getMutex 获取分布式锁
func getMutex(name string) *redsync.Mutex {
	once.Do(func() {
		// 初始化分布式锁对象
		cfg := config.GetConfig().Lock
		redClient := redislib.NewClient(&redislib.Options{
			Addr:     cfg.Addr,
			Password: cfg.Password,
		})
		redSync = redsync.New(goredis.NewPool(redClient))
	})
	// 分布式锁最大超时时间设置为1s
	return redSync.NewMutex(name, redsync.WithExpiry(time.Second))
}

// updateFieldVal 更新字段数据
func (r *RedisObj) updateFieldVal(id string, failList map[string]protocol.EnumMediaErrorCode) error {
	args := []interface{}{id}
	for _, f := range r.fieldList {
		fieldID := f.FieldInfo.FieldId
		val, modifyInfo, err := logic.BuildInsertValue(f, &tool.FieldVal{TxtVal: r.cur[fieldID]})
		if err != nil {
			failList[f.FieldInfo.FieldName] = protocol.EnumMediaErrorCode_RetInvalidOpType
			continue
		}
		if modifyInfo != nil {
			r.infos[fieldID] = modifyInfo
			args = append(args, fieldID, val.TxtVal)
		}
	}
	if len(args) == 1 {
		log.Debugf("id:%s no need to update.", id)
		return nil
	}

	log.Debugf("args of hmset is %+v.", args)
	ret, err := redis.String(r.proxy.Do(r.Ctx, "HMSET", args...))
	if ret != "OK" {
		log.Errorf("Fail to execute hmset, ret is %s, err is %v.", ret, err)
		return errs.New(int(protocol.EnumMediaErrorCode_RetFailUpdate), ret+err.Error())
	}
	return nil
}

// CurVal 实现接口CurVal
func (r *RedisObj) CurVal(field *protocol.FieldInfo) (*tool.FieldVal, bool) {
	val, ok := r.cur[field.FieldId]
	return &tool.FieldVal{TxtVal: val}, ok
}

// SetFieldInfos 统一设置value接口
func (r *RedisObj) SetFieldInfos(id string, baseFieldIDs []*protocol.FieldInfo, fieldList []*protocol.UpdateFieldInfo,
	rsp *adaptor.SetFieldInfosResponse,
) (*model.ModifyInfo, error) {
	// 初始化redis对象内部数据
	r.cur, r.infos, r.fieldList = make(map[uint32]string), make(map[uint32]*protocol.ModifyFieldInfo), fieldList
	_, lockFields := logic.ClassifyFieldsByType(fieldList)
	if len(lockFields) > 0 {
		// 存在需要加锁的字段操作，则以id为粒度加锁，key为{datasourceID}_{id}
		mutex := getMutex(fmt.Sprintf("%d_%s", r.DatasourceID, id))
		if err := mutex.Lock(); err != nil {
			// 加锁失败需要告警
			log.Errorf("Fail to get mutex, err is %v.", err)
			failLockCounter.Incr()
			return nil, err
		}
		// todo:解锁失败暂时不需要重试
		defer mutex.Unlock()
	}

	if err := r.getFieldOldVal(id, baseFieldIDs); err != nil {
		// 读取旧值失败，统一批量修改failList
		logic.BatchHandleFailList(fieldList, rsp.RetInfo.FailList, protocol.EnumMediaErrorCode_RetFailSelect)
		return nil, err
	}
	if err := r.updateFieldVal(id, rsp.RetInfo.FailList); err != nil {
		// 更新新值失败，统一批量修改failList
		logic.BatchHandleFailList(fieldList, rsp.RetInfo.FailList, protocol.EnumMediaErrorCode_RetFailUpdate)
		return nil, err
	}
	// 生成数据变更消息
	return model.BuildModifyInfo(baseFieldIDs, r.infos, r), nil
}

// newQueryArgs 生成查询接口参数
func (g *getInfo) newQueryArgs(id string, fieldIDs []uint32) []interface{} {
	args := []interface{}{id}
	if g.isGetAll {
		return args
	}

	for _, fieldID := range fieldIDs {
		args = append(args, fieldID)
	}
	return args
}

func (g *getInfo) isResultOK(fieldNum int) bool {
	resNum := len(g.data)
	if g.isGetAll {
		// getAll接口的结果一定是偶数
		return resNum%2 == 0
	}
	// get接口结果个数要跟参数中字段个数相同
	return resNum == fieldNum
}

// getFieldVal 读取redis字段数据通用接口
func (g *getInfo) getFieldVal(ctx context.Context, proxy redis.Client, args []interface{}) error {
	cmd := "HMGET"
	if g.isGetAll {
		cmd = "HGETALL"
	}

	log.Debugf("args of get is %+v.", args)
	var err error
	g.data, err = redis.ByteSlices(proxy.Do(ctx, cmd, args...))
	if err != nil || !g.isResultOK(len(args)-1) {
		log.Errorf("Fail to get data from redis, err is %v.", err)
		return errs.New(int(protocol.EnumMediaErrorCode_RetFailSelect), fmt.Sprintf("err is %v, num: %d,%d.",
			err, len(g.data), len(args)))
	}
	return nil
}

func addFieldInfo(docInfo *protocol.DocInfo, fieldID uint32, datasetID int32, val string) {
	f := cache.GetFieldInfoByID(fieldID, datasetID)
	if f == nil {
		return
	}

	data := &tool.FieldVal{TxtVal: val}
	v, ok := docInfo.Fields[f.FieldName]
	if !ok {
		docInfo.Fields[f.FieldName] = tool.NewFieldInfo(protocol.EnumFieldType(f.FieldType), f.FieldID, f.FieldName,
			f.InnerFieldName, data)
	} else {
		tool.FillFieldInfo(v, data)
	}
}

func (g *getInfo) newBatchRsp(id string, datasetID int32, fieldIDs []uint32) *model.BatchGetResponse {
	docInfo := &protocol.DocInfo{
		Id:       id,
		Fields:   make(map[string]*protocol.FieldInfo),
		FailList: make(map[string]*protocol.FailInfo),
	}
	rsp := &model.BatchGetResponse{
		DocInfos: []*protocol.DocInfo{docInfo},
		FailList: make(map[string]string),
	}

	if !g.isGetAll {
		// 处理get接口结果数据
		for i, fieldID := range fieldIDs {
			addFieldInfo(docInfo, fieldID, datasetID, bytesToString(g.data[i]))
		}
		return rsp
	}
	// 处理getAll接口结果数据
	for i := 0; i < len(g.data); i += 2 {
		val1, val2 := bytesToString(g.data[i]), bytesToString(g.data[i+1])
		fieldID, err := strconv.Atoi(val1)
		if err != nil {
			log.Errorf("Invalid fieldID:%s, err is %v.", val1, err)
			continue
		}
		addFieldInfo(docInfo, uint32(fieldID), datasetID, val2)
	}
	return rsp
}

// bytesToString []byte转为string高性能版本
func bytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

func (g *getInfo) fetchRsp(fieldIDs []uint32, rsp *adaptor.GetFieldInfosResponse) {
	if !g.isGetAll {
		// 处理get接口结果数据
		for i, fieldID := range fieldIDs {
			rsp.FieldInfos = append(rsp.FieldInfos, &adaptor.GetFieldInfo{
				FieldId:    fieldID,
				FieldValue: bytesToString(g.data[i]),
			})
		}
		return
	}

	// 处理getAll接口结果数据
	for i := 0; i < len(g.data); i += 2 {
		id, val := bytesToString(g.data[i]), bytesToString(g.data[i+1])
		fieldID, err := strconv.Atoi(id)
		if err != nil {
			log.Errorf("Invalid fieldID:%s, err is %v.", id, err)
			continue
		}
		rsp.FieldInfos = append(rsp.FieldInfos, &adaptor.GetFieldInfo{
			FieldId:    uint32(fieldID),
			FieldValue: val,
		})
	}
}

// BatchGetFields 统一批量获取接口
func (r *RedisObj) BatchGetFields(datasetID int32, ids, fields []string,
	extra map[string][]string,
) (*model.BatchGetResponse, error) {
	fieldKey := inf.NewFieldKey(datasetID, fields, extra)
	fieldIDs := fieldKey.FieldIDs
	r.isGetAll = logic.IsGetAllRequest(fieldIDs)
	// redis数据源批量读只用于兼容旧的读接口，目前只支持读取第一个id
	id := ids[0]
	if err := r.getFieldVal(r.Ctx, r.proxy, r.newQueryArgs(id, fieldIDs)); err != nil {
		return nil, err
	}

	if len(r.data) == 0 {
		// 数据为空直接跳过
		return nil, nil
	}
	return r.newBatchRsp(id, datasetID, fieldIDs), nil
}

// GetFieldInfos 统一获取value接口
func (r *RedisObj) GetFieldInfos(ids []string, fieldKey *inf.FieldKey, rsp *adaptor.GetFieldInfosResponse) error {
	fieldIDs := fieldKey.FieldIDs
	r.isGetAll = logic.IsGetAllRequest(fieldIDs)
	if err := r.getFieldVal(r.Ctx, r.proxy, r.newQueryArgs(ids[0], fieldIDs)); err != nil {
		return err
	}

	if len(r.data) == 0 {
		// 数据为空直接跳过
		return nil
	}
	// 回填原始数据
	r.fetchRsp(fieldIDs, rsp)
	return nil
}
