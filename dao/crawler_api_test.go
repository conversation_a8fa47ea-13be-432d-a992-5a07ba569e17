package dao

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/hydra/tms"
	"github.com/agiledragon/gomonkey/v2"
)

func Test_invokeCrawlerAddTask(t *testing.T) {
	c := tms.NewTmsClientProxy(
		client.WithServiceName("trpc.hydra.tms.Tms"),
		client.WithProtocol("trpc"),
		client.WithNetwork("tcp4"))

	g := gomonkey.ApplyMethod(reflect.TypeOf(c), "AddJob", func(
		_ *tms.TmsClientProxyImpl,
		ctx context.Context,
		req *tms.AddJobRequest,
		opts ...client.Option) (rsp *tms.AddJobReply, err error) {
		return &tms.AddJobReply{
			Code: 0,
		}, nil
	})
	defer g.Reset()

	type args struct {
		ctx context.Context
		req *tms.AddJobRequest
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &tms.AddJobRequest{TaskID: "testTaskID"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rsp, err := InvokeCrawlerAddTask(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InvokeCrawlerAddTask() error = %v, wantErr %v", err, tt.wantErr)
			}
			if err == nil && rsp.Code != 0 {
				t.Errorf("InvokeCrawlerAddTask() rsp.Code = %v, want = 0", rsp.Code)
			}
		})
	}
}

func TestAddCrawTask(t *testing.T) {
	proxy1 := &report.ReportCrawlTraceInfo{}
	out := []gomonkey.OutputCell{ // mock返回值
		{},
	}
	patch := gomonkey.ApplyMethodSeq(reflect.TypeOf(proxy1), "DoReport", out) // mock对象的方法
	defer patch.Reset()

	type args struct {
		ctx  context.Context
		args *CrawTaskArgs
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
				args: &CrawTaskArgs{
					TaskID:        "testTaskID",
					Source:        "douyin",
					CrawTxt:       "",
					URL:           "",
					BacktraceDays: 7,
					TopicID:       "",
					TopicName:     "三体",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 使用 gomonkey 将 InvokeCrawlerAddTask 函数替换为空返回函数
			patch := gomonkey.ApplyFunc(InvokeCrawlerAddTask,
				func(ctx context.Context, req *tms.AddJobRequest) (*tms.AddJobReply, error) {
					return &tms.AddJobReply{Code: 0}, nil
				})
			defer patch.Reset()

			if err := AddCrawTask(tt.args.ctx, tt.args.args, false); (err != nil) != tt.wantErr {
				t.Errorf("AddCrawTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
