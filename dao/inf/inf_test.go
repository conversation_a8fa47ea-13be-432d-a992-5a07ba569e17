package inf

import (
	"reflect"
	"testing"

	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
)

func TestFieldKey_GetFieldIDs(t *testing.T) {
	type fields struct {
		FieldIDs  []uint32
		FieldKeys map[uint32]*adaptor.FieldKey
	}
	tests := []struct {
		name   string
		fields fields
		want   []uint32
	}{
		{
			name: "测试存在fieldIDs",
			fields: fields{
				FieldIDs: []uint32{1000, 1001, 1002},
			},
			want: []uint32{1000, 1001, 1002},
		}, {
			name: "测试只存在fieldKey",
			fields: fields{
				FieldKeys: map[uint32]*adaptor.FieldKey{1000: {}},
			},
			want: []uint32{1000},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := &FieldKey{
				FieldIDs:  tt.fields.FieldIDs,
				FieldKeys: tt.fields.FieldKeys,
			}
			if got := f.GetFieldIDs(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFieldIDs() = %v, want %v", got, tt.want)
			}
		})
	}
}
