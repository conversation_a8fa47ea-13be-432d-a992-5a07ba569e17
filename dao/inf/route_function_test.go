package inf

import (
	"testing"

	"git.code.oa.com/video_media/storage_service/common"
)

func TestDefaultMysqlHashFunc(t *testing.T) {
	type args struct {
		id        string
		devSetStr string
		hashFunc  func(string) uint32
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
	}{
		{
			name: "测试格式非法-1",
			args: args{
				id:        "testID",
				devSetStr: "128|64:32:32|d_posting_data_0:t_media_data_:0|d_posting_data_1:t_media_data_:0",
				hashFunc:  common.RSHash,
			},
			want:  "",
			want1: "",
		}, {
			name: "测试格式非法-2",
			args: args{
				id:        "testID",
				devSetStr: "128|64:64|d_posting_data_0:t_media_data_|d_posting_data_1:t_media_data_:0",
				hashFunc:  common.RSHash,
			},
			want:  "",
			want1: "",
		}, {
			name: "测试mysql默认分表函数",
			args: args{
				id:        "testID",
				devSetStr: "128|64:64|d_posting_data_0:t_media_data_:0|d_posting_data_1:t_media_data_:0",
				hashFunc:  common.RSHash,
			},
			want:  "d_posting_data_0",
			want1: "t_media_data_19",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := DefaultMysqlHashFunc(tt.args.id, tt.args.devSetStr, tt.args.hashFunc)
			if got != tt.want {
				t.Errorf("DefaultMysqlHashFunc() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("DefaultMysqlHashFunc() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestNoneMysqlFunc(t *testing.T) {
	type args struct {
		devSetStr string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
	}{
		{
			name:  "测试无分库分表函数",
			args:  args{devSetStr: "d_name_lib_test:t_name_group"},
			want:  "d_name_lib_test",
			want1: "t_name_group",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := NoneMysqlFunc(tt.args.devSetStr)
			if got != tt.want {
				t.Errorf("NoneMysqlFunc() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("NoneMysqlFunc() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestCommunityHashFunc(t *testing.T) {
	type args struct {
		id        string
		devSetStr string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
	}{
		{
			name: "测试传入空值",
			args: args{
				id:        "",
				devSetStr: "128|64:64|d_posting_data_0:t_media_data_:0|d_posting_data_1:t_media_data_:0",
			},
			want:  "",
			want1: "",
		}, {
			name: "测试默认情况",
			args: args{
				id:        "testID",
				devSetStr: "128|64:64|d_posting_data_0:t_media_data_:0|d_posting_data_1:t_media_data_:0",
			},
			want:  "d_posting_data_0",
			want1: "t_media_data_19",
		}, {
			name: "测试帖子分表场景",
			args: args{
				id:        "5testID",
				devSetStr: "128|64:64|d_posting_data_0:t_media_data_:0|d_posting_data_1:t_media_data_:0",
			},
			want:  "ti_d_posting_data_1",
			want1: "t_media_data_10",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := CommunityHashFunc(tt.args.id, tt.args.devSetStr)
			if got != tt.want {
				t.Errorf("CommunityHashFunc() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("CommunityHashFunc() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
