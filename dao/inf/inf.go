// Package inf 提供数据适配层服务路由接口以及存储相关接口
package inf

import (
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
)

// 存储类型
const (
	// MysqlType mysql基础类型
	MysqlType int = iota
	// RedisType redis存储类型
	RedisType
	// MysqlMapType mysql存储map类型
	MysqlMapType
	// HBaseType HBase存储类型
	HBaseType
	// ElasticType ES存储类型
	ElasticType
	// UnionType Union存储类型
	UnionType
)

// RouteObj 路由对象接口
type RouteObj interface {
	// RouteRuleFunc 根据路由函数获得带有具体参数(ip,port,库名,表名...)的存储对象，返回路由对象hash值
	RouteRuleFunc(key, id, devSetStr string) string
	// GetConn 根据RouteRuleFunc生成的存储对象信息进行连接操作
	GetConn(connectInfo, authInfo, extraInfo string) error
	// GetKey 获取设备信息表缓存的key值
	GetKey() string
}

// FieldKey 字段key
type FieldKey struct {
	// FieldIDs 字段ID列表
	FieldIDs []uint32
	// FieldKeys 字段Key列表
	FieldKeys map[uint32]*adaptor.FieldKey
}

// NewFieldKey 通过请求字段名生成请求的fieldKey
func NewFieldKey(datasetID int32, fields []string, extra map[string][]string) *FieldKey {
	var fieldKey FieldKey
	for _, field := range fields {
		if field == "*" {
			// 处理getaAll场景请求
			fieldKey.FieldIDs = []uint32{logic.GetAllFieldID}
			break
		}

		f := cache.GetFieldInfoByName(field, datasetID)
		if f == nil {
			continue
		}
		fieldKey.AddFieldID(protocol.EnumFieldType(f.FieldType), f.FieldID, extra[f.FieldName]...)
	}
	return &fieldKey
}

// GetFieldIDs 获取字段ID列表
func (f *FieldKey) GetFieldIDs() []uint32 {
	if len(f.FieldIDs) > 0 {
		// 优先使用fieldIDs返回字段ID列表
		return f.FieldIDs
	}

	// 使用map的key值组成字段ID列表
	var fieldIDs []uint32
	for id := range f.FieldKeys {
		fieldIDs = append(fieldIDs, id)
	}
	return fieldIDs
}

// AddFieldID Get请求添加字段ID
func (f *FieldKey) AddFieldID(fieldType protocol.EnumFieldType, fieldID uint32, keys ...string) {
	if fieldType < protocol.EnumFieldType_FieldTypeMapKV || len(keys) == 0 || keys[0] == "" {
		// 非map类型字段或未指定key，使用fieldID列表
		f.FieldIDs = append(f.FieldIDs, fieldID)
		return
	}

	if f.FieldKeys == nil {
		f.FieldKeys = make(map[uint32]*adaptor.FieldKey)
	}

	k, ok := f.FieldKeys[fieldID]
	if !ok {
		k = &adaptor.FieldKey{}
		f.FieldKeys[fieldID] = k
	}
	k.Keys = append(k.Keys, keys...)
}

// StoreOperator 存储操作接口
type StoreOperator interface {
	// InsertFieldInfos 统一插入接口，可以指定版本号
	InsertFieldInfos(id string, version int64, fields []*protocol.UpdateFieldInfo) (*model.ModifyInfo, error)
	// SetFieldInfos 统一更新value接口
	SetFieldInfos(id string, base []*protocol.FieldInfo, fields []*protocol.UpdateFieldInfo,
		rsp *adaptor.SetFieldInfosResponse) (*model.ModifyInfo, error)
	// GetFieldInfos 统一获取value接口
	GetFieldInfos(ids []string, fieldKey *FieldKey, rsp *adaptor.GetFieldInfosResponse) error
	// BatchGetFields 统一批量获取value接口 extra:字段附加信息，当字段为map字段时会传入请求key的列表
	BatchGetFields(datasetID int32, ids, fields []string, extra map[string][]string) (*model.BatchGetResponse, error)
}

// StoreObj 存储类型对象接口
type StoreObj interface {
	RouteObj
	StoreOperator
}

// SearchOperator 搜索操作接口
type SearchOperator interface {
	Search(condGroups []*protocol.SearchCondGroup, logic protocol.Logical, exprCond string,
		includes []string, sort []*protocol.SortInfo, pageInfo *protocol.PageInfo) (int64, []*protocol.DocInfo, error)
}

// SearchObj 搜索对象接口
type SearchObj interface {
	StoreObj
	SearchOperator
}
