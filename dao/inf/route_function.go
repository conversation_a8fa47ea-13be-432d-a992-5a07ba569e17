package inf

import (
	"hash/crc32"
	"strconv"
	"strings"

	tool "git.code.oa.com/video_media/storage_service/common"
)

// 路由算法键值
const (
	NoneFunction      = "none"
	DefaultMysqlHash  = "default_mysql_hash"
	MysqlRsHash       = "mysql_rshash"
	CommunityHashById = "community_hash"
)

// DefaultHashFunc 默认哈希算法
func DefaultHashFunc(id string) uint32 {
	return crc32.ChecksumIEEE([]byte(id))
}

// DefaultMysqlHashFunc mysql默认分表函数
// 例如:devSetStr:{10|5:5|dbName1:tableName_:0|dbName2:tableName_:5}
// {表总个数|分库表个数|库名库名:表名前缀:开始标号}
func DefaultMysqlHashFunc(id, rule string, hashFunc func(string) uint32) (string, string) {
	dbList := strings.Split(rule, "|")
	tbNum, _ := strconv.Atoi(dbList[0])
	pos := int(hashFunc(id)) % tbNum
	tbNumList := strings.Split(dbList[1], ":")
	if len(tbNumList)+2 != len(dbList) {
		// 格式不满足
		return "", ""
	}

	offset := 0
	for _, n := range tbNumList {
		num, _ := strconv.Atoi(n)
		if pos < num {
			break
		}
		pos -= num
		offset++
	}

	tbList := strings.Split(dbList[offset+2], ":")
	if len(tbList) < 3 {
		// 格式不满足
		return "", ""
	}
	begin, _ := strconv.Atoi(tbList[2])
	return tbList[0], tbList[1] + strconv.Itoa(begin+pos)
}

// NoneMysqlFunc 指定库表路由
// devSetStr:{dbName:tableName} {数据库名:表名}
func NoneMysqlFunc(rule string) (string, string) {
	routeInfos := strings.Split(rule, ":")
	return routeInfos[0], routeInfos[1]
}

// NoneESFunc 指定ES的index
func NoneESFunc(rule string) string {
	return rule
}

// CommunityHashFunc 社区号帖子数据专用路由函数
func CommunityHashFunc(id, rule string) (string, string) {
	if len(id) == 0 {
		return "", ""
	}

	dbName, tbName := DefaultMysqlHashFunc(id, rule, tool.RSHash)
	// 4到9开头的数据暂时放到tidb里面
	if id[0] >= '4' && id[0] <= '9' {
		dbName = "ti_" + dbName
	}
	return dbName, tbName
}

// NoneHbaseFunc hbase路由拆分
// rule:{hbaseName:tableName:familyName} {集群名:表名:列族名}
func NoneHbaseFunc(rule string) (string, string, string) {
	routeInfos := strings.Split(rule, ":")
	return routeInfos[0], routeInfos[1], routeInfos[2]
}
