package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/hydra/tms"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
	"github.com/avast/retry-go/v4"
)

const (
	AppName = "outer_circulation_monitor"
)

// CrawTaskArgs 抓取任务需要的参数
type CrawTaskArgs struct {
	CrawType      int
	TaskID        string
	Source        string
	URL           string
	CrawTxt       string
	TopicName     string
	TopicID       string
	AccountName   string
	BacktraceDays int
	Transmit      string
}

func getCrawlKey(args *CrawTaskArgs) string {
	var crawlKey string
	if args.CrawTxt != "" {
		crawlKey = args.CrawTxt
	} else if args.TopicName != "" {
		crawlKey = args.TopicName
	} else if args.TopicID != "" {
		crawlKey = args.TopicID
	} else if args.AccountName != "" {
		crawlKey = args.AccountName
	}
	return crawlKey
}

type processReportFunc func(ctx context.Context, err error) func()

var ProcessReport processReportFunc = func(ctx context.Context, err error) func() {
	return func() {
		var reportInfo report.ReportCrawlTraceInfo

		if v := ctx.Value("craw_args"); v != nil {
			args, _ := v.(*CrawTaskArgs)
			reportInfo.CrawlSource = args.Source
			reportInfo.CrawlType = args.CrawType
			reportInfo.CrawlKey = getCrawlKey(args)
			reportInfo.MsgType = model.MsgTypeAddCrawTask
			reportInfo.Transmit = args.Transmit
			_, _, reportInfo.OrderID, _ = model.ExtractCrawID(reportInfo.Transmit)
			reportInfo.ReqInfo, _ = ctx.Value("craw_req").(string)
		}
		if v := ctx.Value("craw_rsp"); v != nil {
			reportInfo.RspInfo = fmt.Sprintf("%+v", v.(*tms.AddJobReply))
		}

		if err == nil {
			reportInfo.RspCode = 0
			reportInfo.RspInfo = "ok"
		} else {
			reportInfo.RspCode = -1
			reportInfo.RspInfo = err.Error()
		}
		reportInfo.DoReport(ctx)
	}
}

// AddCrawTask 添加抓取任务（IP话题、CP等抓取任务）
func AddCrawTask(ctx context.Context, args *CrawTaskArgs, isSearch bool) error {
	var err error
	var rsp *tms.AddJobReply
	req := &tms.AddJobRequest{
		Caller: AppName,
		Job: &tms.Job{
			TaskID:       conf.GetCrawlerID(args.CrawType, args.Source, args.URL, isSearch), // 抓取侧是根据这个参数，来区分不同类型的抓取任务
			BizID:        args.Transmit,
			JobArguments: getJobArgs(args),
		},
	}
	ctx = context.WithValue(ctx, "craw_args", args)
	ctx = context.WithValue(ctx, "craw_rsp", rsp)
	ctx = context.WithValue(ctx, "craw_req", fmt.Sprintf("%+v", req))
	defer ProcessReport(ctx, err)()

	if _, err = InvokeCrawlerAddTask(ctx, req); err != nil {
		return err
	}

	log.InfoContextf(ctx, "AddCrawTask suc.%s", args.Transmit)
	// IP 话题抓取时，需要单独触发抓取话题属性
	if !(args.CrawType == model.TopicType && !isSearch) {
		return nil
	}
	args.CrawType = model.TopicFieldType
	args.Transmit = strings.Replace(args.Transmit, strconv.Itoa(model.TopicType)+"_",
		strconv.Itoa(model.TopicFieldType)+"_", 1)
	req.Job.TaskID = conf.GetCrawlerID(model.TopicFieldType, args.Source, args.URL, isSearch)
	req.Job.BizID = args.Transmit
	req.Job.JobArguments = getJobArgs(args)
	if _, err = InvokeCrawlerAddTask(ctx, req); err != nil {
		return err
	}
	log.InfoContextf(ctx, "craw topic field suc.%s", args.Transmit)
	return nil
}

type jobArgs struct {
	Source        string `json:"source"`
	TopicID       string `json:"topic_id"`
	TopicName     string `json:"topic_name"`
	Query         string `json:"query"`
	URL           string `json:"url"`
	BacktraceDays int    `json:"backtrace_days"`
	Transmit      string `json:"transmit"`
}

func getJobArgs(args *CrawTaskArgs) string {
	jobArguments, _ := json.Marshal(&jobArgs{
		Source:        args.Source,
		Query:         args.CrawTxt,
		URL:           args.URL,
		BacktraceDays: args.BacktraceDays,
		TopicID:       args.TopicID,
		TopicName:     args.TopicName,
		Transmit:      args.Transmit, // 用taskID作为唯一key，此字段在抓取侧返回结果时，会带过来
	})
	return string(jobArguments)
}

// InvokeCrawlerAddTask 请求抓取侧接口，触发抓取任务
func InvokeCrawlerAddTask(ctx context.Context, req *tms.AddJobRequest) (*tms.AddJobReply, error) {
	c := tms.NewTmsClientProxy(
		client.WithServiceName("trpc.hydra.tms.Tms"),
		client.WithProtocol("trpc"),
		client.WithNetwork("tcp4"))
	var rsp *tms.AddJobReply
	retError := retry.Do(
		func() error {
			var err error
			rsp, err = c.AddJob(ctx, req) // 避免上游调度超时，不用上游传来的ctx
			if err != nil {
				log.ErrorContextf(ctx, "AddTask err:%+v", err)
			}
			if rsp != nil && rsp.Code != 0 {
				err = fmt.Errorf("AddTask err, code:%d, msg:%s", rsp.Code, rsp.Msg)
				log.ErrorContextf(ctx, "AddTask code err:%+v", err)
			}
			return err
		},
		retry.Attempts(3), // 重试次数
		retry.Delay(time.Millisecond*time.Duration(100)), // 延迟重试
		retry.MaxDelay(time.Millisecond*1000),            // 最大延迟时间
		retry.LastErrorOnly(true),
		retry.OnRetry(func(idx uint, err error) { // 每次重试的时候调用方法
			log.ErrorContextf(ctx, "Failed to send(%+v), error message is %+v; retry time:%d", req, rsp, idx)
		}))
	log.InfoContextf(ctx, "InvokeCrawlerAddTask suc:%+v-%+v", req, retError)
	return rsp, retError
}
