package dao

import (
	"context"
)

// TopicInfo 话题列表信息
type TopicInfo struct {
	OrderID   int    `db:"c_order_id"`   // OrderID 关联的订单ID
	IPID      int    `db:"c_ip_id"`      // IPID ip 表的 c_id
	TopicID   string `db:"c_topic_id"`   // TopicID 话题 id
	TopicName string `db:"c_topic_name"` // TopicName 话题名
	TopicURL  string `db:"c_topic_url"`  // TopicURL 话题 url
	Rank      int    `db:"c_rank"`       // Rank 排名
	CrawKey   string `db:"c_craw_key"`   // CrawKey 抓取 key
	CrawCID   string `db:"c_craw_cid"`   // CrawCID 所属 cid
}

// CreateTopicInfo 创建话题信息
func CreateTopicInfo(ctx context.Context, topicInfo *TopicInfo) error {
	_, err := proxy.NamedExec(ctx,
		"INSERT INTO t_topic_list "+
			"(c_order_id, c_ip_id, c_topic_id, c_topic_name, c_topic_url, c_rank, c_craw_key, c_craw_cid) VALUES "+
			"(:c_order_id, :c_ip_id, :c_topic_id, :c_topic_name, :c_topic_url, :c_rank, :c_craw_key, :c_craw_cid) "+
			"ON DUPLICATE KEY UPDATE "+
			"c_order_id=:c_order_id,c_topic_name=:c_topic_name, c_topic_url=:c_topic_url, "+
			"c_rank=:c_rank, c_craw_key=:c_craw_key, c_craw_cid=:c_craw_cid",
		[]*TopicInfo{topicInfo})
	return err
}
