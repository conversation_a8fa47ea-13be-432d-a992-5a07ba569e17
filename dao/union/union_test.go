package union

import (
	"context"
	"reflect"
	"testing"

	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/trpcprotocol/unionplus/common"
	"git.code.oa.com/v/data_platform/unionplus/lib/trpc_go_union_client/unionplus"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"
)

func Test_parseConnInfo(t *testing.T) {
	type args struct {
		connInfo string
	}
	tests := []struct {
		name    string
		args    args
		want    *router
		wantErr bool
	}{
		{
			name: "测试正常解析连接信息",
			args: args{"view=2001&set=unionplus.main.play&alias=polaris://trpc.unionplus.unionplus_access" +
				".ReadTrpcUnionPlus.XXXXX"},
			want: &router{
				view:      "2001",
				setName:   "unionplus.main.play",
				aliasName: "polaris://trpc.unionplus.unionplus_access.ReadTrpcUnionPlus.XXXXX",
			},
			wantErr: false,
		}, {
			name: "测试解析乱序连接信息",
			args: args{"alias=polaris://trpc.unionplus.unionplus_access.ReadTrpcUnionPlus.XXXXX" +
				"&set=unionplus.main.play&view=2001"},
			want: &router{
				view:      "2001",
				setName:   "unionplus.main.play",
				aliasName: "polaris://trpc.unionplus.unionplus_access.ReadTrpcUnionPlus.XXXXX",
			},
			wantErr: false,
		}, {
			name:    "测试没有view信息",
			args:    args{"set=unionplus.main.play"},
			want:    nil,
			wantErr: true,
		}, {
			name:    "测试只配置view信息",
			args:    args{"view=2001"},
			want:    &router{view: "2001"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseConnInfo(tt.args.connInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseConnInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("parseConnInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_parseAuthInfo(t *testing.T) {
	type args struct {
		authInfo string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		want1   string
		wantErr bool
	}{
		{
			name:    "测试解析鉴权信息",
			args:    args{"testApp:testKey"},
			want:    "testApp",
			want1:   "testKey",
			wantErr: false,
		}, {
			name:    "测试解析异常鉴权信息",
			args:    args{"invalidAuth"},
			want:    "",
			want1:   "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := parseAuthInfo(tt.args.authInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseAuthInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("parseAuthInfo() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("parseAuthInfo() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestUnion_createDocInfo(t *testing.T) {
	type fields struct {
		Ctx   context.Context
		Name  string
		View  string
		proxy unionplus.Client
	}
	type args struct {
		key    string
		fields []*item.FieldInfo
		data   map[string]*common.Value
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *protocol.DocInfo
	}{
		{
			name:   "测试生成文档数据",
			fields: fields{},
			args: args{
				key: "testVid",
				fields: []*item.FieldInfo{{
					FieldID:   9,
					FieldType: 1,
					DataSetID: 2001,
					FieldName: "titleX",
				}, {
					FieldID:   3087,
					FieldType: 3,
					DataSetID: 2001,
					FieldName: "related_topic_id",
				}},
				data: map[string]*common.Value{
					"titleX":           {Val: &common.Value_Strval{Strval: []byte("测试标题")}},
					"related_topic_id": {Val: &common.Value_Arrval{Arrval: &common.Value_ListValue{Listval: []*common.Value{{Val: &common.Value_Intval{Intval: 1}}, {Val: &common.Value_Floatval{Floatval: 2.1}}, {Val: &common.Value_Boolval{Boolval: true}}}}}},
				},
			},
			want: &protocol.DocInfo{
				Id: "testVid",
				Fields: map[string]*protocol.FieldInfo{"titleX": {
					FieldName: "titleX",
					FieldId:   9,
					FieldType: 1,
					StrValue:  "测试标题",
				}, "related_topic_id": {
					FieldName: "related_topic_id",
					FieldId:   3087,
					FieldType: 3,
					VecStr:    []string{"1", "2.1", "true"},
				}},
				FailList: make(map[string]*protocol.FailInfo),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			u := &Union{
				Ctx:   tt.fields.Ctx,
				Name:  tt.fields.Name,
				View:  tt.fields.View,
				proxy: tt.fields.proxy,
			}
			if got := u.createDocInfo(tt.args.key, tt.args.fields, tt.args.data); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("createDocInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}
