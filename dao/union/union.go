// Package union union相关逻辑
package union

import (
	"context"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/trpcprotocol/unionplus/common"
	"git.code.oa.com/v/data_platform/unionplus/lib/trpc_go_union_client/unionplus"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"
)

// Union union存储对象
type Union struct {
	// Ctx 上下文
	Ctx context.Context
	// Name Union服务实例名
	Name string
	// View union视图
	View string
	// proxy union plus客户端
	proxy unionplus.Client
}

// NewUnionPlusProxy 用于mock
var NewUnionPlusProxy = func(appID, appKey string, opts ...client.Option) unionplus.Client {
	return unionplus.New(appID, appKey, opts...)
}

// RouteRuleFunc 实现路由接口
func (u *Union) RouteRuleFunc(_, _, devSetStr string) string {
	u.Name = devSetStr
	return u.Name
}

// router union路由信息
type router struct {
	// view union视图
	view string
	// setName 服务set名
	setName string
	// aliasName 服务别名
	aliasName string
}

// Options 返回调用options
func (u *router) Options() []client.Option {
	if u.setName != "" {
		// 优先使用setName路由
		return []client.Option{client.WithCalleeSetName(u.setName)}
	}
	if u.aliasName != "" {
		// 支持使用别名路由
		return []client.Option{client.WithTarget(u.aliasName), client.WithDisableServiceRouter()}
	}
	return nil
}

// parseConnInfo 解析路由信息 连接串格式:view=XXX&set=XXX&alias=XXX
func parseConnInfo(connInfo string) (*router, error) {
	var r router
	params := strings.Split(connInfo, "&")
	for _, param := range params {
		k, v, found := strings.Cut(param, "=")
		if !found {
			continue
		}
		switch k {
		case "view":
			r.view = v
		case "set":
			r.setName = v
		case "alias":
			r.aliasName = v
		default:
		}
	}
	if r.view == "" {
		return nil, fmt.Errorf("view not found")
	}
	return &r, nil
}

// parseAuthInfo 解析鉴权信息
func parseAuthInfo(authInfo string) (string, string, error) {
	auths := strings.Split(authInfo, ":")
	if len(auths) != 2 {
		log.Error("Fail to parse auth info.")
		return "", "", fmt.Errorf("invalid auth info")
	}
	return auths[0], auths[1], nil
}

// GetConn 实现连接接口
func (u *Union) GetConn(connectInfo string, authInfo string, _ string) error {
	appID, appKey, err := parseAuthInfo(authInfo)
	if err != nil {
		return err
	}

	r, err := parseConnInfo(connectInfo)
	if err != nil {
		log.Errorf("Fail to parse connect info, err is %v.", err)
		return err
	}

	u.View = r.view
	u.proxy = NewUnionPlusProxy(appID, appKey, r.Options()...)
	return nil
}

// GetKey 返回设备信息表key值
func (u *Union) GetKey() string {
	return u.Name
}

// GetFieldInfos 统一获取value接口
func (u *Union) GetFieldInfos(_ []string, _ *inf.FieldKey, _ *adaptor.GetFieldInfosResponse) error {
	return nil
}

func createFieldInfo(f *item.FieldInfo, v *common.Value) *protocol.FieldInfo {
	fieldInfo := &protocol.FieldInfo{
		FieldName:      f.FieldName,
		InnerFieldName: f.InnerFieldName,
		FieldId:        f.FieldID,
		FieldType:      protocol.EnumFieldType(f.FieldType),
	}

	val := parseValue(v)
	switch fieldInfo.FieldType {
	case protocol.EnumFieldType_FieldTypeStr:
		if len(val) > 0 {
			fieldInfo.StrValue = val[0]
		}
	case protocol.EnumFieldType_FieldTypeSet:
		fieldInfo.VecStr = val
	default:
	}
	return fieldInfo
}

func (u *Union) createDocInfo(key string, fields []*item.FieldInfo, data map[string]*common.Value) *protocol.DocInfo {
	docInfo := &protocol.DocInfo{
		Id:       key,
		Fields:   make(map[string]*protocol.FieldInfo),
		FailList: make(map[string]*protocol.FailInfo),
	}

	for _, f := range fields {
		v, ok := data[f.FieldName]
		if !ok {
			// 不存在该字段
			continue
		}
		docInfo.Fields[f.FieldName] = createFieldInfo(f, v)
	}
	return docInfo
}

// BatchGetFields 统一批量获取接口
func (u *Union) BatchGetFields(datasetID int32, ids, fields []string,
	_ map[string][]string,
) (*model.BatchGetResponse, error) {
	var columns []*common.Select
	fieldInfos := logic.NewFieldCacheList(datasetID, fields)
	for _, f := range fieldInfos {
		columns = append(columns, &common.Select{Column: f.FieldName})
	}

	rsp, err := u.proxy.GetRaw(u.Ctx, &common.QueryReq{
		ViewName: u.View,
		Columns:  columns,
		Where:    &common.Where{Val: &common.Where_Keys_{Keys: &common.Where_Keys{Keys: ids}}},
	})
	if err != nil {
		log.Errorf("Fail to read union, err is %v.", err)
		return nil, err
	}

	getRsp := &model.BatchGetResponse{
		FailList: make(map[string]string),
	}
	for _, row := range rsp.GetData().GetRows() {
		key := row.Key
		if row.Retcode != 0 {
			getRsp.FailList[key] = row.GetErrormsg()
			continue
		}
		getRsp.DocInfos = append(getRsp.DocInfos, u.createDocInfo(key, fieldInfos, row.Columns))
	}
	return getRsp, nil
}

// InsertFieldInfos 统一插入接口，可以指定版本号
func (u *Union) InsertFieldInfos(_ string, _ int64, _ []*protocol.UpdateFieldInfo) (*model.ModifyInfo, error) {
	// todo:不支持union更新
	return nil, nil
}

// SetFieldInfos 统一设置value接口
func (u *Union) SetFieldInfos(_ string, _ []*protocol.FieldInfo, _ []*protocol.UpdateFieldInfo,
	_ *adaptor.SetFieldInfosResponse,
) (*model.ModifyInfo, error) {
	// todo:不支持union更新
	return nil, nil
}
