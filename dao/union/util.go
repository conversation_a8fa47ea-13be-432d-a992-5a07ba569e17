package union

import (
	"fmt"

	"git.code.oa.com/trpcprotocol/unionplus/common"
)

// parseValue union原数据解析为字符串数组
// NOCC:CCN_threshold(设计如此:)
func parseValue(value *common.Value) []string {
	switch value.Val.(type) {
	case *common.Value_Strval:
		return []string{string(value.GetStrval())}
	case *common.Value_Intval:
		return []string{fmt.Sprint(value.GetIntval())}
	case *common.Value_Floatval:
		return []string{fmt.Sprint(value.GetFloatval())}
	case *common.Value_Boolval:
		return []string{fmt.Sprint(value.GetBoolval())}
	case *common.Value_Arrval:
		var result []string
		for _, arrayItem := range value.GetArrval().Listval {
			if v := parseValue(arrayItem); v != nil {
				result = append(result, v...)
			}
		}
		return result
	default:
		return nil
	}
}
