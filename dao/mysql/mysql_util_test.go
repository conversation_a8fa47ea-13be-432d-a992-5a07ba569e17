package mysql

import (
	"reflect"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	tool "git.code.oa.com/video_media/storage_service/common"
)

func Test_createConnFormat(t *testing.T) {
	type args struct {
		connStr string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "测试直连场景",
			args: args{connStr: "direct|11.160.133.32:3306"},
			want: "dsn://%s@tcp(11.160.133.32:3306)/",
		}, {
			name: "测试使用北极星路由场景",
			args: args{connStr: "zkname|mediatest.mdb.mig"},
			want: "mysql+polaris://%s@tcp(mediatest.mdb.mig)/",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := newConnFormat(tt.args.connStr); got != tt.want {
				t.Errorf("newConnFormat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_createSQLQueryFields(t *testing.T) {
	type args struct {
		ids      []string
		fieldIDs []uint32
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
		want2 []interface{}
	}{
		{
			name: "测试单个id和多个字段值",
			args: args{
				ids:      []string{"testVid"},
				fieldIDs: []uint32{1, 2, 3},
			},
			want:  "?",
			want1: "?,?,?",
			want2: []interface{}{"testVid", uint32(1), uint32(2), uint32(3)},
		}, {
			name: "测试参数传空",
			args: args{
				ids:      []string{"testVid1", "testVid2"},
				fieldIDs: nil,
			},
			want:  "",
			want1: "",
			want2: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := createSQLQueryFields(tt.args.ids, tt.args.fieldIDs)
			if got != tt.want {
				t.Errorf("createSQLQueryFields() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("createSQLQueryFields() got1 = %v, want %v", got1, tt.want1)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("createSQLQueryFields() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}

// TestBasicFillData 测试Basic的FillData方法
func TestBasicFillData(t *testing.T) {
	tests := []struct {
		name           string
		setupMockRows  func(sqlmock.Sqlmock)
		expectedCur    map[uint32]string
		expectedError  bool
		specialFieldID uint32 // 用于测试特殊字段处理
	}{
		{
			name: "成功填充普通数据",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("test_id", uint32(1001), "test_value", "2023-01-01 00:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]string{
				1001: "test_value",
			},
			expectedError: false,
		},
		{
			name: "测试duration字段特殊处理(字段ID 80079)",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("test_id", uint32(80079), "123.456", "2023-01-01 00:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]string{
				80079: "123", // 小数点后的值应该被去掉
			},
			expectedError:  false,
			specialFieldID: 80079,
		},
		{
			name: "填充多条数据",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("test_id1", uint32(1001), "value1", "2023-01-01 00:00:00").
					AddRow("test_id2", uint32(1002), "value2", "2023-01-01 00:00:01").
					AddRow("test_id3", uint32(1003), "value3", "2023-01-01 00:00:02")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]string{
				1001: "value1",
				1002: "value2",
				1003: "value3",
			},
			expectedError: false,
		},
		{
			name: "空字符串值处理",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("test_id", uint32(1001), "", "2023-01-01 00:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]string{
				1001: "",
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock数据库
			db, mock, err := sqlmock.New()
			assert.NoError(t, err)
			defer db.Close()

			// 设置mock数据
			tt.setupMockRows(mock)

			// 执行查询
			rows, err := db.Query("SELECT c_id, c_field_id, c_field_value, c_mtime FROM test_table")
			assert.NoError(t, err)
			defer rows.Close()

			basic := NewBasic()
			assert.NotNil(t, basic)

			// 处理每一行数据
			for rows.Next() {
				err := basic.FillData(rows)
				if tt.expectedError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
				}
			}

			// 验证填充结果
			if !tt.expectedError {
				assert.Equal(t, len(tt.expectedCur), len(basic.cur))
				for fieldID, expectedValue := range tt.expectedCur {
					actualValue, exists := basic.cur[fieldID]
					assert.True(t, exists, "字段ID %d 应该存在", fieldID)
					assert.Equal(t, expectedValue, actualValue, "字段ID %d 的值不匹配", fieldID)
				}
			}

			// 验证所有预期的mock调用都被执行
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestBasicFillGetField 测试Basic的FillGetField方法
func TestBasicFillGetField(t *testing.T) {
	tests := []struct {
		name           string
		setupMockRows  func(sqlmock.Sqlmock)
		expectedField  *adaptor.GetFieldInfo
		expectedError  bool
		specialFieldID uint32
	}{
		{
			name: "成功填充普通字段",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("test_id_001", uint32(2001), "title_value", "2023-01-01 12:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "test_id_001",
				FieldId:    2001,
				FieldValue: "title_value",
				Mtime:      "2023-01-01 12:00:00",
			},
			expectedError: false,
		},
		{
			name: "测试duration字段特殊处理(字段ID 80079)",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("video_001", uint32(80079), "456.789", "2023-01-01 13:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "video_001",
				FieldId:    80079,
				FieldValue: "456", // 小数点后的值应该被去掉
				Mtime:      "2023-01-01 13:00:00",
			},
			expectedError:  false,
			specialFieldID: 80079,
		},
		{
			name: "空值处理",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("empty_test", uint32(3001), "", "2023-01-01 14:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "empty_test",
				FieldId:    3001,
				FieldValue: "",
				Mtime:      "2023-01-01 14:00:00",
			},
			expectedError: false,
		},
		{
			name: "包含特殊字符的值",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("special_test", uint32(4001), "测试标题with特殊字符!@#", "2023-01-01 15:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "special_test",
				FieldId:    4001,
				FieldValue: "测试标题with特殊字符!@#",
				Mtime:      "2023-01-01 15:00:00",
			},
			expectedError: false,
		},
		{
			name: "长文本值处理",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				longText := strings.Repeat("长文本内容", 100) // 创建长文本
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("long_text_test", uint32(5001), longText, "2023-01-01 16:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "long_text_test",
				FieldId:    5001,
				FieldValue: strings.Repeat("长文本内容", 100),
				Mtime:      "2023-01-01 16:00:00",
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock数据库
			db, mock, err := sqlmock.New()
			assert.NoError(t, err)
			defer db.Close()

			// 设置mock数据
			tt.setupMockRows(mock)

			// 执行查询
			rows, err := db.Query("SELECT c_id, c_field_id, c_field_value, c_mtime FROM test_table")
			assert.NoError(t, err)
			defer rows.Close()

			basic := NewBasic()
			assert.NotNil(t, basic)

			// 处理第一行数据
			if rows.Next() {
				field, err := basic.FillGetField(rows)

				if tt.expectedError {
					assert.Error(t, err)
					assert.Nil(t, field)
				} else {
					assert.NoError(t, err)
					assert.NotNil(t, field)

					// 验证字段内容
					assert.Equal(t, tt.expectedField.Id, field.Id)
					assert.Equal(t, tt.expectedField.FieldId, field.FieldId)
					assert.Equal(t, tt.expectedField.FieldValue, field.FieldValue)
					assert.Equal(t, tt.expectedField.Mtime, field.Mtime)
				}
			}

			// 验证所有预期的mock调用都被执行
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestBasicCurVal 测试Basic的CurVal方法
func TestBasicCurVal(t *testing.T) {
	tests := []struct {
		name           string
		cur            map[uint32]string
		field          *protocol.FieldInfo
		expectedVal    *tool.FieldVal
		expectedExists bool
	}{
		{
			name: "字段存在",
			cur: map[uint32]string{
				1001: "test_value_1",
				1002: "test_value_2",
			},
			field: &protocol.FieldInfo{
				FieldId: 1001,
			},
			expectedVal: &tool.FieldVal{
				TxtVal: "test_value_1",
			},
			expectedExists: true,
		},
		{
			name: "字段不存在",
			cur: map[uint32]string{
				1001: "test_value_1",
			},
			field: &protocol.FieldInfo{
				FieldId: 1002,
			},
			expectedVal: &tool.FieldVal{
				TxtVal: "",
			},
			expectedExists: false,
		},
		{
			name: "空的cur",
			cur:  map[uint32]string{},
			field: &protocol.FieldInfo{
				FieldId: 1001,
			},
			expectedVal: &tool.FieldVal{
				TxtVal: "",
			},
			expectedExists: false,
		},
		{
			name: "nil field",
			cur: map[uint32]string{
				1001: "test_value",
			},
			field: nil,
			expectedVal: &tool.FieldVal{
				TxtVal: "",
			},
			expectedExists: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			basic := &Basic{cur: tt.cur}

			// 防止panic
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("CurVal panicked: %v", r)
				}
			}()

			var val *tool.FieldVal
			var exists bool

			if tt.field != nil {
				val, exists = basic.CurVal(tt.field)
			} else {
				val = &tool.FieldVal{TxtVal: ""}
				exists = false
			}

			assert.Equal(t, tt.expectedExists, exists)
			assert.Equal(t, tt.expectedVal.TxtVal, val.TxtVal)
		})
	}
}

// TestNewBasic 测试NewBasic构造函数
func TestNewBasic(t *testing.T) {
	basic := NewBasic()
	assert.NotNil(t, basic)
	assert.NotNil(t, basic.cur)
	assert.Equal(t, 0, len(basic.cur))
	assert.IsType(t, map[uint32]string{}, basic.cur)
}

// TestBasicMethods 测试Basic的其他方法存在性
func TestBasicMethods(t *testing.T) {
	basic := NewBasic()

	t.Run("测试AddIns方法", func(t *testing.T) {
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("AddIns method panicked: %v", r)
			}
		}()

		// 创建测试参数
		para := &parameter{
			id:   "test_id",
			args: make([]interface{}, 0),
		}
		fieldID := uint32(1001)
		val := &tool.FieldVal{TxtVal: "test_value"}

		// 测试方法调用
		basic.AddIns(fieldID, val, para)

		// 验证参数已添加
		assert.Equal(t, 3, len(para.args))
		assert.Equal(t, "test_id", para.args[0])
		assert.Equal(t, fieldID, para.args[1])
		assert.Equal(t, "test_value", para.args[2])
	})

	t.Run("测试InsertSQL方法", func(t *testing.T) {
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("InsertSQL method panicked: %v", r)
			}
		}()

		para := &parameter{
			id:   "test_id",
			args: []interface{}{"test_id", uint32(1001), "test_value"},
		}

		sql, args := basic.InsertSQL("test_db", "test_table", "", para)
		assert.NotEmpty(t, sql)
		assert.NotEmpty(t, args)
	})

	t.Run("测试UpdateSQL方法", func(t *testing.T) {
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("UpdateSQL method panicked: %v", r)
			}
		}()

		para := &parameter{
			id:   "test_id",
			args: []interface{}{"test_id", uint32(1001), "test_value"},
		}

		sql, args := basic.UpdateSQL("test_db", "test_table", para)
		assert.NotEmpty(t, sql)
		assert.NotEmpty(t, args)
	})

	t.Run("测试QueryFromUpdate方法", func(t *testing.T) {
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("QueryFromUpdate method panicked: %v", r)
			}
		}()

		fields := []*protocol.UpdateFieldInfo{
			{
				FieldInfo: &protocol.FieldInfo{
					FieldId: 1001,
				},
			},
		}

		sql, args := basic.QueryFromUpdate("test_db", "test_table", "test_index", "test_id", fields)
		assert.NotEmpty(t, sql)
		assert.NotEmpty(t, args)
	})
}

// TestBasicFillDataErrorScenarios 测试FillData的错误场景
func TestBasicFillDataErrorScenarios(t *testing.T) {
	tests := []struct {
		name          string
		setupMockRows func(sqlmock.Sqlmock)
		expectedError bool
	}{
		{
			name: "Scan错误 - 类型不匹配",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				// 故意设置错误的数据类型来触发Scan错误
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value", "c_mtime"}).
					AddRow("test_id", "invalid_uint32", "test_value", "2023-01-01 00:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock数据库
			db, mock, err := sqlmock.New()
			assert.NoError(t, err)
			defer db.Close()

			// 设置mock数据
			tt.setupMockRows(mock)

			// 执行查询
			rows, err := db.Query("SELECT c_id, c_field_id, c_field_value, c_mtime FROM test_table")
			assert.NoError(t, err)
			defer rows.Close()

			basic := NewBasic()

			if rows.Next() {
				err := basic.FillData(rows)
				if tt.expectedError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
				}
			}

			// 验证所有预期的mock调用都被执行
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestBasicFillGetFieldErrorScenarios 测试FillGetField的错误场景
func TestBasicFillGetFieldErrorScenarios(t *testing.T) {
	tests := []struct {
		name          string
		setupMockRows func(sqlmock.Sqlmock)
		expectedError bool
	}{
		{
			name: "Scan错误 - 字段数不匹配",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				// 只提供3个字段，但期望4个字段
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_value"}).
					AddRow("test_id", uint32(1001), "test_value")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock数据库
			db, mock, err := sqlmock.New()
			assert.NoError(t, err)
			defer db.Close()

			// 设置mock数据
			tt.setupMockRows(mock)

			// 执行查询
			rows, err := db.Query("SELECT c_id, c_field_id, c_field_value FROM test_table")
			assert.NoError(t, err)
			defer rows.Close()

			basic := NewBasic()

			if rows.Next() {
				field, err := basic.FillGetField(rows)
				if tt.expectedError {
					assert.Error(t, err)
					assert.Nil(t, field)
				} else {
					assert.NoError(t, err)
					assert.NotNil(t, field)
				}
			}

			// 验证所有预期的mock调用都被执行
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
