package mysql

import (
	"database/sql"
	"fmt"
	"math"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	tool "git.code.oa.com/video_media/storage_service/common"
)

// 数据库路由方式
const (
	directConn = "direct"
	zkNameConn = "zkname"
)

// errUpdateTimeout 自定义更新超时错误码
const errUpdateTimeout = 10001

// isUpdateTimeout 是否更新超时
func isUpdateTimeout(err error) bool {
	return errs.Code(err) == errUpdateTimeout
}

// 基础读写sql
const (
	SQLSelect    = "select c_id,c_field_id,c_field_value,c_mtime from %s.%s %s where c_id in (%s) and c_field_id in (%s)"
	SQLSelectAll = "select c_id,c_field_id,c_field_value,c_mtime from %s.%s %s where c_id=?"
	SQLInsert    = "insert %s into %s.%s (c_id,c_field_id,c_field_value) values %s"
	SQLUpdate    = `insert into %s.%s (c_id, c_field_id, c_field_value)
values
  %s ON DUPLICATE KEY
UPDATE
  c_field_value =
values(c_field_value)`
	SQLUpdateWithVersion = `insert into %s.%s (c_id, c_field_id, c_field_value)
values
  %s ON DUPLICATE KEY
UPDATE
  c_field_value = CASE
    WHEN c_field_id = ? THEN c_field_value + 1
    ELSE
    VALUES(c_field_value)
  END;`
)

// parameter sql参数结构 todo:待优化
type parameter struct {
	id         string
	versionFID int
	args       []interface{}
}

// sqlExecutor sql执行结构
type sqlExecutor struct {
	sql  string
	args []interface{}
}

// sqlBuilder 生成sql接口
type sqlBuilder interface {
	// AddIns 添加insert的插入值
	AddIns(fieldID uint32, val *tool.FieldVal, para *parameter)
	// InsertSQL 获取insert sql及参数
	InsertSQL(dbName, tbName, ignore string, para *parameter) (string, []interface{})
	// UpdateSQL 获取update sql及参数
	UpdateSQL(dbName, tbName string, para *parameter) (string, []interface{})
	// QueryFromUpdate 通过更新请求获取query sql及参数
	QueryFromUpdate(dbName, tbName, index, id string, fields []*protocol.UpdateFieldInfo) (string, []interface{})
	// QueryFromGet 通过get请求获取query sql及参数
	QueryFromGet(dbName, tbName, forceIndex string, ids []string, key *inf.FieldKey) []*sqlExecutor
}

// Dao mysql数据模型接口
type Dao interface {
	tool.CurVal
	sqlBuilder
	// FillData 填充数据
	FillData(rows *sql.Rows) error
	// FillGetField 填充get响应字段
	FillGetField(rows *sql.Rows) (*adaptor.GetFieldInfo, error)
}

// Basic mysql基础类型
type Basic struct {
	cur map[uint32]string
}

// NewBasic 创建mysql基础类型
func NewBasic() *Basic {
	return &Basic{cur: make(map[uint32]string)}
}

// CurVal 实现CurVal接口
func (b *Basic) CurVal(field *protocol.FieldInfo) (*tool.FieldVal, bool) {
	val, ok := b.cur[field.FieldId]
	return &tool.FieldVal{TxtVal: val}, ok
}

// FillData 实现Dao接口
func (b *Basic) FillData(rows *sql.Rows) error {
	var (
		fieldID           uint32
		id, result, mtime string
	)
	if err := rows.Scan(&id, &fieldID, &result, &mtime); err != nil {
		log.Errorf("Fail to scan data, err is %v.", err)
		return err
	}
	// todo:针对duration字段做的特殊处理，去掉小数点后的值，待数据清洗后需要删除这个逻辑
	if fieldID == 80079 {
		pos := strings.Index(result, ".")
		if pos > -1 {
			result = result[0:pos]
		}
	}
	log.Debugf("[FillData]fieldID:%d, value:%s.", fieldID, result)
	b.cur[fieldID] = result
	return nil
}

// FillGetField 实现Dao接口
func (b *Basic) FillGetField(rows *sql.Rows) (*adaptor.GetFieldInfo, error) {
	// 为获取db数据对象建立对象池，优化性能和内存占用
	var field adaptor.GetFieldInfo
	if err := rows.Scan(&field.Id, &field.FieldId, &field.FieldValue, &field.Mtime); err != nil {
		log.Errorf("Fail to scan data, err is %v.", err)
		return nil, err
	}
	// todo:针对duration字段做的特殊处理，去掉小数点后的值，待数据清洗后需要删除这个逻辑
	if field.FieldId == 80079 {
		pos := strings.Index(field.FieldValue, ".")
		if pos > -1 {
			field.FieldValue = field.FieldValue[0:pos]
		}
	}
	log.Debugf("[FillGetField]id:%s,fieldID:%d, value:%s. Mtime:%s.",
		field.Id, field.FieldId, field.FieldValue, field.Mtime)
	return &field, nil
}

// AddIns 实现sqlBuilder接口
func (b *Basic) AddIns(fieldID uint32, val *tool.FieldVal, para *parameter) {
	para.args = append(para.args, para.id, fieldID, val.TxtVal)
}

// InsertSQL 实现sqlBuilder接口
func (b *Basic) InsertSQL(dbName, tbName, ignore string, para *parameter) (string, []interface{}) {
	paraNum := len(para.args) / 3
	if paraNum == 0 {
		return "", nil
	}
	return fmt.Sprintf(SQLInsert, ignore, dbName, tbName, strings.Repeat(",(?,?,?)", paraNum)[1:]), para.args
}

// UpdateSQL 实现sqlBuilder接口
func (b *Basic) UpdateSQL(dbName, tbName string, para *parameter) (string, []interface{}) {
	if len(para.args)/3 == 0 {
		// 参数模式为(id,fieldID,fieldValue)... 3取模为0时说明无更新参数，可以跳过更新
		return "", nil
	}

	format := SQLUpdate
	if para.versionFID > 0 {
		format = SQLUpdateWithVersion
		para.args = append(para.args, para.id, para.versionFID, "1", para.versionFID)
	}
	return fmt.Sprintf(format, dbName, tbName, strings.Repeat(",(?,?,?)", len(para.args)/3)[1:]), para.args
}

// QueryFromUpdate 实现sqlBuilder接口
func (b *Basic) QueryFromUpdate(dbName, tbName, index, id string, fields []*protocol.UpdateFieldInfo) (
	string, []interface{},
) {
	num := len(fields)
	if num == 0 {
		return "", nil
	}

	args := make([]interface{}, 0, num+1)
	args = append(args, id)
	for _, f := range fields {
		args = append(args, f.GetFieldInfo().GetFieldId())
	}
	return fmt.Sprintf(SQLSelect, dbName, tbName, index, "?", strings.Repeat(",?", num)[1:]), args
}

// createSQLQueryFields 创建查询sql值域
func createSQLQueryFields(ids []string, fieldIDs []uint32) (string, string, []interface{}) {
	vidNum, fieldNum := len(ids), len(fieldIDs)
	if vidNum == 0 || fieldNum == 0 {
		return "", "", nil
	}

	args := make([]interface{}, 0, vidNum+fieldNum)
	for _, id := range ids {
		args = append(args, id)
	}
	for _, fieldID := range fieldIDs {
		args = append(args, fieldID)
	}
	params := strings.Repeat(",?", int(math.Max(float64(vidNum), float64(fieldNum))))
	return params[1 : 2*vidNum], params[1 : 2*fieldNum], args
}

// QueryFromGet 实现sqlBuilder接口
func (b *Basic) QueryFromGet(dbName, tbName, forceIndex string, ids []string, key *inf.FieldKey) []*sqlExecutor {
	fieldIDs := key.FieldIDs
	if logic.IsGetAllRequest(fieldIDs) {
		// *号全量查找
		return []*sqlExecutor{{
			sql:  fmt.Sprintf(SQLSelectAll, dbName, tbName, forceIndex),
			args: []interface{}{ids[0]},
		}}
	}

	pIDs, pFields, args := createSQLQueryFields(ids, fieldIDs)
	return []*sqlExecutor{{
		sql:  fmt.Sprintf(SQLSelect, dbName, tbName, forceIndex, pIDs, pFields),
		args: args,
	}}
}

// scanRows 将查询结果填入结果map中，返回行数
func scanRows(rows *sql.Rows, dao Dao) int {
	defer rows.Close()
	var num int
	for rows.Next() {
		num++
		if err := dao.FillData(rows); err != nil {
			// 读取数据失败先跳过
			log.Errorf("Fail to scan data, err is %s.", err)
			continue
		}
	}
	return num
}

// newConnFormat 创建mysql连接串
func newConnFormat(connStr string) string {
	connArray := strings.Split(connStr, "|")
	if len(connArray) < 2 {
		return ""
	}

	var pre string
	switch connArray[0] {
	case directConn:
		pre = "dsn://%s"
	case zkNameConn:
		pre = "mysql+polaris://%s"
	default:
		return ""
	}
	return pre + fmt.Sprintf("@tcp(%s)/", connArray[1])
}
