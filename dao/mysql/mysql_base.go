// Package mysql 数据适配层服务mysql类型存储相关逻辑
package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
)

// mtimeName 变更时间字段名称
const mtimeName = "modify_time"

// baseInfo 数据库基础信息
type baseInfo struct {
	// DBName 数据库名
	DBName string
	// TBName 数据表名
	TBName string
	// ForceIndex 强制使用索引名(FORCE INDEX)
	ForceIndex string
	// VersionFieldID 版本字段ID
	VersionFieldID int
}

// MysqlObj mysql存储对象
type MysqlObj struct {
	baseInfo
	Dao
	// Ctx 上下文
	Ctx context.Context
	// db 数据库实例连接对象
	db mysql.Client
	// unlock 不加锁字段列表
	unlock []*protocol.UpdateFieldInfo
	// lock 加锁字段列表
	lock []*protocol.UpdateFieldInfo
	// infos 变更信息列表，key为fieldID
	infos map[uint32]*protocol.ModifyFieldInfo
}

// GetKey mysql连接对象按照数据库名在设备信息表里做区分
func (m *MysqlObj) GetKey() string {
	return m.DBName
}

// RouteRuleFunc 数据库分库分表路由规则
func (m *MysqlObj) RouteRuleFunc(key, id, rule string) string {
	switch key {
	case inf.NoneFunction:
		m.DBName, m.TBName = inf.NoneMysqlFunc(rule)
	case inf.DefaultMysqlHash:
		m.DBName, m.TBName = inf.DefaultMysqlHashFunc(id, rule, inf.DefaultHashFunc)
	case inf.MysqlRsHash:
		m.DBName, m.TBName = inf.DefaultMysqlHashFunc(id, rule, tool.RSHash)
	case inf.CommunityHashById:
		m.DBName, m.TBName = inf.CommunityHashFunc(id, rule)
	}
	return m.DBName + "." + m.TBName
}

// GetConn 获取数据库连接对象
func (m *MysqlObj) GetConn(connInfo, authInfo, extraInfo string) error {
	var extra string
	// extraInfo:{db连接串}|{强制索引名}|{版本字段ID}
	for i, info := range strings.Split(extraInfo, "|") {
		if info == "" {
			continue
		}

		switch i {
		case 0:
			extra = info
		case 1:
			m.ForceIndex = "force index(" + info + ")"
		case 2:
			m.VersionFieldID, _ = strconv.Atoi(info)
		default:
		}
	}

	conn := fmt.Sprintf(newConnFormat(connInfo), authInfo) + extra
	m.db = mysql.NewClientProxy("trpc.storage_service.adaptor_layer.mysql", client.WithTarget(conn),
		client.WithNamespace("Production"))
	log.Debugf("connect string of mysql is %s.", conn)
	return nil
}

// updateDB 执行更新sql，tx不为空则使用事务
func (m *MysqlObj) updateDB(tx *sql.Tx, fn func() (string, []interface{})) error {
	_, err := m.updateDBWithResult(tx, fn)
	return err
}

// updateDB 执行更新sql返回result，tx不为空则使用事务
func (m *MysqlObj) updateDBWithResult(tx *sql.Tx, fn func() (string, []interface{})) (ret sql.Result, err error) {
	execSQL, args := fn()
	if execSQL == "" {
		return nil, nil
	}

	defer func() {
		log.Debugf("exec sql is %s, args is %v.", execSQL, args)
		if err != nil {
			if !mysql.IsDupEntryError(err) {
				log.Errorf("fail to exec db, err is %v, sql is %s, args is %v.", err, execSQL, args)
			}
			if errs.Code(err) == errs.RetClientNetErr {
				// 更新db超时的场景统一返回错误码errUpdateTimeout
				err = errs.New(errUpdateTimeout, err.Error())
			}
		}
	}()
	if tx != nil {
		// 使用事务进行更新
		return tx.Exec(execSQL, args...)
	}
	return m.db.Exec(m.Ctx, execSQL, args...)
}

// GetUnlockFieldInfo 获取无需加锁的字段内容
func (m *MysqlObj) GetUnlockFieldInfo(id string, baseFieldIDs []*protocol.FieldInfo) error {
	queryList := m.unlock
	for i := range baseFieldIDs {
		// 添加基础字段到查询sql
		queryList = append(queryList, &protocol.UpdateFieldInfo{FieldInfo: baseFieldIDs[i]})
	}
	query, args := m.QueryFromUpdate(m.DBName, m.TBName, m.ForceIndex, id, queryList)
	if query == "" {
		// 字符串类型字段列表为空，直接退出执行
		return nil
	}

	if err := m.db.Query(m.Ctx, m.FillData, query, args...); err != nil {
		log.Errorf("Fail to query unlock type fields, query is %s, err is %v.", query, err)
		return err
	}
	return nil
}

// BuildInsertSQL 生成插入字段sql
func (m *MysqlObj) BuildInsertSQL(id string, fields []*protocol.UpdateFieldInfo) (string, []interface{}) {
	return m.buildInsertSQL(id, "", fields)
}

// BuildInsertIgnoreSQL 生成插入字段sql
func (m *MysqlObj) BuildInsertIgnoreSQL(id string, fields []*protocol.UpdateFieldInfo) (string,
	[]interface{},
) {
	return m.buildInsertSQL(id, "ignore", fields)
}

func (m *MysqlObj) buildInsertSQL(id, ignore string, fields []*protocol.UpdateFieldInfo) (string,
	[]interface{},
) {
	if len(fields) == 0 {
		return "", nil
	}

	// 每次生成sql需要新申请参数对象
	para := &parameter{id: id}
	for _, f := range fields {
		// 插入操作原值一定是空的
		val, modifyInfo, _ := logic.BuildInsertValue(f, &tool.FieldVal{})
		if modifyInfo != nil {
			m.infos[modifyInfo.FieldId] = modifyInfo
			m.AddIns(f.FieldInfo.FieldId, val, para)
		}
	}
	return m.InsertSQL(m.DBName, m.TBName, ignore, para)
}

func sortUpdateFields(fields []*protocol.UpdateFieldInfo) {
	sort.Slice(fields, func(i, j int) bool {
		// 排序规则为modify_time排最后，其余字段按fieldID大小排序
		fieldI := fields[i].GetFieldInfo()
		if fieldI.GetFieldName() == mtimeName {
			return false
		}

		fieldJ := fields[j].GetFieldInfo()
		if fieldJ.GetFieldName() == mtimeName {
			return true
		}
		return fieldI.GetFieldId() < fieldJ.GetFieldId()
	})
}

// needAddModifyInfo 判断是否需要添加变更消息
func (m *MysqlObj) needAddModifyInfo(modifyInfo *protocol.ModifyFieldInfo) bool {
	if modifyInfo == nil {
		return false
	}
	// 由于modify_time字段永远排在最后，因此可以这样判断是否只有modify_time发生变更
	if modifyInfo.FieldName == mtimeName && len(m.infos) == 0 {
		// 仅有modify_time字段变更时不需要更新和添加到变更消息中
		return false
	}
	return true
}

// BuildUpdateSql 生成更新字段sql
func (m *MysqlObj) BuildUpdateSql(id string, fields []*protocol.UpdateFieldInfo,
	failList map[string]protocol.EnumMediaErrorCode,
) (string, []interface{}) {
	// 对更新请求的字段排序，防止并发更新出现死锁
	sortUpdateFields(fields)

	// 每次生成sql需要新申请参数对象
	para := &parameter{id: id, versionFID: m.VersionFieldID}
	for _, f := range fields {
		curVal, _ := m.CurVal(f.FieldInfo)
		val, modifyInfo, err := logic.BuildInsertValue(f, curVal)
		if err != nil {
			failList[f.FieldInfo.FieldName] = protocol.EnumMediaErrorCode_RetInvalidOpType
			continue
		}

		if m.needAddModifyInfo(modifyInfo) {
			// 添加每个字段的变更消息，可能存在多次构造sql的场景，因此采用map新值覆盖旧值
			m.infos[modifyInfo.FieldId] = modifyInfo
			// 发生变更的字段添加到批量更新参数里进行更新
			m.AddIns(f.FieldInfo.FieldId, val, para)
		}
	}
	return m.UpdateSQL(m.DBName, m.TBName, para)
}

func (m *MysqlObj) handleForNoRows(id string, failList map[string]protocol.EnumMediaErrorCode) error {
	if err := m.updateDB(nil, func() (string, []interface{}) {
		return m.BuildInsertSQL(id, m.lock)
	}); err != nil {
		// 执行insert失败(主键冲突/执行失败)直接退出
		return err
	}

	// 执行insert成功需要补充更新未上锁字段
	return m.updateDB(nil, func() (string, []interface{}) {
		return m.BuildUpdateSql(id, m.unlock, failList)
	})
}

// GetAndUpdateFieldInfo 对字段加行锁，更新全部字段值
func (m *MysqlObj) GetAndUpdateFieldInfo(id string, failList map[string]protocol.EnumMediaErrorCode) error {
	sqlFunc := func() (string, []interface{}) {
		// 创建sql语句function
		return m.BuildUpdateSql(id, append(m.unlock, m.lock...), failList)
	}
	query, args := m.QueryFromUpdate(m.DBName, m.TBName, m.ForceIndex, id, m.lock)
	if query == "" {
		// 没有需要加锁的字段，直接更新全部字段
		return m.updateDB(nil, sqlFunc)
	}

	var tErr error
	for i := 0; i < 2; i++ {
		rowNum := 0
		// 加锁的场景需要开启事务
		tErr = m.db.Transaction(m.Ctx, func(tx *sql.Tx) error {
			// 事务执行逻辑，先上锁再更新全部字段
			rows, err := tx.Query(query+" for update", args...)
			if err != nil {
				log.Errorf("Fail to query for update, sql is %s, err is %v.", query, err)
				return err
			}
			rowNum = scanRows(rows, m.Dao)
			if rowNum == 0 {
				return nil
			}
			// 查询不为空，加锁成功直接更新unlock&lock fields
			return m.updateDB(tx, sqlFunc)
		})
		if rowNum == 0 {
			// 查询结果为空，for update无法锁住更新操作，需要提交事务并使用insert语句进行更新操作
			// todo:在insert语句中支持增加更新entity_version，需要解决entity_version有值导致insert返回DupEntryError出现死循环问题
			log.Debugf("no rows of query, sql is %s.", query)
			tErr = m.handleForNoRows(id, failList)
		}
		if !mysql.IsDupEntryError(tErr) {
			// 执行事务成功或者非主键冲突错误直接退出循环
			break
		}
		// 如果执行结果为主键冲突则重新执行加锁更新操作
	}
	return tErr
}

func newFailListByFailQuery(ids []string, errMsg string) map[string]string {
	failList := make(map[string]string)
	for _, id := range ids {
		failList[id] = errMsg
	}
	return failList
}

// BatchGetFields 统一批量获取接口
func (m *MysqlObj) BatchGetFields(datasetID int32, ids, fields []string,
	extra map[string][]string,
) (*model.BatchGetResponse, error) {
	fieldKey := inf.NewFieldKey(datasetID, fields, extra)
	hit := make(map[string]int)
	var bRsp model.BatchGetResponse
	for _, ex := range m.QueryFromGet(m.DBName, m.TBName, m.ForceIndex, ids, fieldKey) {
		query, args := ex.sql, ex.args
		if err := m.db.Query(m.Ctx, func(rows *sql.Rows) error {
			val, err := m.FillGetField(rows)
			if err != nil {
				return nil
			}

			if f := cache.GetFieldInfoByID(val.FieldId, datasetID); f != nil {
				i, ok := hit[val.Id]
				if ok {
					tool.FillDocInfo(bRsp.DocInfos[i], f, val, err)
					return nil
				}
				hit[val.Id] = len(bRsp.DocInfos)
				bRsp.DocInfos = append(bRsp.DocInfos, tool.NewDocInfo(f, val, err))
			}
			return nil
		}, query, args...); err != nil {
			log.Errorf("Fail to query sql is %s, args is %v, err is %v.", query, args, err)
			// 只有执行sql失败时才会返回err，此时可以认为该id是完全查询失败的
			bRsp.FailList = newFailListByFailQuery(ids, "fail to query, err is "+err.Error())
			return nil, err
		}
		log.Debugf("GetFieldInfos is %+v, query is %s, args is %v.", bRsp, query, args)
	}
	return &bRsp, nil
}

// SetFieldInfos mysql数据源set接口
func (m *MysqlObj) SetFieldInfos(id string, baseFieldIDs []*protocol.FieldInfo, fieldList []*protocol.UpdateFieldInfo,
	rsp *adaptor.SetFieldInfosResponse,
) (*model.ModifyInfo, error) {
	m.infos = make(map[uint32]*protocol.ModifyFieldInfo)
	// 存储旧的结果 key:fieldInfoId value:oldVal
	m.unlock, m.lock = logic.ClassifyFieldsByType(fieldList)
	log.Debugf("unlockList is %+v, lockList is %+v.", m.unlock, m.lock)

	// 先查询不带锁的字段原值
	if err := m.GetUnlockFieldInfo(id, baseFieldIDs); err != nil {
		logic.BatchHandleFailList(m.unlock, rsp.RetInfo.FailList, protocol.EnumMediaErrorCode_RetFailSelect)
		// 获取不加锁字段旧值失败，置空unLockFields
		m.unlock = nil
	}

	// 然后查询并更新全部字段值
	fields := append(m.unlock, m.lock...)
	err := m.GetAndUpdateFieldInfo(id, rsp.RetInfo.FailList)
	if err != nil {
		log.Errorf("Fail to get and update fields, err is %v.", err)
		logic.BatchHandleFailList(fields, rsp.RetInfo.FailList, protocol.EnumMediaErrorCode_RetFailUpdate)
		var modifyInfo *model.ModifyInfo
		if isUpdateTimeout(err) {
			// 只有更新超时的场景需要返回modifyInfo不为空且err不为空，以触发检查补发消息流程
			modifyInfo = model.BuildModifyInfo(baseFieldIDs, m.infos, m.Dao)
		}
		return modifyInfo, err
	}
	return model.BuildModifyInfo(baseFieldIDs, m.infos, m.Dao), nil
}

// GetFieldInfos mysql get接口 todo:待废弃
func (m *MysqlObj) GetFieldInfos(ids []string, fieldKey *inf.FieldKey, rsp *adaptor.GetFieldInfosResponse) error {
	for _, ex := range m.QueryFromGet(m.DBName, m.TBName, m.ForceIndex, ids, fieldKey) {
		query, args := ex.sql, ex.args
		if err := m.db.Query(m.Ctx, func(rows *sql.Rows) error {
			fieldInfo, err := m.FillGetField(rows)
			if err != nil {
				return err
			}
			rsp.FieldInfos = append(rsp.FieldInfos, fieldInfo)
			return nil
		}, query, args...); err != nil {
			log.Errorf("Fail to query sql:%s, err is:%v.", query, err)
			logic.HandleAllFailList(fieldKey.GetFieldIDs(), rsp.FailList, protocol.EnumMediaErrorCode_RetFailSelect)
			return err
		}
		log.Debugf("GetFieldInfos is %+v, query is %s, args is %v.", rsp.FieldInfos, query, args)
	}
	return nil
}

// InsertFieldInfos mysql插入数据接口（version先不使用）
func (m *MysqlObj) InsertFieldInfos(id string, _ int64, fields []*protocol.UpdateFieldInfo) (*model.ModifyInfo, error) {
	result, err := m.updateDBWithResult(nil, func() (string, []interface{}) {
		return m.BuildInsertIgnoreSQL(id, fields)
	})
	if err != nil {
		log.Errorf("Fail to insert db, err is %v.", err)
		return nil, err
	}
	if result == nil {
		// 未执行插入sql，直接跳过
		return nil, nil
	}

	var modifyInfo *model.ModifyInfo
	// 根据影响row行数来填充response
	if rowAffect, _ := result.RowsAffected(); rowAffect > 0 {
		// insert有数据变更，则生成所有相关字段的变更通知，字段旧值为空
		modifyInfo = model.BuildModifyInfo(nil, m.infos, m.Dao)
	}
	return modifyInfo, nil
}
