package mysql

import (
	"fmt"
	"testing"

	"strings"

	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	tool "git.code.oa.com/video_media/storage_service/common"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

// TestAddIns 测试AddIns方法
func TestAddIns(t *testing.T) {
	tests := []struct {
		name           string
		fieldID        uint32
		val            *tool.FieldVal
		para           *parameter
		expectedArgs   []interface{}
		expectedLength int
	}{
		{
			name:    "添加单个键值对",
			fieldID: 1001,
			val: &tool.FieldVal{
				MapVal: map[string]string{
					"key1": "value1",
				},
			},
			para: &parameter{
				id:   "test_id",
				args: []interface{}{},
			},
			expectedArgs:   []interface{}{"test_id", uint32(1001), "key1", "value1"},
			expectedLength: 4,
		},
		{
			name:    "添加多个键值对",
			fieldID: 1002,
			val: &tool.FieldVal{
				MapVal: map[string]string{
					"key1": "value1",
					"key2": "value2",
					"key3": "value3",
				},
			},
			para: &parameter{
				id:   "test_id",
				args: []interface{}{},
			},
			expectedLength: 12, // 3个键值对 * 4个参数
		},
		{
			name:    "空的MapVal",
			fieldID: 1003,
			val: &tool.FieldVal{
				MapVal: map[string]string{},
			},
			para: &parameter{
				id:   "test_id",
				args: []interface{}{},
			},
			expectedArgs:   []interface{}{},
			expectedLength: 0,
		},
		{
			name:    "已有参数的情况下添加",
			fieldID: 1004,
			val: &tool.FieldVal{
				MapVal: map[string]string{
					"new_key": "new_value",
				},
			},
			para: &parameter{
				id:   "test_id",
				args: []interface{}{"existing", "params"},
			},
			expectedLength: 6, // 2个已有参数 + 4个新参数
		},
		{
			name:    "nil val",
			fieldID: 1005,
			val:     nil,
			para: &parameter{
				id:   "test_id",
				args: []interface{}{},
			},
			expectedLength: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mapType := NewMapType()

			// 防止panic
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("AddIns panicked: %v", r)
				}
			}()

			if tt.val != nil {
				mapType.AddIns(tt.fieldID, tt.val, tt.para)
			}

			assert.Equal(t, tt.expectedLength, len(tt.para.args))

			if tt.name == "添加单个键值对" {
				assert.Equal(t, tt.expectedArgs, tt.para.args)
			}

			if tt.name == "添加多个键值对" {
				// 验证参数格式正确（每4个为一组）
				assert.Equal(t, 0, len(tt.para.args)%4)
				// 验证每组的第一个参数都是id
				for i := 0; i < len(tt.para.args); i += 4 {
					assert.Equal(t, "test_id", tt.para.args[i])
					assert.Equal(t, tt.fieldID, tt.para.args[i+1])
				}
			}
		})
	}
}

// TestInsertSQL 测试InsertSQL方法
func TestInsertSQL(t *testing.T) {
	tests := []struct {
		name           string
		dbName         string
		tbName         string
		ignore         string
		para           *parameter
		expectedSQL    string
		expectedArgs   []interface{}
		expectedReturn bool
	}{
		{
			name:   "单个插入",
			dbName: "test_db",
			tbName: "test_table",
			ignore: "",
			para: &parameter{
				args: []interface{}{"id1", uint32(1001), "key1", "value1"},
			},
			expectedSQL:    "insert  into test_db.test_table (c_id,c_field_id,c_field_key,c_field_value) values (?,?,?,?)",
			expectedArgs:   []interface{}{"id1", uint32(1001), "key1", "value1"},
			expectedReturn: true,
		},
		{
			name:   "多个插入",
			dbName: "test_db",
			tbName: "test_table",
			ignore: "IGNORE",
			para: &parameter{
				args: []interface{}{
					"id1", uint32(1001), "key1", "value1",
					"id1", uint32(1001), "key2", "value2",
				},
			},
			expectedSQL:    "insert IGNORE into test_db.test_table (c_id,c_field_id,c_field_key,c_field_value) values (?,?,?,?),(?,?,?,?)",
			expectedArgs:   []interface{}{"id1", uint32(1001), "key1", "value1", "id1", uint32(1001), "key2", "value2"},
			expectedReturn: true,
		},
		{
			name:   "空参数",
			dbName: "test_db",
			tbName: "test_table",
			ignore: "",
			para: &parameter{
				args: []interface{}{},
			},
			expectedSQL:    "",
			expectedArgs:   nil,
			expectedReturn: false,
		},
		{
			name:   "参数数量不是4的倍数",
			dbName: "test_db",
			tbName: "test_table",
			ignore: "",
			para: &parameter{
				args: []interface{}{"id1", uint32(1001), "key1"}, // 只有3个参数
			},
			expectedSQL:    "",
			expectedArgs:   nil,
			expectedReturn: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mapType := NewMapType()
			sql, args := mapType.InsertSQL(tt.dbName, tt.tbName, tt.ignore, tt.para)

			if tt.expectedReturn {
				assert.Equal(t, tt.expectedSQL, sql)
				assert.Equal(t, tt.expectedArgs, args)
			} else {
				assert.Empty(t, sql)
				assert.Nil(t, args)
			}
		})
	}
}

// TestUpdateSQL 测试UpdateSQL方法
func TestUpdateSQL(t *testing.T) {
	tests := []struct {
		name           string
		dbName         string
		tbName         string
		para           *parameter
		expectedSQL    string
		expectedArgs   []interface{}
		expectedReturn bool
	}{
		{
			name:   "单个更新",
			dbName: "test_db",
			tbName: "test_table",
			para: &parameter{
				args: []interface{}{"id1", uint32(1001), "key1", "value1"},
			},
			expectedSQL: `insert into
  test_db.test_table (c_id, c_field_id, c_field_key, c_field_value)
values
  (?,?,?,?) ON DUPLICATE KEY
UPDATE
  c_field_value =
values(c_field_value)`,
			expectedArgs:   []interface{}{"id1", uint32(1001), "key1", "value1"},
			expectedReturn: true,
		},
		{
			name:   "多个更新",
			dbName: "test_db",
			tbName: "test_table",
			para: &parameter{
				args: []interface{}{
					"id1", uint32(1001), "key1", "value1",
					"id1", uint32(1002), "key2", "value2",
				},
			},
			expectedSQL: `insert into
  test_db.test_table (c_id, c_field_id, c_field_key, c_field_value)
values
  (?,?,?,?),(?,?,?,?) ON DUPLICATE KEY
UPDATE
  c_field_value =
values(c_field_value)`,
			expectedArgs:   []interface{}{"id1", uint32(1001), "key1", "value1", "id1", uint32(1002), "key2", "value2"},
			expectedReturn: true,
		},
		{
			name:   "空参数",
			dbName: "test_db",
			tbName: "test_table",
			para: &parameter{
				args: []interface{}{},
			},
			expectedSQL:    "",
			expectedArgs:   nil,
			expectedReturn: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mapType := NewMapType()
			sql, args := mapType.UpdateSQL(tt.dbName, tt.tbName, tt.para)

			if tt.expectedReturn {
				assert.Equal(t, tt.expectedSQL, sql)
				assert.Equal(t, tt.expectedArgs, args)
			} else {
				assert.Empty(t, sql)
				assert.Nil(t, args)
			}
		})
	}
}

// TestQueryFromUpdate 测试QueryFromUpdate方法
func TestQueryFromUpdate(t *testing.T) {
	tests := []struct {
		name      string
		dbName    string
		tbName    string
		index     string
		id        string
		fields    []*protocol.UpdateFieldInfo
		wantSQL   string
		checkArgs bool
	}{
		{
			name:   "单个字段单个键",
			dbName: "testDB",
			tbName: "testTB",
			index:  "",
			id:     "testVid",
			fields: []*protocol.UpdateFieldInfo{{
				FieldInfo: &protocol.FieldInfo{
					FieldName: "testMapField",
					FieldId:   1001,
					FieldType: protocol.EnumFieldType_FieldTypeMapKV,
					MapVal: map[string]*protocol.MapValue{
						"key1": {
							Type:     protocol.EnumFieldType_FieldTypeStr,
							StrValue: "value1",
						},
					},
				},
				UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
			}},
			wantSQL:   fmt.Sprintf(SQLSelectMap, "testDB", "testTB", "", "(?,?)"),
			checkArgs: true,
		},
		{
			name:   "单个字段多个键",
			dbName: "testDB",
			tbName: "testTB",
			index:  "force index(idx_test)",
			id:     "testVid",
			fields: []*protocol.UpdateFieldInfo{{
				FieldInfo: &protocol.FieldInfo{
					FieldName: "testMapField",
					FieldId:   1001,
					FieldType: protocol.EnumFieldType_FieldTypeMapKV,
					MapVal: map[string]*protocol.MapValue{
						"key1": {Type: protocol.EnumFieldType_FieldTypeStr, StrValue: "value1"},
						"key2": {Type: protocol.EnumFieldType_FieldTypeStr, StrValue: "value2"},
					},
				},
				UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
			}},
			wantSQL:   fmt.Sprintf(SQLSelectMap, "testDB", "testTB", "force index(idx_test)", "(?,?),(?,?)"),
			checkArgs: true,
		},
		{
			name:   "多个字段",
			dbName: "testDB",
			tbName: "testTB",
			index:  "",
			id:     "testVid",
			fields: []*protocol.UpdateFieldInfo{
				{
					FieldInfo: &protocol.FieldInfo{
						FieldId: 1001,
						MapVal: map[string]*protocol.MapValue{
							"key1": {Type: protocol.EnumFieldType_FieldTypeStr, StrValue: "value1"},
						},
					},
				},
				{
					FieldInfo: &protocol.FieldInfo{
						FieldId: 1002,
						MapVal: map[string]*protocol.MapValue{
							"key2": {Type: protocol.EnumFieldType_FieldTypeStr, StrValue: "value2"},
						},
					},
				},
			},
			wantSQL:   fmt.Sprintf(SQLSelectMap, "testDB", "testTB", "", "(?,?),(?,?)"),
			checkArgs: true,
		},
		{
			name:      "空字段列表",
			dbName:    "testDB",
			tbName:    "testTB",
			index:     "",
			id:        "testVid",
			fields:    []*protocol.UpdateFieldInfo{},
			wantSQL:   "",
			checkArgs: false,
		},
		{
			name:   "字段无MapVal",
			dbName: "testDB",
			tbName: "testTB",
			index:  "",
			id:     "testVid",
			fields: []*protocol.UpdateFieldInfo{{
				FieldInfo: &protocol.FieldInfo{
					FieldId: 1001,
					MapVal:  map[string]*protocol.MapValue{},
				},
			}},
			wantSQL:   "",
			checkArgs: false,
		},
		{
			name:   "nil FieldInfo",
			dbName: "testDB",
			tbName: "testTB",
			index:  "",
			id:     "testVid",
			fields: []*protocol.UpdateFieldInfo{{
				FieldInfo: nil,
			}},
			wantSQL:   "",
			checkArgs: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewMapType()

			// 防止panic
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("QueryFromUpdate panicked: %v", r)
				}
			}()

			gotSQL, gotArgs := m.QueryFromUpdate(tt.dbName, tt.tbName, tt.index, tt.id, tt.fields)

			assert.Equal(t, tt.wantSQL, gotSQL)

			if tt.checkArgs {
				assert.NotNil(t, gotArgs)
				assert.Equal(t, tt.id, gotArgs[0]) // 第一个参数应该是id
				assert.True(t, len(gotArgs) > 1)   // 应该有更多参数
			} else {
				assert.Nil(t, gotArgs)
			}
		})
	}
}

// TestQueryFromGet 测试QueryFromGet方法
func TestQueryFromGet(t *testing.T) {
	tests := []struct {
		name             string
		dbName           string
		tbName           string
		index            string
		ids              []string
		key              *inf.FieldKey
		expectedCount    int
		expectedContains []string
	}{
		{
			name:   "通过FieldIDs查询-全量查询",
			dbName: "test_db",
			tbName: "test_table",
			index:  "",
			ids:    []string{"id1"},
			key: &inf.FieldKey{
				FieldIDs: []uint32{0}, // 0表示全量查询
			},
			expectedCount:    1,
			expectedContains: []string{fmt.Sprintf(SQLSelectAllMap, "test_db", "test_table", "")},
		},
		{
			name:   "通过FieldIDs查询-指定字段",
			dbName: "test_db",
			tbName: "test_table",
			index:  "",
			ids:    []string{"id1"},
			key: &inf.FieldKey{
				FieldIDs: []uint32{1001, 1002},
			},
			expectedCount: 1,
		},
		{
			name:   "通过FieldKeys查询",
			dbName: "test_db",
			tbName: "test_table",
			index:  "",
			ids:    []string{"id1"},
			key: &inf.FieldKey{
				FieldKeys: map[uint32]*adaptor.FieldKey{
					1001: {Keys: []string{"key1", "key2"}},
					1002: {Keys: []string{"key3"}},
				},
			},
			expectedCount: 1,
		},
		{
			name:   "同时有FieldIDs和FieldKeys",
			dbName: "test_db",
			tbName: "test_table",
			index:  "",
			ids:    []string{"id1"},
			key: &inf.FieldKey{
				FieldIDs: []uint32{1001},
				FieldKeys: map[uint32]*adaptor.FieldKey{
					1002: {Keys: []string{"key1"}},
				},
			},
			expectedCount: 2, // 应该返回两个executor
		},
		{
			name:   "空的查询条件",
			dbName: "test_db",
			tbName: "test_table",
			index:  "",
			ids:    []string{"id1"},
			key: &inf.FieldKey{
				FieldIDs:  []uint32{},
				FieldKeys: map[uint32]*adaptor.FieldKey{},
			},
			expectedCount: 0,
		},
		{
			name:   "FieldKeys为空Keys",
			dbName: "test_db",
			tbName: "test_table",
			index:  "",
			ids:    []string{"id1"},
			key: &inf.FieldKey{
				FieldKeys: map[uint32]*adaptor.FieldKey{
					1001: {Keys: []string{}},
				},
			},
			expectedCount: 0,
		},
		{
			name:   "空的ids列表",
			dbName: "test_db",
			tbName: "test_table",
			index:  "",
			ids:    []string{},
			key: &inf.FieldKey{
				FieldIDs: []uint32{1001},
			},
			expectedCount: 0, // 没有id应该返回空结果
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewMapType()

			// 防止panic
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("QueryFromGet panicked: %v", r)
				}
			}()

			var executors []*sqlExecutor
			if len(tt.ids) > 0 {
				executors = m.QueryFromGet(tt.dbName, tt.tbName, tt.index, tt.ids, tt.key)
			}

			assert.Equal(t, tt.expectedCount, len(executors))

			if tt.expectedCount > 0 {
				for _, executor := range executors {
					assert.NotEmpty(t, executor.sql)
					assert.NotEmpty(t, executor.args)
					if len(tt.ids) > 0 {
						assert.Equal(t, tt.ids[0], executor.args[0]) // 第一个参数应该是id
					}
				}

				if len(tt.expectedContains) > 0 {
					found := false
					for _, expected := range tt.expectedContains {
						for _, executor := range executors {
							if executor.sql == expected {
								found = true
								break
							}
						}
					}
					assert.True(t, found, "Expected SQL not found in executors")
				}
			}
		})
	}
}

// TestCurVal 测试CurVal方法
func TestCurVal(t *testing.T) {
	tests := []struct {
		name           string
		cur            map[uint32]map[string]string
		field          *protocol.FieldInfo
		expectedVal    *tool.FieldVal
		expectedExists bool
	}{
		{
			name: "字段存在",
			cur: map[uint32]map[string]string{
				1001: {"key1": "value1", "key2": "value2"},
			},
			field: &protocol.FieldInfo{
				FieldId: 1001,
			},
			expectedVal: &tool.FieldVal{
				MapVal: map[string]string{"key1": "value1", "key2": "value2"},
			},
			expectedExists: true,
		},
		{
			name: "字段不存在",
			cur: map[uint32]map[string]string{
				1001: {"key1": "value1"},
			},
			field: &protocol.FieldInfo{
				FieldId: 1002,
			},
			expectedVal: &tool.FieldVal{
				MapVal: nil,
			},
			expectedExists: false,
		},
		{
			name: "空的cur",
			cur:  map[uint32]map[string]string{},
			field: &protocol.FieldInfo{
				FieldId: 1001,
			},
			expectedVal: &tool.FieldVal{
				MapVal: nil,
			},
			expectedExists: false,
		},
		{
			name: "nil field",
			cur: map[uint32]map[string]string{
				1001: {"key1": "value1"},
			},
			field: nil,
			expectedVal: &tool.FieldVal{
				MapVal: nil,
			},
			expectedExists: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mapType := &MapType{cur: tt.cur}

			// 防止panic
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("CurVal panicked: %v", r)
				}
			}()

			var val *tool.FieldVal
			var exists bool

			if tt.field != nil {
				val, exists = mapType.CurVal(tt.field)
			} else {
				val = &tool.FieldVal{MapVal: nil}
				exists = false
			}

			assert.Equal(t, tt.expectedExists, exists)
			assert.Equal(t, tt.expectedVal, val)
		})
	}
}

// TestNewMapType 测试NewMapType构造函数
func TestNewMapType(t *testing.T) {
	mapType := NewMapType()
	assert.NotNil(t, mapType)
	assert.NotNil(t, mapType.cur)
	assert.Equal(t, 0, len(mapType.cur))
}

// TestFillGetField 测试FillGetField方法
func TestFillGetField(t *testing.T) {
	tests := []struct {
		name          string
		setupMockRows func(sqlmock.Sqlmock)
		expectedField *adaptor.GetFieldInfo
		expectedError bool
	}{
		{
			name: "成功填充普通map字段",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("test_id_001", uint32(2001), "key1", "value1", "2023-01-01 12:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "test_id_001",
				FieldId:    2001,
				FieldKey:   "key1",
				FieldValue: "value1",
				Mtime:      "2023-01-01 12:00:00",
			},
			expectedError: false,
		},
		{
			name: "空key值处理",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("empty_key_test", uint32(3001), "", "empty_key_value", "2023-01-01 14:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "empty_key_test",
				FieldId:    3001,
				FieldKey:   "",
				FieldValue: "empty_key_value",
				Mtime:      "2023-01-01 14:00:00",
			},
			expectedError: false,
		},
		{
			name: "空value值处理",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("empty_value_test", uint32(4001), "test_key", "", "2023-01-01 15:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "empty_value_test",
				FieldId:    4001,
				FieldKey:   "test_key",
				FieldValue: "",
				Mtime:      "2023-01-01 15:00:00",
			},
			expectedError: false,
		},
		{
			name: "包含特殊字符的key和value",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("special_test", uint32(5001), "key@#$", "测试值with特殊字符!@#", "2023-01-01 16:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "special_test",
				FieldId:    5001,
				FieldKey:   "key@#$",
				FieldValue: "测试值with特殊字符!@#",
				Mtime:      "2023-01-01 16:00:00",
			},
			expectedError: false,
		},
		{
			name: "长key和value处理",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				longKey := strings.Repeat("longKey", 50)
				longValue := strings.Repeat("长文本值", 100)
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("long_test", uint32(6001), longKey, longValue, "2023-01-01 17:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedField: &adaptor.GetFieldInfo{
				Id:         "long_test",
				FieldId:    6001,
				FieldKey:   strings.Repeat("longKey", 50),
				FieldValue: strings.Repeat("长文本值", 100),
				Mtime:      "2023-01-01 17:00:00",
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock数据库
			db, mock, err := sqlmock.New()
			assert.NoError(t, err)
			defer db.Close()

			// 设置mock数据
			tt.setupMockRows(mock)

			// 执行查询
			rows, err := db.Query("SELECT c_id, c_field_id, c_field_key, c_field_value, c_mtime FROM test_table")
			assert.NoError(t, err)
			defer rows.Close()

			mapType := NewMapType()
			assert.NotNil(t, mapType)

			// 处理第一行数据
			if rows.Next() {
				field, err := mapType.FillGetField(rows)

				if tt.expectedError {
					assert.Error(t, err)
					assert.Nil(t, field)
				} else {
					assert.NoError(t, err)
					assert.NotNil(t, field)

					// 验证字段内容
					assert.Equal(t, tt.expectedField.Id, field.Id)
					assert.Equal(t, tt.expectedField.FieldId, field.FieldId)
					assert.Equal(t, tt.expectedField.FieldKey, field.FieldKey)
					assert.Equal(t, tt.expectedField.FieldValue, field.FieldValue)
					assert.Equal(t, tt.expectedField.Mtime, field.Mtime)
				}
			}

			// 验证所有预期的mock调用都被执行
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestFillData 测试FillData方法
func TestFillData(t *testing.T) {
	tests := []struct {
		name          string
		setupMockRows func(sqlmock.Sqlmock)
		expectedCur   map[uint32]map[string]string
		expectedError bool
	}{
		{
			name: "成功填充单个map字段",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("test_id", uint32(1001), "key1", "value1", "2023-01-01 00:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]map[string]string{
				1001: {
					"key1": "value1",
				},
			},
			expectedError: false,
		},
		{
			name: "填充多个key到同一个字段",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("test_id", uint32(1001), "key1", "value1", "2023-01-01 00:00:00").
					AddRow("test_id", uint32(1001), "key2", "value2", "2023-01-01 00:00:01").
					AddRow("test_id", uint32(1001), "key3", "value3", "2023-01-01 00:00:02")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]map[string]string{
				1001: {
					"key1": "value1",
					"key2": "value2",
					"key3": "value3",
				},
			},
			expectedError: false,
		},
		{
			name: "填充多个不同字段",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("test_id", uint32(1001), "key1", "value1", "2023-01-01 00:00:00").
					AddRow("test_id", uint32(1002), "keyA", "valueA", "2023-01-01 00:00:01").
					AddRow("test_id", uint32(1003), "keyX", "valueX", "2023-01-01 00:00:02")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]map[string]string{
				1001: {"key1": "value1"},
				1002: {"keyA": "valueA"},
				1003: {"keyX": "valueX"},
			},
			expectedError: false,
		},
		{
			name: "空key和空value处理",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("test_id", uint32(1001), "", "", "2023-01-01 00:00:00").
					AddRow("test_id", uint32(1001), "empty_value", "", "2023-01-01 00:00:01").
					AddRow("test_id", uint32(1001), "", "empty_key", "2023-01-01 00:00:02")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]map[string]string{
				1001: {
					"":            "empty_key", // 后面的覆盖前面的空key
					"empty_value": "",
				},
			},
			expectedError: false,
		},
		{
			name: "特殊字符处理",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("test_id", uint32(1001), "key@#$", "测试值!@#", "2023-01-01 00:00:00").
					AddRow("test_id", uint32(1001), "中文key", "中文value", "2023-01-01 00:00:01")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]map[string]string{
				1001: {
					"key@#$": "测试值!@#",
					"中文key":  "中文value",
				},
			},
			expectedError: false,
		},
		{
			name: "重复key覆盖测试",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("test_id", uint32(1001), "same_key", "old_value", "2023-01-01 00:00:00").
					AddRow("test_id", uint32(1001), "same_key", "new_value", "2023-01-01 00:00:01")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedCur: map[uint32]map[string]string{
				1001: {
					"same_key": "new_value", // 后面的值覆盖前面的值
				},
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock数据库
			db, mock, err := sqlmock.New()
			assert.NoError(t, err)
			defer db.Close()

			// 设置mock数据
			tt.setupMockRows(mock)

			// 执行查询
			rows, err := db.Query("SELECT c_id, c_field_id, c_field_key, c_field_value, c_mtime FROM test_table")
			assert.NoError(t, err)
			defer rows.Close()

			mapType := NewMapType()
			assert.NotNil(t, mapType)

			// 处理每一行数据
			for rows.Next() {
				err := mapType.FillData(rows)
				if tt.expectedError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
				}
			}

			// 验证填充结果
			if !tt.expectedError {
				assert.Equal(t, len(tt.expectedCur), len(mapType.cur))
				for fieldID, expectedMap := range tt.expectedCur {
					actualMap, exists := mapType.cur[fieldID]
					assert.True(t, exists, "字段ID %d 应该存在", fieldID)
					assert.Equal(t, len(expectedMap), len(actualMap), "字段ID %d 的map长度不匹配", fieldID)

					for key, expectedValue := range expectedMap {
						actualValue, keyExists := actualMap[key]
						assert.True(t, keyExists, "字段ID %d 的key '%s' 应该存在", fieldID, key)
						assert.Equal(t, expectedValue, actualValue, "字段ID %d key '%s' 的值不匹配", fieldID, key)
					}
				}
			}

			// 验证所有预期的mock调用都被执行
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestMapTypeFillDataErrorScenarios 测试FillData的错误场景
func TestMapTypeFillDataErrorScenarios(t *testing.T) {
	tests := []struct {
		name          string
		setupMockRows func(sqlmock.Sqlmock)
		expectedError bool
	}{
		{
			name: "Scan错误 - 类型不匹配",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				// 故意设置错误的数据类型来触发Scan错误
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("test_id", "invalid_uint32", "key1", "value1", "2023-01-01 00:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedError: true,
		},
		{
			name: "Scan错误 - 字段数不匹配",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				// 只提供4个字段，但期望5个字段
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value"}).
					AddRow("test_id", uint32(1001), "key1", "value1")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock数据库
			db, mock, err := sqlmock.New()
			assert.NoError(t, err)
			defer db.Close()

			// 设置mock数据
			tt.setupMockRows(mock)

			// 执行查询
			rows, err := db.Query("SELECT c_id, c_field_id, c_field_key, c_field_value, c_mtime FROM test_table")
			assert.NoError(t, err)
			defer rows.Close()

			mapType := NewMapType()

			if rows.Next() {
				err := mapType.FillData(rows)
				if tt.expectedError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
				}
			}

			// 验证所有预期的mock调用都被执行
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TestMapTypeFillGetFieldErrorScenarios 测试FillGetField的错误场景
func TestMapTypeFillGetFieldErrorScenarios(t *testing.T) {
	tests := []struct {
		name          string
		setupMockRows func(sqlmock.Sqlmock)
		expectedError bool
	}{
		{
			name: "Scan错误 - 类型不匹配",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				// 故意设置错误的数据类型来触发Scan错误
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key", "c_field_value", "c_mtime"}).
					AddRow("test_id", "invalid_uint32", "key1", "value1", "2023-01-01 00:00:00")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedError: true,
		},
		{
			name: "Scan错误 - 字段数不匹配",
			setupMockRows: func(mock sqlmock.Sqlmock) {
				// 只提供3个字段，但期望5个字段
				rows := mock.NewRows([]string{"c_id", "c_field_id", "c_field_key"}).
					AddRow("test_id", uint32(1001), "key1")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock数据库
			db, mock, err := sqlmock.New()
			assert.NoError(t, err)
			defer db.Close()

			// 设置mock数据
			tt.setupMockRows(mock)

			// 执行查询
			rows, err := db.Query("SELECT c_id, c_field_id, c_field_key, c_field_value, c_mtime FROM test_table")
			assert.NoError(t, err)
			defer rows.Close()

			mapType := NewMapType()

			if rows.Next() {
				field, err := mapType.FillGetField(rows)
				if tt.expectedError {
					assert.Error(t, err)
					assert.Nil(t, field)
				} else {
					assert.NoError(t, err)
					assert.NotNil(t, field)
				}
			}

			// 验证所有预期的mock调用都被执行
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
