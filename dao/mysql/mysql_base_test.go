package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/mysql"
	"git.code.oa.com/trpc-go/trpc-database/mysql/mockmysql"
	"git.code.oa.com/trpc-go/trpc-go/client"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"

	"github.com/golang/mock/gomock"
	"github.com/prashantv/gostub"
)

func initMock(proxy *mockmysql.MockClient) {
	proxy.EXPECT().Query(gomock.Any(), gomock.Any(), fmt.Sprintf(SQLSelect, "DB1", "TB1", "", "?", "?"),
		gomock.Any(), gomock.Any()).Do(func(ctx context.Context, next mysql.NextFunc,
		query string, args ...interface{},
	) error {
		return next(&sql.Rows{})
	}).AnyTimes()
	proxy.EXPECT().Query(gomock.Any(), gomock.Any(), fmt.Sprintf(SQLSelectMap, "DB1", "TB1", "", "(?,?)"),
		gomock.Any(), gomock.Any(), gomock.Any()).Do(
		func(ctx context.Context, next mysql.NextFunc, query string, args ...interface{}) error {
			return next(&sql.Rows{})
		}).AnyTimes()
	proxy.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil)
	proxy.EXPECT().Exec(gomock.Any(), fmt.Sprintf(SQLInsert, "", "DB1", "TB1", "(?,?,?)"), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil, nil).AnyTimes()
	proxy.EXPECT().Exec(gomock.Any(), fmt.Sprintf(SQLUpdate, "DB1", "TB1", "(?,?,?)"), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	proxy.EXPECT().Exec(gomock.Any(), fmt.Sprintf(SQLUpdateMap, "DB1", "TB1", "(?,?,?,?)"), gomock.Any(), gomock.Any(),
		gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
}

func TestMysqlObj_SetFieldInfos(t *testing.T) {
	type fields struct {
		baseInfo baseInfo
		Dao      Dao
		db       mysql.Client
		unlock   []*protocol.UpdateFieldInfo
		lock     []*protocol.UpdateFieldInfo
		infos    map[uint32]*protocol.ModifyFieldInfo
	}
	type args struct {
		id           string
		baseFieldIDs []*protocol.FieldInfo
		fieldList    []*protocol.UpdateFieldInfo
		rsp          *adaptor.SetFieldInfosResponse
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// mock mysql proxy
	proxy := mockmysql.NewMockClient(ctrl)
	stub := gostub.Stub(&mysql.NewClientProxy, func(name string, opts ...client.Option) mysql.Client {
		return proxy
	})
	defer stub.Reset()
	initMock(proxy)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *model.ModifyInfo
		wantErr bool
	}{
		{
			name: "测试mysql存储set接口",
			fields: fields{
				baseInfo: baseInfo{DBName: "DB1", TBName: "TB1"},
				Dao:      NewBasic(),
				db:       proxy,
			},
			args: args{
				id: "testID1",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "title", FieldId: 9,
						FieldType: protocol.EnumFieldType_FieldTypeStr, StrValue: "测试标题",
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName: "title", FieldId: 9,
					FieldType: protocol.EnumFieldType_FieldTypeStr, OldStrValue: "", NewStrValue: "测试标题",
				}},
			},
			wantErr: false,
		}, {
			name: "测试mysql存储set接口-加锁场景",
			fields: fields{
				baseInfo: baseInfo{DBName: "DB1", TBName: "TB1"},
				Dao:      NewBasic(),
				db:       proxy,
			},
			args: args{
				id: "testID1",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "long_video_list", FieldId: 80150,
						FieldType: protocol.EnumFieldType_FieldTypeSet, VecStr: []string{"testVid1"},
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeAppend,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName: "long_video_list", FieldId: 80150,
					FieldType: protocol.EnumFieldType_FieldTypeSet,
					OldVecStr: nil, NewVecStr: []string{"testVid1"},
				}},
			},
			wantErr: false,
		}, {
			name: "测试mysql-map存储set接口",
			fields: fields{
				baseInfo: baseInfo{DBName: "DB1", TBName: "TB1"},
				Dao:      NewMapType(),
				db:       proxy,
			},
			args: args{
				id: "testID1",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "title", FieldId: 1,
						FieldType: protocol.EnumFieldType_FieldTypeMapKV, MapVal: map[string]*protocol.MapValue{"cn": {
							Type: protocol.EnumFieldType_FieldTypeStr, StrValue: "测试标题",
						}},
					}, UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName: "title", FieldId: 1,
					FieldType: protocol.EnumFieldType_FieldTypeMapKV, NewMapVal: map[string]*protocol.MapValue{"cn": {Type: protocol.EnumFieldType_FieldTypeStr, StrValue: "测试标题"}},
				}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MysqlObj{
				baseInfo: tt.fields.baseInfo,
				Dao:      tt.fields.Dao,
				db:       tt.fields.db,
				unlock:   tt.fields.unlock,
				lock:     tt.fields.lock,
				infos:    tt.fields.infos,
			}
			got, err := m.SetFieldInfos(tt.args.id, tt.args.baseFieldIDs, tt.args.fieldList, tt.args.rsp)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetFieldInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetFieldInfos() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func Test_sortUpdateFields(t *testing.T) {
	type args struct {
		fields []*protocol.UpdateFieldInfo
	}
	tests := []struct {
		name string
		args args
		want []*protocol.UpdateFieldInfo
	}{
		{
			name: "入参为空",
			args: args{fields: nil},
			want: nil,
		}, {
			name: "更新字段包括modify_time",
			args: args{fields: []*protocol.UpdateFieldInfo{
				{FieldInfo: &protocol.FieldInfo{FieldName: "modify_time", FieldId: 80102}},
				{FieldInfo: &protocol.FieldInfo{FieldName: "user_description", FieldId: 80275}},
				{FieldInfo: &protocol.FieldInfo{FieldName: "titleX", FieldId: 9}},
				{FieldInfo: &protocol.FieldInfo{FieldName: "modify_time", FieldId: 80102}},
				{FieldInfo: &protocol.FieldInfo{FieldName: "presenter_id", FieldId: 12}},
			}},
			want: []*protocol.UpdateFieldInfo{
				{FieldInfo: &protocol.FieldInfo{FieldName: "titleX", FieldId: 9}},
				{FieldInfo: &protocol.FieldInfo{FieldName: "presenter_id", FieldId: 12}},
				{FieldInfo: &protocol.FieldInfo{FieldName: "user_description", FieldId: 80275}},
				{FieldInfo: &protocol.FieldInfo{FieldName: "modify_time", FieldId: 80102}},
				{FieldInfo: &protocol.FieldInfo{FieldName: "modify_time", FieldId: 80102}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sortUpdateFields(tt.args.fields)
		})
		if !reflect.DeepEqual(tt.args.fields, tt.want) {
			t.Errorf("sortUpdateFields() got = %v, want = %v", tt.args.fields, tt.want)
		}
	}
}
