package mysql

import (
	"database/sql"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	tool "git.code.oa.com/video_media/storage_service/common"
)

// map类型字段读写sql
const (
	SQLSelectMap = `select
  c_id,
  c_field_id,
  c_field_key,
  c_field_value,
  c_mtime
from
  %s.%s %s
where
  c_id = ?
  and (c_field_id, c_field_key) in (%s)`
	SQLSelectFieldMap = `select
  c_id,
  c_field_id,
  c_field_key,
  c_field_value,
  c_mtime
from
  %s.%s %s
where
  c_id = ?
  and c_field_id in (%s)`
	SQLSelectAllMap = "select c_id,c_field_id,c_field_key,c_field_value,c_mtime from %s.%s %s where c_id=?"
	SQLUpdateMap    = `insert into
  %s.%s (c_id, c_field_id, c_field_key, c_field_value)
values
  %s ON DUPLICATE KEY
UPDATE
  c_field_value =
values(c_field_value)`
	SQLInsertMap = "insert %s into %s.%s (c_id,c_field_id,c_field_key,c_field_value) values %s"
)

// MapType mysql实现map类型字段
type MapType struct {
	// cur 当前值{fieldID}:{fieldKey}:{type+fieldVal}
	cur map[uint32]map[string]string
}

// NewMapType 创建mysql实现map类型
func NewMapType() *MapType {
	return &MapType{cur: make(map[uint32]map[string]string)}
}

// CurVal 实现CurVal接口
func (m *MapType) CurVal(field *protocol.FieldInfo) (*tool.FieldVal, bool) {
	val, ok := m.cur[field.FieldId]
	return &tool.FieldVal{MapVal: val}, ok
}

// FillData 实现Dao接口
func (m *MapType) FillData(rows *sql.Rows) error {
	var (
		fieldID                     uint32
		id, fieldKey, result, mtime string
	)
	if err := rows.Scan(&id, &fieldID, &fieldKey, &result, &mtime); err != nil {
		log.Errorf("Fail to scan data, err is %v.", err)
		return err
	}

	log.Debugf("[FillData]id:%s, fieldID:%d, fieldKey:%s, value:%s, mtime:%s",
		id, fieldID, fieldKey, result, mtime)
	val, ok := m.cur[fieldID]
	if !ok {
		val = make(map[string]string)
		m.cur[fieldID] = val
	}
	val[fieldKey] = result
	return nil
}

// FillGetField 实现Dao接口
func (m *MapType) FillGetField(rows *sql.Rows) (*adaptor.GetFieldInfo, error) {
	var data adaptor.GetFieldInfo
	if err := rows.Scan(&data.Id, &data.FieldId, &data.FieldKey, &data.FieldValue, &data.Mtime); err != nil {
		log.Errorf("Fail to scan data, err is %v.", err)
		return nil, err
	}
	return &data, nil
}

// AddIns 实现sqlBuilder接口
func (m *MapType) AddIns(fieldID uint32, val *tool.FieldVal, para *parameter) {
	for k, v := range val.MapVal {
		para.args = append(para.args, para.id, fieldID, k, v)
	}
}

// InsertSQL 实现sqlBuilder接口
func (m *MapType) InsertSQL(dbName, tbName, ignore string, para *parameter) (string, []interface{}) {
	paraNum := len(para.args) / 4
	if paraNum == 0 {
		return "", nil
	}
	return fmt.Sprintf(SQLInsertMap, ignore, dbName, tbName, strings.Repeat(",(?,?,?,?)", paraNum)[1:]), para.args
}

// UpdateSQL 实现sqlBuilder接口
func (m *MapType) UpdateSQL(dbName, tbName string, para *parameter) (string, []interface{}) {
	paraNum := len(para.args) / 4
	if paraNum == 0 {
		return "", nil
	}
	return fmt.Sprintf(SQLUpdateMap, dbName, tbName, strings.Repeat(",(?,?,?,?)", paraNum)[1:]), para.args
}

// QueryFromUpdate 实现sqlBuilder接口
func (m *MapType) QueryFromUpdate(dbName, tbName, index, id string, fields []*protocol.UpdateFieldInfo) (string,
	[]interface{},
) {
	// 通过更新请求生成查询sql
	keyNum, args := 0, []interface{}{id}
	for _, f := range fields {
		fieldID := f.GetFieldInfo().GetFieldId()
		for k := range f.GetFieldInfo().GetMapVal() {
			args = append(args, fieldID, k)
		}
		keyNum += len(f.GetFieldInfo().GetMapVal())
	}
	if keyNum == 0 {
		return "", nil
	}
	return fmt.Sprintf(SQLSelectMap, dbName, tbName, index, strings.Repeat(",(?,?)", keyNum)[1:]), args
}

// QueryFromGet 实现sqlBuilder接口
func (m *MapType) QueryFromGet(dbName, tbName, index string, ids []string, key *inf.FieldKey) []*sqlExecutor {
	log.Debugf("fieldKey is %+v.", key)
	var executors []*sqlExecutor
	if len(key.FieldIDs) > 0 {
		// 指定了fieldID优先按照fieldID构造查询语句
		if logic.IsGetAllRequest(key.FieldIDs) {
			// *号全量查找 todo:GetAll不支持批量接口
			return []*sqlExecutor{{
				sql:  fmt.Sprintf(SQLSelectAllMap, dbName, tbName, index),
				args: []interface{}{ids[0]},
			}}
		}
		// todo:map类型字段也先不支持批量接口
		_, pFields, args := createSQLQueryFields(ids[0:1], key.FieldIDs)
		executors = append(executors, &sqlExecutor{
			sql:  fmt.Sprintf(SQLSelectFieldMap, dbName, tbName, index, pFields),
			args: args,
		})
	}

	// map类型字段暂时先只支持单个id
	// 通过查询请求的key值构造查询语句
	keyNum, args := 0, []interface{}{ids[0]}
	for fieldID, v := range key.FieldKeys {
		for _, k := range v.Keys {
			args = append(args, fieldID, k)
		}
		keyNum += len(v.Keys)
	}

	if keyNum > 0 {
		executors = append(executors, &sqlExecutor{
			sql:  fmt.Sprintf(SQLSelectMap, dbName, tbName, index, strings.Repeat(",(?,?)", keyNum)[1:]),
			args: args,
		})
	}
	return executors
}
