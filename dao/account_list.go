// Package dao 数据访问层
package dao

import "context"

// AccountInfo 账号信息
type AccountInfo struct {
	OrderID int    `db:"c_order_id"` // OrderID 关联的订单ID
	IPID    int    `db:"c_ip_id"`    // IPID ip 表的 c_id
	MID     string `db:"c_mid"`      // MID 账号 id
	Author  string `db:"c_author"`   // Author 账号名称
	Avatar  string `db:"c_avatar"`   // Avatar 头像
	URL     string `db:"c_url"`      // URL 个人主页URL
	Rank    int    `db:"c_rank"`     // Rank 排名
	CrawKey string `db:"c_craw_key"` // CrawKey 抓取 key
	CrawCID string `db:"c_craw_cid"` // CrawCID 所属 cid
}

// CreateAccountInfo 创建账号信息
func CreateAccountInfo(ctx context.Context, accountInfo *AccountInfo) error {
	_, err := proxy.NamedExec(ctx,
		"INSERT INTO t_account_list "+
			"(c_order_id, c_ip_id, c_mid, c_author, c_avatar, c_url, c_rank, c_craw_key, c_craw_cid) VALUES "+
			"(:c_order_id, :c_ip_id, :c_mid, :c_author, :c_avatar, :c_url, :c_rank, :c_craw_key, :c_craw_cid) "+
			"ON DUPLICATE KEY UPDATE "+
			"c_order_id=:c_order_id, c_author=:c_author, c_avatar=:c_avatar, c_url=:c_url, "+
			"c_rank=:c_rank, c_craw_key=:c_craw_key, c_craw_cid=:c_craw_cid",
		[]*AccountInfo{accountInfo})
	return err
}
