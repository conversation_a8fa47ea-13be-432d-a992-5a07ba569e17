package dao

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	tRpcErrs "git.code.oa.com/trpc-go/trpc-go/errs"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	upb "git.code.oa.com/trpcprotocol/video_media/universal_mgr"
	"git.code.oa.com/video_media/media_go_commlib/dataaccess"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/errcodes"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/model"
	"github.com/avast/retry-go"
)

// CoverVideosSrvReq 专辑视频列表服务请求参数
type CoverVideosSrvReq struct {
	// AppID 调用方标识
	AppID string `json:"appid"`
	// AppKey 调用方标识
	AppKey string `json:"appkey"`
	// CoverID 专辑id
	CoverID string `json:"cid"`
	// OAName 操作人
	OAName string `json:"oaname"`
	// ListType 操作视频的类型 1：长视频， 2：碎视频， 3：互动视频； 旧接口中用105 表示未发布视频列表
	ListType int `json:"list_type"`
	// ListID 列表ID（当ListType=100时必填）
	ListID string `json:"list_id"`
	// ListType 操作视频的类型 1：长视频， 2：碎视频， 3：互动视频；
	DstListType int `json:"dst_list_type"`
	// Pos 操作位置 0：最开始位置， -1：最末尾位置， 大于0：列表第pos+1个位置
	Pos int32 `json:"pos"`
	// VideoIDs 操作的视频
	VideoIDs []string `json:"vids"`
	// OpCheckupVid 是否上架视频  0:不上架，1:上架(默认为0)
	OpCheckupVid int `json:"op_checkup_vid"`
}

// CoverVideosSrvRsp 专辑视频列表服务回包
type CoverVideosSrvRsp struct {
	// Code 返回值
	Code int `json:"code"`
	// Msg 返回信息
	Msg string `json:"msg"`
}

var accessMediaCoverR, accessMediaVideoR, accessMediaVideoW, accessMediaCoverW dataaccess.DataAccess

func init() {
	accessMediaCoverR.AccessSetOptions(
		dataaccess.WithProjID(1),
		dataaccess.WithDimID(2),
		dataaccess.WithAppID("dbccb1b7d0d4947b72e7616dd2f3da4d"),
		dataaccess.WithAppKey("66e300d13276a186b4b2960a0fdda2b4"))
	accessMediaCoverW.AccessSetOptions(
		dataaccess.WithProjID(1),
		dataaccess.WithDimID(2),
		dataaccess.WithAppID("dbccb1b7d0d4947b72e7616dd2f3da4d"),
		dataaccess.WithAppKey("347e3cba346f467005aa984c5cb2b83b"))
	accessMediaVideoW.AccessSetOptions(
		dataaccess.WithProjID(1),
		dataaccess.WithDimID(3),
		dataaccess.WithAppID("dbccb1b7d0d4947b72e7616dd2f3da4d"),
		dataaccess.WithAppKey("87fb624ee389ed9fff8a87ef53547a2a"))
	accessMediaVideoR.AccessSetOptions(
		dataaccess.WithProjID(1),
		dataaccess.WithDimID(3),
		dataaccess.WithAppID("497cca84d4681424ab2b03067840d2c2"),
		dataaccess.WithAppKey("f80a5794a7cd59812c2a0ee41f9ac41a"))
}

func isValidCid(coverID string) bool {
	return len(coverID) == 15
}

func isValidVid(videoID string) bool {
	return len(videoID) == 11
}

// GetUnshelveVideoCovers 获取非上架视频的所属专辑（上架视频返回空）
func GetUnshelveVideoCovers(ctx context.Context, videoID string) (string, error) {
	videoFieldInfos, err := accessMediaVideoR.GetMediaData(ctx, videoID, []string{"state", "covers"})
	if err != nil {
		log.ErrorContextf(ctx, "DataGet cover err:%s", err.Error())
		return "", err
	}

	// 若视频为上架，则直接返回为空（上游不处理 已上架的视频）
	if videoFieldInfos["state"] == strconv.Itoa(model.VideoCheckUpState) {
		return "", nil
	}
	return videoFieldInfos["covers"], nil
}

// GetListIDFromListVal 从媒资结果中获取自定义列表ID（看点和跟播列表的ID）
func GetListIDFromListVal(listVal, videoID string) string {
	// 使用 | 分割原始字符串，获取所有 K:xxx+xxx 的部分
	parts := strings.Split(listVal, "|")
	for _, part := range parts {
		// 对每部分使用 : 分割，得到 K 和 xxx+xxx
		kv := strings.Split(part, ":")
		if len(kv) != 2 {
			continue // 如果格式不匹配，则跳过
		}
		k, v := kv[0], kv[1]
		// 使用 + 分割 xxx+xxx，检查是否有匹配的 xxx
		values := strings.Split(v, "+")
		for _, value := range values {
			if value == videoID {
				return k // 如果找到匹配的 xxx，返回对应的 K 值
			}
		}
	}
	return "" // 如果没有找到匹配，返回空字符串和 false
}

// GetVideoCoversListType 获取VID所属的专辑列表类型
func GetVideoCoversListType(ctx context.Context, videoID string, coverID string) (int, string, error) {
	log.InfoContextf(ctx, "getVideoCoversListType enter:%s-%s", coverID, videoID)
	if !isValidCid(coverID) || !isValidVid(videoID) { // 数据异常，我们返回0值，不报错。以让异常数据可以被扭转状态
		return 0, "", nil
	}
	mapListType := map[string]int{ // 这些是外显的已发布列表
		"long_video_list":     model.LongVideoListType,
		"clip_video_list":     model.ClipVideoListType,
		"aspect_list":         model.AspectVideoListType,
		"following_play_list": model.FollowingPlayList,
	}

	var listTypes []string
	for k := range mapListType {
		listTypes = append(listTypes, k)
	}
	coverFieldInfos, err := accessMediaCoverR.GetMediaData(ctx, coverID, listTypes)
	if err != nil {
		log.ErrorContextf(ctx, "DataGet cover err:%s", err.Error())
		return 0, "", err
	}
	var listVal string
	listType := 0
	for listName, id := range mapListType {
		if strings.Contains(coverFieldInfos[listName], videoID) {
			listType = id
			listVal = coverFieldInfos[listName]
			break
		}
	}

	// 对于看点列表，跟播列表这种map类型的，需要特殊处理，并返回listID
	var listID string
	if listType == model.AspectVideoListType || listType == model.FollowingPlayList {
		listID = GetListIDFromListVal(listVal, videoID)
	}
	log.InfoContextf(ctx, "getVideoCoversListType， listType：%d", listType)
	return listType, listID, nil
}

// AddCoverVideosList 加入专辑视频列表
func AddCoverVideosList(ctx context.Context, coverID string, videoID string, listType int,
	waitAddCoverListID string, pos int) error {
	log.InfoContextf(ctx, "addCoverVideosList enter:%s-%s-%d-%s-%d", coverID, videoID, listType, waitAddCoverListID, pos)
	if !isValidCid(coverID) || !isValidVid(videoID) || listType <= 0 {
		log.ErrorContextf(ctx, "%s INVALID PARAM", videoID)
		return tRpcErrs.New(errcodes.ERR_INVALID_PARAM, errcodes.GetErrMsg(errcodes.ERR_INVALID_PARAM))
	}
	header := &thttp.ClientReqHeader{}
	header.AddHeader("ContentType", "application/json")
	proxy := thttp.NewClientProxy("trpc.video_media.cover_videos_list_srv.cover_videos_list_srv",
		client.WithReqHead(header),
		client.WithTarget("polaris://trpc.video_media.cover_videos_list_srv.cover_videos_list_srv"),
		client.WithSerializationType(codec.SerializationTypeJSON),
	)

	var req CoverVideosSrvReq
	var rsp CoverVideosSrvRsp
	req.AppID = model.AppID
	req.AppKey = model.AppKey
	req.Pos = int32(pos)
	req.ListType = listType
	req.ListID = waitAddCoverListID
	req.CoverID = coverID
	req.VideoIDs = append(req.VideoIDs, videoID)
	req.OAName = model.AppID
	err := proxy.Post(ctx, "/cover_videos_add", req, &rsp)
	if err != nil {
		log.ErrorContextf(ctx, "http failed: %s", err)
		return tRpcErrs.New(errcodes.ERR_SEND_REQUEST, err.Error())
	}
	if rsp.Code != 0 && rsp.Code != 4001 { // 4001 表示逻辑错误，一般是视频已存在列表了
		log.ErrorContextf(ctx, "cover_videos_add err:%d-%s", rsp.Code, rsp.Msg)
		return tRpcErrs.New(errcodes.ERR_REQ_MEDIAAPI_FAIL, rsp.Msg)
	}
	log.DebugContextf(ctx, "addCoverVideosList - rsp:%+v", rsp)
	return nil
}

// CheckupCover 修改专辑状态为上架状态
func CheckupCover(ctx context.Context, coverID string, operator string) error {
	log.InfoContextf(ctx, "checkupCover enter.:%s-%s", coverID, operator)
	accessMediaCoverW.AccessSetOptions(dataaccess.WithCaller(operator))
	return retry.Do(
		func() error {
			rsp, err := accessMediaCoverW.UpdateMediaData(ctx, coverID, []*upb.FieldUpdateEntry{
				{
					ExternalName: "checkup_state",
					UpdateType:   upb.EnumUpdateType_Set,
					FieldValues:  []string{"4"}, // 4 为上架
					OpInfos: map[string]string{
						"oaName": operator,
					},
				},
			})
			if err != nil {
				log.ErrorContextf(ctx, "checkupCover ERR:%s-%+v", coverID, rsp)
				err = fmt.Errorf("专辑上架失败:%+v", err)
				return err
			}
			return nil
		},
		retry.Attempts(3),         // 重试次数
		retry.LastErrorOnly(true), // 仅返回最后一次错误
		retry.OnRetry(func(n uint, err error) { // 每次重试的时候调用方法
			log.ErrorContextf(ctx, "retry checkupCover #%d, because got err: %s", n, err)
		}))
}

// CleanVideoAutoAddCoverConf 清除视频的自动加专辑配置
func CleanVideoAutoAddCoverConf(ctx context.Context, videoID string) error {
	log.InfoContextf(ctx, "cleanVideoAutoAddCoverConf enter:%s", videoID)
	if !isValidVid(videoID) {
		log.ErrorContextf(ctx, "%s INVALID PARAM", videoID)
		return tRpcErrs.New(errcodes.ERR_INVALID_PARAM, errcodes.GetErrMsg(errcodes.ERR_INVALID_PARAM))
	}
	accessMediaVideoW.AccessSetOptions(dataaccess.WithCaller(model.Operator))
	rsp, err := accessMediaVideoW.UpdateMediaData(ctx, videoID, []*upb.FieldUpdateEntry{
		{
			ExternalName: "add_cover_id",
			UpdateType:   upb.EnumUpdateType_Set,
			FieldValues:  []string{""},
			OpInfos: map[string]string{
				"oaName": model.Operator,
			},
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "cleanVideoAutoAddCoverConf ERR:%s-%+v", videoID, rsp)
		return tRpcErrs.New(errcodes.ERR_REQ_MEDIAAPI_FAIL, err.Error())
	}
	return nil
}

// DelVideoCover 删除视频所属专辑中指定的cid
func DelVideoCover(ctx context.Context, videoID, coverID string) error {
	log.InfoContextf(ctx, "DelVideoCover enter:%s-%s", videoID, coverID)
	if !isValidVid(videoID) || !isValidCid(coverID) {
		return nil
	}
	accessMediaVideoW.AccessSetOptions(dataaccess.WithCaller(model.Operator))
	rsp, err := accessMediaVideoW.UpdateMediaData(ctx, videoID, []*upb.FieldUpdateEntry{
		{
			ExternalName: "covers",
			UpdateType:   upb.EnumUpdateType_Del,
			FieldValues:  []string{coverID},
			OpInfos: map[string]string{
				"oaName": model.Operator,
			},
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "DelVideoCover ERR:%s-%+v", videoID, rsp)
		return tRpcErrs.New(errcodes.ERR_REQ_MEDIAAPI_FAIL, err.Error())
	}
	return nil
}

// RemoveCoverVideosList 从专辑视频列表中移除（移动到未发布视频列表）
func RemoveCoverVideosList(ctx context.Context, coverID string, videoID string, listType int, listID string) error {
	log.InfoContextf(ctx, "removeCoverVideosList enter:%s-%s-%d", coverID, videoID, listType)
	if !isValidCid(coverID) || !isValidVid(videoID) || listType <= 0 {
		log.ErrorContextf(ctx, "%s INVALID PARAM, listType:%d", videoID, listType)
		return tRpcErrs.New(errcodes.ERR_INVALID_PARAM, errcodes.GetErrMsg(errcodes.ERR_INVALID_PARAM))
	}
	header := &thttp.ClientReqHeader{}
	header.AddHeader("ContentType", "application/json")
	proxy := thttp.NewClientProxy("trpc.video_media.cover_videos_list_srv.cover_videos_list_srv",
		client.WithReqHead(header),
		client.WithTarget("polaris://trpc.video_media.cover_videos_list_srv.cover_videos_list_srv"),
		client.WithSerializationType(codec.SerializationTypeJSON),
	)
	var req CoverVideosSrvReq
	var rsp CoverVideosSrvRsp
	req.AppID = model.AppID
	req.AppKey = model.AppKey
	req.ListType = listType
	req.ListID = listID
	req.CoverID = coverID
	req.VideoIDs = append(req.VideoIDs, videoID)
	req.OAName = model.AppID
	// 这里用remove而不是move，因为remove删除后也会自动移动到未发布列表，但移动失败并不回滚，处理策略比较柔和
	// 可避免由于未发布列表存在目标vid导致处理失败的情况
	err := proxy.Post(ctx, "/cover_videos_remove", req, &rsp)
	if err != nil {
		log.ErrorContextf(ctx, "http failed: %s", err)
		return tRpcErrs.New(errcodes.ERR_SEND_REQUEST, err.Error())
	}
	if rsp.Code != 0 {
		log.ErrorContextf(ctx, "cover_videos_remove err:%d-%s", rsp.Code, rsp.Msg)
		return tRpcErrs.New(errcodes.ERR_REQ_MEDIAAPI_FAIL, rsp.Msg)
	}
	log.DebugContextf(ctx, "removeCoverVideosList - rsp:%+v", rsp)
	return nil
}

// getFieldVal 获取属性的原始值
func getFieldVal(fields map[string][]*upb.ValEntry, fieldName string) string {
	if fields == nil {
		return ""
	}
	fieldVal, ok := fields[fieldName]
	if !ok || len(fieldVal) == 0 {
		return ""
	}
	var fieldVals []string
	for _, entry := range fieldVal {
		fieldVals = append(fieldVals, entry.Val)
	}
	return strings.Join(fieldVals, "+")
}

// GetCoverInfo 获取专辑信息
func GetCoverInfo(ctx context.Context, coverID string) (model.CoverInfo, error) {
	log.InfoContextf(ctx, "getCoverInfo enter:%s", coverID)
	var coverInfo model.CoverInfo
	if !isValidCid(coverID) {
		log.ErrorContextf(ctx, "%s INVALID PARAM", coverID)
		return coverInfo, fmt.Errorf(errcodes.GetErrMsg(errcodes.ERR_INVALID_PARAM))
	}
	coverFieldInfos, err := accessMediaCoverR.GetMediaInfo(ctx, []string{coverID})
	if err != nil {
		log.ErrorContextf(ctx, "DataGet cover err:%s", err.Error())
		return coverInfo, err
	}
	coverInfo.LongVideoNum, _ = strconv.Atoi(getFieldVal(coverFieldInfos[coverID], "long_video_num"))
	coverInfo.CheckupGrade, _ = strconv.Atoi(getFieldVal(coverFieldInfos[coverID], "checkup_state"))
	coverInfo.HzPic = getFieldVal(coverFieldInfos[coverID], "new_pic_hz")
	coverInfo.VtPic = getFieldVal(coverFieldInfos[coverID], "new_pic_vt")
	log.InfoContextf(ctx, "getCoverInfo，%+v", coverInfo)
	return coverInfo, nil
}
