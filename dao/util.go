// Package dao 数据适配层服务数据抽象
package dao

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/elastic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/hbase"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/mysql"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/redis"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/union"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	"git.code.oa.com/video_media/storage_service/config_cache/dao"
)

// NewStoreObjWithRouteCfg 存储对象工厂函数
// NOCC:CCN_threshold(设计如此:)
func NewStoreObjWithRouteCfg(ctx context.Context, routeCfg *dao.RouteCfg) (inf.StoreObj, error) {
	if ctx == nil {
		// 未传上下文，使用默认上下文
		ctx = context.Background()
	}
	var obj inf.StoreObj
	switch int(routeCfg.DataType) {
	case inf.MysqlType:
		obj = &mysql.MysqlObj{Ctx: ctx, Dao: mysql.NewBasic()}
	case inf.RedisType:
		obj = &redis.RedisObj{Ctx: ctx, DatasourceID: routeCfg.DataSourceID}
	case inf.MysqlMapType:
		obj = &mysql.MysqlObj{Ctx: ctx, Dao: mysql.NewMapType()}
	case inf.HBaseType:
		obj = &hbase.HbaseObj{Ctx: ctx, DatasourceID: routeCfg.DataSourceID}
	case inf.ElasticType:
		obj = &elastic.ESObj{Ctx: ctx, DatasourceID: routeCfg.DataSourceID}
	case inf.UnionType:
		obj = &union.Union{Ctx: ctx}
	default:
		return nil, errors.New(fmt.Sprintf("invalid route type"))
	}
	return obj, nil
}

// Group 批量请求路由分组信息
type Group struct {
	// Obj 请求ID对应存储对象
	Obj inf.StoreObj
	// IDs 分批请求ID列表
	IDs []string
}

// NewStoreObjs 根据id列表创建StoreObj集合
func NewStoreObjs(ctx context.Context, dataSourceID int32, ids []string) ([]*Group, error) {
	const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteCfg)
	routeCfg := cache.GetRouteCfg(dataSourceID)
	if routeCfg == nil {
		return nil, errs.New(noRoute, "can not find route config")
	}

	m := make(map[string]int)
	g := make([]*Group, 0, len(ids)/2)
	for _, id := range ids {
		obj, err := NewStoreObjWithRouteCfg(ctx, routeCfg)
		if err != nil {
			return nil, errs.New(noRoute, "route data_type invalid")
		}

		hash := obj.RouteRuleFunc(routeCfg.RouteFunc, id, routeCfg.DevSetStr)
		i, ok := m[hash]
		if ok {
			// 添加id到已有分组
			g[i].IDs = append(g[i].IDs, id)
			continue
		}

		if err := getConn(obj); err != nil {
			// 连接存储实例失败，直接退出
			return nil, err
		}
		m[hash] = len(g)
		g = append(g, &Group{
			Obj: obj,
			IDs: []string{id},
		})
	}
	return g, nil
}

// NewStoreObj 生成一个StoreObj,先获取routeCfg,再创建StoreObj,然后连接数据库
func NewStoreObj(ctx context.Context, dataSourceID int32, id string) (inf.StoreObj, error) {
	const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteCfg)
	routeCfg := cache.GetRouteCfg(dataSourceID)
	if routeCfg == nil {
		return nil, errs.New(noRoute, "can not find route config")
	}

	obj, err := NewStoreObjWithRouteCfg(ctx, routeCfg)
	if err != nil {
		return nil, errs.New(noRoute, "route data_type invalid")
	}

	// 根据数据源id和key获取数据连接对象（例如数据库表连接）
	obj.RouteRuleFunc(routeCfg.RouteFunc, id, routeCfg.DevSetStr)
	if err = getConn(obj); err != nil {
		log.Errorf("Fail to connect dataItem[dataSourceId:%d], err is %s.", dataSourceID, err)
		return nil, err
	}
	return obj, nil
}

// isReadOnly 判断是否路由到了只读节点
func isReadOnly() bool {
	const readOnlyPrefix = "saasreadonly"
	// 服务set名以saasreadonly开头的为只读节点
	return strings.HasPrefix(trpc.GlobalConfig().Global.FullSetName, readOnlyPrefix)
}

func getDevCfg(devInfo string) (*dao.DevCfg, error) {
	if isReadOnly() {
		// 如果是只读场景，则路由到只读设备上
		if devCfg := cache.GetDevCfg("read_" + devInfo); devCfg != nil {
			return devCfg, nil
		}
		// 如果找不到只读配置，则使用原始配置
	}
	devCfg := cache.GetDevCfg(devInfo)
	if devCfg == nil {
		const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteStoreItem)
		return nil, errs.New(noRoute, "fail to route storage object")
	}
	return devCfg, nil
}

// getConn 根据数据源id以及路由id获取具体的存储对象
func getConn(dataObj inf.RouteObj) error {
	devCfg, err := getDevCfg(dataObj.GetKey())
	if err != nil {
		log.Errorf("Fail to get device config, key is %s.", dataObj.GetKey())
		return err
	}

	if err := dataObj.GetConn(devCfg.ConnectInfo, devCfg.AuthInfo, devCfg.Options); err != nil {
		return errs.New(int(common_storage_common.EnumMediaErrorCode_RetFailConnectDataSource), "fail to connect dataSource")
	}
	return nil
}

// NewSearchObj 生成一个storeObj,先获取routeCfg,再创建storeObj,然后连接数据库
func NewSearchObj(ctx context.Context, dataSourceID, dataSetID int32) (inf.SearchObj, error) {
	routeCfg := cache.GetRouteCfg(dataSourceID)
	if routeCfg == nil {
		return nil, errs.New(int(common_storage_common.EnumMediaErrorCode_RetNoRouteCfg), "can not find route config")
	}

	// 暂时仅有es实现了Search接口，不需要对应的工厂方法
	esObj := &elastic.ESObj{Ctx: ctx, DatasourceID: routeCfg.DataSourceID, DatasetID: dataSetID}
	// 根据数据源id和key获取数据连接对象（例如数据库表连接） DevSetStr就是ES索引名, 配置了索引在哪个集群,集群的用户名密码
	esObj.RouteRuleFunc(routeCfg.RouteFunc, "", routeCfg.DevSetStr)
	if err := getConn(esObj); err != nil {
		log.Errorf("Fail to connect dataItem[dataSourceId:%d], err is %s.", dataSourceID, err)
		return nil, err
	}
	return esObj, nil
}
