package dao

import (
	"context"
	"errors"
	"strings"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/config_cache/dao"
)

// MockRouteObj 模拟路由对象，用于测试
type MockRouteObj struct {
	key         string
	connSuccess bool
	connError   error
}

func (m *MockRouteObj) RouteRuleFunc(routeFunc, id, devSetStr string) string {
	m.key = devSetStr // 简化实现，直接使用devSetStr作为key
	return m.key
}

func (m *MockRouteObj) GetConn(connectInfo, authInfo, extraInfo string) error {
	if !m.connSuccess {
		return m.connError
	}
	return nil
}

func (m *MockRouteObj) GetKey() string {
	return m.key
}

// MockStoreObj 模拟存储对象，继承MockRouteObj并实现StoreObj接口
type MockStoreObj struct {
	*MockRouteObj
}

// 实现StoreOperator接口的方法（简化实现）
func (m *MockStoreObj) InsertFieldInfos(id string, version int64, fields []*common_storage_common.UpdateFieldInfo) (interface{}, error) {
	return nil, nil
}

func (m *MockStoreObj) SetFieldInfos(id string, base []*common_storage_common.FieldInfo, fields []*common_storage_common.UpdateFieldInfo, rsp interface{}) (interface{}, error) {
	return nil, nil
}

func (m *MockStoreObj) GetFieldInfos(ids []string, fieldKey *inf.FieldKey, rsp interface{}) error {
	return nil
}

func (m *MockStoreObj) BatchGetFields(datasetID int32, ids, fields []string, extra map[string][]string) (interface{}, error) {
	return nil, nil
}

// TestNewStoreObjWithRouteCfg 测试存储对象工厂函数
func TestNewStoreObjWithRouteCfg(t *testing.T) {
	tests := []struct {
		name        string
		ctx         context.Context
		routeCfg    *dao.RouteCfg
		wantObjType string
		wantErr     bool
	}{
		{
			name: "MySQL基础类型",
			ctx:  context.Background(),
			routeCfg: &dao.RouteCfg{
				DataSourceID: 1,
				DataType:     uint32(inf.MysqlType),
				RouteFunc:    inf.NoneFunction,
				DevSetStr:    "testDB:testTable",
			},
			wantObjType: "*mysql.MysqlObj",
			wantErr:     false,
		},
		{
			name: "Redis类型",
			ctx:  context.Background(),
			routeCfg: &dao.RouteCfg{
				DataSourceID: 2,
				DataType:     uint32(inf.RedisType),
				RouteFunc:    inf.NoneFunction,
				DevSetStr:    "testRedis",
			},
			wantObjType: "*redis.RedisObj",
			wantErr:     false,
		},
		{
			name: "MySQL Map类型",
			ctx:  context.Background(),
			routeCfg: &dao.RouteCfg{
				DataSourceID: 3,
				DataType:     uint32(inf.MysqlMapType),
				RouteFunc:    inf.NoneFunction,
				DevSetStr:    "testDB:testTable",
			},
			wantObjType: "*mysql.MysqlObj",
			wantErr:     false,
		},
		{
			name: "HBase类型",
			ctx:  context.Background(),
			routeCfg: &dao.RouteCfg{
				DataSourceID: 4,
				DataType:     uint32(inf.HBaseType),
				RouteFunc:    inf.NoneFunction,
				DevSetStr:    "testHBase:testTable:testFamily",
			},
			wantObjType: "*hbase.HbaseObj",
			wantErr:     false,
		},
		{
			name: "Elasticsearch类型",
			ctx:  context.Background(),
			routeCfg: &dao.RouteCfg{
				DataSourceID: 5,
				DataType:     uint32(inf.ElasticType),
				RouteFunc:    inf.NoneFunction,
				DevSetStr:    "testIndex",
			},
			wantObjType: "*elastic.ESObj",
			wantErr:     false,
		},
		{
			name: "Union类型",
			ctx:  context.Background(),
			routeCfg: &dao.RouteCfg{
				DataSourceID: 6,
				DataType:     uint32(inf.UnionType),
				RouteFunc:    inf.NoneFunction,
				DevSetStr:    "testUnion",
			},
			wantObjType: "*union.Union",
			wantErr:     false,
		},
		{
			name: "空上下文自动创建",
			ctx:  nil,
			routeCfg: &dao.RouteCfg{
				DataSourceID: 1,
				DataType:     uint32(inf.MysqlType),
				RouteFunc:    inf.NoneFunction,
				DevSetStr:    "testDB:testTable",
			},
			wantObjType: "*mysql.MysqlObj",
			wantErr:     false,
		},
		{
			name: "无效的存储类型",
			ctx:  context.Background(),
			routeCfg: &dao.RouteCfg{
				DataSourceID: 999,
				DataType:     999,
				RouteFunc:    inf.NoneFunction,
				DevSetStr:    "invalid",
			},
			wantObjType: "",
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			obj, err := NewStoreObjWithRouteCfg(tt.ctx, tt.routeCfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewStoreObjWithRouteCfg() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if obj == nil {
					t.Errorf("NewStoreObjWithRouteCfg() returned nil object")
					return
				}
				// 对象创建成功，无需检查具体类型
				t.Logf("Created object successfully for type %d", tt.routeCfg.DataType)
			}
		})
	}
}

// CacheInterface cache接口，用于测试中的模拟
type CacheInterface interface {
	GetRouteCfg(datasourceID int32) *dao.RouteCfg
	GetDevCfg(devName string) *dao.DevCfg
}

// MockCache 模拟Cache实现
type MockCache struct {
	routeData map[int32]*dao.RouteCfg
	devData   map[string]*dao.DevCfg
}

func NewMockCache() *MockCache {
	return &MockCache{
		routeData: make(map[int32]*dao.RouteCfg),
		devData:   make(map[string]*dao.DevCfg),
	}
}

func (m *MockCache) SetRouteData(dataSourceID int32, cfg *dao.RouteCfg) {
	m.routeData[dataSourceID] = cfg
}

func (m *MockCache) SetDevData(devName string, cfg *dao.DevCfg) {
	m.devData[devName] = cfg
}

func (m *MockCache) GetRouteCfg(datasourceID int32) *dao.RouteCfg {
	return m.routeData[datasourceID]
}

func (m *MockCache) GetDevCfg(devName string) *dao.DevCfg {
	return m.devData[devName]
}

// TestableUtilFunctions 可测试的工具函数包装器
type TestableUtilFunctions struct {
	cache CacheInterface
}

func NewTestableUtilFunctions(cache CacheInterface) *TestableUtilFunctions {
	return &TestableUtilFunctions{cache: cache}
}

// NewStoreObj 可测试版本的NewStoreObj
func (t *TestableUtilFunctions) NewStoreObj(ctx context.Context, dataSourceID int32, id string) (inf.StoreObj, error) {
	const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteCfg)
	routeCfg := t.cache.GetRouteCfg(dataSourceID)
	if routeCfg == nil {
		return nil, errs.New(noRoute, "can not find route config")
	}

	obj, err := NewStoreObjWithRouteCfg(ctx, routeCfg)
	if err != nil {
		return nil, errs.New(noRoute, "route data_type invalid")
	}

	// 设置路由规则
	obj.RouteRuleFunc(routeCfg.RouteFunc, id, routeCfg.DevSetStr)

	// 获取设备配置
	devCfg := t.cache.GetDevCfg(obj.GetKey())
	if devCfg == nil {
		const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteStoreItem)
		return nil, errs.New(noRoute, "fail to route storage object")
	}

	// 尝试连接
	if err := obj.GetConn(devCfg.ConnectInfo, devCfg.AuthInfo, devCfg.Options); err != nil {
		return nil, errs.New(int(common_storage_common.EnumMediaErrorCode_RetFailConnectDataSource), "fail to connect dataSource")
	}
	return obj, nil
}

// NewStoreObjs 可测试版本的NewStoreObjs
func (t *TestableUtilFunctions) NewStoreObjs(ctx context.Context, dataSourceID int32, ids []string) ([]*Group, error) {
	const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteCfg)
	routeCfg := t.cache.GetRouteCfg(dataSourceID)
	if routeCfg == nil {
		return nil, errs.New(noRoute, "can not find route config")
	}

	m := make(map[string]int)
	g := make([]*Group, 0, len(ids)/2)
	for _, id := range ids {
		obj, err := NewStoreObjWithRouteCfg(ctx, routeCfg)
		if err != nil {
			return nil, errs.New(noRoute, "route data_type invalid")
		}

		hash := obj.RouteRuleFunc(routeCfg.RouteFunc, id, routeCfg.DevSetStr)
		i, ok := m[hash]
		if ok {
			// 添加id到已有分组
			g[i].IDs = append(g[i].IDs, id)
			continue
		}

		// 获取设备配置并连接
		devCfg := t.cache.GetDevCfg(obj.GetKey())
		if devCfg == nil {
			const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteStoreItem)
			return nil, errs.New(noRoute, "fail to route storage object")
		}

		if err := obj.GetConn(devCfg.ConnectInfo, devCfg.AuthInfo, devCfg.Options); err != nil {
			return nil, errs.New(int(common_storage_common.EnumMediaErrorCode_RetFailConnectDataSource), "fail to connect dataSource")
		}

		m[hash] = len(g)
		g = append(g, &Group{
			Obj: obj,
			IDs: []string{id},
		})
	}
	return g, nil
}

// TestNewStoreObj 测试NewStoreObj函数
func TestNewStoreObj(t *testing.T) {
	tests := []struct {
		name         string
		dataSourceID int32
		id           string
		setupCache   func(*MockCache)
		wantErr      bool
		expectedErr  string
	}{
		{
			name:         "成功创建Redis对象",
			dataSourceID: 1,
			id:           "testID",
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testRedis",
				})
				cache.SetDevData("testRedis", &dao.DevCfg{
					ConnectInfo: "localhost:6379",
					AuthInfo:    "",
				})
			},
			wantErr: false,
		},
		{
			name:         "路由配置不存在",
			dataSourceID: 999,
			id:           "testID",
			setupCache: func(cache *MockCache) {
				// 不设置路由配置
			},
			wantErr:     true,
			expectedErr: "can not find route config",
		},
		{
			name:         "设备配置不存在",
			dataSourceID: 2,
			id:           "testID",
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(2, &dao.RouteCfg{
					DataSourceID: 2,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "missingDev",
				})
				// 不设置设备配置
			},
			wantErr:     true,
			expectedErr: "fail to route storage object",
		},
		{
			name:         "无效的存储类型",
			dataSourceID: 3,
			id:           "testID",
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(3, &dao.RouteCfg{
					DataSourceID: 3,
					DataType:     999, // 无效类型
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testDev",
				})
			},
			wantErr:     true,
			expectedErr: "route data_type invalid",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := NewMockCache()
			tt.setupCache(cache)

			testUtil := NewTestableUtilFunctions(cache)

			obj, err := testUtil.NewStoreObj(context.Background(), tt.dataSourceID, tt.id)

			if tt.wantErr {
				if err == nil {
					t.Errorf("NewStoreObj() expected error but got none")
					return
				}
				if tt.expectedErr != "" && !strings.Contains(err.Error(), tt.expectedErr) {
					t.Errorf("NewStoreObj() error = %v, expected to contain %v", err, tt.expectedErr)
				}
			} else {
				if err != nil {
					t.Errorf("NewStoreObj() unexpected error = %v", err)
					return
				}
				if obj == nil {
					t.Errorf("NewStoreObj() returned nil object")
				}
			}
		})
	}
}

// TestNewStoreObjs 测试NewStoreObjs函数
func TestNewStoreObjs(t *testing.T) {
	tests := []struct {
		name           string
		dataSourceID   int32
		ids            []string
		setupCache     func(*MockCache)
		wantErr        bool
		expectedErr    string
		expectedGroups int
	}{
		{
			name:         "成功创建单个组",
			dataSourceID: 1,
			ids:          []string{"id1", "id2"},
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testRedis",
				})
				cache.SetDevData("testRedis", &dao.DevCfg{
					ConnectInfo: "localhost:6379",
					AuthInfo:    "",
				})
			},
			wantErr:        false,
			expectedGroups: 1,
		},
		{
			name:         "空ID列表",
			dataSourceID: 1,
			ids:          []string{},
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testRedis",
				})
			},
			wantErr:        false,
			expectedGroups: 0,
		},
		{
			name:         "路由配置不存在",
			dataSourceID: 999,
			ids:          []string{"id1"},
			setupCache: func(cache *MockCache) {
				// 不设置路由配置
			},
			wantErr:     true,
			expectedErr: "can not find route config",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := NewMockCache()
			tt.setupCache(cache)

			testUtil := NewTestableUtilFunctions(cache)

			groups, err := testUtil.NewStoreObjs(context.Background(), tt.dataSourceID, tt.ids)

			if tt.wantErr {
				if err == nil {
					t.Errorf("NewStoreObjs() expected error but got none")
					return
				}
				if tt.expectedErr != "" && !strings.Contains(err.Error(), tt.expectedErr) {
					t.Errorf("NewStoreObjs() error = %v, expected to contain %v", err, tt.expectedErr)
				}
			} else {
				if err != nil {
					t.Errorf("NewStoreObjs() unexpected error = %v", err)
					return
				}
				if len(groups) != tt.expectedGroups {
					t.Errorf("NewStoreObjs() returned %d groups, expected %d", len(groups), tt.expectedGroups)
				}
			}
		})
	}
}

// MockStoreObj需要实现inf.StoreObj接口
// 这里简化实现，主要用于测试路由逻辑

// NewSearchObj 可测试版本的NewSearchObj
func (t *TestableUtilFunctions) NewSearchObj(ctx context.Context, dataSourceID, dataSetID int32) (inf.SearchObj, error) {
	routeCfg := t.cache.GetRouteCfg(dataSourceID)
	if routeCfg == nil {
		return nil, errs.New(int(common_storage_common.EnumMediaErrorCode_RetNoRouteCfg), "can not find route config")
	}

	// 暂时仅有es实现了Search接口，不需要对应的工厂方法
	esObj, err := NewStoreObjWithRouteCfg(ctx, routeCfg)
	if err != nil {
		return nil, err
	}

	// 根据数据源id和key获取数据连接对象（例如数据库表连接） DevSetStr就是ES索引名, 配置了索引在哪个集群,集群的用户名密码
	esObj.RouteRuleFunc(routeCfg.RouteFunc, "", routeCfg.DevSetStr)

	// 获取设备配置并连接
	devCfg := t.cache.GetDevCfg(esObj.GetKey())
	if devCfg == nil {
		const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteStoreItem)
		return nil, errs.New(noRoute, "fail to route storage object")
	}

	if err := esObj.GetConn(devCfg.ConnectInfo, devCfg.AuthInfo, devCfg.Options); err != nil {
		return nil, errs.New(int(common_storage_common.EnumMediaErrorCode_RetFailConnectDataSource), "fail to connect dataSource")
	}

	// 类型断言，假设ES对象实现了SearchObj接口
	if searchObj, ok := esObj.(inf.SearchObj); ok {
		return searchObj, nil
	}
	return nil, errs.New(int(common_storage_common.EnumMediaErrorCode_RetFailConnectDataSource), "object does not implement SearchObj interface")
}

// TestNewSearchObj 测试NewSearchObj函数
func TestNewSearchObj(t *testing.T) {
	tests := []struct {
		name         string
		dataSourceID int32
		dataSetID    int32
		setupCache   func(*MockCache)
		wantErr      bool
		expectedErr  string
	}{
		{
			name:         "成功创建ES搜索对象",
			dataSourceID: 1,
			dataSetID:    100,
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.ElasticType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testIndex",
				})
				cache.SetDevData("testIndex", &dao.DevCfg{
					ConnectInfo: "http://localhost:9200",
					AuthInfo:    "",
				})
			},
			wantErr: false,
		},
		{
			name:         "路由配置不存在",
			dataSourceID: 999,
			dataSetID:    100,
			setupCache: func(cache *MockCache) {
				// 不设置路由配置
			},
			wantErr:     true,
			expectedErr: "can not find route config",
		},
		{
			name:         "设备配置不存在",
			dataSourceID: 2,
			dataSetID:    100,
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(2, &dao.RouteCfg{
					DataSourceID: 2,
					DataType:     uint32(inf.ElasticType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "missingIndex",
				})
				// 不设置设备配置
			},
			wantErr:     true,
			expectedErr: "fail to route storage object",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := NewMockCache()
			tt.setupCache(cache)

			testUtil := NewTestableUtilFunctions(cache)

			obj, err := testUtil.NewSearchObj(context.Background(), tt.dataSourceID, tt.dataSetID)

			if tt.wantErr {
				if err == nil {
					t.Errorf("NewSearchObj() expected error but got none")
					return
				}
				if tt.expectedErr != "" && !strings.Contains(err.Error(), tt.expectedErr) {
					t.Errorf("NewSearchObj() error = %v, expected to contain %v", err, tt.expectedErr)
				}
			} else {
				if err != nil {
					t.Errorf("NewSearchObj() unexpected error = %v", err)
					return
				}
				if obj == nil {
					t.Errorf("NewSearchObj() returned nil object")
				}
			}
		})
	}
}

// TestRouteObjConnections 测试连接相关的功能
func TestRouteObjConnections(t *testing.T) {
	tests := []struct {
		name          string
		setupMockObj  func() *MockRouteObj
		devCfg        *dao.DevCfg
		wantErr       bool
		expectedError string
	}{
		{
			name: "连接成功",
			setupMockObj: func() *MockRouteObj {
				return &MockRouteObj{
					key:         "testDevice",
					connSuccess: true,
					connError:   nil,
				}
			},
			devCfg: &dao.DevCfg{
				ConnectInfo: "localhost:6379",
				AuthInfo:    "",
			},
			wantErr: false,
		},
		{
			name: "连接失败",
			setupMockObj: func() *MockRouteObj {
				return &MockRouteObj{
					key:         "testDevice",
					connSuccess: false,
					connError:   errors.New("connection failed"),
				}
			},
			devCfg: &dao.DevCfg{
				ConnectInfo: "localhost:6379",
				AuthInfo:    "",
			},
			wantErr:       true,
			expectedError: "connection failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockObj := tt.setupMockObj()

			err := mockObj.GetConn(tt.devCfg.ConnectInfo, tt.devCfg.AuthInfo, tt.devCfg.Options)

			if tt.wantErr {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.expectedError != "" && !strings.Contains(err.Error(), tt.expectedError) {
					t.Errorf("Error = %v, expected to contain %v", err, tt.expectedError)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error = %v", err)
				}
			}
		})
	}
}

// GlobalConfigInterface trpc配置接口，用于模拟
type GlobalConfigInterface interface {
	GetFullSetName() string
}

// MockGlobalConfig 模拟全局配置
type MockGlobalConfig struct {
	fullSetName string
}

func (m *MockGlobalConfig) GetFullSetName() string {
	return m.fullSetName
}

func (m *MockGlobalConfig) SetFullSetName(name string) {
	m.fullSetName = name
}

// EnhancedTestableUtilFunctions 增强的可测试工具函数，支持全局配置注入
type EnhancedTestableUtilFunctions struct {
	cache        CacheInterface
	globalConfig GlobalConfigInterface
}

func NewEnhancedTestableUtilFunctions(cache CacheInterface, globalConfig GlobalConfigInterface) *EnhancedTestableUtilFunctions {
	return &EnhancedTestableUtilFunctions{
		cache:        cache,
		globalConfig: globalConfig,
	}
}

// isReadOnly 可测试版本的isReadOnly函数
func (e *EnhancedTestableUtilFunctions) isReadOnly() bool {
	const readOnlyPrefix = "saasreadonly"
	return strings.HasPrefix(e.globalConfig.GetFullSetName(), readOnlyPrefix)
}

// getDevCfg 可测试版本的getDevCfg函数
func (e *EnhancedTestableUtilFunctions) getDevCfg(devInfo string) (*dao.DevCfg, error) {
	if e.isReadOnly() {
		// 如果是只读场景，则路由到只读设备上
		if devCfg := e.cache.GetDevCfg("read_" + devInfo); devCfg != nil {
			return devCfg, nil
		}
		// 如果找不到只读配置，则使用原始配置
	}
	devCfg := e.cache.GetDevCfg(devInfo)
	if devCfg == nil {
		const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteStoreItem)
		return nil, errs.New(noRoute, "fail to route storage object")
	}
	return devCfg, nil
}

// getConn 可测试版本的getConn函数
func (e *EnhancedTestableUtilFunctions) getConn(dataObj inf.RouteObj) error {
	key := dataObj.GetKey()
	devCfg, err := e.getDevCfg(key)
	if err != nil {
		// 调试信息：打印key和缓存中的所有配置
		return err
	}

	if err := dataObj.GetConn(devCfg.ConnectInfo, devCfg.AuthInfo, devCfg.Options); err != nil {
		return errs.New(int(common_storage_common.EnumMediaErrorCode_RetFailConnectDataSource), "fail to connect dataSource")
	}
	return nil
}

// NewStoreObjs 增强版本，支持依赖注入
func (e *EnhancedTestableUtilFunctions) NewStoreObjs(ctx context.Context, dataSourceID int32, ids []string) ([]*Group, error) {
	const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteCfg)
	routeCfg := e.cache.GetRouteCfg(dataSourceID)
	if routeCfg == nil {
		return nil, errs.New(noRoute, "can not find route config")
	}

	m := make(map[string]int)
	g := make([]*Group, 0, len(ids)/2)
	for _, id := range ids {
		obj, err := NewStoreObjWithRouteCfg(ctx, routeCfg)
		if err != nil {
			return nil, errs.New(noRoute, "route data_type invalid")
		}

		hash := obj.RouteRuleFunc(routeCfg.RouteFunc, id, routeCfg.DevSetStr)
		i, ok := m[hash]
		if ok {
			// 添加id到已有分组
			g[i].IDs = append(g[i].IDs, id)
			continue
		}

		if err := e.getConn(obj); err != nil {
			// 连接存储实例失败，直接退出
			return nil, err
		}
		m[hash] = len(g)
		g = append(g, &Group{
			Obj: obj,
			IDs: []string{id},
		})
	}
	return g, nil
}

// NewStoreObj 增强版本，支持依赖注入
func (e *EnhancedTestableUtilFunctions) NewStoreObj(ctx context.Context, dataSourceID int32, id string) (inf.StoreObj, error) {
	const noRoute = int(common_storage_common.EnumMediaErrorCode_RetNoRouteCfg)
	routeCfg := e.cache.GetRouteCfg(dataSourceID)
	if routeCfg == nil {
		return nil, errs.New(noRoute, "can not find route config")
	}

	obj, err := NewStoreObjWithRouteCfg(ctx, routeCfg)
	if err != nil {
		return nil, errs.New(noRoute, "route data_type invalid")
	}

	// 根据数据源id和key获取数据连接对象
	obj.RouteRuleFunc(routeCfg.RouteFunc, id, routeCfg.DevSetStr)
	if err = e.getConn(obj); err != nil {
		return nil, err
	}
	return obj, nil
}

// TestIsReadOnly_Enhanced 全面测试isReadOnly函数
func TestIsReadOnly_Enhanced(t *testing.T) {
	tests := []struct {
		name        string
		fullSetName string
		want        bool
	}{
		{
			name:        "只读节点 - saasreadonly前缀",
			fullSetName: "saasreadonly.test.service",
			want:        true,
		},
		{
			name:        "只读节点 - saasreadonly开头",
			fullSetName: "saasreadonly123",
			want:        true,
		},
		{
			name:        "非只读节点 - 普通服务名",
			fullSetName: "production.service",
			want:        false,
		},
		{
			name:        "非只读节点 - 包含但不以saasreadonly开头",
			fullSetName: "test.saasreadonly.service",
			want:        false,
		},
		{
			name:        "空服务名",
			fullSetName: "",
			want:        false,
		},
		{
			name:        "仅saasreadonly",
			fullSetName: "saasreadonly",
			want:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockConfig := &MockGlobalConfig{fullSetName: tt.fullSetName}
			enhancedUtil := NewEnhancedTestableUtilFunctions(nil, mockConfig)

			got := enhancedUtil.isReadOnly()
			if got != tt.want {
				t.Errorf("isReadOnly() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestGetDevCfg 全面测试getDevCfg函数，包括只读场景
func TestGetDevCfg(t *testing.T) {
	tests := []struct {
		name           string
		devInfo        string
		fullSetName    string
		setupCache     func(*MockCache)
		wantErr        bool
		expectedDevCfg *dao.DevCfg
	}{
		{
			name:        "只读场景 - 找到只读配置",
			devInfo:     "testDevice",
			fullSetName: "saasreadonly.test",
			setupCache: func(cache *MockCache) {
				cache.SetDevData("read_testDevice", &dao.DevCfg{
					ConnectInfo: "readonly://localhost:6379",
					AuthInfo:    "readonly_auth",
				})
				cache.SetDevData("testDevice", &dao.DevCfg{
					ConnectInfo: "write://localhost:6379",
					AuthInfo:    "write_auth",
				})
			},
			wantErr: false,
			expectedDevCfg: &dao.DevCfg{
				ConnectInfo: "readonly://localhost:6379",
				AuthInfo:    "readonly_auth",
			},
		},
		{
			name:        "只读场景 - 只读配置不存在，回退到普通配置",
			devInfo:     "testDevice",
			fullSetName: "saasreadonly.test",
			setupCache: func(cache *MockCache) {
				// 不设置只读配置，只设置普通配置
				cache.SetDevData("testDevice", &dao.DevCfg{
					ConnectInfo: "write://localhost:6379",
					AuthInfo:    "write_auth",
				})
			},
			wantErr: false,
			expectedDevCfg: &dao.DevCfg{
				ConnectInfo: "write://localhost:6379",
				AuthInfo:    "write_auth",
			},
		},
		{
			name:        "只读场景 - 两种配置都不存在",
			devInfo:     "missingDevice",
			fullSetName: "saasreadonly.test",
			setupCache: func(cache *MockCache) {
				// 不设置任何配置
			},
			wantErr: true,
		},
		{
			name:        "非只读场景 - 正常配置",
			devInfo:     "testDevice",
			fullSetName: "production.service",
			setupCache: func(cache *MockCache) {
				cache.SetDevData("testDevice", &dao.DevCfg{
					ConnectInfo: "production://localhost:6379",
					AuthInfo:    "production_auth",
				})
			},
			wantErr: false,
			expectedDevCfg: &dao.DevCfg{
				ConnectInfo: "production://localhost:6379",
				AuthInfo:    "production_auth",
			},
		},
		{
			name:        "非只读场景 - 配置不存在",
			devInfo:     "missingDevice",
			fullSetName: "production.service",
			setupCache: func(cache *MockCache) {
				// 不设置任何配置
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := NewMockCache()
			tt.setupCache(cache)

			mockConfig := &MockGlobalConfig{fullSetName: tt.fullSetName}
			enhancedUtil := NewEnhancedTestableUtilFunctions(cache, mockConfig)

			got, err := enhancedUtil.getDevCfg(tt.devInfo)

			if (err != nil) != tt.wantErr {
				t.Errorf("getDevCfg() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if got == nil {
					t.Errorf("getDevCfg() returned nil config")
					return
				}
				if got.ConnectInfo != tt.expectedDevCfg.ConnectInfo {
					t.Errorf("getDevCfg() ConnectInfo = %v, want %v", got.ConnectInfo, tt.expectedDevCfg.ConnectInfo)
				}
				if got.AuthInfo != tt.expectedDevCfg.AuthInfo {
					t.Errorf("getDevCfg() AuthInfo = %v, want %v", got.AuthInfo, tt.expectedDevCfg.AuthInfo)
				}
			}
		})
	}
}

// TestNewStoreObjs_Enhanced 全面测试NewStoreObjs函数的分组逻辑
func TestNewStoreObjs_Enhanced(t *testing.T) {
	tests := []struct {
		name           string
		dataSourceID   int32
		ids            []string
		setupCache     func(*MockCache)
		wantGroupCount int
		wantTotalIDs   int
		wantErr        bool
		expectedErrMsg string
	}{
		{
			name:         "多个ID相同路由 - 单个分组",
			dataSourceID: 1,
			ids:          []string{"id1", "id2", "id3"},
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "sameDevice",
				})
				cache.SetDevData("sameDevice", &dao.DevCfg{
					ConnectInfo: "localhost:6379",
					AuthInfo:    "",
				})
			},
			wantGroupCount: 1,
			wantTotalIDs:   3,
			wantErr:        false,
		},
		{
			name:         "空ID列表",
			dataSourceID: 1,
			ids:          []string{},
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testDevice",
				})
			},
			wantGroupCount: 0,
			wantTotalIDs:   0,
			wantErr:        false,
		},
		{
			name:         "路由配置不存在",
			dataSourceID: 999,
			ids:          []string{"id1"},
			setupCache: func(cache *MockCache) {
				// 不设置路由配置
			},
			wantErr:        true,
			expectedErrMsg: "can not find route config",
		},
		{
			name:         "存储类型无效",
			dataSourceID: 1,
			ids:          []string{"id1"},
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     999, // 无效类型
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testDevice",
				})
			},
			wantErr:        true,
			expectedErrMsg: "route data_type invalid",
		},
		{
			name:         "设备连接失败",
			dataSourceID: 1,
			ids:          []string{"id1"},
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "failDevice",
				})
				// 不设置设备配置，导致连接失败
			},
			wantErr:        true,
			expectedErrMsg: "fail to route storage object",
		},
		{
			name:         "单个ID",
			dataSourceID: 1,
			ids:          []string{"single_id"},
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testDevice",
				})
				cache.SetDevData("testDevice", &dao.DevCfg{
					ConnectInfo: "localhost:6379",
					AuthInfo:    "",
				})
			},
			wantGroupCount: 1,
			wantTotalIDs:   1,
			wantErr:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := NewMockCache()
			tt.setupCache(cache)

			mockConfig := &MockGlobalConfig{fullSetName: "production.service"}
			enhancedUtil := NewEnhancedTestableUtilFunctions(cache, mockConfig)

			groups, err := enhancedUtil.NewStoreObjs(context.Background(), tt.dataSourceID, tt.ids)

			if (err != nil) != tt.wantErr {
				t.Errorf("NewStoreObjs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				if tt.expectedErrMsg != "" && (err == nil || !strings.Contains(err.Error(), tt.expectedErrMsg)) {
					t.Errorf("NewStoreObjs() error = %v, expected to contain %v", err, tt.expectedErrMsg)
				}
				return
			}

			if len(groups) != tt.wantGroupCount {
				t.Errorf("NewStoreObjs() group count = %v, want %v", len(groups), tt.wantGroupCount)
				return
			}

			totalIDs := 0
			for _, group := range groups {
				totalIDs += len(group.IDs)
				if group.Obj == nil {
					t.Errorf("NewStoreObjs() group has nil Obj")
				}
			}

			if totalIDs != tt.wantTotalIDs {
				t.Errorf("NewStoreObjs() total IDs = %v, want %v", totalIDs, tt.wantTotalIDs)
			}
		})
	}
}

// TestGetConn 全面测试getConn函数
func TestGetConn(t *testing.T) {
	tests := []struct {
		name          string
		setupMockObj  func() *MockRouteObj
		setupCache    func(*MockCache)
		fullSetName   string
		wantErr       bool
		expectedError string
	}{
		{
			name: "正常连接成功",
			setupMockObj: func() *MockRouteObj {
				return &MockRouteObj{
					key:         "testDevice",
					connSuccess: true,
				}
			},
			setupCache: func(cache *MockCache) {
				cache.SetDevData("testDevice", &dao.DevCfg{
					ConnectInfo: "localhost:6379",
					AuthInfo:    "test_auth",
					Options:     "test_options",
				})
			},
			fullSetName: "production.service",
			wantErr:     false,
		},
		{
			name: "设备配置不存在",
			setupMockObj: func() *MockRouteObj {
				return &MockRouteObj{
					key:         "missingDevice",
					connSuccess: true,
				}
			},
			setupCache: func(cache *MockCache) {
				// 不设置设备配置
			},
			fullSetName:   "production.service",
			wantErr:       true,
			expectedError: "fail to route storage object",
		},
		{
			name: "连接失败",
			setupMockObj: func() *MockRouteObj {
				return &MockRouteObj{
					key:         "testDevice",
					connSuccess: false,
					connError:   errors.New("connection refused"),
				}
			},
			setupCache: func(cache *MockCache) {
				cache.SetDevData("testDevice", &dao.DevCfg{
					ConnectInfo: "localhost:6379",
					AuthInfo:    "",
				})
			},
			fullSetName:   "production.service",
			wantErr:       true,
			expectedError: "fail to connect dataSource",
		},
		{
			name: "只读场景 - 使用只读配置连接成功",
			setupMockObj: func() *MockRouteObj {
				return &MockRouteObj{
					key:         "testDevice",
					connSuccess: true,
				}
			},
			setupCache: func(cache *MockCache) {
				cache.SetDevData("read_testDevice", &dao.DevCfg{
					ConnectInfo: "readonly://localhost:6379",
					AuthInfo:    "readonly_auth",
				})
				cache.SetDevData("testDevice", &dao.DevCfg{
					ConnectInfo: "write://localhost:6379",
					AuthInfo:    "write_auth",
				})
			},
			fullSetName: "saasreadonly.test",
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := NewMockCache()
			tt.setupCache(cache)

			mockConfig := &MockGlobalConfig{fullSetName: tt.fullSetName}
			enhancedUtil := NewEnhancedTestableUtilFunctions(cache, mockConfig)

			mockObj := tt.setupMockObj()

			err := enhancedUtil.getConn(mockObj)

			if (err != nil) != tt.wantErr {
				t.Errorf("getConn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && tt.expectedError != "" {
				if err == nil || !strings.Contains(err.Error(), tt.expectedError) {
					t.Errorf("getConn() error = %v, expected to contain %v", err, tt.expectedError)
				}
			}
		})
	}
}

// TestNewStoreObj_Enhanced 增强版本的NewStoreObj测试
func TestNewStoreObj_Enhanced(t *testing.T) {
	tests := []struct {
		name          string
		ctx           context.Context
		dataSourceID  int32
		id            string
		setupCache    func(*MockCache)
		fullSetName   string
		wantErr       bool
		expectedError string
	}{
		{
			name:         "成功创建对象 - 包含路由调用",
			ctx:          context.Background(),
			dataSourceID: 1,
			id:           "test_id_123",
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testDevice",
				})
				cache.SetDevData("testDevice", &dao.DevCfg{
					ConnectInfo: "localhost:6379",
					AuthInfo:    "",
				})
			},
			fullSetName: "production.service",
			wantErr:     false,
		},
		{
			name:         "空上下文自动处理",
			ctx:          nil,
			dataSourceID: 1,
			id:           "test_id_456",
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType), // 改为Redis类型，更简单
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testRedis",
				})
				cache.SetDevData("testRedis", &dao.DevCfg{
					ConnectInfo: "localhost:6379",
					AuthInfo:    "",
				})
			},
			fullSetName: "production.service",
			wantErr:     false,
		},
		{
			name:         "只读场景创建对象",
			ctx:          context.Background(),
			dataSourceID: 1,
			id:           "readonly_id",
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "testDevice",
				})
				cache.SetDevData("read_testDevice", &dao.DevCfg{
					ConnectInfo: "readonly://localhost:6379",
					AuthInfo:    "readonly_auth",
				})
			},
			fullSetName: "saasreadonly.test",
			wantErr:     false,
		},
		{
			name:         "连接失败场景",
			ctx:          context.Background(),
			dataSourceID: 1,
			id:           "fail_id",
			setupCache: func(cache *MockCache) {
				cache.SetRouteData(1, &dao.RouteCfg{
					DataSourceID: 1,
					DataType:     uint32(inf.RedisType),
					RouteFunc:    inf.NoneFunction,
					DevSetStr:    "failDevice",
				})
				// 不设置设备配置
			},
			fullSetName:   "production.service",
			wantErr:       true,
			expectedError: "fail to route storage object",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cache := NewMockCache()
			tt.setupCache(cache)

			mockConfig := &MockGlobalConfig{fullSetName: tt.fullSetName}
			enhancedUtil := NewEnhancedTestableUtilFunctions(cache, mockConfig)

			obj, err := enhancedUtil.NewStoreObj(tt.ctx, tt.dataSourceID, tt.id)

			if (err != nil) != tt.wantErr {
				t.Errorf("NewStoreObj() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr {
				if tt.expectedError != "" && (err == nil || !strings.Contains(err.Error(), tt.expectedError)) {
					t.Errorf("NewStoreObj() error = %v, expected to contain %v", err, tt.expectedError)
				}
			} else {
				if obj == nil {
					t.Errorf("NewStoreObj() returned nil object")
				}
			}
		})
	}
}
