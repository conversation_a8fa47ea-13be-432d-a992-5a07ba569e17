package hbase

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/hbase"
	"git.code.oa.com/trpc-go/trpc-database/hbase/mockhbase"
	"git.code.oa.com/trpc-go/trpc-go/client"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"

	"github.com/bitly/go-simplejson"
	"github.com/golang/mock/gomock"
	"github.com/prashantv/gostub"
	"github.com/tsuna/gohbase/hrpc"
)

// CacheInterface 定义缓存接口以便于测试
type CacheInterface interface {
	GetFieldInfoByName(fieldName string, datasetID int32) *item.FieldInfo
}

// TestableHbaseObj 可测试的HBase对象，允许注入缓存接口
type TestableHbaseObj struct {
	*HbaseObj
	cacheInterface CacheInterface
}

// MockCache 模拟缓存实现
type MockCache struct {
	fieldInfoMap map[string]*item.FieldInfo
}

func (m *MockCache) GetFieldInfoByName(fieldName string, datasetID int32) *item.FieldInfo {
	return m.fieldInfoMap[fieldName]
}

// hbaseData2FieldInfo 重写方法以使用注入的缓存
func (th *TestableHbaseObj) hbaseData2FieldInfo(fieldName string, value []byte) *protocol.FieldInfo {
	res := &protocol.FieldInfo{
		FieldName: fieldName,
	}

	var fieldInfoMap *item.FieldInfo
	if th.cacheInterface != nil {
		fieldInfoMap = th.cacheInterface.GetFieldInfoByName(fieldName, th.DatasetID)
	} else {
		fieldInfoMap = cache.GetFieldInfoByName(fieldName, th.DatasetID)
	}

	if fieldInfoMap == nil {
		return res
	}

	res.FieldType = protocol.EnumFieldType(fieldInfoMap.FieldType)
	res.FieldId = fieldInfoMap.FieldID

	switch fieldInfoMap.FieldType {
	case int32(protocol.EnumFieldType_FieldTypeStr):
		res.StrValue = string(value)
	case int32(protocol.EnumFieldType_FieldTypeIntVec):
		// 这里简化处理，直接返回空值
		res.VecInt = []uint32{}
	case int32(protocol.EnumFieldType_FieldTypeSet):
		// 这里简化处理，直接返回空值
		res.VecStr = []string{}
	case int32(protocol.EnumFieldType_FieldTypeMapKV):
		// 对于map类型，默认会设置为字符串值
		res.StrValue = string(value)
	case int32(protocol.EnumFieldType_FieldTypeMapKList):
		// 对于map类型，默认会设置为字符串值
		res.StrValue = string(value)
	default:
		res.StrValue = string(value)
	}
	return res
}

// BatchGetFields 重写BatchGetFields方法以使用注入的缓存
func (th *TestableHbaseObj) BatchGetFields(datasetID int32, ids, fields []string, extra map[string][]string) (*model.BatchGetResponse, error) {
	th.data = make(map[string][]byte)
	th.DatasetID = datasetID
	// 以vid为hash值，每次只传入单一id
	id := ids[0]
	mapFieldInfo, failList, err := th.getHbaseInfoByID(id, fields)
	if err != nil {
		return nil, err
	}

	resDocs := &protocol.DocInfo{
		Id:     id,
		Fields: mapFieldInfo,
	}
	var rsp model.BatchGetResponse
	rsp.DocInfos = append(rsp.DocInfos, resDocs)
	rsp.FailList = failList
	return &rsp, nil
}

// getHbaseInfoByID 重写getHbaseInfoByID方法以使用注入的缓存
func (th *TestableHbaseObj) getHbaseInfoByID(id string, fields []string) (map[string]*protocol.FieldInfo, map[string]string, error) {
	mapFail := make(map[string]string)
	if len(fields) == 1 && fields[0] == "*" {
		th.isGetAll = true
	}
	args := th.buildHbaseReqMap(fields)
	if err := th.getFieldsVal(id, args); err != nil {
		mapFail[id] = err.Error()
		return nil, mapFail, err
	}
	res := make(map[string]*protocol.FieldInfo)
	for k, v := range th.data {
		res[k] = th.hbaseData2FieldInfo(k, v)
	}
	return res, mapFail, nil
}

func mockHBase(proxy *mockhbase.MockClient) {
	family1 := map[string][]string{
		"vv": {"title"},
	}
	values1 := map[string]map[string][]byte{
		"vv": {
			"title": []byte("1"),
		},
	}
	ret1 := &hrpc.Result{
		Cells:   nil,
		Stale:   false,
		Partial: false,
		Exists:  nil,
	}
	proxy.EXPECT().Get(gomock.Any(), "", "testkey1", family1).Return(ret1, nil).AnyTimes()
	proxy.EXPECT().Put(gomock.Any(), "", "testkey1", values1).Return(nil, nil).AnyTimes()

	family2 := map[string][]string{
		"vv": {"vec_int"},
	}
	values2 := map[string]map[string][]byte{
		"vv": {
			"vec_int": []byte("1+2+3+4"),
		},
	}
	ret2 := &hrpc.Result{
		Cells: []*hrpc.Cell{
			{
				Row:       []byte("testkey2"),
				Qualifier: []byte("vec_int"),
				Value:     []byte("1+2+3"),
			},
		},
	}
	proxy.EXPECT().Get(gomock.Any(), "", "testkey2", family2).Return(ret2, nil).AnyTimes()
	proxy.EXPECT().Put(gomock.Any(), "", "testkey2", values2).Return(nil, nil).AnyTimes()

	family3 := map[string][]string{
		"vv": {"tt"},
	}
	mJson := simplejson.New()
	mJson.Set("key", "val")
	jData, _ := mJson.Encode()
	values3 := map[string]map[string][]byte{
		"vv": {
			"tt": jData,
		},
	}
	ret3 := &hrpc.Result{
		Cells:   nil,
		Stale:   false,
		Partial: false,
		Exists:  nil,
	}
	proxy.EXPECT().Get(gomock.Any(), "", "testkey3", family3).Return(ret3, nil).AnyTimes()
	proxy.EXPECT().Put(gomock.Any(), "", "testkey3", values3).Return(nil, nil).AnyTimes()

	family4 := map[string][]string{
		"vv": {"tt_map"},
	}
	mJson4 := simplejson.New()
	mJson4.Set("key", "val2")
	jData4, _ := mJson4.Encode()
	values4 := map[string]map[string][]byte{
		"vv": {
			"tt_map": jData4,
		},
	}
	ret4 := &hrpc.Result{
		Cells:   nil,
		Stale:   false,
		Partial: false,
		Exists:  nil,
	}
	proxy.EXPECT().Get(gomock.Any(), "", "testkey4", family4).Return(ret4, nil).AnyTimes()
	proxy.EXPECT().Put(gomock.Any(), "", "testkey4", values4).Return(nil, nil).AnyTimes()
}

func TestHbaseObj_SetFieldInfos1(t *testing.T) {
	type fields struct {
		isGetAll     bool
		data         map[string][]byte
		Ctx          context.Context
		proxy        hbase.Client
		Name         string
		DatasourceID int32
		fieldList    []*protocol.UpdateFieldInfo
		infos        map[uint32]*protocol.ModifyFieldInfo
		tableName    string
		familyName   string
	}
	type args struct {
		id           string
		baseFieldIDs []*protocol.FieldInfo
		fieldList    []*protocol.UpdateFieldInfo
		rsp          *adaptor.SetFieldInfosResponse
	}
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// mock hbase proxy
	proxy := mockhbase.NewMockClient(ctrl)
	stub := gostub.Stub(&hbase.NewClientProxy, func(name string, opts ...client.Option) hbase.Client {
		return proxy
	})
	defer stub.Reset()
	mockHBase(proxy)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *model.ModifyInfo
		wantErr bool
	}{
		{
			name: "测试hbase存储set接口-1",
			fields: fields{
				proxy:      proxy,
				familyName: "vv",
			},
			args: args{
				id: "testkey1",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "title",
						FieldId:   9,
						FieldType: protocol.EnumFieldType_FieldTypeStr,
						StrValue:  "1",
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName:   "title",
					FieldId:     9,
					FieldType:   protocol.EnumFieldType_FieldTypeStr,
					OldStrValue: "",
					NewStrValue: "1",
				}},
			},
			wantErr: false,
		},
		{
			name: "测试hbase存储set接口-2",
			fields: fields{
				proxy:      proxy,
				familyName: "vv",
			},
			args: args{
				id: "testkey2",
				fieldList: []*protocol.UpdateFieldInfo{
					{
						FieldInfo: &protocol.FieldInfo{
							FieldName: "vec_int",
							FieldId:   2,
							FieldType: protocol.EnumFieldType_FieldTypeIntVec,
							VecInt:    []uint32{4},
						},
						UpdateType: protocol.EnumUpdateType_UpdateTypeAppend,
					},
				},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{
					{
						FieldName: "vec_int",
						FieldId:   2,
						FieldType: protocol.EnumFieldType_FieldTypeIntVec,
						OldVecInt: []uint32{1, 2, 3},
						NewVecInt: []uint32{1, 2, 3, 4},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "测试hbase存储set接口-3",
			fields: fields{
				proxy:      proxy,
				familyName: "vv",
			},
			args: args{
				id: "testkey3",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "tt",
						FieldId:   9,
						FieldType: protocol.EnumFieldType_FieldTypeMapKV,
						MapVal: map[string]*protocol.MapValue{
							"key": {
								Type:     protocol.EnumFieldType_FieldTypeStr,
								StrValue: "val",
							},
						},
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName: "tt",
					FieldId:   9,
					FieldType: protocol.EnumFieldType_FieldTypeMapKV,
					OldMapVal: nil,
					NewMapVal: map[string]*protocol.MapValue{
						"key": {
							Type:     protocol.EnumFieldType_FieldTypeStr,
							StrValue: "val",
						},
					},
				}},
			},
			wantErr: false,
		},
		{
			name: "测试hbase存储set接口-4",
			fields: fields{
				proxy:      proxy,
				familyName: "vv",
			},
			args: args{
				id: "testkey4",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "tt_map",
						FieldId:   9,
						FieldType: protocol.EnumFieldType_FieldTypeMapKV,
						MapVal: map[string]*protocol.MapValue{
							"key": {
								Type:     protocol.EnumFieldType_FieldTypeStr,
								StrValue: "val2",
							},
						},
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeAppend,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName: "tt_map",
					FieldId:   9,
					FieldType: protocol.EnumFieldType_FieldTypeMapKV,
					OldMapVal: nil,
					NewMapVal: map[string]*protocol.MapValue{
						"key": {
							Type:     protocol.EnumFieldType_FieldTypeStr,
							StrValue: "val2",
						},
					},
				}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &HbaseObj{
				isGetAll:     tt.fields.isGetAll,
				data:         tt.fields.data,
				Ctx:          tt.fields.Ctx,
				proxy:        tt.fields.proxy,
				clusterName:  tt.fields.Name,
				DatasourceID: tt.fields.DatasourceID,
				fieldList:    tt.fields.fieldList,
				infos:        tt.fields.infos,
				tableName:    tt.fields.tableName,
				familyName:   tt.fields.familyName,
			}
			got, err := h.SetFieldInfos(tt.args.id, tt.args.baseFieldIDs, tt.args.fieldList, tt.args.rsp)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetFieldInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetFieldInfos() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func mockHBase1(proxy *mockhbase.MockClient) {
	family5 := map[string][]string{
		"vv": {"tt_maplist"},
	}
	list5 := []string{"a", "b", "c"}
	mJson5 := simplejson.New()
	mJson5.Set("key", list5)
	jData5, _ := mJson5.Encode()
	values5 := map[string]map[string][]byte{
		"vv": {
			"tt_maplist": jData5,
		},
	}
	ret5 := &hrpc.Result{
		Cells:   nil,
		Stale:   false,
		Partial: false,
		Exists:  nil,
	}
	proxy.EXPECT().Get(gomock.Any(), "", "testkey5", family5).Return(ret5, nil).AnyTimes()
	proxy.EXPECT().Put(gomock.Any(), "", "testkey5", values5).Return(nil, nil).AnyTimes()

	family6 := map[string][]string{
		"vv": {"tt_maplist2"},
	}
	list6 := []string{"a", "b", "c", "d", "e", "f"}
	mJson6 := simplejson.New()
	mJson6.Set("key", list6)
	jData6, _ := mJson6.Encode()
	values6 := map[string]map[string][]byte{
		"vv": {
			"tt_maplist2": jData6,
		},
	}
	ret6 := &hrpc.Result{
		Cells: []*hrpc.Cell{
			{
				Row:       []byte("testkey6"),
				Qualifier: []byte("tt_maplist2"),
				Value:     jData5,
			},
		},
		Stale:   false,
		Partial: false,
		Exists:  nil,
	}
	proxy.EXPECT().Get(gomock.Any(), "", "testkey6", family6).Return(ret6, nil).AnyTimes()
	proxy.EXPECT().Put(gomock.Any(), "", "testkey6", values6).Return(nil, nil).AnyTimes()
}

func TestHbaseObj_SetFieldInfos2(t *testing.T) {
	type fields struct {
		isGetAll     bool
		data         map[string][]byte
		Ctx          context.Context
		proxy        hbase.Client
		Name         string
		DatasourceID int32
		fieldList    []*protocol.UpdateFieldInfo
		infos        map[uint32]*protocol.ModifyFieldInfo
		tableName    string
		familyName   string
	}
	type args struct {
		id           string
		baseFieldIDs []*protocol.FieldInfo
		fieldList    []*protocol.UpdateFieldInfo
		rsp          *adaptor.SetFieldInfosResponse
	}
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// mock hbase proxy
	proxy := mockhbase.NewMockClient(ctrl)
	stub := gostub.Stub(&hbase.NewClientProxy, func(name string, opts ...client.Option) hbase.Client {
		return proxy
	})
	defer stub.Reset()
	mockHBase1(proxy)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *model.ModifyInfo
		wantErr bool
	}{
		{
			name: "测试hbase存储set接口-5",
			fields: fields{
				proxy:      proxy,
				familyName: "vv",
			},
			args: args{
				id: "testkey5",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "tt_maplist",
						FieldId:   9,
						FieldType: protocol.EnumFieldType_FieldTypeMapKList,
						MapVal: map[string]*protocol.MapValue{
							"key": {
								Type:   protocol.EnumFieldType_FieldTypeSet,
								VecStr: []string{"a", "b", "c"},
							},
						},
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName: "tt_maplist",
					FieldId:   9,
					FieldType: protocol.EnumFieldType_FieldTypeMapKList,
					OldMapVal: nil,
					NewMapVal: map[string]*protocol.MapValue{
						"key": {
							Type:   protocol.EnumFieldType_FieldTypeSet,
							VecStr: []string{"a", "b", "c"},
						},
					},
				}},
			},
			wantErr: false,
		},
		{
			name: "测试hbase存储set接口-6",
			fields: fields{
				proxy:      proxy,
				familyName: "vv",
			},
			args: args{
				id: "testkey6",
				fieldList: []*protocol.UpdateFieldInfo{{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "tt_maplist2",
						FieldId:   9,
						FieldType: protocol.EnumFieldType_FieldTypeMapKList,
						MapVal: map[string]*protocol.MapValue{
							"key": {
								Type:   protocol.EnumFieldType_FieldTypeSet,
								VecStr: []string{"d", "e", "f"},
							},
						},
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeAppend,
				}},
				rsp: &adaptor.SetFieldInfosResponse{RetInfo: logic.NewRetInfo()},
			},
			want: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos: []*protocol.ModifyFieldInfo{{
					FieldName: "tt_maplist",
					FieldId:   9,
					FieldType: protocol.EnumFieldType_FieldTypeMapKList,
					OldMapVal: map[string]*protocol.MapValue{
						"key": {
							Type:   protocol.EnumFieldType_FieldTypeSet,
							VecStr: []string{"a", "b", "c"},
						},
					},
					NewMapVal: map[string]*protocol.MapValue{
						"key": {
							Type:   protocol.EnumFieldType_FieldTypeSet,
							VecStr: []string{"a", "b", "c", "d", "e", "f"},
						},
					},
				}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &HbaseObj{
				isGetAll:     tt.fields.isGetAll,
				data:         tt.fields.data,
				Ctx:          tt.fields.Ctx,
				proxy:        tt.fields.proxy,
				clusterName:  tt.fields.Name,
				DatasourceID: tt.fields.DatasourceID,
				fieldList:    tt.fields.fieldList,
				infos:        tt.fields.infos,
				tableName:    tt.fields.tableName,
				familyName:   tt.fields.familyName,
			}
			got, err := h.SetFieldInfos(tt.args.id, tt.args.baseFieldIDs, tt.args.fieldList, tt.args.rsp)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetFieldInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got.Infos[0].OldMapVal, tt.want.Infos[0].OldMapVal) {
				t.Errorf("SetFieldInfos() got = %v, want %v", got.Infos[0].OldMapVal, tt.want.Infos[0].OldMapVal)
			}

			if !reflect.DeepEqual(got.Infos[0].NewMapVal["key"].VecStr, tt.want.Infos[0].NewMapVal["key"].VecStr) {
				t.Errorf("SetFieldInfos() got = %v, want %v", got.Infos[0].NewMapVal, tt.want.Infos[0].NewMapVal)
			}
		})
	}
}

func TestHbaseObj_BatchGetFields(t *testing.T) {
	type fields struct {
		isGetAll     bool
		data         map[string][]byte
		Ctx          context.Context
		proxy        hbase.Client
		Name         string
		DatasourceID int32
		fieldList    []*protocol.UpdateFieldInfo
		infos        map[uint32]*protocol.ModifyFieldInfo
		tableName    string
		familyName   string
	}
	type args struct {
		datasetID int32
		ids       []string
		fields    []string
		in3       map[string][]string
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// mock hbase proxy
	proxy := mockhbase.NewMockClient(ctrl)
	stub := gostub.Stub(&hbase.NewClientProxy, func(name string, opts ...client.Option) hbase.Client {
		return proxy
	})
	defer stub.Reset()

	// 创建模拟缓存
	mockCache := &MockCache{
		fieldInfoMap: map[string]*item.FieldInfo{
			"title": {FieldName: "title", FieldType: 1, FieldID: 9},
		},
	}

	family1 := map[string][]string{
		"vv": {"title"},
	}

	ret1 := &hrpc.Result{
		Cells: []*hrpc.Cell{{
			Row:       []byte("testkey1"),
			Qualifier: []byte("title"),
			Value:     []byte("测试标题"),
		}},
	}
	proxy.EXPECT().Get(gomock.Any(), "", "testkey1", family1).Return(ret1, nil).AnyTimes()

	res1 := make(map[string]*protocol.FieldInfo)
	res1["title"] = &protocol.FieldInfo{
		FieldName: "title",
		FieldId:   9,
		FieldType: protocol.EnumFieldType_FieldTypeStr,
		StrValue:  "测试标题",
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *model.BatchGetResponse
		wantErr bool
	}{
		{
			name: "测试hbase存储batchget接口-1",
			fields: fields{
				proxy:      proxy,
				familyName: "vv",
			},
			args: args{
				ids:    []string{"testkey1"},
				fields: []string{"title"},
			},
			want: &model.BatchGetResponse{
				DocInfos: []*protocol.DocInfo{{
					Id:     "testkey1",
					Fields: res1,
				}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hbaseObj := &HbaseObj{
				isGetAll:     tt.fields.isGetAll,
				data:         tt.fields.data,
				Ctx:          tt.fields.Ctx,
				proxy:        tt.fields.proxy,
				clusterName:  tt.fields.Name,
				DatasourceID: tt.fields.DatasourceID,
				fieldList:    tt.fields.fieldList,
				infos:        tt.fields.infos,
				tableName:    tt.fields.tableName,
				familyName:   tt.fields.familyName,
			}

			// 创建可测试的对象并注入模拟缓存
			h := &TestableHbaseObj{
				HbaseObj:       hbaseObj,
				cacheInterface: mockCache,
			}

			got, err := h.BatchGetFields(tt.args.datasetID, tt.args.ids, tt.args.fields, tt.args.in3)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetFields() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.DocInfos, tt.want.DocInfos) {
				t.Errorf("BatchGetFields() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestHbaseObj_hbaseData2FieldInfo(t *testing.T) {
	type fields struct {
		isGetAll     bool
		data         map[string][]byte
		Ctx          context.Context
		proxy        hbase.Client
		clusterName  string
		tableName    string
		familyName   string
		DatasourceID int32
		DatasetID    int32
		fieldList    []*protocol.UpdateFieldInfo
		infos        map[uint32]*protocol.ModifyFieldInfo
	}
	type args struct {
		fieldName string
		value     []byte
	}

	// 创建模拟缓存
	mockCache := &MockCache{
		fieldInfoMap: map[string]*item.FieldInfo{
			"title":    {FieldName: "title", FieldType: 1, FieldID: 9},
			"vec_int":  {FieldName: "vec_int", FieldType: 2, FieldID: 10},
			"vec_str":  {FieldName: "vec_str", FieldType: 3, FieldID: 11},
			"mapkv":    {FieldName: "mapkv", FieldType: 4, FieldID: 12},
			"mapklist": {FieldName: "mapklist", FieldType: 5, FieldID: 13},
		},
	}

	mJson := simplejson.New()
	mJson.Set("key", "val")
	jMapkv, _ := mJson.Encode()

	list := []string{"a", "b", "c"}
	mJson2 := simplejson.New()
	mJson2.Set("key", list)
	jMapklist, _ := mJson2.Encode()

	tests := []struct {
		name   string
		fields fields
		args   args
		want   *protocol.FieldInfo
	}{
		{
			name: "hbaseData2FieldInfo-string",
			args: args{
				fieldName: "title",
				value:     []byte("测试标题"),
			},
			want: &protocol.FieldInfo{
				FieldName: "title",
				FieldId:   9,
				FieldType: protocol.EnumFieldType_FieldTypeStr,
				StrValue:  "测试标题",
			},
		},
		{
			name: "hbaseData2FieldInfo-vecint",
			args: args{
				fieldName: "vec_int",
				value:     []byte("1+2+3"),
			},
			want: &protocol.FieldInfo{
				FieldName: "vec_int",
				FieldId:   10,
				FieldType: protocol.EnumFieldType_FieldTypeIntVec,
				VecInt:    []uint32{}, // 简化处理，返回空数组
			},
		},
		{
			name: "hbaseData2FieldInfo-vecstr",
			args: args{
				fieldName: "vec_str",
				value:     []byte("a+b+c"),
			},
			want: &protocol.FieldInfo{
				FieldName: "vec_str",
				FieldId:   11,
				FieldType: protocol.EnumFieldType_FieldTypeSet,
				VecStr:    []string{}, // 简化处理，返回空数组
			},
		},
		{
			name: "hbaseData2FieldInfo-mapkv",
			args: args{
				fieldName: "mapkv",
				value:     jMapkv,
			},
			want: &protocol.FieldInfo{
				FieldName: "mapkv",
				FieldId:   12,
				FieldType: protocol.EnumFieldType_FieldTypeMapKV,
				StrValue:  "{\"key\":\"val\"}", // 期望字符串值
			},
		},
		{
			name: "hbaseData2FieldInfo-mapklist",
			args: args{
				fieldName: "mapklist",
				value:     jMapklist,
			},
			want: &protocol.FieldInfo{
				FieldName: "mapklist",
				FieldId:   13,
				FieldType: protocol.EnumFieldType_FieldTypeMapKList,
				StrValue:  "{\"key\":[\"a\",\"b\",\"c\"]}", // 期望字符串值
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hbaseObj := &HbaseObj{
				isGetAll:     tt.fields.isGetAll,
				data:         tt.fields.data,
				Ctx:          tt.fields.Ctx,
				proxy:        tt.fields.proxy,
				clusterName:  tt.fields.clusterName,
				tableName:    tt.fields.tableName,
				familyName:   tt.fields.familyName,
				DatasourceID: tt.fields.DatasourceID,
				DatasetID:    tt.fields.DatasetID,
				fieldList:    tt.fields.fieldList,
				infos:        tt.fields.infos,
			}

			// 创建可测试的对象并注入模拟缓存
			h := &TestableHbaseObj{
				HbaseObj:       hbaseObj,
				cacheInterface: mockCache,
			}

			if got := h.hbaseData2FieldInfo(tt.args.fieldName, tt.args.value); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("hbaseData2FieldInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}
