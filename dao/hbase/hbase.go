// Package hbase hbase相关逻辑
package hbase

import (
	"context"
	"encoding/json"
	"fmt"
	"unsafe"

	"git.code.oa.com/trpc-go/trpc-database/hbase"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"

	"github.com/bitly/go-simplejson"
	"github.com/tsuna/gohbase/hrpc"
)

// HbaseObj hbase存储对象
type HbaseObj struct {
	// isGetAll 是否获取所有字段
	isGetAll bool
	// data 当前字段值
	data map[string][]byte
	// Ctx 上下文
	Ctx context.Context
	// proxy hbase客户端
	proxy hbase.Client
	// clusterName hbase集群名
	clusterName string
	// tableName 表名
	tableName string
	// familyName 列族名
	familyName string
	// DatasourceID 数据源ID
	DatasourceID int32
	// DatasetID 数据集ID
	DatasetID int32
	// fieldList 更新字段列表
	fieldList []*protocol.UpdateFieldInfo
	// infos 变更消息队列，key为fieldID
	infos map[uint32]*protocol.ModifyFieldInfo
}

// RouteRuleFunc 实现路由接口
func (h *HbaseObj) RouteRuleFunc(key, id, rule string) string {
	// hbase场景目前只需要支持直连配置
	switch key {
	case inf.NoneFunction:
		h.clusterName, h.tableName, h.familyName = inf.NoneHbaseFunc(rule)
	default:
		return ""
	}
	// 以vid为hash值，每次只传入单一id
	return id
}

// GetConn 实现连接接口
func (h *HbaseObj) GetConn(connInfo, _, extraInfo string) error {
	log.Debugf("connInfo is %s, extraInfo is %s", connInfo, extraInfo)
	h.proxy = hbase.NewClientProxy("trpc.storage_service.adaptor_layer.hbase",
		client.WithTarget(connInfo+extraInfo))

	return nil
}

// GetKey 返回设备信息表key值
func (h *HbaseObj) GetKey() string {
	return h.clusterName
}

func (h *HbaseObj) buildHbaseReqMap(cols []string) map[string][]string {
	if h.isGetAll {
		return nil
	}
	return map[string][]string{h.familyName: cols}
}

// getFieldsCurVal 获取当前值
func (h *HbaseObj) getFieldsCurVal(id string, base []*protocol.FieldInfo) error {
	// 获取旧值(base+fieldList)
	cols := make([]string, 0, len(base)+len(h.fieldList))
	for _, f := range base {
		cols = append(cols, f.FieldName)
	}
	for _, f := range h.fieldList {
		cols = append(cols, f.FieldInfo.FieldName)
	}
	args := h.buildHbaseReqMap(cols)
	if err := h.getFieldsVal(id, args); err != nil {
		return err
	}
	return nil
}

// CurVal 实现接口CurVal
func (h *HbaseObj) CurVal(field *protocol.FieldInfo) (*tool.FieldVal, bool) {
	val, ok := h.data[field.FieldName]
	return &tool.FieldVal{TxtVal: bytesToString(val)}, ok
}

// updateFieldVal 更新字段数据
func (h *HbaseObj) updateFieldVal(id string, failList map[string]protocol.EnumMediaErrorCode) error {
	args := h.buildUpdateArgs(failList)
	if len(args) == 0 {
		log.Debugf("id:%s no need to update.", id)
		return nil
	}
	log.Debugf("args is %s.", args)
	values := map[string]map[string][]byte{h.familyName: args}
	// judge err
	_, err := h.proxy.Put(h.Ctx, h.tableName, id, values)
	if err != nil {
		log.Errorf("Fail to put data to hbase, err is %v.", err)
		return errs.New(int(protocol.EnumMediaErrorCode_RetFailUpdate), err.Error())
	}
	return nil
}

// NOCC:CCN_threshold(设计如此:)
func (h *HbaseObj) buildHbaseInsertValue(update *protocol.UpdateFieldInfo, cur []byte) (*model.HbaseFieldVal,
	*protocol.ModifyFieldInfo, error,
) {
	var (
		oldVal, newVal model.HbaseFieldVal
		ok             bool
		err            error
	)
	switch update.GetFieldInfo().GetFieldType() {
	case protocol.EnumFieldType_FieldTypeStr:
		oldVal.TxtVal = bytesToString(cur)
		newVal.TxtVal, ok = logic.BuildStrFieldOpStr(update.UpdateType, oldVal.TxtVal, update.FieldInfo.StrValue)
	case protocol.EnumFieldType_FieldTypeIntVec:
		oldVal.TxtVal = bytesToString(cur)
		newVal.TxtVal, ok = logic.BuildVecFieldOpStr(update.UpdateType, oldVal.TxtVal, update.FieldInfo.VecInt)
	case protocol.EnumFieldType_FieldTypeSet:
		oldVal.TxtVal = bytesToString(cur)
		newVal.TxtVal, ok, err = logic.BuildSetFieldOpStr(update.UpdateType, int(update.Pos), "+", oldVal.TxtVal,
			update.FieldInfo.VecStr)
	case protocol.EnumFieldType_FieldTypeMapKV:
		oldVal.MapKVVal = getOldKVMap(cur)
		newVal.MapKVVal, ok, err = h.getNewKVMap(update, cur)
	case protocol.EnumFieldType_FieldTypeMapKList:
		oldVal.MapKListVal = getOldKListMap(cur)
		newVal.MapKListVal, ok, err = h.getNewKListMap(update, cur)
	default:
	}

	var modifyInfo *protocol.ModifyFieldInfo
	if ok {
		modifyInfo = model.NewHbaseModifyFieldInfo(update.FieldInfo, &oldVal, &newVal)
	}
	return &newVal, modifyInfo, err
}

func (h *HbaseObj) buildUpdateArgs(failList map[string]protocol.EnumMediaErrorCode) map[string][]byte {
	args := make(map[string][]byte)

	for _, f := range h.fieldList {
		fieldID := f.FieldInfo.FieldId
		fieldName := f.FieldInfo.FieldName

		val, modifyInfo, err := h.buildHbaseInsertValue(f, h.data[fieldName])
		if err != nil {
			failList[f.FieldInfo.FieldName] = protocol.EnumMediaErrorCode_RetInvalidOpType
			continue
		}
		if modifyInfo != nil {
			h.infos[fieldID] = modifyInfo
			args[fieldName] = h.getArgValue(val, modifyInfo)
		}
	}
	return args
}

func (h *HbaseObj) getArgValue(val *model.HbaseFieldVal, mInfo *protocol.ModifyFieldInfo) []byte {
	switch mInfo.FieldType {
	case protocol.EnumFieldType_FieldTypeMapKV, protocol.EnumFieldType_FieldTypeMapKList:
		return h.insertMapValue(mInfo)
	default:
		return []byte(val.TxtVal)
	}
}

func getOldKVMap(byteVal []byte) map[string]string {
	val := make(map[string]string)
	_ = json.Unmarshal(byteVal, &val)
	return val
}

func (h *HbaseObj) getNewKVMap(update *protocol.UpdateFieldInfo, byteVal []byte) (map[string]string, bool, error) {
	val := getOldKVMap(byteVal)
	for k, v := range update.FieldInfo.MapVal {
		cur := val[k]
		newVal, ok := logic.BuildStrFieldOpStr(update.UpdateType, cur, v.StrValue)
		if ok {
			val[k] = newVal
		}
	}

	return val, true, nil
}

func getOldKListMap(byteVal []byte) map[string][]string {
	val := make(map[string][]string)
	_ = json.Unmarshal(byteVal, &val)
	return val
}

func (h *HbaseObj) getNewKListMap(update *protocol.UpdateFieldInfo, byteVal []byte) (map[string][]string, bool, error) {
	val := getOldKListMap(byteVal)
	for k, v := range update.FieldInfo.MapVal {
		cur := val[k]
		newVal, err := h.buildVecFieldOpStr(update.UpdateType, cur, v.VecStr)
		if err != nil {
			return nil, false, err
		}
		val[k] = newVal
	}

	return val, true, nil
}

func (h *HbaseObj) buildVecFieldOpStr(ty protocol.EnumUpdateType, curVal, newVal []string) ([]string, error) {
	newVal = logic.RemoveRepeat(newVal)
	var result []string
	switch ty {
	case protocol.EnumUpdateType_UpdateTypeSet:
		result = newVal
	case protocol.EnumUpdateType_UpdateTypeDel:
		result = logic.DeleteStrSliceElms(curVal, newVal)
	case protocol.EnumUpdateType_UpdateTypeAppend:
		result = append(curVal, newVal...)
	default:
		return nil, fmt.Errorf("not support this update type:%d", ty)
	}
	return result, nil
}

func (h *HbaseObj) insertMapValue(mInfo *protocol.ModifyFieldInfo) []byte {
	mJson := simplejson.New()
	for km, vm := range mInfo.NewMapVal {
		if vm.Type == protocol.EnumFieldType_FieldTypeStr {
			mJson.Set(km, vm.StrValue)
		} else if vm.Type == protocol.EnumFieldType_FieldTypeSet {
			mJson.Set(km, vm.VecStr)
		} else {
			log.Errorf("unkown map type:%d", vm.Type)
			continue
		}
	}
	jData, _ := mJson.Encode()
	return jData
}

// SetFieldInfos 统一设置value接口
func (h *HbaseObj) SetFieldInfos(id string, baseFieldIDs []*protocol.FieldInfo, fieldList []*protocol.UpdateFieldInfo,
	rsp *adaptor.SetFieldInfosResponse,
) (*model.ModifyInfo, error) {
	// 初始化hbase对象内部数据
	h.infos = make(map[uint32]*protocol.ModifyFieldInfo)
	h.data = make(map[string][]byte)
	h.fieldList = fieldList

	// 根据修改字段获取旧值
	if err := h.getFieldsCurVal(id, baseFieldIDs); err != nil {
		// 读取旧值失败，统一批量修改failList
		log.Errorf("Fail to get fields from hbase, err is %v.", err)
		logic.BatchHandleFailList(fieldList, rsp.RetInfo.FailList, protocol.EnumMediaErrorCode_RetFailSelect)
		return nil, err
	}
	if err := h.updateFieldVal(id, rsp.RetInfo.FailList); err != nil {
		// 更新新值失败，统一批量修改failList
		log.Errorf("Fail to update fields in hbase, err is %v.", err)
		logic.BatchHandleFailList(fieldList, rsp.RetInfo.FailList, protocol.EnumMediaErrorCode_RetFailUpdate)
		return nil, err
	}
	// 生成数据变更消息
	return model.BuildModifyInfo(baseFieldIDs, h.infos, h), nil
}

func (h *HbaseObj) convertHbaseForm(result *hrpc.Result) {
	for _, c := range result.Cells {
		log.Debugf("id:%s, column:%s, value:%s\n", c.Row, c.Qualifier, c.Value)
		h.data[bytesToString(c.Qualifier)] = c.Value
	}
}

// getFieldsVal 读取hbase字段数据通用接口
func (h *HbaseObj) getFieldsVal(rowKey string, args map[string][]string) error {
	log.Debugf("args of get is %+v.", args)
	// judge err
	result, err := h.proxy.Get(h.Ctx, h.tableName, rowKey, args)
	if err != nil {
		log.Errorf("Fail to get data from hbase, vid is %s, err is %v.", rowKey, err)
		return errs.New(int(protocol.EnumMediaErrorCode_RetFailSelect), err.Error())
	}
	h.convertHbaseForm(result)
	return nil
}

// BatchGetFields 统一批量获取接口
func (h *HbaseObj) BatchGetFields(datasetID int32, ids, fields []string, _ map[string][]string,
) (*model.BatchGetResponse, error) {
	h.data = make(map[string][]byte)
	h.DatasetID = datasetID
	// 以vid为hash值，每次只传入单一id
	id := ids[0]
	mapFieldInfo, failList, err := h.getHbaseInfoByID(id, fields)
	if err != nil {
		return nil, err
	}

	resDocs := &protocol.DocInfo{
		Id:     id,
		Fields: mapFieldInfo,
	}
	var rsp model.BatchGetResponse
	rsp.DocInfos = append(rsp.DocInfos, resDocs)
	rsp.FailList = failList
	return &rsp, nil
}

func (h *HbaseObj) getHbaseInfoByID(id string, fields []string) (map[string]*protocol.FieldInfo,
	map[string]string, error,
) {
	mapFail := make(map[string]string)
	if len(fields) == 1 && fields[0] == "*" {
		h.isGetAll = true
	}
	args := h.buildHbaseReqMap(fields)
	log.Debugf("args:%v", args)
	if err := h.getFieldsVal(id, args); err != nil {
		mapFail[id] = err.Error()
		return nil, mapFail, err
	}
	res := make(map[string]*protocol.FieldInfo)
	for k, v := range h.data {
		res[k] = h.hbaseData2FieldInfo(k, v)
	}
	return res, mapFail, nil
}

func (h *HbaseObj) hbaseData2FieldInfo(fieldName string, value []byte) *protocol.FieldInfo {
	res := &protocol.FieldInfo{
		FieldName: fieldName,
	}
	fieldInfoMap := cache.GetFieldInfoByName(fieldName, h.DatasetID)
	if fieldInfoMap == nil {
		return res
	}
	res.FieldType = protocol.EnumFieldType(fieldInfoMap.FieldType)
	res.FieldId = fieldInfoMap.FieldID
	switch fieldInfoMap.FieldType {
	case int32(protocol.EnumFieldType_FieldTypeStr):
		res.StrValue = bytesToString(value)
	case int32(protocol.EnumFieldType_FieldTypeIntVec):
		res.VecInt = tool.ConvertStrToUInt32Vec(bytesToString(value), "+")
	case int32(protocol.EnumFieldType_FieldTypeSet):
		res.VecStr = tool.ConvertStrToStrVec(bytesToString(value), "+")
	case int32(protocol.EnumFieldType_FieldTypeMapKV):
		res.MapVal = byteToMapKV(value)
	case int32(protocol.EnumFieldType_FieldTypeMapKList):
		res.MapVal = byteToMapKList(value)
	default:
		res.StrValue = bytesToString(value)
	}
	return res
}

// InsertFieldInfos 统一插入接口
func (h *HbaseObj) InsertFieldInfos(_ string, _ int64, _ []*protocol.UpdateFieldInfo) (*model.ModifyInfo, error) {
	return nil, nil
}

// GetFieldInfos ...
func (h *HbaseObj) GetFieldInfos(_ []string, _ *inf.FieldKey, _ *adaptor.GetFieldInfosResponse) error {
	return nil
}

func bytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

func byteToMapKV(byteValue []byte) map[string]*protocol.MapValue {
	mapKV := getOldKVMap(byteValue)
	return model.NewKVMapVal(mapKV)
}

func byteToMapKList(byteValue []byte) map[string]*protocol.MapValue {
	mapKList := getOldKListMap(byteValue)
	return model.NewKListMapVal(mapKList)
}
