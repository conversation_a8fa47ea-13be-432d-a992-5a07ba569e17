package dao

import (
	"testing"

	tRpcErrs "git.code.oa.com/trpc-go/trpc-go/errs"
	event "git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/video_media/media_event_hub/processor/config"
	"git.woa.com/video_media/media_event_hub/processor/entity"
)

// Mock implementations for testing Factory

// testError 用于测试的错误类型
type testError struct {
	message string
}

func (e *testError) Error() string {
	return e.message
}

func TestFactory_GetDataAccessor(t *testing.T) {
	tests := []struct {
		name       string
		bridgeID   int64
		viewType   event.EnumViewType
		config     *config.Config
		wantErr    bool
		wantNil    bool
		expectType string
		errCode    int
	}{
		{
			name:     "获取UNIVERSAL_VIEW数据访问器成功",
			bridgeID: 1,
			viewType: event.EnumViewType_UNIVERSAL_VIEW,
			config: &config.Config{
				DaoAuth: map[int64]map[string]config.AccessInfo{
					1: {
						"UNIVERSAL_VIEW": config.AccessInfo{
							AppID:   "test_app_id",
							AppKey:  "test_app_key",
							SetName: "test_set",
						},
					},
				},
			},
			wantErr:    false,
			wantNil:    false,
			expectType: "*dao.Universal",
		},
		{
			name:     "获取UNION_VIEW数据访问器成功",
			bridgeID: 1,
			viewType: event.EnumViewType_UNION_VIEW,
			config: &config.Config{
				DaoAuth: map[int64]map[string]config.AccessInfo{
					1: {
						"UNION_VIEW": config.AccessInfo{
							AppID:   "test_app_id",
							AppKey:  "test_app_key",
							SetName: "test_set",
						},
					},
				},
			},
			wantErr:    false,
			wantNil:    false,
			expectType: "*dao.Union",
		},
		{
			name:     "不支持的视图类型",
			bridgeID: 1,
			viewType: event.EnumViewType(999), // 无效的视图类型
			config: &config.Config{
				DaoAuth: map[int64]map[string]config.AccessInfo{
					1: {},
				},
			},
			wantErr: true,
			wantNil: true,
			errCode: entity.ErrCodeUnsupportedType,
		},
		{
			name:     "获取鉴权信息失败",
			bridgeID: 999, // 不存在的bridgeID
			viewType: event.EnumViewType_UNIVERSAL_VIEW,
			config: &config.Config{
				DaoAuth: map[int64]map[string]config.AccessInfo{},
			},
			wantErr: true,
			wantNil: true,
			errCode: entity.ErrCodeConfigFail,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			factory, err := NewFactory(tt.config)
			if err != nil {
				t.Fatalf("NewFactory() error = %v", err)
			}

			accessor, err := factory.GetDataAccessor(tt.viewType, tt.bridgeID)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetDataAccessor() error = %v, wantErr %v", err, tt.wantErr)
			}

			if (accessor == nil) != tt.wantNil {
				t.Errorf("GetDataAccessor() accessor = %v, wantNil %v", accessor, tt.wantNil)
			}

			// 验证错误码
			if tt.wantErr && tt.errCode != 0 {
				if tRpcErr, ok := err.(*tRpcErrs.Error); ok {
					if int(tRpcErr.Code) != tt.errCode {
						t.Errorf("Expected error code %d, got %d", tt.errCode, tRpcErr.Code)
					}
				} else {
					t.Errorf("Expected tRPC error, got %T", err)
				}
			}

			// 验证返回的数据访问器类型
			if !tt.wantErr && !tt.wantNil && accessor != nil {
				// 这里我们可以进一步验证返回的具体类型
				switch tt.viewType {
				case event.EnumViewType_UNIVERSAL_VIEW:
					if _, ok := accessor.(*Universal); !ok {
						t.Errorf("Expected *Universal, got %T", accessor)
					}
				case event.EnumViewType_UNION_VIEW:
					if _, ok := accessor.(*Union); !ok {
						t.Errorf("Expected *Union, got %T", accessor)
					}
				}
			}
		})
	}
}

func TestNewFactory(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.Config
		wantErr bool
		wantNil bool
		errCode int
	}{
		{
			name: "创建工厂成功",
			config: &config.Config{
				DaoAuth: map[int64]map[string]config.AccessInfo{},
			},
			wantErr: false,
			wantNil: false,
		},
		{
			name:    "配置为空",
			config:  nil,
			wantErr: true,
			wantNil: true,
			errCode: entity.ErrCodeConfigFail,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			factory, err := NewFactory(tt.config)

			if (err != nil) != tt.wantErr {
				t.Errorf("NewFactory() error = %v, wantErr %v", err, tt.wantErr)
			}

			if (factory == nil) != tt.wantNil {
				t.Errorf("NewFactory() factory = %v, wantNil %v", factory, tt.wantNil)
			}

			// 验证错误码
			if tt.wantErr && tt.errCode != 0 {
				if tRpcErr, ok := err.(*tRpcErrs.Error); ok {
					if int(tRpcErr.Code) != tt.errCode {
						t.Errorf("Expected error code %d, got %d", tt.errCode, tRpcErr.Code)
					}
				} else {
					t.Errorf("Expected tRPC error, got %T", err)
				}
			}

			if factory != nil {
				if factory.Config != tt.config {
					t.Errorf("NewFactory() config not set correctly")
				}
			}
		})
	}
}

// 测试真实场景下的初始化连接
func TestFactory_DataAccessor_Connect(t *testing.T) {
	tests := []struct {
		name      string
		viewType  event.EnumViewType
		authInfo  *config.AccessInfo
		wantErr   bool
		setupMock bool
	}{
		{
			name:     "Universal连接初始化成功",
			viewType: event.EnumViewType_UNIVERSAL_VIEW,
			authInfo: &config.AccessInfo{
				AppID:     "test_app_id",
				AppKey:    "test_app_key",
				SetName:   "test_set",
				Namespace: "test_namespace",
			},
			wantErr:   false,
			setupMock: true,
		},
		{
			name:     "Union连接初始化成功",
			viewType: event.EnumViewType_UNION_VIEW,
			authInfo: &config.AccessInfo{
				AppID:   "test_app_id",
				AppKey:  "test_app_key",
				SetName: "test_set",
			},
			wantErr:   false,
			setupMock: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var accessor DataAccessor
			var err error

			// 创建对应类型的数据访问器
			switch tt.viewType {
			case event.EnumViewType_UNIVERSAL_VIEW:
				accessor = &Universal{
					AuthInfo: tt.authInfo,
				}
			case event.EnumViewType_UNION_VIEW:
				accessor = &Union{
					AuthInfo: tt.authInfo,
				}
			}

			if accessor != nil {
				err = accessor.Connect()
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("Connect() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// 测试配置解析
func TestFactory_ConfigParsing(t *testing.T) {
	tests := []struct {
		name        string
		config      *config.Config
		bridgeID    int64
		storageType string
		wantErr     bool
	}{
		{
			name: "解析配置成功",
			config: &config.Config{
				DaoAuth: map[int64]map[string]config.AccessInfo{
					1: {
						"UNIVERSAL_VIEW": config.AccessInfo{
							AppID:   "test_app_id",
							AppKey:  "test_app_key",
							SetName: "test_set",
						},
					},
				},
			},
			bridgeID:    1,
			storageType: "UNIVERSAL_VIEW",
			wantErr:     false,
		},
		{
			name: "桥接ID不存在",
			config: &config.Config{
				DaoAuth: map[int64]map[string]config.AccessInfo{},
			},
			bridgeID:    999,
			storageType: "UNIVERSAL_VIEW",
			wantErr:     true,
		},
		{
			name: "存储类型不存在",
			config: &config.Config{
				DaoAuth: map[int64]map[string]config.AccessInfo{
					1: {},
				},
			},
			bridgeID:    1,
			storageType: "INVALID_VIEW",
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			factory, err := NewFactory(tt.config)
			if err != nil {
				t.Fatalf("NewFactory() error = %v", err)
			}

			// 直接测试GetAccess方法（这是私有方法，我们通过GetDataAccessor间接测试）
			var viewType event.EnumViewType
			switch tt.storageType {
			case "UNIVERSAL_VIEW":
				viewType = event.EnumViewType_UNIVERSAL_VIEW
			case "UNION_VIEW":
				viewType = event.EnumViewType_UNION_VIEW
			default:
				// 对于无效的存储类型，我们创建一个有效的viewType，错误会在GetAccess中被捕获
				viewType = event.EnumViewType_UNIVERSAL_VIEW
			}

			_, err = factory.GetDataAccessor(viewType, tt.bridgeID)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetDataAccessor() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
