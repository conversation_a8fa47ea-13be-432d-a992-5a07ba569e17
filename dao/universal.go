package dao

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	storage_comm "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	event "git.woa.com/trpcprotocol/media_event_hub/common_event"
	access "git.woa.com/trpcprotocol/media_event_hub/sync"
	"git.woa.com/video_media/media_event_hub/processor/config"
	"git.woa.com/video_media/media_event_hub/processor/entity"
	"git.woa.com/video_media/media_event_hub/processor/utils"
	"github.com/avast/retry-go"
)

const (
	// UniversalBatchSize universal请求的批次大小，避免单次请求key过多
	UniversalBatchSize = 20
)

// Universal 媒资视图存储对象(注意：这里我们没有用统一接入平台接口，而是直接请求access层，因为统一接入平台接口没有返回字段信息)
type Universal struct {
	// PrjID 媒资存储的项目ID
	PrjID int
	// DimID 媒资存储的维度ID
	DimID int
	// proxy 媒资数据存储客户端
	proxy access.SyncClientProxy
	// AuthInfo 访问媒资数据存储的配置信息
	AuthInfo *config.AccessInfo
}

// Connect 实现连接接口
func (u *Universal) Connect() error {
	var options []client.Option
	if u.AuthInfo.SetName != "" {
		options = append(options, client.WithCalleeSetName(u.AuthInfo.SetName))
	}
	if u.AuthInfo.Namespace != "" {
		options = append(options, client.WithNamespace(u.AuthInfo.Namespace))
		options = append(options, client.WithDisableServiceRouter())
	}
	options = append(options, client.WithServiceName(access.SyncServer_ServiceDesc.ServiceName))
	u.proxy = access.NewSyncClientProxy(options...)
	return nil
}

// BatchGetFields 实现DataAccessor接口，从universal中批量获取字段
func (u *Universal) BatchGetFields(ctx context.Context, viewID string, dataIDs []string,
	fields []string, timestamps int64) ([]*event.Node, error) {
	if len(dataIDs) == 0 || len(fields) == 0 {
		log.ErrorContextf(ctx, "Empty dataIDs or fields.")
		if len(dataIDs) == 0 {
			return nil, entity.NewError(entity.ErrCodeEmptyParam, nil)
		}
		return nil, entity.NewError(entity.ErrCodeEmptyParam, nil)
	}
	log.InfoContextf(ctx, "BatchGetFields timestamps: %d; dataIDs: %v; viewID: %s", timestamps, dataIDs, viewID)

	u.PrjID, u.DimID, _ = utils.ParseViewID(viewID)

	// 如果数据量小于批次大小，直接单次请求
	if len(dataIDs) <= UniversalBatchSize {
		return u.batchGetFieldsSingle(ctx, viewID, dataIDs, fields, timestamps)
	}

	// 分批并发处理
	return u.batchGetFieldsConcurrent(ctx, viewID, dataIDs, fields, timestamps)
}

// batchGetFieldsSingle 单次请求获取字段
func (u *Universal) batchGetFieldsSingle(ctx context.Context, viewID string, dataIDs []string, fields []string, timestamps int64) ([]*event.Node, error) {
	viewKey := utils.FormatViewKey(event.EnumViewType_UNIVERSAL_VIEW, viewID)

	// 使用批量接口获取数据
	versionDataMap, err := u.batchGetMediaInfo(ctx, dataIDs, fields, timestamps)
	if err != nil {
		log.ErrorContextf(ctx, "Failed to batch get media info: %v", err)
		return nil, entity.NewError(entity.ErrCodeDataQueryFail, err)
	}
	return u.convertToNodes(ctx, viewID, viewKey, versionDataMap), nil
}

// batchGetFieldsConcurrent 并发分批获取字段
func (u *Universal) batchGetFieldsConcurrent(ctx context.Context, viewID string, dataIDs []string, fields []string, timestamps int64) ([]*event.Node, error) {
	// 分批
	var batches [][]string
	for i := 0; i < len(dataIDs); i += UniversalBatchSize {
		end := i + UniversalBatchSize
		if end > len(dataIDs) {
			end = len(dataIDs)
		}
		batches = append(batches, dataIDs[i:end])
	}

	log.InfoContextf(ctx, "BatchGetFields: total %d dataIDs split into %d batches", len(dataIDs), len(batches))

	// 并发执行各批次
	type batchResult struct {
		nodes []*event.Node
		err   error
		index int
	}

	results := make(chan batchResult, len(batches))
	var handlers []func() error
	for i, batch := range batches {
		batchIndex := i
		batchDataIDs := batch
		handlers = append(handlers, func() error {
			nodes, err := u.batchGetFieldsSingle(ctx, viewID, batchDataIDs, fields, timestamps)
			results <- batchResult{
				nodes: nodes,
				err:   err,
				index: batchIndex,
			}
			return nil // trpc.GoAndWait需要返回error，但我们通过channel处理实际错误
		})
	}

	// 执行并发任务
	_ = trpc.GoAndWait(handlers...)
	close(results)

	// 收集结果
	var allNodes []*event.Node
	var errors []error
	resultsByIndex := make(map[int][]*event.Node)

	for result := range results {
		if result.err != nil {
			errors = append(errors, result.err)
			log.ErrorContextf(ctx, "Batch %d failed: %v", result.index, result.err)
		} else {
			resultsByIndex[result.index] = result.nodes
		}
	}

	// 如果有错误，返回第一个错误
	if len(errors) > 0 {
		return nil, entity.NewError(entity.ErrCodeBatchFail, errors[0])
	}

	// 按照原始顺序合并结果
	for i := 0; i < len(batches); i++ {
		if batchNodes, exists := resultsByIndex[i]; exists {
			allNodes = append(allNodes, batchNodes...)
		}
	}

	log.InfoContextf(ctx, "BatchGetFields: successfully retrieved %d nodes from %d batches", len(allNodes), len(batches))
	return allNodes, nil
}

// convertToNodes 将Universal响应转换为节点列表
func (u *Universal) convertToNodes(ctx context.Context, viewID string, viewKey string, versionDataMap map[string]*access.VersionData) []*event.Node {
	var nodes []*event.Node

	// 处理获取到的结果
	for id, versionData := range versionDataMap {
		node := &event.Node{
			DataId: id,
			Values: make(map[string]*event.ValueContainer),
		}

		// 创建ValueContainer
		container := &event.ValueContainer{
			ViewType: event.EnumViewType_UNIVERSAL_VIEW,
			ViewId:   viewID,
			Fields:   make(map[string]*event.FieldValue),
		}

		// 填充字段值
		for _, info := range versionData.Infos {
			fieldValue := &event.FieldValue{
				FieldName: info.FieldName,
				FieldType: u.convertFieldType(info.FieldType),
			}

			// 根据字段类型设置字段值
			switch info.FieldType {
			case storage_comm.EnumFieldType_FieldTypeStr:
				fieldValue.Str = info.StrValue
			case storage_comm.EnumFieldType_FieldTypeSet:
				fieldValue.StrList = info.VecStr
			case storage_comm.EnumFieldType_FieldTypeIntVec:
				for _, iv := range info.VecInt {
					fieldValue.IntList = append(fieldValue.IntList, int64(iv))
				}
			}
			log.InfoContextf(ctx, "id:%s, fieldValue: %+v", id, fieldValue)
			container.Fields[info.FieldName] = fieldValue
		}
		node.Values[viewKey] = container
		nodes = append(nodes, node)
	}
	return nodes
}

// convertFieldType 将Universal字段类型转换为事件系统字段类型
func (u *Universal) convertFieldType(fieldType storage_comm.EnumFieldType) event.EnumFieldType {
	switch fieldType {
	case storage_comm.EnumFieldType_FieldTypeStr:
		return event.EnumFieldType_STR_FIELD
	case storage_comm.EnumFieldType_FieldTypeIntVec:
		return event.EnumFieldType_INT_VEC_FIELD
	case storage_comm.EnumFieldType_FieldTypeSet:
		return event.EnumFieldType_SET_FIELD
	case storage_comm.EnumFieldType_FieldTypeMapKV:
		return event.EnumFieldType_MAP_KV_FIELD
	case storage_comm.EnumFieldType_FieldTypeMapKList:
		return event.EnumFieldType_MAP_KLIST_FIELD
	default:
		return event.EnumFieldType_INVALID_FIELD
	}
}

// batchGetMediaInfo 批量获取媒资信息
func (u *Universal) batchGetMediaInfo(ctx context.Context, dataIDs []string, fields []string, timestamps int64) (map[string]*access.VersionData, error) {
	// 构造VersionTuple列表
	var versionTuples []*access.VersionTuple
	for _, dataID := range dataIDs {
		versionTuple := &access.VersionTuple{
			DataId: dataID,
			Fields: fields,
		}
		versionTuples = append(versionTuples, versionTuple)
	}

	// 构造GetVersionDataReq请求
	req := &access.GetVersionDataReq{
		Data:       versionTuples,
		Timestamps: timestamps,
		ProjId:     int32(u.PrjID),
		DimId:      int32(u.DimID),
	}

	// 获取重试配置，如果没有配置则使用默认值
	retryCount := u.AuthInfo.RetryCount
	retryIntervalMs := u.AuthInfo.RetryIntervalMs

	// 设置默认值
	if retryCount <= 0 {
		retryCount = 3 // 默认重试3次
	}
	if retryIntervalMs <= 0 {
		retryIntervalMs = 1000 // 默认间隔1秒
	}

	var versionDataMap map[string]*access.VersionData
	err := retry.Do(
		func() error {
			// 调用GetVersionData接口
			rsp, err := u.proxy.GetVersionData(ctx, req)
			if err != nil {
				log.ErrorContextf(ctx, "GetVersionData failed: %v", err)
				return entity.NewError(entity.ErrCodeDataQueryFail, err)
			}

			// 检查响应是否为空
			if rsp == nil || rsp.Res == nil {
				log.ErrorContextf(ctx, "Empty response from GetVersionData")
				return entity.NewError(entity.ErrCodeDataEmpty, nil)
			}

			versionDataMap = rsp.Res
			if len(versionDataMap) == 0 {
				log.ErrorContextf(ctx, "Empty version data from GetVersionData")
				return entity.NewError(entity.ErrCodeDataEmpty, nil)
			}

			// 检查是否有阻塞的数据ID
			for dataID, versionData := range versionDataMap {
				if versionData.IsBlock {
					log.WarnContextf(ctx, "Data ID %s is blocked, upstream waiting", dataID)
					return entity.NewError(entity.ErrCodeDataBlock, nil)
				}
			}
			return nil
		},
		retry.Attempts(uint(retryCount+1)), // 重试次数（包含首次尝试）
		retry.Delay(time.Duration(retryIntervalMs)*time.Millisecond),
		retry.LastErrorOnly(true), // 仅返回最后一次错误
		retry.OnRetry(func(n uint, err error) { // 每次重试的时候调用方法
			log.InfoContextf(ctx, "retry GetVersionData #%d, because got err: %s, retrying in %d ms",
				n, err.Error(), retryIntervalMs)
		}),
	)

	if err != nil {
		log.ErrorContextf(ctx, "GetVersionData failed after %d attempts: %v", retryCount+1, err)
		return nil, entity.NewError(entity.ErrCodeRetryFail, err)
	}
	log.InfoContextf(ctx, "Successfully retrieved %d version data entries", len(versionDataMap))
	return versionDataMap, nil
}
