package dao

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/model"
)

// UnshelvedVideo 下架视频信息
type UnshelvedVideo struct {
	// DataID 自增ID（数据唯一ID）
	DataID int `db:"c_id"`
	// VideoID 视频ID
	VideoID string `db:"c_vid"`
	// Covers 视频所属专辑
	Covers string `db:"c_covers"`
	// State 处理状态
	State int `db:"c_state"`
	// CreateTime 创建时间
	CreateTime time.Time `db:"c_ctime"`
	// UpdateTime 修改时间
	UpdateTime time.Time `db:"c_mtime"`
}

var proxy = mysql.NewClientProxy("trpc.mysql.video_msghub_cb.cover_videos_auto_operation")

// Insert 写入下架视频表
func (uv *UnshelvedVideo) Insert(ctx context.Context) error {
	_, err := proxy.NamedExec(ctx, fmt.Sprintf("INSERT INTO t_unshelved_videos "+
		"(c_vid, c_covers, c_ctime) VALUES (:c_vid, :c_covers, now()) "+
		"ON DUPLICATE KEY UPDATE c_covers=:c_covers, c_state=%d", model.TaskWaiting),
		[]UnshelvedVideo{*uv})
	if err != nil {
		log.ErrorContextf(ctx, "INSERT ERR:%+v", err)
		return err
	}
	return nil
}

// GetWaitingVideos 获取待处理的vid
func (uv *UnshelvedVideo) GetWaitingVideos(ctx context.Context, limit int) ([]*UnshelvedVideo, error) {
	log.InfoContextf(ctx, "GetWaitingVideos enter")
	if limit <= 0 {
		limit = 10
	}
	var infos []*UnshelvedVideo
	err := proxy.QueryToStructs(ctx, &infos, "SELECT c_id, c_vid, c_covers FROM t_unshelved_videos "+
		" WHERE (c_state!=? AND c_state<=?) AND c_ctime>= now() - INTERVAL 1 DAY order by c_ctime desc limit ?",
		model.TaskSuc, model.TaskFail*model.MaxFailNum, limit) // 对于失败的任务，在最大重试次数内，也持续重试
	if err != nil {
		return nil, err
	}
	return infos, nil
}

// GetWaitingVideosMaxTimeStamps 获取最旧未处理数据的创建时间
func (uv *UnshelvedVideo) GetWaitingVideosMaxTimeStamps(ctx context.Context) (int64, error) {
	var infos []*UnshelvedVideo
	err := proxy.QueryToStructs(ctx, &infos, "SELECT c_id, c_vid, c_ctime FROM t_unshelved_videos "+
		" WHERE c_state= ? order by c_ctime limit 1", model.TaskWaiting)
	if err != nil {
		return 0, err
	}

	maxTimeStamps := time.Now().Unix() // 使用当前时间作为默认值，而不是0；方便上层根据当前时间做上报
	if len(infos) > 0 {
		maxTimeStamps = infos[0].CreateTime.Unix()
	}
	log.InfoContextf(ctx, "GetWaitingVideosMaxTimeStamps suc:%d-%+v", maxTimeStamps, infos)
	return maxTimeStamps, nil
}

// SetTaskState 设置任务状态
func (uv *UnshelvedVideo) SetTaskState(ctx context.Context, dataID int, state int) error {
	log.InfoContextf(ctx, "SetTaskState enter")
	_, err := proxy.Exec(ctx, "UPDATE t_unshelved_videos SET c_state=?  WHERE c_id=? ", state, dataID)
	if err != nil {
		log.ErrorContextf(ctx, "SetTaskState ERR:%+v", err)
		return err
	}
	return nil
}

// SetTaskSucceed 设置任务成功
func (uv *UnshelvedVideo) SetTaskSucceed(ctx context.Context, dataID int) error {
	return uv.SetTaskState(ctx, dataID, model.TaskSuc)
}

// SetTaskFailed 设置任务失败
func (uv *UnshelvedVideo) SetTaskFailed(ctx context.Context, dataID int) error {
	log.InfoContextf(ctx, "SetTaskFailed enter")
	_, err := proxy.Exec(ctx, "UPDATE t_unshelved_videos SET c_state=c_state+?  WHERE c_vid=?",
		model.TaskFail, dataID)
	if err != nil {
		log.ErrorContextf(ctx, "SetTaskState ERR:%+v", err)
		return err
	}
	return nil
}
