package dao

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

var proxy = mysql.NewClientProxy("trpc.mysql.crawler_schedule.outer_circulation_monitor")

// CoverInfo 监控抓取的重点IP剧
type CoverInfo struct {
	CoverID                 string    `db:"c_cover_id"`                  // CoverID 专辑ID
	CoverType               int       `db:"c_cover_type"`                // CoverType 专辑大分类
	Title                   string    `db:"c_title"`                     // Title 专辑标题
	OfficeAccFlag           int       `db:"c_office_account_flag"`       // OfficeAccFlag 是否抓取IP官号
	TopicFlag               int       `db:"c_topic_flag"`                // TopicFlag 是否抓取话题
	PriorityFlag            int       `db:"c_priority_flag"`             // PriorityFlag 是否优先剧集
	IPOnlineStatus          int       `db:"c_ip_online_status"`          // IPOnlineStatus IP实时状态
	IsDeleted               int       `db:"c_is_deleted"`                // IsDeleted 是否无效删除的数据
	CoverLevel              string    `db:"c_cover_level"`               // CoverLevel 专辑等级
	CopyrightPlaybackStatus int       `db:"c_copyright_playback_status"` // CopyrightPlaybackStatus 网络播放性质（外显）
	CreateTime              time.Time `db:"c_ctime"`                     // CreateTime 创建时间
	UpdateTime              time.Time `db:"c_mtime"`                     // UpdateTime 修改时间
	Ext                     string    `db:"c_ext"`                       // c_ext 扩展字段
}

// InsertCoverInfo 写入关注重点剧集池
func (c *CoverInfo) InsertCoverInfo(ctx context.Context) error {
	if _, err := proxy.NamedExec(ctx,
		"INSERT INTO t_focus_cover_info "+
			"(c_cover_id, c_cover_type, c_title, "+
			"c_office_account_flag,c_topic_flag,c_ctime,c_ip_online_status,c_priority_flag, c_cover_level, "+
			"c_copyright_playback_status) VALUES "+
			"(:c_cover_id, :c_cover_type, :c_title,  "+
			":c_office_account_flag, :c_topic_flag, now(), :c_ip_online_status,:c_priority_flag,:c_cover_level, "+
			"c_copyright_playback_status) "+
			"ON DUPLICATE KEY UPDATE "+
			"c_office_account_flag=:c_office_account_flag, c_cover_level=:c_cover_level, "+
			"c_topic_flag=:c_topic_flag, c_ip_online_status=:c_ip_online_status, c_priority_flag=:c_priority_flag,"+
			"c_copyright_playback_status=:c_copyright_playback_status",
		[]CoverInfo{*c}); err != nil {
		log.ErrorContextf(ctx, "INSERT t_focus_cover_info ERR:%+v", err)
		return err
	}
	return nil
}

// IsExistFocusCoverLoop 专辑是否存在于重点剧池中
func (c *CoverInfo) IsExistFocusCoverLoop(ctx context.Context) (bool, error) {
	var infos []*CoverInfo
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_cover_id FROM t_focus_cover_info WHERE c_cover_id=?",
		c.CoverID); err != nil {
		return false, err
	}
	return len(infos) > 0, nil
}

// GetAllCoverInfo 获取全量重点剧信息
func (c *CoverInfo) GetAllCoverInfo(ctx context.Context) ([]*CoverInfo, error) {
	log.InfoContextf(ctx, "GetAllCoverInfo enter")
	var infos []*CoverInfo
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_cover_id,c_cover_type,c_title,c_cover_level,"+
			" c_office_account_flag,c_topic_flag,c_ip_online_status,c_priority_flag "+
			" FROM t_focus_cover_info"); err != nil {
		return nil, err
	}
	return infos, nil
}

// LongTailCoverInfo 监控抓取的长尾剧
type LongTailCoverInfo struct {
	CoverID    string    `db:"cover_id"`   // CoverID 专辑ID
	CoverType  string    `db:"cover_type"` // CoverType 专辑大分类(注意这里是中文名)
	Title      string    `db:"title"`      // Title 专辑标题
	ImpDate    int       `db:"imp_date"`   // ImpDate 生成长尾IP的日期
	UpdateTime time.Time `db:"mtime"`      // UpdateTime 修改时间
}

// GetDateLongTailCovers 获取每日长尾专辑
func (c *LongTailCoverInfo) GetDateLongTailCovers(ctx context.Context, date string) ([]*LongTailCoverInfo, error) {
	log.InfoContextf(ctx, "GetDateLongTailCovers enter")
	var infos []*LongTailCoverInfo
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT cover_id,cover_type,title "+
			" FROM t_long_tail_ip WHERE imp_date=?", date); err != nil {
		return nil, err
	}
	return infos, nil
}

// IPCrawTask 抓取IP信息
type IPCrawTask struct {
	ID                      int       `db:"c_id"`                        // ID 自增ID
	OrderID                 int       `db:"c_order_id"`                  // OrderID 抓取订单ID
	CrawType                int       `db:"c_craw_type"`                 // CrawType 抓取类型
	Platform                string    `db:"c_platform"`                  // Platform 抓取平台
	CrawKey                 string    `db:"c_craw_key"`                  // CrawKey 抓取关键字
	URL                     string    `db:"c_url"`                       // URL 抓取链接（运营指定抓取的页面）
	CoverID                 string    `db:"c_cover_id"`                  // CoverID 专辑ID
	CoverType               int       `db:"c_cover_type"`                // CoverType 专辑大分类（方便统计）
	Valid                   int       `db:"c_valid"`                     // Valid 是否有效
	PriorityFlag            int       `db:"c_priority_flag"`             // PriorityFlag 是否优先剧集
	CreateTime              time.Time `db:"c_ctime"`                     // CreateTime 创建时间
	UpdateTime              time.Time `db:"c_mtime"`                     // UpdateTime 修改时间
	DayCrawlerState         int       `db:"c_day_crawler_state"`         // DayCrawlerState 每日抓取状态
	DayCrawlerStartTime     time.Time `db:"c_day_crawler_start_time"`    // DayCrawlerStartTime 每日抓取开始时间
	DayCrawlerEndTime       time.Time `db:"c_day_crawler_end_time"`      // DayCrawlerEndTime 每日抓取结束时间
	ConfirmStatus           int       `db:"c_confirm_status"`            // ConfirmStatus 确认状态
	BacktrackStatus         int       `db:"c_backtrack_status"`          // BacktrackStatus 回溯状态
	CorrectKey              string    `db:"c_correct_key"`               // CorrectKey 纠正后的 key
	TopicName               string    `db:"c_topic_name"`                // TopicName 话题名
	TopicURL                string    `db:"c_topic_url"`                 // TopicURL 话题 url
	Nick                    string    `db:"c_nick"`                      // Nick 昵称
	Avatar                  string    `db:"c_avatar"`                    // Avatar 头像
	AccountID               string    `db:"c_mid"`                       // AccountID 账号 id
	PageURL                 string    `db:"c_page_url"`                  // PageURL 个人主页 url
	CoverLevel              string    `db:"c_cover_level"`               // CoverLevel 专辑等级
	CopyrightPlaybackStatus int       `db:"c_copyright_playback_status"` // CopyrightPlaybackStatus 网络播放性质（外显）
}

// crawInfoTbl 抓取信息表名
func (i *IPCrawTask) crawInfoTbl() string {
	if i.OrderID == model.LongTailIPTopicOrder { // 长尾剧的话题抓取
		return "t_long_tail_ip_crawler_task"
	}
	return "t_ip_crawler_conf" // 默认是：重点剧IP官号、IP话题的抓取
}

// String 实现输出字符串格式
func (i *IPCrawTask) String() string {
	data, _ := json.Marshal(i)
	return string(data)
}

// InsertIPCrawInfo 写入IP抓取任务信息
func (i *IPCrawTask) InsertIPCrawInfo(ctx context.Context) error {
	log.InfoContextf(ctx, "InsertIPCrawInfo table name: %s", i.crawInfoTbl())
	if _, err := proxy.NamedExec(ctx,
		"INSERT INTO "+i.crawInfoTbl()+
			" (c_order_id,c_craw_type,c_platform,c_craw_key,c_cover_id,"+
			"c_valid,c_operator,c_ctime,c_priority_flag,c_cover_type, c_cover_level, c_copyright_playback_status) "+
			" VALUES "+
			" (:c_order_id,:c_craw_type,:c_platform,:c_craw_key,:c_cover_id,"+
			":c_valid,'system',now(),:c_priority_flag,:c_cover_type, :c_cover_level, :c_copyright_playback_status) "+
			" ON DUPLICATE KEY UPDATE "+
			" c_order_id=:c_order_id, c_craw_key=:c_craw_key, c_cover_level=:c_cover_level,"+
			"c_copyright_playback_status=:c_copyright_playback_status,"+
			" c_valid=:c_valid,c_priority_flag=:c_priority_flag,c_cover_id=:c_cover_id,c_cover_type=:c_cover_type",
		[]IPCrawTask{*i}); err != nil {
		log.ErrorContextf(ctx, "InsertIPCrawInfo ERR:%+v", err)
		return err
	}
	return nil
}

// CleanIPCrawInfo 清空抓取任务(只是把抓取状态置为非法，不delete，避免自增抓取ID漂移)
func (i *IPCrawTask) CleanIPCrawInfo(ctx context.Context) error {
	_, err := proxy.Exec(ctx, "UPDATE "+i.crawInfoTbl()+" SET c_valid=0")
	return err
}

// UpdateTopicInfo 更新话题信息
func (i *IPCrawTask) UpdateTopicInfo(ctx context.Context) error {
	_, err := proxy.Exec(ctx,
		"UPDATE "+i.crawInfoTbl()+" SET c_topic_name=?, c_topic_url=? WHERE c_id=?",
		i.TopicName, i.TopicURL, i.ID)
	return err
}

// UpdateAccountInfo 更新官号信息
func (i *IPCrawTask) UpdateAccountInfo(ctx context.Context) error {
	_, err := proxy.Exec(ctx,
		"UPDATE "+i.crawInfoTbl()+" SET c_nick=?, c_avatar=?, c_mid=?, c_page_url=? WHERE c_id=?",
		i.Nick, i.Avatar, i.AccountID, i.PageURL, i.ID)
	return err
}

// GetAllCrawTasks 获取待抓取IP信息
func (i *IPCrawTask) GetAllCrawTasks(ctx context.Context) ([]*IPCrawTask, error) {
	log.InfoContextf(ctx, "GetAllCrawTasks enter")
	var infos []*IPCrawTask
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_id,c_order_id,c_craw_type,c_platform,c_craw_key,c_url,c_cover_id,c_ctime,c_mtime,"+
			" c_day_crawler_state,c_day_crawler_start_time,c_day_crawler_end_time,c_valid,c_priority_flag,"+
			" c_confirm_status, c_correct_key, c_nick, c_page_url, c_topic_url, c_topic_name, c_backtrack_status"+
			" FROM "+i.crawInfoTbl()+
			" WHERE c_valid=1 AND c_craw_type != 4"); err != nil {
		return nil, err
	}
	return infos, nil
}

// GetSLevelCrawTasks 获取S级及以上抓取任务抓取IP信息
func (i *IPCrawTask) GetSLevelCrawTasks(ctx context.Context) ([]*IPCrawTask, error) {
	log.InfoContextf(ctx, "GetAllCrawTasks enter")
	var infos []*IPCrawTask
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_id,c_order_id,c_craw_type,c_platform,c_craw_key,c_url,c_cover_id,c_ctime,c_mtime,"+
			" c_day_crawler_state,c_day_crawler_start_time,c_day_crawler_end_time,c_valid,c_priority_flag,"+
			" c_confirm_status, c_correct_key, c_nick, c_page_url, c_topic_url, c_topic_name, c_backtrack_status,"+
			"c_copyright_playback_status"+
			" FROM "+i.crawInfoTbl()+
			" WHERE c_valid=1 AND c_craw_type != 4 AND (c_cover_level = 'S' || c_cover_level = 'S+')"); err != nil {
		return nil, err
	}
	return infos, nil
}

// GetIPCount 获取IP总数量(去重)
func (i *IPCrawTask) GetIPCount(ctx context.Context, typeID int) (uint64, error) {
	log.InfoContextf(ctx, "GetIPCount enter")
	var count uint64
	next := func(rows *sql.Rows) error {
		if err := rows.Scan(&count); err != nil {
			log.ErrorContextf(ctx, "mysql scan fail:%+v", err)
			return err
		}
		return nil
	}

	if err := proxy.Query(ctx, next,
		"select count(DISTINCT(c_cover_id)) "+
			"FROM "+i.crawInfoTbl()+" WHERE c_valid=1 AND c_cover_type=?", typeID); err != nil {
		return count, err
	}
	return count, nil
}

// GetIPSucCount 获取IP 抓取成功总数量(五大平台之和)
func (i *IPCrawTask) GetIPSucCount(ctx context.Context, crawType int, createDate string, typeID int) (uint64, error) {
	log.InfoContextf(ctx, "GetIPSucCount enter：%d-%s", crawType, createDate)
	var count uint64
	next := func(rows *sql.Rows) error {
		if err := rows.Scan(&count); err != nil {
			log.ErrorContextf(ctx, "mysql scan fail:%+v", err)
			return err
		}
		return nil
	}

	if err := proxy.Query(ctx, next,
		"select count(c_cover_id) FROM "+i.crawInfoTbl()+
			" WHERE c_valid=1 AND c_craw_type=? AND c_day_crawler_state=? "+
			" AND c_day_crawler_end_time>=? AND c_day_crawler_end_time<=? "+
			"AND c_cover_type=?",
		crawType, model.CrawlerSuc, createDate+" 00:00:01", createDate+" 23:59:59", typeID); err != nil {
		log.ErrorContextf(ctx, "GetIPSucCount ERR:%+v", err)
		return 0, err
	}
	return count, nil
}

// GetIPUnconfirmedCount 获取IP 未确认总数量(五大平台之和)
func (i *IPCrawTask) GetIPUnconfirmedCount(ctx context.Context, crawType int, createDate string,
	typeID int) (uint64, error) {
	log.InfoContextf(ctx, "GetIPUnconfirmedCount enter：%d", crawType)
	var count uint64
	next := func(rows *sql.Rows) error {
		if err := rows.Scan(&count); err != nil {
			log.ErrorContextf(ctx, "mysql scan fail:%+v", err)
			return err
		}
		return nil
	}
	if err := proxy.Query(ctx, next,
		"select count(c_cover_id) FROM "+i.crawInfoTbl()+
			" WHERE c_valid=1 AND c_craw_type=? AND c_confirm_status=0 "+
			" AND c_cover_type=?"+
			" AND c_day_crawler_end_time>=? AND c_day_crawler_end_time<=? "+
			" AND c_day_crawler_state=? ",
		crawType, typeID, createDate+" 00:00:01", createDate+" 23:59:59", model.CrawlerSuc); err != nil {
		log.ErrorContextf(ctx, "GetIPUnconfirmedCount ERR:%+v", err)
		return 0, err
	}
	return count, nil
}

// GetUnconfirmedCoreCover 获取未确认核心专辑
func (i *IPCrawTask) GetUnconfirmedCoreCover(ctx context.Context, createDate string, typeID int) ([]*IPCrawTask,
	error) {
	log.InfoContextf(ctx, "GetUnconfirmedCoreCover enter:%d", typeID)
	var infos []*IPCrawTask
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT distinct(c_cover_id), c_craw_key "+
			" FROM "+i.crawInfoTbl()+
			" WHERE c_valid=1 AND c_cover_type=? AND (c_cover_level = 'S' || c_cover_level = 'S+') "+
			" AND c_day_crawler_end_time>=? AND c_day_crawler_end_time<=? "+
			" AND c_day_crawler_state=? "+
			" AND c_copyright_playback_status = 8287873 AND c_confirm_status=0",
		typeID, createDate+" 00:00:01", createDate+" 23:59:59", model.CrawlerSuc); err != nil {
		log.ErrorContextf(ctx, "GetUnconfirmedCoreCover ERR:%+v", err)
		return nil, err
	}
	return infos, nil
}

// GetCrawTaskByID 获取抓取任务信息
func (i *IPCrawTask) GetCrawTaskByID(ctx context.Context, id int) error {
	log.InfoContextf(ctx, "GetCrawTaskByID enter:%d", id)
	var infos []*IPCrawTask
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_id,c_order_id,c_craw_type,c_platform,c_craw_key,c_url,c_cover_id,c_ctime,c_mtime,"+
			" c_day_crawler_state,c_day_crawler_start_time,c_day_crawler_end_time,c_valid,c_priority_flag"+
			" FROM "+i.crawInfoTbl()+
			" WHERE c_id=?", id); err != nil {
		return err
	}
	if len(infos) > 0 {
		*i = *infos[0]
	}
	return nil
}

// SetCrawTaskState 更新IP抓取状态
func (i *IPCrawTask) SetCrawTaskState(ctx context.Context) error {
	if err := updateCrawState(ctx, i.crawInfoTbl(),
		i.ID, i.DayCrawlerState); err != nil {
		log.ErrorContextf(ctx, "SetCrawTaskState ERR:%+v", err)
		return err
	}
	return nil
}

// SetCrawTaskBackTrack 设置 IP 抓取回溯状态
func (i *IPCrawTask) SetCrawTaskBackTrack(ctx context.Context) error {
	if _, err := proxy.Exec(ctx,
		"UPDATE "+i.crawInfoTbl()+" SET c_backtrack_status=? WHERE c_id=?",
		i.BacktrackStatus, i.ID); err != nil {
		log.ErrorContextf(ctx, "updateCrawState ERR:%+v", err)
		return err
	}
	return nil
}

// GetManualCheckEmptyKeyTask 获取人工纠正的，但是没填写 key 的
func (i *IPCrawTask) GetManualCheckEmptyKeyTask(ctx context.Context) ([]*IPCrawTask, error) {
	var infos []*IPCrawTask
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_id,c_order_id,c_craw_type,c_platform,c_craw_key,c_url,c_cover_id,c_ctime,c_mtime,"+
			" c_day_crawler_state,c_day_crawler_start_time,c_day_crawler_end_time,c_valid,c_priority_flag"+
			" FROM  "+i.crawInfoTbl()+
			" WHERE c_valid=1 AND c_confirm_status = 2 AND c_correct_key = ''"); err != nil {
		return nil, err
	}
	return infos, nil
}

// CPCrawTask 抓取CP信息
type CPCrawTask struct {
	// ID 自增ID
	ID int `db:"c_id"`
	// Platform 抓取平台
	Platform string `db:"c_platform"`
	// AccountName 账号名
	AccountName string `db:"c_account_name"`
	// AccountID 账号ID
	AccountID string `db:"c_account_id"`
	// AccountType 账号类型
	AccountType int `db:"c_account_type"`
	// LinkURL 抓取URL
	LinkURL string `db:"c_link_url"`
	// AccountState 账号状态
	AccountState int `db:"c_account_state"`
	// CreateTime 创建时间
	CreateTime time.Time `db:"c_ctime"`
	// UpdateTime 修改时间
	UpdateTime time.Time `db:"c_mtime"`
	// DayCrawlerState 每日抓取状态
	DayCrawlerState int `db:"c_day_crawler_state"`
	// DayCrawlerStartTime 每日抓取开始时间
	DayCrawlerStartTime time.Time `db:"c_day_crawler_start_time"`
	// DayCrawlerEndTime 每日抓取结束时间
	DayCrawlerEndTime time.Time `db:"c_day_crawler_end_time"`
}

// GetCPCount 获取CP总数量
func (c *CPCrawTask) GetCPCount(ctx context.Context, crawType int) (uint64, error) {
	log.InfoContextf(ctx, "GetCPCount enter")
	var count uint64
	next := func(rows *sql.Rows) error {
		if err := rows.Scan(&count); err != nil {
			log.ErrorContextf(ctx, "mysql scan fail:%+v", err)
			return err
		}
		return nil
	}
	if err := proxy.Query(ctx, next,
		"select count(*) FROM t_cp_crawler_conf  WHERE c_account_state=1 "+
			"AND c_account_type = ?", crawType); err != nil {
		return count, err
	}
	return count, nil
}

// GetCPSucCount 获取CP抓取成功总数量
func (c *CPCrawTask) GetCPSucCount(ctx context.Context, createDate string, crawType int) (uint64, error) {
	var count uint64
	next := func(rows *sql.Rows) error {
		if err := rows.Scan(&count); err != nil {
			log.ErrorContextf(ctx, "mysql scan fail:%+v", err)
			return err
		}
		return nil
	}
	if err := proxy.Query(ctx, next,
		"select count(*) FROM t_cp_crawler_conf "+
			" WHERE c_account_state=1 AND c_day_crawler_state=3"+
			" AND c_day_crawler_end_time>=? "+
			" AND c_day_crawler_end_time<=? "+
			" AND c_account_type = ?",
		createDate+" 00:00:01", createDate+" 23:59:59", crawType); err != nil {
		log.ErrorContextf(ctx, "GetCPSucCount ERR:%+v", err)
		return 0, err
	}
	return count, nil
}

// GetAllCrawTasks 获取待抓取CP信息
func (c *CPCrawTask) GetAllCrawTasks(ctx context.Context) ([]*CPCrawTask, error) {
	log.InfoContextf(ctx, "GetAllCrawTasks enter")
	var infos []*CPCrawTask
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_id,c_platform,c_account_name,c_account_id,"+
			" c_link_url,c_account_state,c_ctime,c_mtime,"+
			" c_day_crawler_state,c_day_crawler_start_time,c_day_crawler_end_time"+
			" FROM t_cp_crawler_conf "+
			" WHERE c_valid=1"); err != nil {
		return nil, err
	}
	return infos, nil
}

// GetCrawTaskByID 获取抓取任务的账号信息
func (c *CPCrawTask) GetCrawTaskByID(ctx context.Context, id int) error {
	log.InfoContextf(ctx, "GetCrawTaskByID enter:%d", id)
	var infos []*CPCrawTask
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_id,c_account_name,c_account_id,c_account_type,c_day_crawler_start_time"+
			" FROM t_cp_crawler_conf "+
			" WHERE c_id=?", id); err != nil {
		return err
	}
	if len(infos) > 0 {
		*c = *infos[0]
	}
	return nil
}

// SetCrawTaskState 更新CP抓取状态
func (c *CPCrawTask) SetCrawTaskState(ctx context.Context) error {
	if err := updateCrawState(ctx, "t_cp_crawler_conf",
		c.ID, c.DayCrawlerState); err != nil {
		log.ErrorContextf(ctx, "SetCrawTaskState ERR:%+v", err)
		return err
	}
	return nil
}

// updateCrawState 更新 抓取状态
func updateCrawState(ctx context.Context, tblName string, id int, crawState int) error {
	var timeCondSql string
	if crawState == model.CrawlerRunning {
		timeCondSql = ",c_day_crawler_start_time=now()"
	}
	if crawState == model.CrawlerSuc {
		timeCondSql = ",c_day_crawler_end_time=now()"
	}
	if _, err := proxy.Exec(ctx,
		"UPDATE "+tblName+" SET c_day_crawler_state=? "+timeCondSql+" WHERE c_id=?",
		crawState, id); err != nil {
		log.ErrorContextf(ctx, "updateCrawState ERR:%+v", err)
		return err
	}
	return nil
}

// HotCountInfo 每日热度信息
type HotCountInfo struct {
	Date     string `json:"hot_date"`  // 日期
	HotCount int    `json:"hot_count"` // 热度
}

// Episode 单集信息
type Episode struct {
	MarkType    int    `json:"mark_type_show"`   // 外显角标（0：无 | 4：VIP | 17：预告 | 20：限免）
	MarkTypeTxt string `json:"mark_type_txt"`    // 外显角标，翻译后的值
	EpisodeIdx  int    `json:"plat_episode_idx"` // 集号
}

// EpisodeDetailInfo 剧集详情信息
type EpisodeDetailInfo struct {
	Title               string         `json:"title"`                     // 剧名
	HotCount            int            `json:"hot_count"`                 // 当前热度
	MaxHotCount         int            `json:"max_hot_count"`             // 历史最高热度
	HistoryHotCountList []HotCountInfo `json:"history_hot_count_list"`    // 每日热度值
	VideoScore          float64        `json:"video_score"`               // 评分
	BoardTxt            string         `json:"board_txt"`                 // 榜单信息
	LikeCount           int            `json:"like_count"`                // 点赞量
	CommentCount        int            `json:"comment_count"`             // 讨论量
	UpdateTxt           string         `json:"tv_series_update_strategy"` // 排播信息
	TotalEpisodeCount   int            `json:"total_episode_count"`       // 总集数
	Episodes            []Episode      `json:"episodes"`                  // 单集信息
}

// PlaylistInfo 抓取的排播列表信息
type PlaylistInfo struct {
	ID           int    `db:"c_id"`                                   // 自增ID
	OrderID      int    `db:"c_order_id"`                             // 抓取订单ID
	ChannelType  string `json:"channel" db:"c_channel_name"`          // 频道名
	UpdateDate   string `json:"update_date" db:"c_play_date"`         // 播放日期
	UpdateTime   string `json:"update_time" db:"c_play_time"`         // 具体播放时间
	Title        string `json:"title" db:"c_title"`                   // 剧名
	SubTitle     string `json:"sub_title" db:"c_sub_title"`           // 子标题
	UpdateTxt    string `json:"update_txt" db:"c_update_txt"`         // 更新文案
	VipUpdateTxt string `json:"vip_update_txt" db:"c_vip_update_txt"` // VIP更新文案
	IsExclusive  bool   `json:"is_exclusive" db:"c_is_exclusive"`     // 是否独播
	URL          string `json:"url" db:"c_url"`                       // 播放url
	Platform     string `db:"c_platform"`                             // 抓取平台
}

// InsertPlaylistInfo 排播列表信息写入DB
func (p *PlaylistInfo) InsertPlaylistInfo(ctx context.Context) error {
	log.InfoContextf(ctx, "InsertPlaylistInfo enter")
	if _, err := proxy.NamedExec(ctx,
		"INSERT INTO t_playlist_info "+
			"(c_order_id, c_platform, c_channel_name, "+
			"c_play_date,c_play_time,c_title,c_sub_title,c_update_txt,c_vip_update_txt, "+
			"c_is_exclusive,c_url,c_crawler_last_mtime) VALUES "+
			"(:c_order_id, :c_platform, :c_channel_name,  "+
			":c_play_date, :c_play_time, :c_title,:c_sub_title,:c_update_txt,:c_vip_update_txt, "+
			":c_is_exclusive, :c_url, now()) "+
			"ON DUPLICATE KEY UPDATE "+
			"c_play_date=:c_play_date, c_play_time=:c_play_time, "+
			"c_update_txt=:c_update_txt, c_vip_update_txt=:c_vip_update_txt",
		[]PlaylistInfo{*p}); err != nil {
		log.ErrorContextf(ctx, "INSERT t_playlist_info ERR:%+v", err)
		return err
	}
	return nil
}

// GetDatePlaylistInfo 获取当日的排播列表
func (p *PlaylistInfo) GetDatePlaylistInfo(ctx context.Context) ([]*PlaylistInfo, error) {
	log.InfoContextf(ctx, "GetDatePlaylistInfo enter")
	var infos []*PlaylistInfo
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_id,c_title,c_play_time,c_url  FROM t_playlist_info "+
			" WHERE c_play_date=? ", time.Now().Format("2006-01-02")); err != nil {
		return nil, err
	}
	return infos, nil
}

// CrawJob 抓取任务表
type CrawJob struct {
	OrderID             int       `db:"order_id"`                 // 抓取订单ID(注意这个order_id只读在DB中不存在，是为了在select时方便赋值带的虚拟字段)
	ID                  int       `db:"c_id"`                     // 自增ID
	Platform            string    `db:"c_platform"`               // 抓取平台
	TransmitKey         string    `db:"c_transmit_key"`           // 抓取任务key
	Title               string    `db:"c_title"`                  // 抓取任务中文名
	JobArguments        string    `db:"c_job_arguments"`          // 抓取侧API参数
	Ext                 string    `db:"c_ext"`                    // 扩展字段
	DayCrawlerState     int       `db:"c_day_crawler_state"`      // 抓取状态
	DayCrawlerStartTime time.Time `db:"c_day_crawler_start_time"` // 每日抓取开始时间
	DayCrawlerEndTime   time.Time `db:"c_day_crawler_end_time"`   // 每日抓取结束时间
	Valid               int       `db:"c_valid"`                  // 是否合法数据
	CreateTime          time.Time `db:"c_ctime"`                  // 创建时间
	UpdateTime          time.Time `db:"c_mtime"`                  // 修改时间
}

// crawJobTbl 抓取任务表
func (c *CrawJob) crawJobTbl() string {
	return "t_crawler_job_" + strconv.Itoa(c.OrderID)
}

// InsertCrawJob 写入抓取任务表
func (c *CrawJob) InsertCrawJob(ctx context.Context) error {
	log.InfoContextf(ctx, "InsertCrawJob table name: %s-%s", c.crawJobTbl(), c.TransmitKey)
	if _, err := proxy.NamedExec(ctx,
		"INSERT INTO "+c.crawJobTbl()+
			" (c_platform,c_transmit_key,c_title,c_job_arguments,c_ext ,c_valid, c_ctime) "+
			" VALUES "+
			" (:c_platform,:c_transmit_key,:c_title,:c_job_arguments,:c_ext,:c_valid,now()) "+
			" ON DUPLICATE KEY UPDATE "+
			" c_platform=:c_platform, c_title=:c_title, "+
			"c_job_arguments=:c_job_arguments, c_ext=:c_ext, c_valid=:c_valid,"+
			" c_day_crawler_state=:c_day_crawler_state, c_mtime=now() ",
		[]CrawJob{*c}); err != nil {
		log.ErrorContextf(ctx, "InsertCrawJob ERR:%+v", err)
		return err
	}
	return nil
}

// GetAllCrawJobs 获取待抓取任务信息
func (c *CrawJob) GetAllCrawJobs(ctx context.Context, startCTime string) ([]*CrawJob, error) {
	log.InfoContextf(ctx, "GetAllCrawJobs enter:%s; orderID:%d", c.crawJobTbl(), c.OrderID)
	var infos []*CrawJob
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT ? as order_id,c_id,c_platform,c_transmit_key,c_title,c_job_arguments,c_ext,"+
			" c_day_crawler_state,c_day_crawler_start_time,c_day_crawler_end_time,c_ctime,c_mtime"+
			" FROM "+c.crawJobTbl()+" WHERE c_valid=1 AND c_ctime>=? ", c.OrderID, startCTime); err != nil {
		return nil, err
	}
	return infos, nil
}

// CleanCrawJobs 清空指定日期之后的抓取任务
func (c *CrawJob) CleanCrawJobs(ctx context.Context, startCTime string) error {
	log.InfoContextf(ctx, "CleanCrawJobs enter:%s; startCTime:%s", c.crawJobTbl(), startCTime)
	if _, err := proxy.Exec(ctx,
		"DELETE FROM "+c.crawJobTbl()+" WHERE c_ctime>=? ", startCTime); err != nil {
		log.ErrorContextf(ctx, "CleanCrawJobs ERR:%+v", err)
		return err
	}
	return nil
}

// GetCrawJobByID 通过jobID获取job详情信息
func (c *CrawJob) GetCrawJobByID(ctx context.Context, id int) (*CrawJob, error) {
	log.InfoContextf(ctx, "GetCrawJobByID enter:%d", id)
	var infos []*CrawJob
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_id,c_platform,c_transmit_key,c_title,c_job_arguments,c_ext,"+
			" c_day_crawler_state,c_day_crawler_start_time,c_day_crawler_end_time"+
			" FROM "+c.crawJobTbl()+
			" WHERE c_id=?", id); err != nil {
		return nil, err
	}
	var retCrawJob *CrawJob
	if len(infos) > 0 {
		retCrawJob = infos[0]
	}
	return retCrawJob, nil
}

// SetCrawJobState 更新抓取任务状态
func (c *CrawJob) SetCrawJobState(ctx context.Context) error {
	if err := updateCrawState(ctx, c.crawJobTbl(),
		c.ID, c.DayCrawlerState); err != nil {
		log.ErrorContextf(ctx, "SetCrawTaskState ERR:%+v", err)
		return err
	}
	return nil
}

// CrawDetail 抓取结果表
type CrawDetail struct {
	OrderID     int       `db:"order_id"`       // 抓取订单ID(注意这个order_id只读在DB中不存在，是为了在select时方便赋值带的虚拟字段)
	ID          int       `db:"c_id"`           // 自增ID
	Platform    string    `db:"c_platform"`     // 抓取平台
	TransmitKey string    `db:"c_transmit_key"` // 抓取任务key
	Title       string    `db:"c_title"`        // 抓取任务中文名
	Detail      string    `db:"c_detail"`       // 抓取侧返回结果
	Ext         string    `db:"c_ext"`          // 扩展字段
	CrawTime    time.Time `db:"c_craw_time"`    // 抓取返回时间
	CreateTime  time.Time `db:"c_ctime"`        // 创建时间
	UpdateTime  time.Time `db:"c_mtime"`        // 修改时间
}

// crawDetailTbl 抓取结果表
func (c *CrawDetail) crawDetailTbl() string {
	return "t_crawler_result_detail_" + strconv.Itoa(c.OrderID)
}

// GetCrawDetail 获取抓取详情结果
func (c *CrawDetail) GetCrawDetail(ctx context.Context, beginCrawTime string) ([]*CrawDetail, error) {
	log.InfoContextf(ctx, "GetCrawDetail enter")
	if beginCrawTime == "" {
		beginCrawTime = "2000-01-01 00:00:00"
	}
	var infos []*CrawDetail
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT ? as order_id,c_id,c_platform,c_transmit_key,c_title,c_detail,c_ext,"+
			" c_craw_time,c_ctime,c_mtime"+
			" FROM "+c.crawDetailTbl()+" WHERE c_craw_time>=?  order by c_craw_time", // 注意：这里按c_craw_time升序返回，让先抓取到的任务优先返回（保证处理结果的稳定）
		c.OrderID, beginCrawTime); err != nil {
		return nil, err
	}
	return infos, nil
}

// GetCrawDetailByTitle 通过标题获取详情信息
func (c *CrawDetail) GetCrawDetailByTitle(ctx context.Context, title string) ([]*CrawDetail, error) {
	log.InfoContextf(ctx, "GetCrawDetailByTitle enter:%s-%s", title, c.crawDetailTbl())
	var infos []*CrawDetail

	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_id,c_transmit_key,c_title,c_detail,c_ext,c_craw_time"+
			" FROM  "+c.crawDetailTbl()+
			" WHERE c_title=? order by c_craw_time desc limit 1000", title); err != nil {
		return nil, err
	} // 按一个IP每天返回10页搜索结果估算，取前10天的搜索结果（以免有些新剧，前几天搜索不到）
	return infos, nil
}

// IsPushCraw 判断是否应该推送抓取
func (c *CrawJob) IsPushCraw() bool {
	return time.Now().Format("2006-01-02") != c.DayCrawlerEndTime.Format("2006-01-02") ||
		c.DayCrawlerState != model.CrawlerSuc
}

// CommonCrawResult 通用抓取结果
type CommonCrawResult struct {
	Platform    string    `db:"c_platform"`     // 抓取平台
	TransmitKey string    `db:"c_transmit_key"` // 抓取唯一Key
	Title       string    `db:"c_title"`        // 剧集标题
	Detail      string    `db:"c_detail"`       // 详情信息
	Ext         string    `db:"c_ext"`          // 扩展字段
	CrawTime    string    `db:"c_craw_time"`    // 抓取时间
	CTime       time.Time `db:"c_ctime"`        // 创建信息
}

// InsertCrawResult 抓取结果信息写入通用存储
func (p *CommonCrawResult) InsertCrawResult(ctx context.Context, orderID int) error {
	log.InfoContextf(ctx, "CommonCrawResult enter:%d", orderID)
	if orderID <= 0 {
		return fmt.Errorf("orderID invalid")
	}
	if _, err := proxy.NamedExec(ctx,
		"INSERT INTO t_crawler_result_detail_"+strconv.Itoa(orderID)+
			" (c_platform, c_transmit_key, c_title, c_detail,c_ext, c_craw_time, c_ctime) VALUES "+
			"(:c_platform, :c_transmit_key, :c_title, :c_detail,:c_ext, :c_craw_time, now()) "+
			"ON DUPLICATE KEY UPDATE "+
			"c_title=:c_title, c_detail=:c_detail,c_ext=:c_ext ",
		[]CommonCrawResult{*p}); err != nil {
		log.ErrorContextf(ctx, "INSERT t_crawler_result_detail_ ERR:%+v", err)
		return err
	}
	return nil
}

// CrawOrder 抓取订单表信息
type CrawOrder struct {
	CrawType      int    `db:"c_craw_type"`       // 抓取类型（账号、话题等）
	OrderID       int    `db:"c_order_id"`        // 抓取订单ID
	CrawAPICaller string `db:"c_craw_api_caller"` // 抓取侧API请求方
	CrawAPISecret string `db:"c_craw_api_secret"` // 抓取侧API秘钥
	CrawTaskID    string `db:"c_craw_task_id"`    // 抓取侧任务ID
	JobArguments  string `db:"c_job_arguments"`   // 抓取侧API参数
}

// GetCrawOrderInfo 获取抓取订单信息
func (c *CrawOrder) GetCrawOrderInfo(ctx context.Context, orderID int) error {
	var infos []*CrawOrder
	if err := proxy.QueryToStructs(ctx, &infos,
		"SELECT c_craw_type,c_order_id,c_craw_api_caller,c_craw_api_secret,c_craw_task_id,c_job_arguments"+
			" FROM t_crawler_order "+
			" WHERE c_order_id=?", orderID); err != nil {
		return err
	}
	if len(infos) > 0 {
		*c = *infos[0]
	}
	return nil
}
