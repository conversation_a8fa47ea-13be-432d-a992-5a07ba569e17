package elastic

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"strings"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/es"

	"git.code.oa.com/trpc-go/trpc-go"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	elasticv7 "github.com/olivere/elastic/v7"
)

func TestESObj_ESValTrans2FieldInfo(t *testing.T) {
	type fields struct {
		Ctx          context.Context
		proxy        *elasticv7.Client
		Name         string
		DatasourceID int32
		fieldList    []*protocol.UpdateFieldInfo
	}
	type args struct {
		key       string
		fieldType protocol.EnumFieldType
		esVal     interface{}
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		want   *protocol.FieldInfo
	}{
		{"没有缓存", fields{
			Ctx:   trpc.BackgroundContext(),
			proxy: nil,
		}, args{
			key:       "title",
			fieldType: 1,
			esVal:     "测试",
		}, &protocol.FieldInfo{
			FieldName: "title",
			FieldType: 1,
			StrValue:  "测试",
		}},
		{"title字符串", fields{
			Ctx:   trpc.BackgroundContext(),
			proxy: nil,
		}, args{
			key:       "title",
			fieldType: 1,
			esVal:     "测试",
		}, &protocol.FieldInfo{
			FieldName: "title",
			FieldType: 1,
			StrValue:  "测试",
		}},
		{"vec_int", fields{
			Ctx:   trpc.BackgroundContext(),
			proxy: nil,
		}, args{
			key:       "vec_int",
			fieldType: 2,
			esVal:     "1+2+3",
		}, &protocol.FieldInfo{
			FieldName: "vec_int",
			FieldType: 2,
			VecInt:    []uint32{1, 2, 3},
		}},
		{"vec_str", fields{
			Ctx:   trpc.BackgroundContext(),
			proxy: nil,
		}, args{
			key:       "vec_str",
			fieldType: 3,
			esVal:     "1+2+3",
		}, &protocol.FieldInfo{
			FieldName: "vec_str",
			FieldType: 3,
			VecStr:    []string{"1", "2", "3"},
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &ESObj{
				Ctx:          tt.fields.Ctx,
				proxy:        tt.fields.proxy,
				Name:         tt.fields.Name,
				DatasourceID: tt.fields.DatasourceID,
				fieldList:    tt.fields.fieldList,
			}
			if got := e.ESValTrans2FieldInfo(tt.args.key, tt.args.fieldType, tt.args.esVal); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ESValTrans2FieldInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseESItfVal2VecInt(t *testing.T) {
	type args struct {
		v interface{}
	}
	tests := []struct {
		name  string
		args  args
		want  []uint32
		want1 bool
	}{
		{
			"2-suc-string", args{v: "1"}, []uint32{1}, true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := ParseESItfVal2VecInt(tt.args.v)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseESItfVal2VecInt() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("ParseESItfVal2VecInt() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestParseESItfVal2VecStr(t *testing.T) {
	type args struct {
		v interface{}
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			"suc1", args{v: "1"}, []string{"1"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ParseESItfVal2VecStr(tt.args.v); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseESItfVal2VecStr() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestESObj_GetFieldInfosByIDs 全面测试GetFieldInfosByIDs函数
func TestESObj_GetFieldInfosByIDs(t *testing.T) {
	tests := []struct {
		name           string
		ids            []string
		fields         []string
		setupMock      func() (*MockElasticReadClient, *MockESDataService)
		wantSuccess    map[string]*ESDataInfo
		wantFail       map[string]string
		wantErr        bool
		expectedErrMsg string
	}{
		{
			name:   "成功获取多个文档",
			ids:    []string{"1", "2"},
			fields: []string{"title", "content"},
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				// 模拟MultiGet响应
				mockResponse := &elasticv7.MgetResponse{
					Docs: []*elasticv7.GetResult{
						{
							Id:     "1",
							Index:  "test_index",
							Found:  true,
							Source: json.RawMessage(`{"title": "标题1", "content": "内容1"}`),
						},
						{
							Id:     "2",
							Index:  "test_index",
							Found:  true,
							Source: json.RawMessage(`{"title": "标题2", "content": "内容2"}`),
						},
					},
				}
				mockMultiGetService := NewMockMultiGetService(mockResponse, nil)
				mockReadClient := NewMockElasticReadClient(mockMultiGetService)

				// 模拟数据服务
				mockDataService := NewMockESDataService()
				return mockReadClient, mockDataService
			},
			wantSuccess: map[string]*ESDataInfo{
				"1": {ID: "1", Index: "test_index", Found: true},
				"2": {ID: "2", Index: "test_index", Found: true},
			},
			wantFail: map[string]string{},
			wantErr:  false,
		},
		{
			name:   "空ID列表",
			ids:    []string{},
			fields: []string{"title"},
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				return nil, nil
			},
			wantSuccess: nil,
			wantFail:    nil,
			wantErr:     false,
		},
		{
			name:   "获取所有字段（使用*）",
			ids:    []string{"1"},
			fields: []string{"*"},
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				mockResponse := &elasticv7.MgetResponse{
					Docs: []*elasticv7.GetResult{
						{
							Id:     "1",
							Index:  "test_index",
							Found:  true,
							Source: json.RawMessage(`{"title": "标题", "content": "内容", "tags": ["tag1", "tag2"]}`),
						},
					},
				}
				mockMultiGetService := NewMockMultiGetService(mockResponse, nil)
				mockReadClient := NewMockElasticReadClient(mockMultiGetService)
				mockDataService := NewMockESDataService()
				return mockReadClient, mockDataService
			},
			wantSuccess: map[string]*ESDataInfo{
				"1": {ID: "1", Index: "test_index", Found: true},
			},
			wantFail: map[string]string{},
			wantErr:  false,
		},
		{
			name:   "ElasticSearch错误",
			ids:    []string{"1"},
			fields: []string{"title"},
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				mockMultiGetService := NewMockMultiGetService(nil, errors.New("connection timeout"))
				mockReadClient := NewMockElasticReadClient(mockMultiGetService)
				return mockReadClient, nil
			},
			wantSuccess:    nil,
			wantFail:       nil,
			wantErr:        true,
			expectedErrMsg: "connection timeout",
		},
		{
			name:   "部分文档解析失败",
			ids:    []string{"1", "2"},
			fields: []string{"title"},
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				mockResponse := &elasticv7.MgetResponse{
					Docs: []*elasticv7.GetResult{
						{
							Id:     "1",
							Index:  "test_index",
							Found:  true,
							Source: json.RawMessage(`{"title": "正常标题"}`),
						},
						{
							Id:     "2",
							Index:  "test_index",
							Found:  true,
							Source: json.RawMessage(`invalid json`), // 无效JSON会导致解析失败
						},
					},
				}
				mockMultiGetService := NewMockMultiGetService(mockResponse, nil)
				mockReadClient := NewMockElasticReadClient(mockMultiGetService)

				// 模拟数据服务，对第二个文档返回错误
				mockDataService := NewMockESDataService()
				mockDataService.SetParseErrors(map[string]error{
					"2": errors.New("parse error: invalid json"),
				})
				return mockReadClient, mockDataService
			},
			wantSuccess: map[string]*ESDataInfo{
				"1": {ID: "1", Index: "test_index", Found: true},
			},
			wantFail: map[string]string{
				"2": "parse error: invalid json",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建基础ESObj
			proxy, _ := es.NewSimpleElasticClientV7("test", nil, elasticv7.SetURL(""), elasticv7.SetBasicAuth("", ""))
			esObj := &ESObj{
				Ctx:          trpc.BackgroundContext(),
				proxy:        proxy,
				Name:         "test_index",
				DatasourceID: 1,
			}

			var testableESObj *TestableESObj
			if tt.setupMock != nil {
				mockReadClient, mockDataService := tt.setupMock()
				testableESObj = &TestableESObj{
					ESObj:                esObj,
					readClientInterface:  mockReadClient,
					dataServiceInterface: mockDataService,
				}
			} else {
				testableESObj = &TestableESObj{ESObj: esObj}
			}

			gotSuccess, gotFail, err := testableESObj.GetFieldInfosByIDs(tt.ids, tt.fields)

			// 验证错误
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFieldInfosByIDs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && tt.expectedErrMsg != "" && (err == nil || err.Error() != tt.expectedErrMsg) {
				t.Errorf("GetFieldInfosByIDs() error = %v, expectedErrMsg %v", err, tt.expectedErrMsg)
				return
			}

			// 验证成功结果（简化比较，只比较基本字段）
			if len(gotSuccess) != len(tt.wantSuccess) {
				t.Errorf("GetFieldInfosByIDs() gotSuccess length = %v, want %v", len(gotSuccess), len(tt.wantSuccess))
			}
			for id := range tt.wantSuccess {
				if _, exists := gotSuccess[id]; !exists {
					t.Errorf("GetFieldInfosByIDs() missing success result for id %v", id)
				}
			}

			// 验证失败结果
			if !reflect.DeepEqual(gotFail, tt.wantFail) {
				t.Errorf("GetFieldInfosByIDs() gotFail = %v, want %v", gotFail, tt.wantFail)
			}
		})
	}
}

// TestESObj_ParseESGetResult2ESDataInfo 全面测试ParseESGetResult2ESDataInfo函数
func TestESObj_ParseESGetResult2ESDataInfo(t *testing.T) {
	tests := []struct {
		name       string
		getResult  *elasticv7.GetResult
		setupMock  func() *MockESDataService
		want       *ESDataInfo
		wantErr    bool
		checkError bool
	}{
		{
			name: "完整的GetResult转换",
			getResult: &elasticv7.GetResult{
				Id:          "test_id_123",
				Index:       "test_index",
				Version:     &[]int64{1}[0],
				SeqNo:       &[]int64{100}[0],
				PrimaryTerm: &[]int64{5}[0],
				Found:       true,
				Source:      json.RawMessage(`{"title": "测试标题", "content": "测试内容"}`),
			},
			setupMock: func() *MockESDataService {
				mockDataService := NewMockESDataService()
				// 设置ESSource2FieldInfos的响应
				mockDataService.SetESSource2FieldInfosResponse(map[string]*protocol.FieldInfo{
					"title": {
						FieldName: "title",
						FieldType: 1,
						StrValue:  "测试标题",
					},
					"content": {
						FieldName: "content",
						FieldType: 1,
						StrValue:  "测试内容",
					},
				}, nil)
				return mockDataService
			},
			want: &ESDataInfo{
				ID:          "test_id_123",
				Index:       "test_index",
				Version:     &[]int64{1}[0],
				SeqNo:       &[]int64{100}[0],
				PrimaryTerm: &[]int64{5}[0],
				Found:       true,
				FieldInfos: map[string]*protocol.FieldInfo{
					"title": {
						FieldName: "title",
						FieldType: 1,
						StrValue:  "测试标题",
					},
					"content": {
						FieldName: "content",
						FieldType: 1,
						StrValue:  "测试内容",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "文档未找到",
			getResult: &elasticv7.GetResult{
				Id:    "missing_id",
				Index: "test_index",
				Found: false,
			},
			setupMock: func() *MockESDataService {
				mockDataService := NewMockESDataService()
				mockDataService.SetESSource2FieldInfosResponse(nil, nil)
				return mockDataService
			},
			want: &ESDataInfo{
				ID:         "missing_id",
				Index:      "test_index",
				Found:      false,
				FieldInfos: nil,
			},
			wantErr: false,
		},
		{
			name: "Source解析错误",
			getResult: &elasticv7.GetResult{
				Id:     "error_id",
				Index:  "test_index",
				Found:  true,
				Source: json.RawMessage(`invalid json`),
			},
			setupMock: func() *MockESDataService {
				mockDataService := NewMockESDataService()
				mockDataService.SetESSource2FieldInfosResponse(nil, errors.New("json parse error"))
				return mockDataService
			},
			want: &ESDataInfo{
				ID:         "error_id",
				Index:      "test_index",
				Found:      true,
				FieldInfos: nil,
			},
			wantErr:    false, // 函数记录错误但不返回错误
			checkError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			esObj := &ESObj{
				Ctx:          trpc.BackgroundContext(),
				Name:         "test_index",
				DatasourceID: 1,
			}

			mockDataService := tt.setupMock()
			testableESObj := &TestableESObj{
				ESObj:                esObj,
				dataServiceInterface: mockDataService,
			}

			got, err := testableESObj.ParseESGetResult2ESDataInfo(tt.getResult)

			if (err != nil) != tt.wantErr {
				t.Errorf("ParseESGetResult2ESDataInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 比较基本字段
			if got.ID != tt.want.ID {
				t.Errorf("ParseESGetResult2ESDataInfo() ID = %v, want %v", got.ID, tt.want.ID)
			}
			if got.Index != tt.want.Index {
				t.Errorf("ParseESGetResult2ESDataInfo() Index = %v, want %v", got.Index, tt.want.Index)
			}
			if got.Found != tt.want.Found {
				t.Errorf("ParseESGetResult2ESDataInfo() Found = %v, want %v", got.Found, tt.want.Found)
			}

			// 比较FieldInfos
			if !reflect.DeepEqual(got.FieldInfos, tt.want.FieldInfos) {
				t.Errorf("ParseESGetResult2ESDataInfo() FieldInfos = %v, want %v", got.FieldInfos, tt.want.FieldInfos)
			}
		})
	}
}

// TestESObj_ESSource2FieldInfos 全面测试ESSource2FieldInfos函数
func TestESObj_ESSource2FieldInfos(t *testing.T) {
	tests := []struct {
		name      string
		source    json.RawMessage
		setupMock func() *MockESDataService
		want      map[string]*protocol.FieldInfo
		wantErr   bool
	}{
		{
			name:   "标准JSON解析",
			source: json.RawMessage(`{"title": "测试标题", "age": 25, "tags": ["golang", "test"]}`),
			setupMock: func() *MockESDataService {
				mockDataService := NewMockESDataService()
				// 设置字段类型转换
				responses := map[string]*protocol.FieldInfo{
					"title": {FieldName: "title", FieldType: 1, StrValue: "测试标题"},
					"age":   {FieldName: "age", FieldType: 2, VecInt: []uint32{25}},
					"tags":  {FieldName: "tags", FieldType: 3, VecStr: []string{"golang", "test"}},
				}
				for field, response := range responses {
					mockDataService.SetESValTrans2FieldInfoResponseForField(field, response)
				}
				return mockDataService
			},
			want: map[string]*protocol.FieldInfo{
				"title": {FieldName: "title", FieldType: 1, StrValue: "测试标题"},
				"age":   {FieldName: "age", FieldType: 2, VecInt: []uint32{25}},
				"tags":  {FieldName: "tags", FieldType: 3, VecStr: []string{"golang", "test"}},
			},
			wantErr: false,
		},
		{
			name:   "空JSON对象",
			source: json.RawMessage(`{}`),
			setupMock: func() *MockESDataService {
				return NewMockESDataService()
			},
			want:    map[string]*protocol.FieldInfo{},
			wantErr: false,
		},
		{
			name:   "嵌套JSON对象",
			source: json.RawMessage(`{"user": {"name": "张三", "age": 30}, "status": "active"}`),
			setupMock: func() *MockESDataService {
				mockDataService := NewMockESDataService()
				// 设置复杂对象的转换
				mockDataService.SetESValTrans2FieldInfoResponseForField("user", &protocol.FieldInfo{
					FieldName: "user",
					FieldType: 1,
					StrValue:  `{"name":"张三","age":30}`,
				})
				mockDataService.SetESValTrans2FieldInfoResponseForField("status", &protocol.FieldInfo{
					FieldName: "status",
					FieldType: 1,
					StrValue:  "active",
				})
				return mockDataService
			},
			want: map[string]*protocol.FieldInfo{
				"user": {
					FieldName: "user",
					FieldType: 1,
					StrValue:  `{"name":"张三","age":30}`,
				},
				"status": {
					FieldName: "status",
					FieldType: 1,
					StrValue:  "active",
				},
			},
			wantErr: false,
		},
		{
			name:   "无效JSON",
			source: json.RawMessage(`{invalid json`),
			setupMock: func() *MockESDataService {
				return NewMockESDataService()
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:   "NULL值处理",
			source: json.RawMessage(`{"title": "有效标题", "description": null, "count": 0}`),
			setupMock: func() *MockESDataService {
				mockDataService := NewMockESDataService()
				mockDataService.SetESValTrans2FieldInfoResponseForField("title", &protocol.FieldInfo{
					FieldName: "title",
					FieldType: 1,
					StrValue:  "有效标题",
				})
				mockDataService.SetESValTrans2FieldInfoResponseForField("description", &protocol.FieldInfo{
					FieldName: "description",
					FieldType: 1,
					StrValue:  "",
				})
				mockDataService.SetESValTrans2FieldInfoResponseForField("count", &protocol.FieldInfo{
					FieldName: "count",
					FieldType: 2,
					VecInt:    []uint32{0},
				})
				return mockDataService
			},
			want: map[string]*protocol.FieldInfo{
				"title": {
					FieldName: "title",
					FieldType: 1,
					StrValue:  "有效标题",
				},
				"description": {
					FieldName: "description",
					FieldType: 1,
					StrValue:  "",
				},
				"count": {
					FieldName: "count",
					FieldType: 2,
					VecInt:    []uint32{0},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			esObj := &ESObj{
				Ctx:       trpc.BackgroundContext(),
				DatasetID: 1,
			}

			mockDataService := tt.setupMock()
			testableESObj := &TestableESObj{
				ESObj:                esObj,
				dataServiceInterface: mockDataService,
			}

			got, err := testableESObj.ESSource2FieldInfos(tt.source)

			if (err != nil) != tt.wantErr {
				t.Errorf("ESSource2FieldInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ESSource2FieldInfos() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestESObj_GetCurValByID 全面测试GetCurValByID函数
func TestESObj_GetCurValByID(t *testing.T) {
	tests := []struct {
		name        string
		id          string
		setupMock   func() (*MockElasticReadClient, *MockESDataService)
		fieldList   []*protocol.UpdateFieldInfo
		want        func(result *ESDataInfo) bool // 使用函数验证结果，更灵活
		wantErr     bool
		errContains string // 期望错误包含的字符串
	}{
		{
			name: "成功获取现有文档",
			id:   "test_id_1",
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				// 模拟Get响应
				mockGetResponse := &elasticv7.GetResult{
					Id:     "test_id_1",
					Index:  "test_index",
					Found:  true,
					Source: json.RawMessage(`{"title": "测试标题", "content": "测试内容", "priority": 1}`),
				}
				mockGetService := NewMockGetService(mockGetResponse, nil)
				mockReadClient := NewMockElasticReadClientWithGet(nil, mockGetService)

				// 模拟数据服务 - 字段转换
				mockDataService := NewMockESDataService()
				mockDataService.SetESValTrans2FieldInfoResponseForField("title", &protocol.FieldInfo{
					FieldName: "title",
					FieldType: 1,
					StrValue:  "测试标题",
				})
				mockDataService.SetESValTrans2FieldInfoResponseForField("content", &protocol.FieldInfo{
					FieldName: "content",
					FieldType: 1,
					StrValue:  "测试内容",
				})
				mockDataService.SetESValTrans2FieldInfoResponseForField("priority", &protocol.FieldInfo{
					FieldName: "priority",
					FieldType: 2,
					VecInt:    []uint32{1},
				})

				return mockReadClient, mockDataService
			},
			fieldList: []*protocol.UpdateFieldInfo{
				{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "title",
						FieldType: 1,
					},
				},
				{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "content",
						FieldType: 1,
					},
				},
				{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "priority",
						FieldType: 2,
					},
				},
			},
			want: func(result *ESDataInfo) bool {
				return result != nil &&
					result.ID == "test_id_1" &&
					result.Found == true &&
					len(result.FieldInfos) == 3 &&
					result.FieldInfos["title"] != nil &&
					result.FieldInfos["title"].StrValue == "测试标题"
			},
			wantErr: false,
		},
		{
			name: "文档不存在（404错误）",
			id:   "non_existent_id",
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				// 模拟404错误
				mockGetService := NewMockGetService(nil, errors.New("404 not found"))
				mockReadClient := NewMockElasticReadClientWithGet(nil, mockGetService)
				return mockReadClient, NewMockESDataService()
			},
			fieldList: []*protocol.UpdateFieldInfo{},
			want: func(result *ESDataInfo) bool {
				return result != nil &&
					result.ID == "non_existent_id" &&
					result.Found == false
			},
			wantErr: false,
		},
		{
			name: "ElasticSearch连接错误",
			id:   "test_id_error",
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				// 模拟连接错误
				mockGetService := NewMockGetService(nil, errors.New("connection timeout"))
				mockReadClient := NewMockElasticReadClientWithGet(nil, mockGetService)
				return mockReadClient, NewMockESDataService()
			},
			fieldList: []*protocol.UpdateFieldInfo{},
			want: func(result *ESDataInfo) bool {
				return result != nil // 错误情况下也应该返回esDataInfo
			},
			wantErr:     true,
			errContains: "connection timeout",
		},
		{
			name: "JSON解析错误",
			id:   "invalid_json_id",
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				// 模拟无效JSON响应
				mockGetResponse := &elasticv7.GetResult{
					Id:     "invalid_json_id",
					Index:  "test_index",
					Found:  true,
					Source: json.RawMessage(`{invalid json`), // 无效JSON
				}
				mockGetService := NewMockGetService(mockGetResponse, nil)
				mockReadClient := NewMockElasticReadClientWithGet(nil, mockGetService)
				return mockReadClient, NewMockESDataService()
			},
			fieldList: []*protocol.UpdateFieldInfo{
				{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "title",
						FieldType: 1,
					},
				},
			},
			want: func(result *ESDataInfo) bool {
				return result != nil && result.ID == "invalid_json_id"
			},
			wantErr: true,
		},
		{
			name: "部分字段存在",
			id:   "partial_fields_id",
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				// 只有部分字段存在的文档
				mockGetResponse := &elasticv7.GetResult{
					Id:     "partial_fields_id",
					Index:  "test_index",
					Found:  true,
					Source: json.RawMessage(`{"title": "存在的标题"}`), // 只有title字段
				}
				mockGetService := NewMockGetService(mockGetResponse, nil)
				mockReadClient := NewMockElasticReadClientWithGet(nil, mockGetService)

				mockDataService := NewMockESDataService()
				mockDataService.SetESValTrans2FieldInfoResponseForField("title", &protocol.FieldInfo{
					FieldName: "title",
					FieldType: 1,
					StrValue:  "存在的标题",
				})
				return mockReadClient, mockDataService
			},
			fieldList: []*protocol.UpdateFieldInfo{
				{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "title",
						FieldType: 1,
					},
				},
				{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "missing_field", // 这个字段不存在
						FieldType: 1,
					},
				},
			},
			want: func(result *ESDataInfo) bool {
				return result != nil &&
					result.ID == "partial_fields_id" &&
					result.Found == true &&
					len(result.FieldInfos) == 1 && // 只有存在的字段
					result.FieldInfos["title"] != nil &&
					result.FieldInfos["missing_field"] == nil // 不存在的字段应该被跳过
			},
			wantErr: false,
		},
		{
			name: "空字段列表",
			id:   "empty_fields_id",
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				mockGetResponse := &elasticv7.GetResult{
					Id:     "empty_fields_id",
					Index:  "test_index",
					Found:  true,
					Source: json.RawMessage(`{"title": "测试", "content": "内容"}`),
				}
				mockGetService := NewMockGetService(mockGetResponse, nil)
				mockReadClient := NewMockElasticReadClientWithGet(nil, mockGetService)
				return mockReadClient, NewMockESDataService()
			},
			fieldList: []*protocol.UpdateFieldInfo{}, // 空字段列表
			want: func(result *ESDataInfo) bool {
				return result != nil &&
					result.ID == "empty_fields_id" &&
					result.Found == true &&
					len(result.FieldInfos) == 0 // 没有字段需要处理
			},
			wantErr: false,
		},
		{
			name: "复杂数据类型转换",
			id:   "complex_data_id",
			setupMock: func() (*MockElasticReadClient, *MockESDataService) {
				mockGetResponse := &elasticv7.GetResult{
					Id:     "complex_data_id",
					Index:  "test_index",
					Found:  true,
					Source: json.RawMessage(`{"tags": ["tag1", "tag2", "tag3"], "scores": [85, 90, 78], "metadata": {"author": "张三", "category": "技术"}}`),
				}
				mockGetService := NewMockGetService(mockGetResponse, nil)
				mockReadClient := NewMockElasticReadClientWithGet(nil, mockGetService)

				mockDataService := NewMockESDataService()
				mockDataService.SetESValTrans2FieldInfoResponseForField("tags", &protocol.FieldInfo{
					FieldName: "tags",
					FieldType: 3, // VecStr类型
					VecStr:    []string{"tag1", "tag2", "tag3"},
				})
				mockDataService.SetESValTrans2FieldInfoResponseForField("scores", &protocol.FieldInfo{
					FieldName: "scores",
					FieldType: 2, // VecInt类型
					VecInt:    []uint32{85, 90, 78},
				})
				mockDataService.SetESValTrans2FieldInfoResponseForField("metadata", &protocol.FieldInfo{
					FieldName: "metadata",
					FieldType: 1, // String类型（JSON序列化）
					StrValue:  `{"author":"张三","category":"技术"}`,
				})
				return mockReadClient, mockDataService
			},
			fieldList: []*protocol.UpdateFieldInfo{
				{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "tags",
						FieldType: 3,
					},
				},
				{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "scores",
						FieldType: 2,
					},
				},
				{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "metadata",
						FieldType: 1,
					},
				},
			},
			want: func(result *ESDataInfo) bool {
				return result != nil &&
					result.ID == "complex_data_id" &&
					result.Found == true &&
					len(result.FieldInfos) == 3 &&
					result.FieldInfos["tags"] != nil &&
					len(result.FieldInfos["tags"].VecStr) == 3 &&
					result.FieldInfos["scores"] != nil &&
					len(result.FieldInfos["scores"].VecInt) == 3 &&
					result.FieldInfos["metadata"] != nil &&
					strings.Contains(result.FieldInfos["metadata"].StrValue, "张三")
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建基础ESObj
			proxy, _ := es.NewSimpleElasticClientV7("test", nil, elasticv7.SetURL(""), elasticv7.SetBasicAuth("", ""))
			esObj := &ESObj{
				Ctx:          trpc.BackgroundContext(),
				proxy:        proxy,
				Name:         "test_index",
				DatasetID:    1,
				DatasourceID: 1,
				fieldList:    tt.fieldList,
			}

			// 设置mock
			mockReadClient, mockDataService := tt.setupMock()
			testableESObj := &TestableESObj{
				ESObj:                esObj,
				readClientInterface:  mockReadClient,
				dataServiceInterface: mockDataService,
			}

			// 执行测试
			got, err := testableESObj.GetCurValByID(tt.id)

			// 验证错误
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCurValByID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && tt.errContains != "" {
				if err == nil || !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("GetCurValByID() error = %v, expected to contain %v", err, tt.errContains)
					return
				}
			}

			// 验证结果
			if !tt.want(got) {
				t.Errorf("GetCurValByID() result validation failed, got = %+v", got)
			}
		})
	}
}

// 扩展MockESDataService以支持更多测试场景
func (m *MockESDataService) SetParseErrors(errors map[string]error) {
	if m.parseErrors == nil {
		m.parseErrors = make(map[string]error)
	}
	for id, err := range errors {
		m.parseErrors[id] = err
	}
}

func (m *MockESDataService) SetESSource2FieldInfosResponse(fieldInfos map[string]*protocol.FieldInfo, err error) {
	m.esSource2FieldInfosResponse = fieldInfos
	m.esSource2FieldInfosError = err
}

func (m *MockESDataService) SetESValTrans2FieldInfoResponseForField(field string, response *protocol.FieldInfo) {
	if m.esValTrans2FieldInfoResponses == nil {
		m.esValTrans2FieldInfoResponses = make(map[string]*protocol.FieldInfo)
	}
	m.esValTrans2FieldInfoResponses[field] = response
}
