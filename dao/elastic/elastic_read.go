package elastic

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	elasticv7 "github.com/olivere/elastic/v7"
)

// ESDataInfo ES数据信息
type ESDataInfo struct {
	Index       string                         `json:"index"`
	ID          string                         `json:"id"`      // id meta field
	Version     *int64                         `json:"version"` // version number, when Version is set to true in SearchService
	SeqNo       *int64                         `json:"seq_no"`
	PrimaryTerm *int64                         `json:"primary_term"`
	Found       bool                           `json:"found,omitempty"`
	SourceMap   map[string]interface{}         `json:"source_map"` // ES返回的source
	FieldInfos  map[string]*protocol.FieldInfo `json:"field_infos,omitempty"`
}

// NewESDataInfo ...
func NewESDataInfo(index, id string) *ESDataInfo {
	return &ESDataInfo{
		Index:      index,
		ID:         id,
		Found:      true,
		SourceMap:  make(map[string]interface{}),
		FieldInfos: make(map[string]*protocol.FieldInfo),
	}
}

// GetCurValByID 通过ID查询更新字段当前值,实时生效
func (e *ESObj) GetCurValByID(id string) (*ESDataInfo, error) {
	esDataInfo := NewESDataInfo(e.Name, id)
	res, err := e.proxy.Get().Index(e.GetKey()).Id(id).Do(e.NewCtxWithMethod("GetIndex"))
	if err != nil {
		log.Errorf("GetFieldInfosByIDErr, id:%v, err:%v", id, err)
		if strings.Contains(err.Error(), "404") { // 404 不报错返回not_found
			esDataInfo.Found = false
			return esDataInfo, nil
		}
		return esDataInfo, err
	}

	cur := make(map[string]interface{})
	if err := json.Unmarshal(res.Source, &cur); err != nil {
		return esDataInfo, err
	}
	log.Debugf("esRes:%v", res)
	for _, f := range e.fieldList {
		fieldName := f.GetFieldInfo().GetFieldName()
		fieldType := f.GetFieldInfo().GetFieldType()
		val, ok := cur[fieldName]
		if !ok {
			// 更新字段不存在直接跳过
			continue
		}
		esDataInfo.FieldInfos[fieldName] = e.ESValTrans2FieldInfo(fieldName, fieldType, val)
	}
	return esDataInfo, nil
}

// GetFieldInfosByIDs 通过ID查询,实时生效
// NOCC:CCN_threshold(设计如此:)
func (e *ESObj) GetFieldInfosByIDs(ids, fields []string) (map[string]*ESDataInfo, map[string]string, error) {
	if len(ids) == 0 {
		return nil, nil, nil
	}
	var items []*elasticv7.MultiGetItem
	fetSource := elasticv7.NewFetchSourceContext(true)
	fetSource.Include(fields...)
	isGetAll := false
	if len(fields) > 0 && fields[0] == "*" {
		isGetAll = true
	}
	for _, id := range ids {
		item := elasticv7.NewMultiGetItem().Index(e.GetKey()).Id(id)
		if !isGetAll { // getAll不需要传source
			item.FetchSource(fetSource)
		}
		items = append(items, item)
	}
	log.Debugf("GetFieldInfosByIDs:%v, fields:%v", strings.Join(ids, ", "), strings.Join(fields, ", "))
	res, err := e.proxy.MultiGet().Add(items...).Do(e.NewCtxWithMethod("MultiGet"))
	if err != nil {
		log.Errorf("GetFieldInfosByIDErr, id:%v, err:%v", strings.Join(ids, EsValuesDelimiter), err)
		return nil, nil, err
	}
	resStr, _ := json.Marshal(res)
	log.Debugf("MultiGetIDs:%v,res:%v,err:%v", strings.Join(ids, EsValuesDelimiter), string(resStr), err)
	mapSuc := make(map[string]*ESDataInfo)
	mapFail := make(map[string]string)
	for _, doc := range res.Docs {
		if dataInfo, err := e.ParseESGetResult2ESDataInfo(doc); err != nil {
			mapFail[doc.Id] = err.Error()
		} else {
			mapSuc[doc.Id] = dataInfo
		}
	}
	return mapSuc, mapFail, nil
}

// ParseESGetResult2ESDataInfo 将ES返回的GetResult转成ESDataInfo
func (e *ESObj) ParseESGetResult2ESDataInfo(res *elasticv7.GetResult) (*ESDataInfo, error) {
	esDataInfo := &ESDataInfo{
		ID:          res.Id,
		Index:       res.Index,
		Version:     res.Version,
		SeqNo:       res.SeqNo,
		PrimaryTerm: res.PrimaryTerm,
		Found:       res.Found,
	}
	var err error
	esDataInfo.FieldInfos, err = e.ESSource2FieldInfos(res.Source)
	if err != nil {
		log.Errorf(err.Error())
	}
	return esDataInfo, nil
}

// ESSource2FieldInfos ...
func (e *ESObj) ESSource2FieldInfos(source json.RawMessage) (map[string]*protocol.FieldInfo, error) {
	fieldInfos := make(map[string]interface{})
	err := json.Unmarshal(source, &fieldInfos)
	if err != nil {
		return nil, err
	}
	res := make(map[string]*protocol.FieldInfo)
	for k, v := range fieldInfos {
		var fieldType protocol.EnumFieldType
		f := cache.GetFieldInfoByName(k, e.DatasetID)
		if f != nil {
			fieldType = protocol.EnumFieldType(f.FieldType)
		}
		res[k] = e.ESValTrans2FieldInfo(k, fieldType, v)
	}
	return res, nil
}

// ESValTrans2FieldInfo ...
// NOCC:CCN_threshold(设计如此:)
func (e *ESObj) ESValTrans2FieldInfo(key string, fieldType protocol.EnumFieldType,
	esVal interface{},
) *protocol.FieldInfo {
	res := &protocol.FieldInfo{
		FieldName: key,
	}
	defer func() { // 返回前兜底
		if res.FieldType == 0 {
			res.FieldType = protocol.EnumFieldType_FieldTypeStr
			res.StrValue = fmt.Sprintf("%v", esVal)
		}
	}()

	res.FieldType = fieldType
	switch fieldType {
	case protocol.EnumFieldType_FieldTypeStr:
		res.StrValue = fmt.Sprintf("%v", esVal)
	case protocol.EnumFieldType_FieldTypeIntVec:
		if vec, suc := ParseESItfVal2VecInt(esVal); suc {
			res.VecInt = vec
		} else { // 未存成数组
			iv, err := strconv.Atoi(fmt.Sprintf("%v", esVal))
			if err == nil {
				res.VecInt = append(res.VecInt, uint32(iv))
			}
		}
	case protocol.EnumFieldType_FieldTypeSet:
		res.VecStr = ParseESItfVal2VecStr(esVal)
	case protocol.EnumFieldType_FieldTypeMapKV, protocol.EnumFieldType_FieldTypeMapKList:
		bVal, _ := json.Marshal(esVal)
		_ = json.Unmarshal(bVal, &res.MapVal)
	default:
	}
	return res
}

// ParseESItfVal2VecInt ...
func ParseESItfVal2VecInt(esVal interface{}) ([]uint32, bool) {
	var res []uint32
	valStr := fmt.Sprintf("%v", esVal)
	strVals := strings.Split(valStr, "+")
	for _, v := range strVals {
		iVal, err := strconv.Atoi(v)
		if err == nil {
			res = append(res, uint32(iVal))
		}
	}

	return res, true
}

// ParseESItfVal2VecStr ...
func ParseESItfVal2VecStr(esVal interface{}) []string {
	return strings.Split(fmt.Sprintf("%v", esVal), "+")
}
