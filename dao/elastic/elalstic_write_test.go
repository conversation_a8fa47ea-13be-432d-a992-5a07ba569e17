package elastic

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"strings"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/es"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	cache "git.code.oa.com/video_media/storage_service/config_cache"

	elasticv7 "github.com/olivere/elastic/v7"
)

// ESDataServiceInterface ES数据服务接口，用于测试中的模拟
type ESDataServiceInterface interface {
	GetCurValByID(id string) (*ESDataInfo, error)
	IndexData(id string, info map[string]interface{}) error
	UpdateData(id string, info map[string]interface{}) error
	GetFieldInfosByIDs(ids, fields []string) (map[string]*ESDataInfo, map[string]string, error)
	ESValTrans2FieldInfo(key string, fieldType protocol.EnumFieldType, esVal interface{}) *protocol.FieldInfo
	ParseESGetResult2ESDataInfo(res *elasticv7.GetResult) (*ESDataInfo, error)
	ESSource2FieldInfos(source json.RawMessage) (map[string]*protocol.FieldInfo, error)
}

// BulkServiceInterface Bulk服务接口，用于测试中的模拟
type BulkServiceInterface interface {
	Add(requests ...elasticv7.BulkableRequest) *elasticv7.BulkService
	Do(ctx context.Context) (*elasticv7.BulkResponse, error)
}

// MockBulkService 模拟Bulk服务实现
type MockBulkService struct {
	response *elasticv7.BulkResponse
	err      error
}

func NewMockBulkService(response *elasticv7.BulkResponse, err error) *MockBulkService {
	return &MockBulkService{
		response: response,
		err:      err,
	}
}

func (m *MockBulkService) Add(requests ...elasticv7.BulkableRequest) *elasticv7.BulkService {
	// 返回nil，因为我们只是模拟，不需要真实的BulkService
	return nil
}

func (m *MockBulkService) Do(ctx context.Context) (*elasticv7.BulkResponse, error) {
	return m.response, m.err
}

// ElasticClientInterface Elasticsearch客户端接口，用于测试中的模拟
type ElasticClientInterface interface {
	Bulk() BulkServiceInterface
}

// MockElasticClient 模拟Elasticsearch客户端实现
type MockElasticClient struct {
	bulkService BulkServiceInterface
}

func NewMockElasticClient(bulkService BulkServiceInterface) *MockElasticClient {
	return &MockElasticClient{
		bulkService: bulkService,
	}
}

func (m *MockElasticClient) Bulk() BulkServiceInterface {
	return m.bulkService
}

// ElasticReadClientInterface Elasticsearch读取客户端接口，用于测试中的模拟
type ElasticReadClientInterface interface {
	MultiGet() MultiGetServiceInterface
	Get() GetServiceInterface
	Search(indices ...string) SearchServiceInterface
}

// MultiGetServiceInterface MultiGet服务接口，用于测试中的模拟
type MultiGetServiceInterface interface {
	Add(items ...*elasticv7.MultiGetItem) *elasticv7.MgetService
	Do(ctx context.Context) (*elasticv7.MgetResponse, error)
}

// GetServiceInterface Get服务接口，用于测试中的模拟
type GetServiceInterface interface {
	Index(index string) GetServiceInterface
	Id(id string) GetServiceInterface
	Do(ctx context.Context) (*elasticv7.GetResult, error)
}

// SearchServiceInterface Search服务接口，用于测试中的模拟
type SearchServiceInterface interface {
	Index(index string) SearchServiceInterface
	Query(query elasticv7.Query) SearchServiceInterface
	Source(source elasticv7.FetchSourceContext) SearchServiceInterface
	From(from int) SearchServiceInterface
	Size(size int) SearchServiceInterface
	Sort(field string, ascending bool) SearchServiceInterface
	Do(ctx context.Context) (*elasticv7.SearchResult, error)
}

// TestableESObj 可测试的ES对象，允许注入客户端接口和数据服务接口
type TestableESObj struct {
	*ESObj
	clientInterface      ElasticClientInterface
	readClientInterface  ElasticReadClientInterface
	dataServiceInterface ESDataServiceInterface
}

// BulkData 重写BulkData方法以使用注入的客户端
func (te *TestableESObj) BulkData(docInfos map[string]interface{}) (map[string]string, error) {
	var bulkRequest BulkServiceInterface
	if te.clientInterface != nil {
		bulkRequest = te.clientInterface.Bulk()
	} else {
		// 如果没有注入客户端，使用原始的proxy（向后兼容）
		bulkRequest = &RealBulkServiceWrapper{service: te.proxy.Bulk()}
	}

	for id, info := range docInfos {
		bulkRequest.Add(elasticv7.NewBulkIndexRequest().Index(te.GetKey()).Id(id).Doc(info).RetryOnConflict(3))
	}

	rsp, err := bulkRequest.Do(te.NewCtxWithMethod("BulkIndex"))
	if err != nil {
		return nil, err
	}

	failList := make(map[string]string)
	if len(rsp.Failed()) > 0 {
		for _, f := range rsp.Failed() {
			failList[f.Id] = f.Error.Reason
		}
	}
	return failList, nil
}

// SetFieldInfos 重写SetFieldInfos方法以使用注入的数据服务
func (te *TestableESObj) SetFieldInfos(id string, baseFieldIDs []*protocol.FieldInfo, fieldList []*protocol.UpdateFieldInfo,
	rsp *adaptor.SetFieldInfosResponse,
) (*model.ModifyInfo, error) {
	// 初始化es对象内部数据
	te.fieldList = fieldList
	// 读旧值--查回全部字段,因为要覆盖写
	var esData *ESDataInfo
	var err error

	if te.dataServiceInterface != nil {
		esData, err = te.dataServiceInterface.GetCurValByID(id)
	} else {
		esData, err = te.ESObj.GetCurValByID(id)
	}

	if err != nil {
		return nil, err
	}

	// 新旧值合并
	mapInfo, modifyInfos := MergeOldInfoAndUpdateInfo(esData.FieldInfos, fieldList)
	if len(modifyInfos) == 0 {
		return nil, nil
	}

	if esData.Found {
		if te.dataServiceInterface != nil {
			err = te.dataServiceInterface.UpdateData(id, mapInfo)
		} else {
			err = te.ESObj.UpdateData(id, mapInfo)
		}
	} else {
		if te.dataServiceInterface != nil {
			err = te.dataServiceInterface.IndexData(id, mapInfo)
		} else {
			err = te.ESObj.IndexData(id, mapInfo)
		}
	}

	if err != nil {
		return nil, err
	}

	rsp.ModifyInfos = modifyInfos
	return &model.ModifyInfo{Infos: modifyInfos}, nil
}

// BatchGetFields 重写BatchGetFields方法以使用注入的数据服务
func (te *TestableESObj) BatchGetFields(datasetID int32, ids, fields []string,
	extra map[string][]string,
) (*model.BatchGetResponse, error) {
	te.DatasetID = datasetID

	var sucMap map[string]*ESDataInfo
	var failList map[string]string
	var err error

	if te.dataServiceInterface != nil {
		sucMap, failList, err = te.dataServiceInterface.GetFieldInfosByIDs(ids, fields)
	} else {
		sucMap, failList, err = te.ESObj.GetFieldInfosByIDs(ids, fields)
	}

	if err != nil {
		return nil, err
	}

	var bRsp model.BatchGetResponse
	for k, v := range sucMap {
		bRsp.DocInfos = append(bRsp.DocInfos, &protocol.DocInfo{
			Id:     k,
			Fields: v.FieldInfos,
		})
	}
	bRsp.FailList = failList
	return &bRsp, nil
}

// ESSource2FieldInfos 重写ESSource2FieldInfos方法以使用注入的数据服务
func (te *TestableESObj) ESSource2FieldInfos(source json.RawMessage) (map[string]*protocol.FieldInfo, error) {
	fieldInfos := make(map[string]interface{})
	err := json.Unmarshal(source, &fieldInfos)
	if err != nil {
		return nil, err
	}
	res := make(map[string]*protocol.FieldInfo)
	for k, v := range fieldInfos {
		var fieldType protocol.EnumFieldType
		f := cache.GetFieldInfoByName(k, te.DatasetID)
		if f != nil {
			fieldType = protocol.EnumFieldType(f.FieldType)
		}

		if te.dataServiceInterface != nil {
			res[k] = te.dataServiceInterface.ESValTrans2FieldInfo(k, fieldType, v)
		} else {
			res[k] = te.ESObj.ESValTrans2FieldInfo(k, fieldType, v)
		}
	}
	return res, nil
}

// GetCurValByID 重写GetCurValByID方法以使用注入的读取客户端
func (te *TestableESObj) GetCurValByID(id string) (*ESDataInfo, error) {
	esDataInfo := NewESDataInfo(te.Name, id)

	var res *elasticv7.GetResult
	var err error

	if te.readClientInterface != nil {
		// 使用注入的读取客户端
		getService := te.readClientInterface.Get()
		getService = getService.Index(te.GetKey()).Id(id)
		res, err = getService.Do(te.NewCtxWithMethod("GetIndex"))
	} else {
		// 向后兼容：使用原始的ESObj方法
		return te.ESObj.GetCurValByID(id)
	}

	if err != nil {
		if strings.Contains(err.Error(), "404") { // 404 不报错返回not_found
			esDataInfo.Found = false
			return esDataInfo, nil
		}
		return esDataInfo, err
	}

	cur := make(map[string]interface{})
	if err := json.Unmarshal(res.Source, &cur); err != nil {
		return esDataInfo, err
	}

	for _, f := range te.fieldList {
		fieldName := f.GetFieldInfo().GetFieldName()
		fieldType := f.GetFieldInfo().GetFieldType()
		val, ok := cur[fieldName]
		if !ok {
			// 更新字段不存在直接跳过
			continue
		}

		if te.dataServiceInterface != nil {
			esDataInfo.FieldInfos[fieldName] = te.dataServiceInterface.ESValTrans2FieldInfo(fieldName, fieldType, val)
		} else {
			esDataInfo.FieldInfos[fieldName] = te.ESObj.ESValTrans2FieldInfo(fieldName, fieldType, val)
		}
	}
	return esDataInfo, nil
}

// ParseESGetResult2ESDataInfo 重写ParseESGetResult2ESDataInfo方法以使用注入的数据服务
func (te *TestableESObj) ParseESGetResult2ESDataInfo(res *elasticv7.GetResult) (*ESDataInfo, error) {
	esDataInfo := &ESDataInfo{
		ID:          res.Id,
		Index:       res.Index,
		Version:     res.Version,
		SeqNo:       res.SeqNo,
		PrimaryTerm: res.PrimaryTerm,
		Found:       res.Found,
	}

	var err error
	if te.dataServiceInterface != nil {
		// 使用注入的数据服务接口进行ESSource2FieldInfos调用
		esDataInfo.FieldInfos, err = te.dataServiceInterface.ESSource2FieldInfos(res.Source)
	} else {
		// 向后兼容：使用原始的ESObj方法
		esDataInfo.FieldInfos, err = te.ESObj.ESSource2FieldInfos(res.Source)
	}

	if err != nil {
		log.Errorf(err.Error())
	}
	return esDataInfo, nil
}

// GetFieldInfosByIDs 重写GetFieldInfosByIDs方法以使用注入的读取客户端
func (te *TestableESObj) GetFieldInfosByIDs(ids, fields []string) (map[string]*ESDataInfo, map[string]string, error) {
	if len(ids) == 0 {
		return nil, nil, nil
	}

	var res *elasticv7.MgetResponse
	var err error

	if te.readClientInterface != nil {
		// 使用注入的读取客户端
		multiGetService := te.readClientInterface.MultiGet()
		res, err = multiGetService.Do(te.NewCtxWithMethod("MultiGet"))
	} else {
		// 向后兼容：使用原始的ESObj方法
		return te.ESObj.GetFieldInfosByIDs(ids, fields)
	}

	if err != nil {
		return nil, nil, err
	}

	mapSuc := make(map[string]*ESDataInfo)
	mapFail := make(map[string]string)
	for _, doc := range res.Docs {
		var dataInfo *ESDataInfo
		if te.dataServiceInterface != nil {
			dataInfo, err = te.dataServiceInterface.ParseESGetResult2ESDataInfo(doc)
		} else {
			dataInfo, err = te.ESObj.ParseESGetResult2ESDataInfo(doc)
		}

		if err != nil {
			mapFail[doc.Id] = err.Error()
		} else {
			mapSuc[doc.Id] = dataInfo
		}
	}
	return mapSuc, mapFail, nil
}

// RealBulkServiceWrapper 包装真实的BulkService以实现接口
type RealBulkServiceWrapper struct {
	service *elasticv7.BulkService
}

func (r *RealBulkServiceWrapper) Add(requests ...elasticv7.BulkableRequest) *elasticv7.BulkService {
	return r.service.Add(requests...)
}

func (r *RealBulkServiceWrapper) Do(ctx context.Context) (*elasticv7.BulkResponse, error) {
	return r.service.Do(ctx)
}

// RealMultiGetServiceWrapper 包装真实的MultiGetService以实现接口
type RealMultiGetServiceWrapper struct {
	service *elasticv7.MgetService
}

func (r *RealMultiGetServiceWrapper) Add(items ...*elasticv7.MultiGetItem) *elasticv7.MgetService {
	return r.service.Add(items...)
}

func (r *RealMultiGetServiceWrapper) Do(ctx context.Context) (*elasticv7.MgetResponse, error) {
	return r.service.Do(ctx)
}

// Search 重写Search方法以使用注入的读取客户端
func (te *TestableESObj) Search(condGroups []*protocol.SearchCondGroup, logic protocol.Logical, exprCond string,
	includes []string, sort []*protocol.SortInfo, pageInfo *protocol.PageInfo,
) (int64, []*protocol.DocInfo, error) {

	var rsp *elasticv7.SearchResult
	var err error

	if te.readClientInterface != nil {
		// 使用注入的读取客户端
		reqSource, err := GenQuerySource(condGroups, logic, exprCond, includes, sort, pageInfo)
		if err != nil {
			log.Errorf("Error generating query source:%v", err)
			return 0, nil, err
		}

		searchService := te.readClientInterface.Search()
		searchService = searchService.Index(te.Name)
		// 注意：这里简化了Source设置，因为模拟不需要复杂的参数传递
		// reqSource在真实环境中会被使用，但在模拟中我们忽略它
		_ = reqSource
		rsp, err = searchService.Do(te.NewCtxWithMethod("SearchIndex"))
	} else {
		// 向后兼容：使用原始的ESObj方法
		return te.ESObj.Search(condGroups, logic, exprCond, includes, sort, pageInfo)
	}

	log.Debugf("QueryESRsp:%+v, err:%v\n", rsp, err)
	if err != nil {
		return 0, nil, err
	}
	if rsp.Error != nil {
		return 0, nil, errors.New(rsp.Error.Reason)
	}

	var resDocs []*protocol.DocInfo
	for _, info := range rsp.Hits.Hits {
		doc := &protocol.DocInfo{
			Id:       info.Id,
			Fields:   make(map[string]*protocol.FieldInfo),
			FailList: make(map[string]*protocol.FailInfo),
		}

		var fieldInfos map[string]*protocol.FieldInfo
		if te.dataServiceInterface != nil {
			fieldInfos, err = te.ESSource2FieldInfos(info.Source)
		} else {
			fieldInfos, err = te.ESObj.ESSource2FieldInfos(info.Source)
		}

		if err != nil {
			doc.FailList[doc.Id] = &protocol.FailInfo{ErrMsg: err.Error()}
		}
		doc.Fields = fieldInfos
		resDocs = append(resDocs, doc)
	}
	return rsp.TotalHits(), resDocs, nil
}

func TestESObj_BulkData(t *testing.T) {
	type fields struct {
		Ctx          context.Context
		proxy        *elasticv7.Client
		Name         string
		DatasourceID int32
		fieldList    []*protocol.UpdateFieldInfo
	}
	type args struct {
		docInfos map[string]interface{}
	}

	proxy, _ := es.NewSimpleElasticClientV7("trpc.elasticsearch.storage_service.adaptor_layer",
		nil, elasticv7.SetURL(""), elasticv7.SetBasicAuth("", ""))

	// 创建模拟的Bulk响应
	mockResponse := &elasticv7.BulkResponse{
		Took:   0,
		Errors: false,
		Items:  nil,
	}

	// 创建模拟服务
	mockBulkService := NewMockBulkService(mockResponse, nil)
	mockElasticClient := NewMockElasticClient(mockBulkService)

	failList := make(map[string]string)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]string
		wantErr bool
	}{
		{"suc", fields{
			Ctx:   trpc.BackgroundContext(),
			proxy: proxy,
		}, args{docInfos: map[string]interface{}{"1": "123"}}, failList, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			esObj := &ESObj{
				Ctx:          tt.fields.Ctx,
				proxy:        tt.fields.proxy,
				Name:         tt.fields.Name,
				DatasourceID: tt.fields.DatasourceID,
				fieldList:    tt.fields.fieldList,
			}

			// 创建可测试的ESObj，注入模拟客户端
			e := &TestableESObj{
				ESObj:           esObj,
				clientInterface: mockElasticClient,
			}

			got, err := e.BulkData(tt.args.docInfos)
			if (err != nil) != tt.wantErr {
				t.Errorf("BulkData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BulkData() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInitWriteMapFromESCurInfo(t *testing.T) {
	type args struct {
		oldInfo map[string]*protocol.FieldInfo
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		{
			"str", args{oldInfo: map[string]*protocol.FieldInfo{"title": {FieldName: "title", FieldType: 1, StrValue: "测试"}}}, map[string]interface{}{"title": "测试"},
		},
		{
			"str", args{oldInfo: map[string]*protocol.FieldInfo{"title": {FieldName: "title", FieldType: 2, VecInt: []uint32{1}}}}, map[string]interface{}{"title": []uint32{1}},
		},
		{
			"str", args{oldInfo: map[string]*protocol.FieldInfo{"title": {FieldName: "title", FieldType: 3, VecStr: []string{"测试"}}}}, map[string]interface{}{"title": []string{"测试"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InitWriteMapFromESCurInfo(tt.args.oldInfo); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InitWriteMapFromESCurInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMergeOldInfoAndUpdateInfo(t *testing.T) {
	type args struct {
		oldInfo    map[string]*protocol.FieldInfo
		updateList []*protocol.UpdateFieldInfo
	}
	oldInfo := map[string]*protocol.FieldInfo{
		"title":   {StrValue: "测试"},
		"state":   {VecInt: []uint32{1}},
		"vidList": {VecStr: []string{"vid1"}},
	}
	updateInfo := []*protocol.UpdateFieldInfo{
		{
			FieldInfo:  &protocol.FieldInfo{FieldName: "title", StrValue: "修改了", FieldType: 1},
			UpdateType: 1,
		},
		{
			FieldInfo:  &protocol.FieldInfo{FieldName: "state", VecInt: []uint32{2}, FieldType: 2},
			UpdateType: 3,
		},
		{
			FieldInfo:  &protocol.FieldInfo{FieldName: "vidList", VecStr: []string{"vid2"}, FieldType: 3},
			UpdateType: 3,
		},
	}
	modifyInfos := []*protocol.ModifyFieldInfo{
		{FieldName: "title", FieldType: 1, OldStrValue: "测试", NewStrValue: "修改了"},
		{FieldName: "state", FieldType: 2, OldVecInt: []uint32{1}, NewVecInt: []uint32{1, 2}},
		{FieldName: "vidList", FieldType: 3, OldVecStr: []string{"vid1"}, NewVecStr: []string{"vid1", "vid2"}},
	}
	res := make(map[string]interface{})
	res["title"] = "修改了"
	res["state"] = "1+2"
	res["vidList"] = "vid1+vid2"
	tests := []struct {
		name  string
		args  args
		want  map[string]interface{}
		want1 []*protocol.ModifyFieldInfo
	}{
		{
			"suc", args{
				oldInfo:    oldInfo,
				updateList: updateInfo,
			}, res, modifyInfos,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := MergeOldInfoAndUpdateInfo(tt.args.oldInfo, tt.args.updateList)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MergeOldInfoAndUpdateInfo() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("MergeOldInfoAndUpdateInfo() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
