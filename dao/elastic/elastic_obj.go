// Package elastic 数据适配层服务elastic类型存储相关逻辑
package elastic

import (
	"context"
	"net/url"
	"strings"
	"sync"

	"git.code.oa.com/trpc-go/trpc-database/es"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	tool "git.code.oa.com/video_media/storage_service/common"

	elasticv7 "github.com/olivere/elastic/v7"
)

const (
	EsValuesDelimiter       = "+"
	EsValuesDelimiterEncode = "%2B"
)

var esClientsProxy sync.Map

const (
	rpcNameKey codec.ContextKey = "method"
	// ES服务名
	serviceName = "trpc.storage_service.adaptor_layer.elasticsearch"
)

func init() {
	// 重写上报RPC name函数，用于上报被调接口
	es.RewriteRPCNameFunc = func(ctx context.Context, requestURL *url.URL) string {
		// 使用上下文透传的method作为rpc name，为空则使用url path
		method, _ := ctx.Value(rpcNameKey).(string)
		if method != "" {
			return method
		}
		return requestURL.Path
	}
}

// ESObj es存储对象
type ESObj struct {
	// Ctx 上下文
	Ctx context.Context
	// proxy es客户端
	proxy *elasticv7.Client
	// Name es Index名
	Name string
	// DatasetID 数据集ID 主要用在搜索ES结果后,根据此ID去查字段配置,做相应转换
	DatasetID int32
	// DatasourceID 数据源ID
	DatasourceID int32
	// fieldList 更新字段列表
	fieldList []*protocol.UpdateFieldInfo
}

// RouteRuleFunc 实现路由接口
func (e *ESObj) RouteRuleFunc(key, _, rule string) string {
	switch key {
	case inf.NoneFunction:
		e.Name = inf.NoneESFunc(rule)
	}
	return e.Name
}

// GetConn 实现连接接口
func (e *ESObj) GetConn(connInfo, authInfo, _ string) error {
	var err error
	var user, password string
	tokens := strings.Split(authInfo, ":")
	if len(tokens) == 2 {
		user = tokens[0]
		password = tokens[1]
	}
	// 不考虑用LoadOrStore, 因为存的是长链接, 必须明确知道load不到的时候再去new,
	if c, ok := esClientsProxy.Load(e.DatasourceID); ok {
		e.proxy = c.(*elasticv7.Client)
	} else {
		e.proxy, err = es.NewSimpleElasticClientV7(serviceName, []client.Option{client.WithTarget(connInfo)},
			elasticv7.SetSniff(false), elasticv7.SetBasicAuth(user, password))
		if err != nil {
			return err
		}
		esClientsProxy.Store(e.DatasourceID, e.proxy)
	}
	return nil
}

// GetKey 返回设备信息表key值
func (e *ESObj) GetKey() string {
	return e.Name
}

// InsertFieldInfos 统一插入接口
func (e *ESObj) InsertFieldInfos(id string, _ int64, fieldList []*protocol.UpdateFieldInfo) (*model.ModifyInfo, error) {
	return e.SetFieldInfos(id, nil, fieldList, &adaptor.SetFieldInfosResponse{})
}

// CurVal 实现接口CurVal
func (e *ESObj) CurVal(_ *protocol.FieldInfo) (*tool.FieldVal, bool) {
	return &tool.FieldVal{TxtVal: ""}, false
}

// NewCtxWithMethod 创建带rpc name的context
func (e *ESObj) NewCtxWithMethod(method string) context.Context {
	return context.WithValue(e.Ctx, rpcNameKey, serviceName+"/"+method)
}

// SetFieldInfos 统一设置value接口
func (e *ESObj) SetFieldInfos(id string, _ []*protocol.FieldInfo, fieldList []*protocol.UpdateFieldInfo,
	rsp *adaptor.SetFieldInfosResponse,
) (*model.ModifyInfo, error) {
	// 初始化es对象内部数据
	e.fieldList = fieldList
	// 读旧值--查回全部字段,因为要覆盖写
	esData, err := e.GetCurValByID(id)
	if err != nil {
		log.Error("Fail to get current value of %s, err is %v.", id, err)
		return nil, err
	}
	log.Debugf("esData: %v", esData)
	// 新旧值合并
	mapInfo, modifyInfos := MergeOldInfoAndUpdateInfo(esData.FieldInfos, fieldList)
	if len(modifyInfos) == 0 {
		return nil, nil
	}
	if esData.Found {
		err = e.UpdateData(id, mapInfo)
	} else {
		err = e.IndexData(id, mapInfo)
	}
	if err != nil {
		return nil, err
	}
	rsp.ModifyInfos = modifyInfos
	return &model.ModifyInfo{Infos: modifyInfos}, nil
}

// BatchGetFields 统一批量获取接口
func (e *ESObj) BatchGetFields(datasetID int32, ids, fields []string,
	_ map[string][]string,
) (*model.BatchGetResponse, error) {
	e.DatasetID = datasetID
	sucMap, failList, err := e.GetFieldInfosByIDs(ids, fields)
	if err != nil {
		return nil, err
	}

	var bRsp model.BatchGetResponse
	for k, v := range sucMap {
		bRsp.DocInfos = append(bRsp.DocInfos, &protocol.DocInfo{
			Id:     k,
			Fields: v.FieldInfos,
		})
	}
	bRsp.FailList = failList
	return &bRsp, nil
}

// GetFieldInfos 统一获取数据接口
func (e *ESObj) GetFieldInfos(_ []string, _ *inf.FieldKey, _ *adaptor.GetFieldInfosResponse) error {
	// todo:es读接口统一收归到BatchFieldInfos上面，后续将GetFieldInfos接口进行删除
	return nil
}
