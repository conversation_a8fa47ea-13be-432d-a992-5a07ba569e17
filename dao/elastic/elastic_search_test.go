package elastic

import (
	"context"
	"encoding/json"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/es"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	storeCom "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"

	"github.com/olivere/elastic/v7"
	elasticv7 "github.com/olivere/elastic/v7"
	es7 "github.com/olivere/elastic/v7"
)

func TestESObj_ExecuteQueryES(t *testing.T) {
	type fields struct {
		Ctx          context.Context
		proxy        *elasticv7.Client
		Name         string
		DatasourceID int32
		fieldList    []*protocol.UpdateFieldInfo
	}
	type args struct {
		condGroups []*storeCom.SearchCondGroup
		logic      common_storage_common.Logical
		exprCond   string
		includes   []string
		sort       []*storeCom.SortInfo
		pageInfo   *storeCom.PageInfo
	}
	jsonRaw := json.RawMessage(`{"title": "测试"}`)
	proxy, _ := es.NewSimpleElasticClientV7("trpc.elasticsearch.storage_service.adaptor_layer",
		nil, elasticv7.SetURL(""), elasticv7.SetBasicAuth("", ""))

	// 创建模拟的Search响应
	mockSearchResponse := &elasticv7.SearchResult{
		Hits: &elasticv7.SearchHits{
			TotalHits: &elasticv7.TotalHits{Value: 2},
			Hits:      []*elasticv7.SearchHit{{Id: "1", Source: jsonRaw}, {Id: "2", Source: jsonRaw}},
		},
	}

	// 创建模拟服务
	mockSearchService := NewMockSearchService(mockSearchResponse, nil)
	mockReadClient := NewMockElasticReadClientWithSearch(nil, nil, mockSearchService)

	// 创建模拟数据服务，用于ESSource2FieldInfos
	mockDataService := NewMockESDataService()
	fieldInfos := map[string]*storeCom.FieldInfo{
		"title": {
			FieldName: "title",
			FieldType: 1,
			StrValue:  "测试",
		},
	}

	// 设置ESValTrans2FieldInfo的响应
	mockDataService.SetESValTrans2FieldInfoResponse(&protocol.FieldInfo{
		FieldName: "title",
		FieldType: 1,
		StrValue:  "测试",
	})

	resDocs := []*storeCom.DocInfo{
		{Id: "1", Fields: fieldInfos},
		{Id: "2", Fields: fieldInfos},
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*storeCom.DocInfo
		wantErr bool
	}{
		{
			"suc11", fields{
				Ctx:   trpc.BackgroundContext(),
				proxy: proxy,
			}, args{
				condGroups: []*storeCom.SearchCondGroup{{Conds: []*storeCom.SearchCond{{FieldName: "title", Op: 1,
					Value: &storeCom.Value{Value: &storeCom.Value_StringValue{StringValue: "测试"}}}}, Logical: 0}},
				logic:    0,
				includes: []string{"title"},
				sort:     []*storeCom.SortInfo{{FieldName: "mtime", Sort: 0}},
				pageInfo: &storeCom.PageInfo{PageID: 1, Size: 10},
			}, resDocs, false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			esObj := &ESObj{
				Ctx:          tt.fields.Ctx,
				proxy:        tt.fields.proxy,
				Name:         tt.fields.Name,
				DatasourceID: tt.fields.DatasourceID,
				fieldList:    tt.fields.fieldList,
			}

			// 创建可测试的ESObj，注入模拟客户端和数据服务
			e := &TestableESObj{
				ESObj:                esObj,
				readClientInterface:  mockReadClient,
				dataServiceInterface: mockDataService,
			}

			_, _, err := e.Search(tt.args.condGroups, tt.args.logic, tt.args.exprCond,
				tt.args.includes, tt.args.sort, tt.args.pageInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("Search() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestParseEqualCond2Query(t *testing.T) {
	type args struct {
		fieldName string
		value     *storeCom.Value
	}
	tests := []struct {
		name string
		args args
		want elastic.Query
	}{
		{
			"string", args{fieldName: "title", value: &storeCom.Value{Value: &storeCom.Value_StringValue{StringValue: "测试"}}},
			es7.NewTermQuery("title", "测试"),
		},
		{
			"float", args{fieldName: "ratio", value: &storeCom.Value{Value: &storeCom.Value_FloatValue{FloatValue: 0.11}}},
			es7.NewTermQuery("ratio", "0.11"),
		},
		{
			"int", args{fieldName: "state", value: &storeCom.Value{Value: &storeCom.Value_IntValue{IntValue: 1}}},
			es7.NewTermQuery("state", "1"),
		},
		{
			"intList", args{fieldName: "state", value: &storeCom.Value{Value: &storeCom.Value_IntListValue{IntListValue: &storeCom.IntValueList{IntValueList: []int64{1, 2}}}}},
			es7.NewTermsQuery("state", 1, 2),
		},
		{
			"strList", args{fieldName: "state", value: &storeCom.Value{Value: &storeCom.Value_StrListValue{StrListValue: &storeCom.StringValueList{StringValueList: []string{"1", "2"}}}}},
			es7.NewTermsQuery("state", "1", "2"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ParseEqualCond2Query(tt.args.fieldName, tt.args.value)
			source1, err1 := got.Source()
			source2, err2 := tt.want.Source()
			if err1 != nil || err2 != nil && source1 != source2 {
				t.Errorf("ParseEqualCond2Query() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseFromSize(t *testing.T) {
	type args struct {
		pageInfo *storeCom.PageInfo
	}
	tests := []struct {
		name     string
		args     args
		wantFrom int
		wantSize int
	}{
		{
			"pageNil", args{pageInfo: nil}, 0, 10,
		},
		{
			"num0", args{pageInfo: &storeCom.PageInfo{
				PageID: 0,
				Size:   0,
			}}, 0, 10,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotFrom, gotSize := ParseFromSize(tt.args.pageInfo)
			if gotFrom != tt.wantFrom {
				t.Errorf("ParseFromSize() gotFrom = %v, want %v", gotFrom, tt.wantFrom)
			}
			if gotSize != tt.wantSize {
				t.Errorf("ParseFromSize() gotSize = %v, want %v", gotSize, tt.wantSize)
			}
		})
	}
}

func TestParseRangeCond2Query(t *testing.T) {
	type args struct {
		fieldName string
		value     *storeCom.Value
		op        common_storage_common.Operator
	}
	tests := []struct {
		name    string
		args    args
		want    elastic.Query
		wantErr bool
	}{
		{
			"string>", args{fieldName: "title", op: 3, value: &storeCom.Value{Value: &storeCom.Value_StringValue{StringValue: "测试"}}},
			es7.NewRangeQuery("title").Gt("测试"), false,
		},
		{
			"float>=", args{fieldName: "ratio", op: 4,
				value: &storeCom.Value{Value: &storeCom.Value_FloatValue{FloatValue: 0.11}}},
			es7.NewRangeQuery("ratio").Gte(0.11), false,
		},
		{
			"int<", args{fieldName: "state", op: 5,
				value: &storeCom.Value{Value: &storeCom.Value_IntValue{IntValue: 1}}},
			es7.NewRangeQuery("state").Lt(1), false,
		},
		{
			"int<=", args{fieldName: "state", op: 6,
				value: &storeCom.Value{Value: &storeCom.Value_IntValue{IntValue: 1}}},
			es7.NewRangeQuery("state").Lte(1), false,
		},
		{
			"intList", args{fieldName: "state", op: 4, value: &storeCom.Value{Value: &storeCom.Value_IntListValue{IntListValue: &storeCom.IntValueList{IntValueList: []int64{1, 2}}}}},
			nil, false,
		},
		{
			"strList", args{fieldName: "state", op: 4, value: &storeCom.Value{Value: &storeCom.Value_StrListValue{StrListValue: &storeCom.StringValueList{StringValueList: []string{"1", "2"}}}}},
			nil, false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := ParseRangeCond2Query(tt.args.fieldName, tt.args.value, tt.args.op)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseRangeCond2Query() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestParseSortInfo(t *testing.T) {
	type args struct {
		sortInfo []*storeCom.SortInfo
	}
	tests := []struct {
		name          string
		args          args
		wantSortInfos []es7.Sorter
	}{
		{
			"倒叙", args{sortInfo: []*storeCom.SortInfo{{FieldName: "mtime", Sort: 0}}},
			[]es7.Sorter{&es7.SortInfo{
				Field:     "mtime",
				Ascending: false,
			}},
		},
		{
			"正序", args{sortInfo: []*storeCom.SortInfo{{FieldName: "mtime", Sort: 1}}},
			[]es7.Sorter{&es7.SortInfo{
				Field:     "mtime",
				Ascending: true,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSortInfos := ParseSortInfo(tt.args.sortInfo)
			if len(gotSortInfos) != len(tt.wantSortInfos) {
				t.Errorf("ParseSortInfo() = %#v, want %#v", gotSortInfos, tt.wantSortInfos)
			}
		})
	}
}

func TestParseTextFieldCond2Query(t *testing.T) {
	type args struct {
		fieldName string
		value     *protocol.Value
		op        storeCom.Operator
	}
	tests := []struct {
		name    string
		args    args
		want    es7.Query
		wantErr bool
	}{
		{
			"string", args{value: &storeCom.Value{Value: &storeCom.Value_StringValue{StringValue: "测试"}},
				fieldName: "title", op: 8}, es7.NewMatchQuery("title", "测试"), false,
		}, {
			"string", args{value: &storeCom.Value{Value: &storeCom.Value_StringValue{StringValue: "测试"}},
				fieldName: "title", op: 9}, es7.NewMatchPhraseQuery("title", "测试"), false,
		},
		{
			"string", args{value: &storeCom.Value{Value: &storeCom.Value_StringValue{StringValue: "测试"}},
				fieldName: "title", op: 10}, es7.NewMatchQuery("title", "测试"), false,
		},

		{
			"not_string", args{value: &storeCom.Value{Value: &storeCom.Value_IntValue{IntValue: 1}},
				fieldName: "title", op: 1}, nil, true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseTextFieldCond2Query(tt.args.fieldName, tt.args.value, tt.args.op)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseTextFieldCond2Query() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseTextFieldCond2Query() got = %v, want %v", got, tt.want)
			}
		})
	}
}
