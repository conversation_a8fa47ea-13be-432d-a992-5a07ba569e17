package elastic

import (
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"

	elasticv7 "github.com/olivere/elastic/v7"
)

// MergeOldInfoAndUpdateInfo 根据旧值和新值拼出最终map+变更消息
// 写ES时,long,float等类型都传string就可以,只要ES的mapping提前定义好就行,但要注意的是,读出来的时候也是string
func MergeOldInfoAndUpdateInfo(oldInfo map[string]*protocol.FieldInfo,
	updateList []*protocol.UpdateFieldInfo,
) (map[string]interface{}, []*protocol.ModifyFieldInfo) {
	newMap := InitWriteMapFromESCurInfo(oldInfo)
	var modifyInfos []*protocol.ModifyFieldInfo
	var val interface{}
	var modify bool
	for _, update := range updateList {
		minfo := model.InitModifyFieldInfo(update.FieldInfo)
		if _, ok := oldInfo[update.FieldInfo.FieldName]; !ok { // 不存在构造一个空的,防止空指针+拼变更消息
			oldInfo[update.FieldInfo.FieldName] = &protocol.FieldInfo{}
		}
		switch update.GetFieldInfo().GetFieldType() {
		case protocol.EnumFieldType_FieldTypeStr:
			val, modify = logic.BuildStrFieldOpStr(update.UpdateType, oldInfo[update.FieldInfo.FieldName].StrValue,
				update.FieldInfo.StrValue)
			minfo.OldStrValue = oldInfo[update.FieldInfo.FieldName].StrValue
			minfo.NewStrValue = val.(string)
		case protocol.EnumFieldType_FieldTypeIntVec:
			val, modify = logic.BuildESVecFieldOp(update.UpdateType, oldInfo[update.FieldInfo.FieldName].VecInt,
				update.FieldInfo.VecInt)
			minfo.OldVecInt = oldInfo[update.FieldInfo.FieldName].VecInt
			minfo.NewVecInt = val.([]uint32)
			// int数组转string数组-oneline
			val = strings.Trim(strings.Replace(fmt.Sprint(minfo.NewVecInt), " ", "+", -1), "[]")
		case protocol.EnumFieldType_FieldTypeSet:
			val, modify, _ = logic.BuildESSetFieldOp(update.UpdateType, update.Pos,
				oldInfo[update.FieldInfo.FieldName].VecStr, update.FieldInfo.VecStr)
			minfo.OldVecStr = oldInfo[update.FieldInfo.FieldName].VecStr
			minfo.NewVecStr = val.([]string)
			val = strings.Join(minfo.NewVecStr, "+")
		default:
		}
		newMap[update.FieldInfo.FieldName] = val
		if modify {
			modifyInfos = append(modifyInfos, minfo)
		}
	}

	return newMap, modifyInfos
}

// InitWriteMapFromESCurInfo ...
func InitWriteMapFromESCurInfo(oldInfo map[string]*protocol.FieldInfo) map[string]interface{} {
	newMap := make(map[string]interface{})
	for k, v := range oldInfo {
		switch v.FieldType {
		case protocol.EnumFieldType_FieldTypeStr:
			newMap[k] = v.StrValue
		case protocol.EnumFieldType_FieldTypeIntVec:
			newMap[k] = v.VecInt
		case protocol.EnumFieldType_FieldTypeSet:
			newMap[k] = v.VecStr
		case protocol.EnumFieldType_FieldTypeMapKV, protocol.EnumFieldType_FieldTypeMapKList:
			newMap[k] = v.MapVal
		}
	}
	return newMap
}

// BulkData 批量提交到ES, 内部重试3次
func (e *ESObj) BulkData(docInfos map[string]interface{}) (map[string]string, error) {
	bulkRequest := e.proxy.Bulk()
	for id, info := range docInfos {
		bulkRequest.Add(elasticv7.NewBulkIndexRequest().Index(e.GetKey()).Id(id).Doc(info).RetryOnConflict(3))
	}
	rsp, err := bulkRequest.Do(e.NewCtxWithMethod("BulkIndex"))
	if err != nil {
		log.Errorf("ESBulkErr:%v", err)
		return nil, err
	}
	failList := make(map[string]string)
	if len(rsp.Failed()) > 0 {
		for _, f := range rsp.Failed() {
			failList[f.Id] = f.Error.Reason
		}
	}
	return failList, nil
}

// UpdateData 更新ES, 内部重试3次
func (e *ESObj) UpdateData(id string, info map[string]interface{}) error {
	rsp, err := e.proxy.Update().Index(e.GetKey()).Id(id).Doc(info).RetryOnConflict(3).
		Do(e.NewCtxWithMethod("Update"))
	if err != nil {
		log.Errorf("ESUpdateDataErr:%v", err)
		return err
	}
	log.Debugf("UpdateData_ID:%s, res:%s", id, rsp.Result)
	return nil
}

// IndexData Index
func (e *ESObj) IndexData(id string, info map[string]interface{}) error {
	rsp, err := e.proxy.Index().Index(e.GetKey()).Id(id).BodyJson(info).Do(e.NewCtxWithMethod("Index"))
	if err != nil {
		log.Errorf("ESIndexDataErr:%v", err)
		return err
	}
	log.Debugf("IndexData_ID:%s, res:%s", id, rsp.Result)
	return nil
}
