package elastic

import (
	"encoding/json"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/antlr_es_dsl"

	es7 "github.com/olivere/elastic/v7"
)

// GenQuerySource 生成请求的Source
func GenQuerySource(condGroups []*protocol.SearchCondGroup, logic protocol.Logical, exprCond string,
	includes []string, sortParams []*protocol.SortInfo, pageInfo *protocol.PageInfo) (interface{}, error) {
	querySource := make(map[string]interface{})
	querySource["timeout"] = "5s"
	var err error
	if exprCond != "" {
		querySource["query"], _ = antlr_es_dsl.ParseExpr2ESDsl(exprCond)
	} else {
		condDSL, _ := ParseCondGroups2BoolQuery(condGroups, logic)
		querySource["query"], _ = condDSL.Source()
	}
	if err != nil {
		log.Errorf("make query source err:%v", err)
		return querySource, err
	}
	querySource["from"], querySource["size"] = ParseFromSize(pageInfo)
	sortInfos := ParseSortInfos(sortParams)
	if len(sortInfos) > 0 {
		querySource["sort"] = sortInfos
	}
	if len(includes) > 0 {
		querySource["_source"] = includes
	}
	reqByte, _ := json.Marshal(querySource)
	log.Debugf("reqSource:%v", string(reqByte))
	return querySource, nil
}

// Search 搜索
func (e *ESObj) Search(condGroups []*protocol.SearchCondGroup, logic protocol.Logical, exprCond string,
	includes []string, sort []*protocol.SortInfo, pageInfo *protocol.PageInfo,
) (int64, []*protocol.DocInfo, error) {
	reqSource, err := GenQuerySource(condGroups, logic, exprCond, includes, sort, pageInfo)
	if err != nil {
		log.Errorf("Error generating query source:%v", err)
		return 0, nil, err
	}
	rsp, err := e.proxy.Search().Index(e.Name).Source(reqSource).Do(e.NewCtxWithMethod("SearchIndex"))
	log.Debugf("QueryESRsp:%+v, err:%v\n", rsp, err)
	if err != nil {
		return 0, nil, err
	}
	if rsp.Error != nil {
		return 0, nil, errors.New(rsp.Error.Reason)
	}
	var resDocs []*protocol.DocInfo
	for _, info := range rsp.Hits.Hits {
		doc := &protocol.DocInfo{
			Id:       info.Id,
			Fields:   make(map[string]*protocol.FieldInfo),
			FailList: make(map[string]*protocol.FailInfo),
		}
		fieldInfos, err := e.ESSource2FieldInfos(info.Source)
		if err != nil {
			doc.FailList[doc.Id] = &protocol.FailInfo{ErrMsg: err.Error()}
		}
		doc.Fields = fieldInfos
		resDocs = append(resDocs, doc)
	}
	return rsp.TotalHits(), resDocs, nil
}

// ParseSortInfos 解析排序信息
func ParseSortInfos(sortParams []*protocol.SortInfo) []interface{} {
	var sortInfos []interface{}
	for _, s := range sortParams {
		stMap := make(map[string]map[string]interface{})
		subMap := make(map[string]interface{})
		subMap["order"] = "asc" //默认asc
		if s.Sort == protocol.Sort_Desc {
			subMap["order"] = "desc"
		}
		stMap[s.GetFieldName()] = subMap
		sortInfos = append(sortInfos, stMap)
	}
	return sortInfos
}

const (
	Zero            = 0
	DefaultPageNum  = 1
	DefaultPageSize = 10
)

// ParseFromSize 解析分页信息
func ParseFromSize(pageInfo *protocol.PageInfo) (from, size int) {
	if pageInfo == nil {
		return Zero, DefaultPageSize
	}
	num, size := pageInfo.PageID, int(pageInfo.Size)
	if num == Zero {
		num = DefaultPageNum
	}
	if size == Zero {
		size = DefaultPageSize
	}
	from = int(num-1) * size
	return
}

// ParseSortInfo 解析排序信息
func ParseSortInfo(sortInfo []*protocol.SortInfo) (sortInfos []es7.Sorter) {
	for _, s := range sortInfo {
		one := &es7.SortInfo{
			Field:     s.FieldName,
			Ascending: false,
		}
		if s.Sort == protocol.Sort_Asc {
			one.Ascending = true
		}
		sortInfos = append(sortInfos, one)
	}
	return
}

// ParseCondGroups2BoolQuery 将条件数组转成boolQuery
func ParseCondGroups2BoolQuery(groups []*protocol.SearchCondGroup,
	logical protocol.Logical,
) (*es7.BoolQuery, error) {
	bq := es7.NewBoolQuery()
	var err error
	for _, oneGroup := range groups {
		subBq, err := ParseCondGroup2BoolQuery(oneGroup)
		if err != nil {
			log.Errorf("ParseCondGroups2BoolQueryError: %v", err)
			return bq, err
		}
		if logical == protocol.Logical_LogicalAnd {
			bq.Must(subBq)
		} else {
			bq.Should(subBq)
		}
	}
	bqs, _ := bq.Source()
	log.Debugf("ParseCondGroups2BoolQueryRes:%v", bqs)
	return bq, err
}

// ParseCondGroup2BoolQuery 将条件数组转成boolQuery
// NOCC:CCN_threshold(设计如此:)
func ParseCondGroup2BoolQuery(group *protocol.SearchCondGroup) (*es7.BoolQuery, error) {
	bq := es7.NewBoolQuery()
	var err error
	for _, cond := range group.Conds {
		if cond.FieldName == "" {
			continue
		}
		var query es7.Query
		switch cond.Op {
		case protocol.Operator_gt, protocol.Operator_gte,
			protocol.Operator_lt, protocol.Operator_lte, protocol.Operator_between:
			query, err = ParseRangeCond2Query(cond.FieldName, cond.Value, cond.Op)
		case protocol.Operator_like:
			query, err = ParseTextFieldCond2Query(cond.FieldName, cond.Value, cond.Op)
		case protocol.Operator_match:
			query, err = ParseTextFieldCond2Query(cond.FieldName, cond.Value, cond.Op)
		case protocol.Operator_ne, protocol.Operator_notIn:
			query = es7.NewBoolQuery().MustNot(ParseEqualCond2Query(cond.FieldName, cond.Value))
		default: // protocol.Operator_eq, protocol.Operator_in 和其他
			query = ParseEqualCond2Query(cond.FieldName, cond.Value)
		}
		if err != nil {
			return nil, err
		}
		if group.Logical == protocol.Logical_LogicalAnd {
			if cond.Op == protocol.Operator_like || cond.Op == protocol.Operator_match {
				bq.Must(query)
			} else {
				bq.Filter(query)
			}
		} else {
			bq.Should(query)
		}
	}
	bqs, _ := bq.Source()
	log.Debugf("ParseCondGroup2BoolQueryRes:%v", bqs)
	return bq, err
}

// ParseTextFieldCond2Query 解析文本类型的字段
func ParseTextFieldCond2Query(fieldName string, value *protocol.Value, op protocol.Operator) (es7.Query, error) {
	var val interface{}
	switch value.Value.(type) {
	case *protocol.Value_StringValue:
		val = value.GetStringValue()
	default:
		return nil, errors.New(fmt.Sprintf("field:[%s],like only support string value", fieldName))
	}
	switch op {
	case protocol.Operator_match:
		return es7.NewMatchQuery(fieldName, val), nil
	case protocol.Operator_like:
		return es7.NewMatchPhraseQuery(fieldName, val), nil
	default:
		return es7.NewMatchQuery(fieldName, val), nil
	}
}

// ParseRangeCond2Query ...
// NOCC:CCN_threshold(设计如此:)
func ParseRangeCond2Query(fieldName string, value *protocol.Value, op protocol.Operator) (es7.Query, error) {
	rq := es7.NewRangeQuery(fieldName)
	var val1, val2 interface{}
	switch value.Value.(type) {
	case *protocol.Value_StringValue:
		val1 = value.GetStringValue()
	case *protocol.Value_FloatValue:
		val1 = value.GetFloatValue()
	case *protocol.Value_IntValue:
		val1 = value.GetIntValue()
	case *protocol.Value_IntListValue:
		vals := value.GetIntListValue().IntValueList
		if len(vals) >= 2 {
			val1 = vals[0]
			val2 = vals[1]
		}
	case *protocol.Value_StrListValue:
		vals := value.GetStrListValue().StringValueList
		if len(vals) >= 2 {
			val1 = vals[0]
			val2 = vals[1]
		}
	}
	switch op {
	case protocol.Operator_gt:
		rq.Gt(val1)
	case protocol.Operator_gte:
		rq.Gte(val1)
	case protocol.Operator_lt:
		rq.Lt(val1)
	case protocol.Operator_lte:
		rq.Lte(val1)
	case protocol.Operator_between:
		rq.Gte(val1).Lte(val2)
	}
	return rq, nil
}

// ParseEqualCond2Query ... equal支持所有类型,所以不用返回error
// NOCC:CCN_threshold(设计如此:)
func ParseEqualCond2Query(fieldName string, value *protocol.Value) es7.Query {
	if value == nil {
		return es7.NewTermQuery(fieldName, "")
	}
	var termsVals []interface{}
	switch value.Value.(type) {
	case *protocol.Value_StringValue:
		return es7.NewTermQuery(fieldName, value.GetStringValue())
	case *protocol.Value_FloatValue:
		return es7.NewTermQuery(fieldName, value.GetFloatValue())
	case *protocol.Value_IntValue:
		return es7.NewTermQuery(fieldName, value.GetIntValue())
	case *protocol.Value_IntListValue:
		for _, v := range value.GetIntListValue().IntValueList {
			termsVals = append(termsVals, v)
		}
		return es7.NewTermsQuery(fieldName, termsVals...)
	case *protocol.Value_StrListValue:
		for _, v := range value.GetStrListValue().StringValueList {
			termsVals = append(termsVals, v)
		}
		return es7.NewTermsQuery(fieldName, termsVals...)
	default:
		return es7.NewTermQuery(fieldName, value.GetStringValue())
	}
}
