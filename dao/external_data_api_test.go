package dao

import (
	"testing"
	"time"
)

func Test_isValidPlanData(t *testing.T) {
	mockNow := func() time.Time {
		return time.Date(2023, 11, 10, 0, 0, 0, 0, time.UTC)
	}

	testCases := []struct {
		name     string
		planInfo *PlanInfo
		expected bool
	}{
		{
			name: "invalid_old_start_time",
			planInfo: &PlanInfo{
				Category:  "电视剧",
				Level:     "S",
				StartTime: "2022-06-02",
				EndTime:   "2022-07-24",
			},
			expected: false, // 开始时间太早，超出范围
		},
		{
			name: "valid_normal_case",
			planInfo: &PlanInfo{
				Category:  "综艺",
				Level:     "A",
				StartTime: "2023-11-06",
				EndTime:   "2023-12-28",
			},
			expected: true, // Mock 时间在范围内
		},
		{
			name: "valid_empty_end_time",
			planInfo: &PlanInfo{
				Category:  "电视剧",
				Level:     "A",
				StartTime: "2023-11-06",
				EndTime:   "",
			},
			expected: true, // 空结束时间，其他条件满足
		},
		{
			name: "valid_dirty_end_time_0001",
			planInfo: &PlanInfo{
				Category:  "综艺",
				Level:     "S",
				StartTime: "2023-11-06",
				EndTime:   "0001-01-01",
			},
			expected: true, // 脏数据结束时间，其他条件满足
		},
		{
			name: "valid_dirty_end_time_0000",
			planInfo: &PlanInfo{
				Category:  "电影",
				Level:     "S+",
				StartTime: "2023-11-06",
				EndTime:   "0000-00-00",
			},
			expected: false, // 电影不在允许的品类中
		},
		{
			name: "valid_dirty_end_time_1900",
			planInfo: &PlanInfo{
				Category:  "电视剧",
				Level:     "A",
				StartTime: "2023-11-06",
				EndTime:   "1900-01-01",
			},
			expected: true, // 脏数据结束时间，其他条件满足
		},
		{
			name: "invalid_category",
			planInfo: &PlanInfo{
				Category:  "动漫",
				Level:     "S",
				StartTime: "2023-11-06",
				EndTime:   "2023-12-28",
			},
			expected: false, // 品类不在允许列表中
		},
		{
			name: "invalid_level",
			planInfo: &PlanInfo{
				Category:  "电视剧",
				Level:     "B",
				StartTime: "2023-11-06",
				EndTime:   "2023-12-28",
			},
			expected: false, // 等级不符合要求
		},
		{
			name: "invalid_start_time_format",
			planInfo: &PlanInfo{
				Category:  "电视剧",
				Level:     "A",
				StartTime: "invalid-date",
				EndTime:   "2023-12-28",
			},
			expected: false, // 开始时间格式错误
		},
		{
			name: "invalid_end_time_too_early",
			planInfo: &PlanInfo{
				Category:  "电视剧",
				Level:     "A",
				StartTime: "2023-11-06",
				EndTime:   "2023-05-15", // 结束时间+150天 = 2023-10-12，早于当前时间2023-11-10
			},
			expected: false, // 结束时间+150天早于当前时间
		},
		{
			name: "valid_malformed_end_time",
			planInfo: &PlanInfo{
				Category:  "综艺",
				Level:     "S",
				StartTime: "2023-11-06",
				EndTime:   "invalid-date",
			},
			expected: true, // 结束时间格式错误但其他条件满足
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := isValidPlanData(tc.planInfo, []string{"电视剧", "综艺"}, mockNow)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v", tc.expected, result)
			}
		})
	}
}

func Test_isInvalidEndTime(t *testing.T) {
	testCases := []struct {
		name     string
		endTime  string
		expected bool
	}{
		{
			name:     "empty_string",
			endTime:  "",
			expected: true,
		},
		{
			name:     "dirty_data_0001",
			endTime:  "0001-01-01",
			expected: true,
		},
		{
			name:     "dirty_data_0000",
			endTime:  "0000-00-00",
			expected: true,
		},
		{
			name:     "dirty_data_1900",
			endTime:  "1900-01-01",
			expected: true,
		},
		{
			name:     "valid_date",
			endTime:  "2023-12-31",
			expected: false,
		},
		{
			name:     "valid_future_date",
			endTime:  "2024-06-15",
			expected: false,
		},
		{
			name:     "malformed_date",
			endTime:  "invalid-date",
			expected: false, // 格式错误但不是已知的脏数据
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := isInvalidEndTime(tc.endTime)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for endTime: %s", tc.expected, result, tc.endTime)
			}
		})
	}
}
