package dao

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
)

// SendQyWxMsg 发送企微机器人消息
func SendQyWxMsg(ctx context.Context, path, content string) error {
	// Content 企微消息内容
	type Content struct {
		Content string `json:"content"`
	}
	type args struct {
		MsgType  string  `json:"msgtype"`
		Markdown Content `json:"markdown"`
	}
	reqBody := args{
		MsgType: "markdown",
		Markdown: Content{
			Content: content,
		},
	}
	var apiRsp struct {
		Status int    `json:"errcode"`
		Msg    string `json:"errmsg"`
	}

	reqHead := &thttp.ClientReqHeader{
		Host:   "qyapi.weixin.qq.com",
		Schema: "https",
	}
	reqHead.AddHeader("Content-Type", "application/json;charset=utf-8")
	opts := []client.Option{
		client.WithReqHead(reqHead),
		client.WithSerializationType(codec.SerializationTypeJSON),
	}
	tProxy := thttp.NewClientProxy("qyapi.weixin.qq.com", opts...)
	if err := tProxy.Post(ctx, path, reqBody, &apiRsp); err != nil {
		return fmt.Errorf("send http failed:%v", err)
	}
	if apiRsp.Status != 0 {
		return fmt.Errorf("read rsp failed:%+v", apiRsp)
	}
	return nil
}
