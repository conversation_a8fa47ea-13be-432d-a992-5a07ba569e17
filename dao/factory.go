package dao

import (
	"git.code.oa.com/trpc-go/trpc-go/log"
	event "git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/video_media/media_event_hub/processor/config"
	"git.woa.com/video_media/media_event_hub/processor/entity"
)

// Factory 数据访问工厂
type Factory struct {
	Config *config.Config
}

// NewFactory 创建数据访问工厂
func NewFactory(cfg *config.Config) (*Factory, error) {
	if cfg == nil {
		return nil, entity.NewError(entity.ErrCodeConfigFail, nil)
	}
	return &Factory{Config: cfg}, nil
}

// GetDataAccessor 根据视图类型获取数据访问器
func (f *Factory) GetDataAccessor(viewType event.EnumViewType, bridgeID int64) (DataAccessor, error) {
	// 先检查视图类型是否支持
	switch viewType {
	case event.EnumViewType_UNIVERSAL_VIEW, event.EnumViewType_UNION_VIEW:
		// 支持的类型，继续处理
	default:
		return nil, entity.NewError(entity.ErrCodeUnsupportedType, nil)
	}

	// 获取存储类型标识
	storageType := viewType.String()

	// 从配置中获取鉴权信息
	authInfo, err := f.Config.GetAccess(bridgeID, storageType)
	if err != nil {
		log.Errorf("Failed to get access info for bridgeID %d, storageType %s: %v", bridgeID, storageType, err)
		return nil, entity.NewError(entity.ErrCodeConfigFail, err)
	}

	// 创建对应的数据访问器
	var accessor DataAccessor

	switch viewType {
	case event.EnumViewType_UNIVERSAL_VIEW:
		accessor = &Universal{
			AuthInfo: authInfo,
		}
	case event.EnumViewType_UNION_VIEW:
		accessor = &Union{
			AuthInfo: authInfo,
		}
	}

	// 初始化连接
	err = accessor.Connect()
	if err != nil {
		log.Errorf("Failed to connect to storage for view type %v: %v", viewType, err)
		return nil, entity.NewError(entity.ErrCodeConnectFail, err)
	}

	log.Infof("创建数据访问对象成功, viewType: %v,AuthInfo:%+v", viewType, authInfo)
	return accessor, nil
}
