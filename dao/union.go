package dao

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/unionplus/common"
	"git.code.oa.com/v/data_platform/unionplus/lib/trpc_go_union_client/unionplus"
	event "git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/video_media/media_event_hub/processor/config"
	"git.woa.com/video_media/media_event_hub/processor/entity"
)

// UnionBatchSize Union存储的批次大小
const UnionBatchSize = 20

// Union 实现DataAccessor接口，用于访问union数据存储
type Union struct {
	// union plus客户端
	proxy unionplus.Client
	// AuthInfo 访问union数据存储的配置信息
	AuthInfo *config.AccessInfo
}

// Connect 实现连接接口
func (u *Union) Connect() error {
	// 使用配置的AppID和AppKey
	appID := u.AuthInfo.AppID
	appKey := u.AuthInfo.AppKey

	var options []client.Option
	// 优先使用连接信息中的set，如果没有则使用auth中的SetName
	setName := u.AuthInfo.SetName
	if setName != "" {
		options = []client.Option{client.WithCalleeSetName(setName)}
	}
	u.proxy = unionplus.New(appID, appKey, options...)
	return nil
}

// BatchGetFields 实现DataAccessor接口，从union中批量获取字段
func (u *Union) BatchGetFields(ctx context.Context, viewID string, dataIDs []string, fields []string, timestamps int64) ([]*event.Node, error) {
	if len(dataIDs) == 0 || len(fields) == 0 {
		log.Error("Empty dataIDs or fields.")
		if len(dataIDs) == 0 {
			return nil, entity.NewError(entity.ErrCodeEmptyParam, nil)
		}
		return nil, entity.NewError(entity.ErrCodeEmptyParam, nil)
	}

	// 如果数据量小于批次大小，直接单次请求
	if len(dataIDs) <= UnionBatchSize {
		return u.batchGetFieldsSingle(ctx, viewID, dataIDs, fields)
	}

	// 分批并发处理
	return u.batchGetFieldsConcurrent(ctx, viewID, dataIDs, fields)
}

// batchGetFieldsSingle 单次请求获取字段
func (u *Union) batchGetFieldsSingle(ctx context.Context, viewID string, dataIDs []string, fields []string) ([]*event.Node, error) {
	var columns []*common.Select
	for _, f := range fields {
		columns = append(columns, &common.Select{Column: f})
	}
	req := &common.QueryReq{
		ViewName: viewID,
		Columns:  columns,
		Where: &common.Where{
			Val: &common.Where_Keys_{
				Keys: &common.Where_Keys{Keys: dataIDs},
			},
		},
	}

	rsp, err := u.proxy.GetRaw(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Failed to read from union, err: %v", err)
		return nil, entity.NewError(entity.ErrCodeDataQueryFail, err)
	}
	if rsp == nil {
		log.ErrorContextf(ctx, "Union response is nil")
		return nil, entity.NewError(entity.ErrCodeDataEmpty, nil)
	}

	return u.convertToNodes(ctx, viewID, rsp), nil
}

// batchGetFieldsConcurrent 并发分批获取字段
func (u *Union) batchGetFieldsConcurrent(ctx context.Context, viewID string, dataIDs []string, fields []string) ([]*event.Node, error) {
	// 分批
	var batches [][]string
	for i := 0; i < len(dataIDs); i += UnionBatchSize {
		end := i + UnionBatchSize
		if end > len(dataIDs) {
			end = len(dataIDs)
		}
		batches = append(batches, dataIDs[i:end])
	}

	log.InfoContextf(ctx, "BatchGetFields: total %d dataIDs split into %d batches", len(dataIDs), len(batches))

	// 并发执行各批次
	type batchResult struct {
		nodes []*event.Node
		err   error
		index int
	}

	results := make(chan batchResult, len(batches))
	var handlers []func() error
	for i, batch := range batches {
		batchIndex := i
		batchDataIDs := batch
		handlers = append(handlers, func() error {
			nodes, err := u.batchGetFieldsSingle(ctx, viewID, batchDataIDs, fields)
			results <- batchResult{
				nodes: nodes,
				err:   err,
				index: batchIndex,
			}
			return nil // trpc.GoAndWait需要返回error，但我们通过channel处理实际错误
		})
	}

	// 执行并发任务
	_ = trpc.GoAndWait(handlers...)
	close(results)

	// 收集结果
	var allNodes []*event.Node
	var errors []error
	resultsByIndex := make(map[int][]*event.Node)

	for result := range results {
		if result.err != nil {
			errors = append(errors, result.err)
			log.ErrorContextf(ctx, "Batch %d failed: %v", result.index, result.err)
		} else {
			resultsByIndex[result.index] = result.nodes
		}
	}

	// 如果有错误，返回第一个错误
	if len(errors) > 0 {
		return nil, entity.NewError(entity.ErrCodeBatchFail, errors[0])
	}

	// 按照原始顺序合并结果
	for i := 0; i < len(batches); i++ {
		if batchNodes, exists := resultsByIndex[i]; exists {
			allNodes = append(allNodes, batchNodes...)
		}
	}

	log.InfoContextf(ctx, "BatchGetFields: successfully retrieved %d nodes from %d batches", len(allNodes), len(batches))
	return allNodes, nil
}

// convertToNodes 将Union响应转换为节点列表
func (u *Union) convertToNodes(ctx context.Context, viewID string, rsp *common.QueryRsp) []*event.Node {
	var nodes []*event.Node
	viewKey := fmt.Sprintf("%d_%s", event.EnumViewType_UNION_VIEW, viewID)

	for _, row := range rsp.GetData().GetRows() {
		if row.Retcode != 0 {
			log.WarnContextf(ctx, "Failed to get data for key %s: %s", row.Key, row.GetErrormsg())
			continue
		}

		node := event.Node{
			DataId: row.Key,
			Values: make(map[string]*event.ValueContainer),
		}

		// 创建ValueContainer
		container := &event.ValueContainer{
			ViewType: event.EnumViewType_UNION_VIEW,
			ViewId:   viewID,
			Fields:   make(map[string]*event.FieldValue),
		}

		// 填充字段值
		for fieldName, value := range row.Columns {
			fieldValue := u.convertToFieldValue(fieldName, value)
			container.Fields[fieldName] = fieldValue
		}
		node.Values[viewKey] = container
		nodes = append(nodes, &node)
	}
	return nodes
}

// parseValue 解析Union的Value到字符串切片
func parseValue(v *common.Value) []string {
	switch v.Val.(type) {
	case *common.Value_Strval:
		return []string{string(v.GetStrval())}
	case *common.Value_Intval:
		return []string{fmt.Sprintf("%d", v.GetIntval())}
	case *common.Value_Floatval:
		return []string{fmt.Sprintf("%f", v.GetFloatval())}
	case *common.Value_Boolval:
		return []string{fmt.Sprintf("%t", v.GetBoolval())}
	case *common.Value_Arrval:
		var result []string
		for _, item := range v.GetArrval().Listval {
			result = append(result, parseValue(item)...)
		}
		return result
	default:
		return []string{}
	}
}

// convertToFieldValue 将Union的Value转换为事件系统的FieldValue
func (u *Union) convertToFieldValue(fieldName string, v *common.Value) *event.FieldValue {
	fieldValue := &event.FieldValue{
		FieldName: fieldName,
	}

	values := parseValue(v)
	if len(values) == 0 {
		fieldValue.FieldType = event.EnumFieldType_INVALID_FIELD
		return fieldValue
	}

	switch v.Val.(type) {
	case *common.Value_Strval:
		fieldValue.FieldType = event.EnumFieldType_STR_FIELD
		fieldValue.Str = values[0]
	case *common.Value_Intval:
		fieldValue.FieldType = event.EnumFieldType_INT_FIELD
		fieldValue.Str = values[0]
	case *common.Value_Floatval:
		fieldValue.FieldType = event.EnumFieldType_FLOAT_FIELD
		fieldValue.Str = values[0]
	case *common.Value_Arrval:
		fieldValue.FieldType = event.EnumFieldType_SET_FIELD
		fieldValue.StrList = values
	default:
		fieldValue.FieldType = event.EnumFieldType_STR_FIELD
		if len(values) > 0 {
			fieldValue.Str = values[0]
		}
	}
	return fieldValue
}
