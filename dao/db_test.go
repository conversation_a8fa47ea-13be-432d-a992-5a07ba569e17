package dao

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"git.code.oa.com/trpc-go/trpc-database/mysql/mockmysql"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"github.com/agiledragon/gomonkey/v2"
)

func TestCoverInfo_InsertCoverInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockCli := mockmysql.NewMockClient(ctrl)
	mockCli.EXPECT().NamedExec(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(
		_ interface{}, _ interface{}, _ interface{}) (sql.Result, error) {
		return nil, nil
	}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mockCli)
	defer mockMysql.Reset()

	type fields struct {
		CoverID        string
		CoverType      int
		Title          string
		OfficeAccFlag  int
		TopicFlag      int
		IPOnlineStatus int
		IsDeleted      int
		CreateTime     time.Time
		UpdateTime     time.Time
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				CoverID:       "mzc00200y98zmrd",
				CoverType:     1,
				Title:         "testTitle",
				OfficeAccFlag: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CoverInfo{
				CoverID:        tt.fields.CoverID,
				CoverType:      tt.fields.CoverType,
				Title:          tt.fields.Title,
				OfficeAccFlag:  tt.fields.OfficeAccFlag,
				TopicFlag:      tt.fields.TopicFlag,
				IPOnlineStatus: tt.fields.IPOnlineStatus,
				IsDeleted:      tt.fields.IsDeleted,
				CreateTime:     tt.fields.CreateTime,
				UpdateTime:     tt.fields.UpdateTime,
			}
			if err := c.InsertCoverInfo(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("InsertCoverInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCoverInfo_IsExistFocusCoverLoop(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mClient := mockmysql.NewMockClient(ctrl)
	mClient.EXPECT().QueryToStructs(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, dst interface{}, query string, args ...interface{}) error {
			if len(args) != 0 {
				if args[0].(string) == "err" {
					return fmt.Errorf("123")
				}
			}
			infoPtr, _ := dst.(*[]*CoverInfo)
			*infoPtr = []*CoverInfo{
				{},
			}
			return nil
		}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mClient)
	defer mockMysql.Reset()

	type fields struct {
		CoverID        string
		CoverType      int
		Title          string
		OfficeAccFlag  int
		TopicFlag      int
		IPOnlineStatus int
		IsDeleted      int
		CreateTime     time.Time
		UpdateTime     time.Time
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				CoverID:       "mzc00200y98zmrd",
				CoverType:     1,
				Title:         "testTitle",
				OfficeAccFlag: 1,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CoverInfo{
				CoverID:        tt.fields.CoverID,
				CoverType:      tt.fields.CoverType,
				Title:          tt.fields.Title,
				OfficeAccFlag:  tt.fields.OfficeAccFlag,
				TopicFlag:      tt.fields.TopicFlag,
				IPOnlineStatus: tt.fields.IPOnlineStatus,
				IsDeleted:      tt.fields.IsDeleted,
				CreateTime:     tt.fields.CreateTime,
				UpdateTime:     tt.fields.UpdateTime,
			}
			got, err := c.IsExistFocusCoverLoop(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsExistFocusCoverLoop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsExistFocusCoverLoop() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCoverInfo_GetAllCoverInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mClient := mockmysql.NewMockClient(ctrl)
	mClient.EXPECT().QueryToStructs(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, dst interface{}, query string, args ...interface{}) error {
			if len(args) != 0 {
				if args[0].(string) == "err" {
					return fmt.Errorf("123")
				}
			}
			infoPtr, _ := dst.(*[]*CoverInfo)
			*infoPtr = []*CoverInfo{
				{},
			}
			return nil
		}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mClient)
	defer mockMysql.Reset()

	type fields struct {
		CoverID        string
		CoverType      int
		Title          string
		OfficeAccFlag  int
		TopicFlag      int
		IPOnlineStatus int
		IsDeleted      int
		CreateTime     time.Time
		UpdateTime     time.Time
	}
	type args struct {
		ctx context.Context
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*CoverInfo
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				CoverID:       "mzc00200y98zmrd",
				CoverType:     1,
				Title:         "testTitle",
				OfficeAccFlag: 1,
			},
			want:    []*CoverInfo{nil},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CoverInfo{
				CoverID:        tt.fields.CoverID,
				CoverType:      tt.fields.CoverType,
				Title:          tt.fields.Title,
				OfficeAccFlag:  tt.fields.OfficeAccFlag,
				TopicFlag:      tt.fields.TopicFlag,
				IPOnlineStatus: tt.fields.IPOnlineStatus,
				IsDeleted:      tt.fields.IsDeleted,
				CreateTime:     tt.fields.CreateTime,
				UpdateTime:     tt.fields.UpdateTime,
			}
			_, err := c.GetAllCoverInfo(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCoverInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestIPCrawTask_InsertIPCrawInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockCli := mockmysql.NewMockClient(ctrl)
	mockCli.EXPECT().NamedExec(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(
		_ interface{}, _ interface{}, _ interface{}) (sql.Result, error) {
		return nil, nil
	}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mockCli)
	defer mockMysql.Reset()

	type fields struct {
		ID                  int
		CrawType            int
		Platform            string
		CrawKey             string
		CoverID             string
		Valid               int
		CreateTime          time.Time
		UpdateTime          time.Time
		DayCrawlerState     int
		DayCrawlerStartTime time.Time
		DayCrawlerEndTime   time.Time
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				CoverID:  "mzc00200y98zmrd",
				CrawType: model.OfficeAccountType,
				Platform: "douyin",
				CrawKey:  "testCrawKey",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &IPCrawTask{
				ID:                  tt.fields.ID,
				CrawType:            tt.fields.CrawType,
				Platform:            tt.fields.Platform,
				CrawKey:             tt.fields.CrawKey,
				CoverID:             tt.fields.CoverID,
				Valid:               tt.fields.Valid,
				CreateTime:          tt.fields.CreateTime,
				UpdateTime:          tt.fields.UpdateTime,
				DayCrawlerState:     tt.fields.DayCrawlerState,
				DayCrawlerStartTime: tt.fields.DayCrawlerStartTime,
				DayCrawlerEndTime:   tt.fields.DayCrawlerEndTime,
			}
			if err := i.InsertIPCrawInfo(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("InsertIPCrawInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestIPCrawTask_GetAllCrawTasks(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mClient := mockmysql.NewMockClient(ctrl)
	mClient.EXPECT().QueryToStructs(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, dst interface{}, query string, args ...interface{}) error {
			if len(args) != 0 {
				if args[0].(string) == "err" {
					return fmt.Errorf("123")
				}
			}
			infoPtr, _ := dst.(*[]*IPCrawTask)
			*infoPtr = []*IPCrawTask{
				{},
			}
			return nil
		}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mClient)
	defer mockMysql.Reset()

	type fields struct {
		ID                  int
		CrawType            int
		Platform            string
		CrawKey             string
		CoverID             string
		Valid               int
		CreateTime          time.Time
		UpdateTime          time.Time
		DayCrawlerState     int
		DayCrawlerStartTime time.Time
		DayCrawlerEndTime   time.Time
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*IPCrawTask
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				CoverID:  "mzc00200y98zmrd",
				CrawType: model.OfficeAccountType,
				Platform: "douyin",
				CrawKey:  "testCrawKey",
			},
			want:    []*IPCrawTask{{}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &IPCrawTask{
				ID:                  tt.fields.ID,
				CrawType:            tt.fields.CrawType,
				Platform:            tt.fields.Platform,
				CrawKey:             tt.fields.CrawKey,
				CoverID:             tt.fields.CoverID,
				Valid:               tt.fields.Valid,
				CreateTime:          tt.fields.CreateTime,
				UpdateTime:          tt.fields.UpdateTime,
				DayCrawlerState:     tt.fields.DayCrawlerState,
				DayCrawlerStartTime: tt.fields.DayCrawlerStartTime,
				DayCrawlerEndTime:   tt.fields.DayCrawlerEndTime,
			}
			got, err := i.GetAllCrawTasks(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllCrawTasks() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllCrawTasks() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIPCrawTask_GetIPCount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mClient := mockmysql.NewMockClient(ctrl)
	mClient.EXPECT().Query(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, dst interface{}, query string, args ...interface{}) error {
			return nil
		}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mClient)
	defer mockMysql.Reset()

	type fields struct {
		ID                  int
		CrawType            int
		Platform            string
		CrawKey             string
		CoverID             string
		Valid               int
		CreateTime          time.Time
		UpdateTime          time.Time
		DayCrawlerState     int
		DayCrawlerStartTime time.Time
		DayCrawlerEndTime   time.Time
	}
	type args struct {
		ctx    context.Context
		TypeID int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint64
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx:    trpc.BackgroundContext(),
				TypeID: 1,
			},
			fields: fields{
				CoverID:  "mzc00200y98zmrd",
				CrawType: model.OfficeAccountType,
				Platform: "douyin",
				CrawKey:  "testCrawKey",
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &IPCrawTask{
				ID:                  tt.fields.ID,
				CrawType:            tt.fields.CrawType,
				Platform:            tt.fields.Platform,
				CrawKey:             tt.fields.CrawKey,
				CoverID:             tt.fields.CoverID,
				Valid:               tt.fields.Valid,
				CreateTime:          tt.fields.CreateTime,
				UpdateTime:          tt.fields.UpdateTime,
				DayCrawlerState:     tt.fields.DayCrawlerState,
				DayCrawlerStartTime: tt.fields.DayCrawlerStartTime,
				DayCrawlerEndTime:   tt.fields.DayCrawlerEndTime,
			}
			got, err := i.GetIPCount(tt.args.ctx, tt.args.TypeID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetIPCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetIPCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCPCrawTask_GetAllCrawTasks(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mClient := mockmysql.NewMockClient(ctrl)
	mClient.EXPECT().QueryToStructs(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, dst interface{}, query string, args ...interface{}) error {
			if len(args) != 0 {
				if args[0].(string) == "err" {
					return fmt.Errorf("123")
				}
			}
			infoPtr, _ := dst.(*[]*CPCrawTask)
			*infoPtr = []*CPCrawTask{
				{},
			}
			return nil
		}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mClient)
	defer mockMysql.Reset()

	type fields struct {
		ID                  int
		Platform            string
		AccountName         string
		AccountID           string
		AccountType         int
		LinkURL             string
		AccountState        int
		CreateTime          time.Time
		UpdateTime          time.Time
		DayCrawlerState     int
		DayCrawlerStartTime time.Time
		DayCrawlerEndTime   time.Time
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*CPCrawTask
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				AccountName:  "三体",
				AccountState: 1,
				Platform:     "douyin",
				LinkURL:      "testCrawKey",
			},
			want:    []*CPCrawTask{{}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CPCrawTask{
				ID:                  tt.fields.ID,
				Platform:            tt.fields.Platform,
				AccountName:         tt.fields.AccountName,
				AccountID:           tt.fields.AccountID,
				AccountType:         tt.fields.AccountType,
				LinkURL:             tt.fields.LinkURL,
				AccountState:        tt.fields.AccountState,
				CreateTime:          tt.fields.CreateTime,
				UpdateTime:          tt.fields.UpdateTime,
				DayCrawlerState:     tt.fields.DayCrawlerState,
				DayCrawlerStartTime: tt.fields.DayCrawlerStartTime,
				DayCrawlerEndTime:   tt.fields.DayCrawlerEndTime,
			}
			got, err := c.GetAllCrawTasks(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllCrawTasks() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllCrawTasks() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCPCrawTask_GetCrawTaskByID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mClient := mockmysql.NewMockClient(ctrl)
	mClient.EXPECT().QueryToStructs(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, dst interface{}, query string, args ...interface{}) error {
			infoPtr, _ := dst.(*[]*CPCrawTask)
			*infoPtr = []*CPCrawTask{
				{
					ID:           0,
					AccountName:  "三体",
					AccountState: 1,
					Platform:     "douyin",
					LinkURL:      "testCrawKey",
				},
			}
			return nil
		}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mClient)
	defer mockMysql.Reset()

	type fields struct {
		ID                  int
		Platform            string
		AccountName         string
		AccountID           string
		AccountType         int
		LinkURL             string
		AccountState        int
		CreateTime          time.Time
		UpdateTime          time.Time
		DayCrawlerState     int
		DayCrawlerStartTime time.Time
		DayCrawlerEndTime   time.Time
	}
	type args struct {
		ctx context.Context
		id  int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				AccountName:  "三体",
				AccountState: 1,
				Platform:     "douyin",
				LinkURL:      "testCrawKey",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CPCrawTask{
				ID:                  tt.fields.ID,
				Platform:            tt.fields.Platform,
				AccountName:         tt.fields.AccountName,
				AccountID:           tt.fields.AccountID,
				AccountType:         tt.fields.AccountType,
				LinkURL:             tt.fields.LinkURL,
				AccountState:        tt.fields.AccountState,
				CreateTime:          tt.fields.CreateTime,
				UpdateTime:          tt.fields.UpdateTime,
				DayCrawlerState:     tt.fields.DayCrawlerState,
				DayCrawlerStartTime: tt.fields.DayCrawlerStartTime,
				DayCrawlerEndTime:   tt.fields.DayCrawlerEndTime,
			}
			if err := c.GetCrawTaskByID(tt.args.ctx, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("GetCrawTaskByID() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCPCrawTask_SetCrawTaskState(t *testing.T) {
	patches := gomonkey.ApplyFunc(updateCrawState, func(ctx context.Context,
		tblName string, id int, crawState int) error {
		return nil
	})
	defer patches.Reset()

	type fields struct {
		ID                  int
		Platform            string
		AccountName         string
		AccountID           string
		AccountType         int
		LinkURL             string
		AccountState        int
		CreateTime          time.Time
		UpdateTime          time.Time
		DayCrawlerState     int
		DayCrawlerStartTime time.Time
		DayCrawlerEndTime   time.Time
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				AccountName:  "三体",
				AccountState: 1,
				Platform:     "douyin",
				LinkURL:      "testCrawKey",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CPCrawTask{
				ID:                  tt.fields.ID,
				Platform:            tt.fields.Platform,
				AccountName:         tt.fields.AccountName,
				AccountID:           tt.fields.AccountID,
				AccountType:         tt.fields.AccountType,
				LinkURL:             tt.fields.LinkURL,
				AccountState:        tt.fields.AccountState,
				CreateTime:          tt.fields.CreateTime,
				UpdateTime:          tt.fields.UpdateTime,
				DayCrawlerState:     tt.fields.DayCrawlerState,
				DayCrawlerStartTime: tt.fields.DayCrawlerStartTime,
				DayCrawlerEndTime:   tt.fields.DayCrawlerEndTime,
			}
			if err := c.SetCrawTaskState(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("SetCrawTaskState() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestIPCrawTask_GetCrawTaskByID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mClient := mockmysql.NewMockClient(ctrl)
	mClient.EXPECT().QueryToStructs(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, dst interface{}, query string, args ...interface{}) error {
			infoPtr, _ := dst.(*[]*IPCrawTask)
			*infoPtr = []*IPCrawTask{
				{
					ID:       0,
					CrawType: model.OfficeAccountType,
					Platform: "douyin",
					CrawKey:  "三体",
					CoverID:  "mzc00200y98zmrd",
				},
			}
			return nil
		}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mClient)
	defer mockMysql.Reset()

	type fields struct {
		ID                  int
		CrawType            int
		Platform            string
		CrawKey             string
		CoverID             string
		Valid               int
		CreateTime          time.Time
		UpdateTime          time.Time
		DayCrawlerState     int
		DayCrawlerStartTime time.Time
		DayCrawlerEndTime   time.Time
	}
	type args struct {
		ctx context.Context
		id  int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				CrawKey:  "三体",
				CoverID:  "mzc00200y98zmrd",
				Platform: "douyin",
				CrawType: model.OfficeAccountType,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &IPCrawTask{
				ID:                  tt.fields.ID,
				CrawType:            tt.fields.CrawType,
				Platform:            tt.fields.Platform,
				CrawKey:             tt.fields.CrawKey,
				CoverID:             tt.fields.CoverID,
				Valid:               tt.fields.Valid,
				CreateTime:          tt.fields.CreateTime,
				UpdateTime:          tt.fields.UpdateTime,
				DayCrawlerState:     tt.fields.DayCrawlerState,
				DayCrawlerStartTime: tt.fields.DayCrawlerStartTime,
				DayCrawlerEndTime:   tt.fields.DayCrawlerEndTime,
			}
			if err := i.GetCrawTaskByID(tt.args.ctx, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("GetCrawTaskByID() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestIPCrawTask_GetIPSucCount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mClient := mockmysql.NewMockClient(ctrl)
	mClient.EXPECT().Query(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, dst interface{}, query string, args ...interface{}) error {
			return nil
		}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mClient)
	defer mockMysql.Reset()

	type fields struct {
		ID                  int
		CrawType            int
		Platform            string
		CrawKey             string
		CoverID             string
		Valid               int
		CreateTime          time.Time
		UpdateTime          time.Time
		DayCrawlerState     int
		DayCrawlerStartTime time.Time
		DayCrawlerEndTime   time.Time
	}
	type args struct {
		ctx        context.Context
		crawType   int
		CreateDate string
		TypeID     int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint64
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx:    trpc.BackgroundContext(),
				TypeID: 1,
			},
			fields: fields{
				CrawKey:  "三体",
				CoverID:  "mzc00200y98zmrd",
				Platform: "douyin",
				CrawType: model.OfficeAccountType,
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &IPCrawTask{
				ID:                  tt.fields.ID,
				CrawType:            tt.fields.CrawType,
				Platform:            tt.fields.Platform,
				CrawKey:             tt.fields.CrawKey,
				CoverID:             tt.fields.CoverID,
				Valid:               tt.fields.Valid,
				CreateTime:          tt.fields.CreateTime,
				UpdateTime:          tt.fields.UpdateTime,
				DayCrawlerState:     tt.fields.DayCrawlerState,
				DayCrawlerStartTime: tt.fields.DayCrawlerStartTime,
				DayCrawlerEndTime:   tt.fields.DayCrawlerEndTime,
			}
			got, err := i.GetIPSucCount(tt.args.ctx, tt.args.crawType, tt.args.CreateDate, tt.args.TypeID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetIPSucCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetIPSucCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIPCrawTask_SetCrawTaskState(t *testing.T) {
	patches := gomonkey.ApplyFunc(updateCrawState, func(ctx context.Context,
		tblName string, id int, crawState int) error {
		return nil
	})
	defer patches.Reset()

	type fields struct {
		ID                  int
		CrawType            int
		Platform            string
		CrawKey             string
		CoverID             string
		Valid               int
		CreateTime          time.Time
		UpdateTime          time.Time
		DayCrawlerState     int
		DayCrawlerStartTime time.Time
		DayCrawlerEndTime   time.Time
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			fields: fields{
				CrawKey:  "三体",
				CoverID:  "mzc00200y98zmrd",
				Platform: "douyin",
				CrawType: model.OfficeAccountType,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &IPCrawTask{
				ID:                  tt.fields.ID,
				CrawType:            tt.fields.CrawType,
				Platform:            tt.fields.Platform,
				CrawKey:             tt.fields.CrawKey,
				CoverID:             tt.fields.CoverID,
				Valid:               tt.fields.Valid,
				CreateTime:          tt.fields.CreateTime,
				UpdateTime:          tt.fields.UpdateTime,
				DayCrawlerState:     tt.fields.DayCrawlerState,
				DayCrawlerStartTime: tt.fields.DayCrawlerStartTime,
				DayCrawlerEndTime:   tt.fields.DayCrawlerEndTime,
			}
			if err := i.SetCrawTaskState(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("SetCrawTaskState() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_updateCrawState(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mClient := mockmysql.NewMockClient(ctrl)
	mClient.EXPECT().Exec(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, _ interface{}, _ ...interface{}) (sql.Result, error) {
			return nil, nil
		}).AnyTimes()
	mockMysql := gomonkey.ApplyGlobalVar(&proxy, mClient)
	defer mockMysql.Reset()

	type args struct {
		ctx       context.Context
		tblName   string
		id        int
		crawState int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx:       trpc.BackgroundContext(),
				tblName:   "t_test",
				id:        123,
				crawState: model.CrawlerRunning,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := updateCrawState(tt.args.ctx, tt.args.tblName, tt.args.id, tt.args.crawState); (err != nil) != tt.wantErr {
				t.Errorf("updateCrawState() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
