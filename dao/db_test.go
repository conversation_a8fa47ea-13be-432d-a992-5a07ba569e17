package dao

import (
	"context"
	"database/sql"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.code.oa.com/trpc-go/trpc-database/mysql/mockmysql"
	"github.com/golang/mock/gomock"
)

func TestUnshelvedVideo_Insert(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	uVideo := &UnshelvedVideo{
		// 填写 UnshelvedVideo 结构体的字段值，作为测试输入
		// 根据您的结构体进行修改
	}

	ctx := context.Background()
	mockCli := mockmysql.NewMockClient(ctrl)
	mockCli.EXPECT().NamedExec(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(
		_ interface{}, _ interface{}, _ interface{}) (sql.Result, error) {
		return nil, nil
	})

	// 直接设置mock对象替换全局变量
	originalProxy := proxy
	proxy = mockCli
	defer func() {
		proxy = originalProxy
	}()

	// 进行测试，调用您的函数
	err := uVideo.Insert(ctx)
	// 检查结果
	if err != nil {
		t.Errorf("InsertData() returned error: %v", err)
	}
}

func TestGetWaitingVideos(t *testing.T) {
	ctx := context.Background()
	limit := 2

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCli := mockmysql.NewMockClient(ctrl)
	mockCli.EXPECT().QueryToStructs(ctx, gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, ptr interface{}, query string, args ...interface{}) error {
			infos := ptr.(*[]*UnshelvedVideo)
			*infos = append(*infos, &UnshelvedVideo{
				VideoID: "abcdefghijk1",
				Covers:  "cover1,cover2",
			})
			*infos = append(*infos, &UnshelvedVideo{
				VideoID: "abcdefghijk2",
				Covers:  "cover3,cover4",
			})
			return nil
		}).AnyTimes()

	// 直接设置mock对象替换全局变量
	originalProxy := proxy
	proxy = mockCli
	defer func() {
		proxy = originalProxy
	}()

	// 测试并检查结果
	unshelvedVideo := &UnshelvedVideo{}
	infos, err := unshelvedVideo.GetWaitingVideos(ctx, limit)
	assert.NoError(t, err)
	assert.Len(t, infos, 2, "Expected to get 2 waiting videos")
	assert.Equal(t, "abcdefghijk1", infos[0].VideoID, "Expected to get the correct video ID")
	assert.Equal(t, "cover1,cover2", infos[0].Covers, "Expected to get the correct covers")
	assert.Equal(t, "abcdefghijk2", infos[1].VideoID, "Expected to get the correct video ID")
	assert.Equal(t, "cover3,cover4", infos[1].Covers, "Expected to get the correct covers")
}

func TestSetTaskState(t *testing.T) {
	ctx := context.Background()
	dataID := 111
	state := 1

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCli := mockmysql.NewMockClient(ctrl)
	mockCli.EXPECT().Exec(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context,
		query string, args ...interface{}) (sql.Result, error) {
		return nil, nil
	}).AnyTimes()

	// 直接设置mock对象替换全局变量
	originalProxy := proxy
	proxy = mockCli
	defer func() {
		proxy = originalProxy
	}()

	// 测试并检查结果
	unshelvedVideo := &UnshelvedVideo{}
	err := unshelvedVideo.SetTaskState(ctx, dataID, state)
	assert.NoError(t, err)
}
