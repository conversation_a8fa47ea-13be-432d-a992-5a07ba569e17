package common

import (
	"runtime/debug"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	storeComm "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/service_protocol/trpc-protocol/VideoMediaInf"
	"git.woa.com/jce/jce"

	"github.com/Shopify/sarama"
)

// SaveInfo kafka保留信息
type SaveInfo struct {
	// Container 生产者容器
	Container string
}

// TimeoutRequest 调用底层数据适配层超时变更消息结构
type TimeoutRequest struct {
	// Id 数据id
	Id string `json:"id"`
	// DataSourceId 数据源id
	DataSourceId int32 `json:"dataSourceId"`
	// BaseFields 基础字段信息
	BaseFields []*storeComm.FieldInfo `json:"baseFields"`
	// UpdateFields 变更请求
	UpdateFields []*storeComm.UpdateFieldInfo `json:"updateFields"`
	// 变更消息基础信息
	DataSetId  int32  `json:"dataSetId"`
	LocalIp    string `json:"localIp"`
	RemoteIp   string `json:"remoteIp"`
	TimeStamp  int32  `json:"timeStamp"`
	SequenceId int64  `json:"sequenceId"`
}

// KafkaProducerCfg kafka配置结构
type KafkaProducerCfg struct {
	// ClientId 客户端id
	ClientId string `yaml:"clientId"`
	// Address 接入点信息
	Address string `yaml:"address"`
	// Timeout 超时时间
	Timeout time.Duration `yaml:"timeout"`
	// MaxLength 发送数据最大长度
	MaxLength int `yaml:"maxLength"`
}

// ProducerClient kafka生产者客户端
type ProducerClient struct {
	KafkaProducerCfg
	// asyncProducer 异步生产者
	asyncProducer sarama.AsyncProducer
	// errHandler 失败回调函数
	errHandler func(*sarama.ProducerError)
}

// NewKafkaProducer 创建kafka生产者客户端
func NewKafkaProducer(config KafkaProducerCfg) *ProducerClient {
	if !config.check() {
		log.Errorf("Invalid init config:%+v.", config)
		return nil
	}

	return (&ProducerClient{
		KafkaProducerCfg: config,
	}).initAsyncProducer()
}

// check 检查初始化参数 true:检查通过 false:检查没通过
func (config *KafkaProducerCfg) check() bool {
	if "" == config.Address || "" == config.ClientId {
		return false
	}

	// 未配置超时时间或者最大长度，则使用默认值
	if config.Timeout == 0 {
		config.Timeout = 3 * time.Second
	}

	if config.MaxLength == 0 {
		config.MaxLength = 131072
	}

	return true
}

// SetErrHandler 设置异步生产失败回调函数
func (p *ProducerClient) SetErrHandler(handler func(errors *sarama.ProducerError)) {
	if p == nil {
		return
	}

	p.errHandler = handler
}

// Close 关闭生产者客户端
func (p *ProducerClient) Close() error {
	if p.asyncProducer != nil {
		return p.asyncProducer.Close()
	}
	return nil
}

// initAsyncProducer 初始化异步生产者
func (p *ProducerClient) initAsyncProducer() *ProducerClient {
	producer, err := sarama.NewAsyncProducer(strings.Split(p.Address, ","),
		newProducerConfig(p.ClientId, p.Timeout, p.MaxLength))
	if err != nil {
		log.Error(err)
		return nil
	}
	// 消费状态消息,防止死锁
	go func(producer sarama.AsyncProducer) {
		if nil == producer {
			log.Error("invalid producer, producer is nil!")
			return
		}
		errors := producer.Errors()
		success := producer.Successes()
		for {
			select {
			case err := <-errors:
				if p.errHandler != nil && err != nil {
					p.errHandler(err)
				}
				continue
			case <-success:
				continue
			}
		}
	}(producer)

	p.asyncProducer = producer
	return p
}

// SendAsyncMsg 异步发送消息接口
func (p *ProducerClient) SendAsyncMsg(containerName, key, topics string, msg []byte) bool {
	defer func() {
		// 异常情况下恢复
		if err, ok := recover().(error); ok {
			log.Errorf("panic in %v", err)
			log.Error(string(debug.Stack()))
			if p.asyncProducer != nil {
				_ = p.asyncProducer.Close()
			}
			p.initAsyncProducer()
		}
	}()

	if msg == nil {
		return false
	}

	return asyncProducer(
		p.asyncProducer,
		containerName,
		topics,
		key,
		msg,
	)
}

func asyncProducer(p sarama.AsyncProducer, containerName, topics, key string, s []byte) bool {
	if nil == p {
		return false
	}

	msg := &sarama.ProducerMessage{
		Topic:    topics,
		Key:      sarama.ByteEncoder(key),
		Value:    sarama.ByteEncoder(s),
		Metadata: SaveInfo{Container: containerName},
	}
	// todo:超时处理
	p.Input() <- msg
	return true
}

// newProducerConfig 创建生产者配置
func newProducerConfig(clientId string, timeout time.Duration, maxLength int) *sarama.Config {
	config := sarama.NewConfig()
	config.ClientID = clientId
	// 等待服务器所有副本都保存成功后的响应
	config.Producer.RequiredAcks = sarama.WaitForAll
	// 按照key向partition发送消息
	config.Producer.Partitioner = sarama.NewHashPartitioner
	// 是否等待成功和失败后的响应,只有上面的RequireACKs设置不是NoResponse这里才有用.
	config.Producer.Return.Successes = true
	config.Producer.Return.Errors = true
	config.Producer.Timeout = timeout
	config.Metadata.Timeout = timeout
	// 请显示指定协议版本号
	config.Version = sarama.V1_1_1_0
	// 一次请求最大大小。一次请求中包含多个batch。为了保证消息的读写质量和最优化Kafka的性能，虫洞平台限制单次最大大小为128K。
	config.Producer.MaxMessageBytes = maxLength
	return config
}

// ModifyInfoConv 变更消息转换对象
type ModifyInfoConv struct {
	ID           string
	DataSetID    int32
	AppID        string
	OperatorName string
	LocalIP      string
	RemoteIP     string
	ExtInfo      string
	SequenceID   int64
	TenantID     string
	// HandleErr 序列化失败处理函数
	HandleErr func(string, string, *VideoMediaInf.ModifyNotify)
}

func transToJceMapVal(raw map[string]*storeComm.MapValue) map[string]VideoMediaInf.MapValue {
	val := make(map[string]VideoMediaInf.MapValue)
	for k, v := range raw {
		val[k] = VideoMediaInf.MapValue{
			Type:     VideoMediaInf.EnumFieldType(v.Type),
			StrValue: v.StrValue,
			VecStr:   v.VecStr,
		}
	}
	return val
}

// modifyNotifyCovertPBToJce 将PB协议结构体转化为Jce协议结构体
func (m *ModifyInfoConv) modifyNotifyCovertPBToJce(baseInfo map[string]*storeComm.FieldInfo,
	modifyInfos []*storeComm.ModifyFieldInfo,
) VideoMediaInf.ModifyNotify {
	notify := VideoMediaInf.ModifyNotify{
		Id:           m.ID,
		DataSetId:    m.DataSetID,
		TimeStamp:    int32(time.Now().Unix()),
		AppId:        m.AppID,
		OperatorName: m.OperatorName,
		LocalIp:      m.LocalIP,
		RemoteIp:     m.RemoteIP,
		ExtInfo:      m.ExtInfo,
		SequenceId:   m.SequenceID,
		BaseInfo:     make(map[string]VideoMediaInf.FieldInfo, 0),
		TenantID:     m.TenantID,
	}
	for _, modifyInfo := range modifyInfos {
		notify.ModifyFieldInfos = append(notify.ModifyFieldInfos, VideoMediaInf.ModifyFieldInfo{
			FieldName:   modifyInfo.FieldName,
			FieldId:     modifyInfo.FieldId,
			FieldType:   VideoMediaInf.EnumFieldType(modifyInfo.FieldType),
			OldStrValue: modifyInfo.OldStrValue,
			NewStrValue: modifyInfo.NewStrValue,
			OldVecInt:   modifyInfo.OldVecInt,
			NewVecInt:   modifyInfo.NewVecInt,
			OldVecStr:   modifyInfo.OldVecStr,
			NewVecStr:   modifyInfo.NewVecStr,
			OldMapVal:   transToJceMapVal(modifyInfo.OldMapVal),
			NewMapVal:   transToJceMapVal(modifyInfo.NewMapVal),
		})
	}

	for fieldName, fieldInfo := range baseInfo {
		notify.BaseInfo[fieldName] = VideoMediaInf.FieldInfo{
			FieldName:      fieldInfo.FieldName,
			FieldType:      VideoMediaInf.EnumFieldType(fieldInfo.FieldType),
			StrValue:       fieldInfo.StrValue,
			VecInt:         fieldInfo.VecInt,
			FieldId:        fieldInfo.FieldId,
			InnerFieldName: fieldInfo.InnerFieldName,
			VecStr:         fieldInfo.VecStr,
			MapVal:         transToJceMapVal(fieldInfo.MapVal),
		}
	}
	return notify
}

// ToBytes 转换为字节数据
func (m *ModifyInfoConv) ToBytes(baseInfo map[string]*storeComm.FieldInfo,
	modifyInfos []*storeComm.ModifyFieldInfo,
) (*VideoMediaInf.ModifyNotify, []byte) {
	notify := m.modifyNotifyCovertPBToJce(baseInfo, modifyInfos)
	codecBuf := jce.NewBuffer()
	if err := notify.WriteTo(codecBuf); err != nil {
		log.Errorf("Fail to encode notify to binary, err is %s.", err)
		if m.HandleErr != nil {
			m.HandleErr(m.RemoteIP, "KafkaProducerSendError", &notify)
		}
		return nil, nil
	}
	log.Debugf("Send kafka msg value:%v.", notify)
	return &notify, codecBuf.ToBytes()
}
