package common

import (
	"reflect"
	"testing"

	"git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	storeComm "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/config_cache/dao"
)

func TestIsFieldEmpty(t *testing.T) {
	type args struct {
		f *storeComm.FieldInfo
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "测试文本类型字段-1",
			args: args{f: &storeComm.FieldInfo{
				FieldName: "titleX",
				FieldId:   9,
				FieldType: storeComm.EnumFieldType_FieldTypeStr,
				StrValue:  "",
			}},
			want: true,
		}, {
			name: "测试文本类型字段-2",
			args: args{f: &storeComm.FieldInfo{
				FieldName: "titleX",
				FieldId:   9,
				FieldType: storeComm.EnumFieldType_FieldTypeStr,
				StrValue:  "测试标题",
			}},
			want: false,
		}, {
			name: "测试选项类型字段-1",
			args: args{f: &storeComm.FieldInfo{
				FieldName: "vcats",
				FieldId:   80045,
				FieldType: storeComm.EnumFieldType_FieldTypeIntVec,
				VecInt:    nil,
			}},
			want: true,
		}, {
			name: "测试选项类型字段-2",
			args: args{f: &storeComm.FieldInfo{
				FieldName: "vcats",
				FieldId:   80045,
				FieldType: storeComm.EnumFieldType_FieldTypeIntVec,
				VecInt:    []uint32{1, 2},
			}},
			want: false,
		}, {
			name: "测试set类型字段-1",
			args: args{f: &storeComm.FieldInfo{
				FieldName: "covers",
				FieldId:   80086,
				FieldType: storeComm.EnumFieldType_FieldTypeSet,
				VecStr:    nil,
			}},
			want: true,
		}, {
			name: "测试set类型字段-2",
			args: args{f: &storeComm.FieldInfo{
				FieldName: "covers",
				FieldId:   80086,
				FieldType: storeComm.EnumFieldType_FieldTypeSet,
				VecStr:    []string{"testCid"},
			}},
			want: false,
		}, {
			name: "测试map类型字段-1",
			args: args{f: &storeComm.FieldInfo{
				FieldName: "testMap",
				FieldId:   80086,
				FieldType: storeComm.EnumFieldType_FieldTypeMapKV,
				MapVal: map[string]*storeComm.MapValue{"1": {
					Type:     storeComm.EnumFieldType_FieldTypeStr,
					StrValue: "",
				}, "2": {
					Type:     storeComm.EnumFieldType_FieldTypeStr,
					StrValue: "",
				}},
			}},
			want: true,
		}, {
			name: "测试map类型字段-2",
			args: args{f: &storeComm.FieldInfo{
				FieldName: "testMap",
				FieldId:   80086,
				FieldType: storeComm.EnumFieldType_FieldTypeMapKV,
				MapVal: map[string]*storeComm.MapValue{"1": {
					Type:     storeComm.EnumFieldType_FieldTypeStr,
					StrValue: "",
				}, "2": {
					Type:     storeComm.EnumFieldType_FieldTypeStr,
					StrValue: "test",
				}},
			}},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsFieldEmpty(tt.args.f); got != tt.want {
				t.Errorf("IsFieldEmpty() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewFieldInfo(t *testing.T) {
	type args struct {
		ty        storeComm.EnumFieldType
		fieldID   uint32
		fieldName string
		innerName string
		val       *FieldVal
	}
	tests := []struct {
		name string
		args args
		want *storeComm.FieldInfo
	}{
		{
			name: "测试创建map字段数据",
			args: args{
				ty:        storeComm.EnumFieldType_FieldTypeMapKList,
				fieldID:   3200,
				fieldName: "aspect_list",
				innerName: "aspect_list",
				val: &FieldVal{
					MapVal: map[string]string{"testCid1": "test"},
				},
			},
			want: &storeComm.FieldInfo{
				FieldName:      "aspect_list",
				InnerFieldName: "aspect_list",
				FieldId:        3200,
				FieldType:      storeComm.EnumFieldType_FieldTypeMapKList,
				MapVal: map[string]*storeComm.MapValue{"testCid1": {
					Type:   storeComm.EnumFieldType_FieldTypeSet,
					VecStr: []string{"test"},
				}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewFieldInfo(tt.args.ty, tt.args.fieldID, tt.args.fieldName, tt.args.innerName, tt.args.val); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewFieldInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFillDocInfo(t *testing.T) {
	type args struct {
		docInfo map[string]*storeComm.DocInfo
		f       *dao.FieldInfo
		val     *adaptor_layer.GetFieldInfo
	}
	tests := []struct {
		name string
		args args
		want map[string]*storeComm.DocInfo
	}{
		{
			name: "测试构建不存在ID的文档",
			args: args{
				docInfo: map[string]*storeComm.DocInfo{},
				f: &dao.FieldInfo{
					DataSetID:      2001,
					DataSourceID:   1,
					FieldID:        9,
					FieldType:      int32(storeComm.EnumFieldType_FieldTypeStr),
					InnerFieldName: "titleX",
					FieldName:      "titleX",
				},
				val: &adaptor_layer.GetFieldInfo{
					FieldId:    9,
					FieldValue: "test title",
					Id:         "testVid",
				},
			},
			want: map[string]*storeComm.DocInfo{"testVid": {
				Id: "testVid",
				Fields: map[string]*storeComm.FieldInfo{"titleX": {
					FieldName:      "titleX",
					InnerFieldName: "titleX",
					FieldId:        9,
					FieldType:      storeComm.EnumFieldType_FieldTypeStr,
					StrValue:       "test title",
				}},
			}},
		}, {
			name: "测试添加map字段",
			args: args{
				docInfo: map[string]*storeComm.DocInfo{"testVid": {
					Id: "testVid",
					Fields: map[string]*storeComm.FieldInfo{"testMap": {
						FieldName:      "testMap",
						InnerFieldName: "testMap",
						FieldId:        1001,
						FieldType:      storeComm.EnumFieldType_FieldTypeMapKV,
						MapVal: map[string]*storeComm.MapValue{"key1": {
							Type:     storeComm.EnumFieldType_FieldTypeStr,
							StrValue: "val1",
						}},
					}},
				}},
				f: &dao.FieldInfo{
					DataSetID:      2001,
					DataSourceID:   1,
					FieldID:        1001,
					FieldType:      int32(storeComm.EnumFieldType_FieldTypeMapKV),
					InnerFieldName: "testMap",
					FieldName:      "testMap",
				},
				val: &adaptor_layer.GetFieldInfo{
					FieldId:    1001,
					FieldValue: "val2",
					FieldKey:   "key2",
					Id:         "testVid",
				},
			},
			want: map[string]*storeComm.DocInfo{"testVid": {
				Id: "testVid",
				Fields: map[string]*storeComm.FieldInfo{"testMap": {
					FieldName:      "testMap",
					InnerFieldName: "testMap",
					FieldId:        1001,
					FieldType:      storeComm.EnumFieldType_FieldTypeMapKV,
					MapVal: map[string]*storeComm.MapValue{"key1": {
						Type:     storeComm.EnumFieldType_FieldTypeStr,
						StrValue: "val1",
					}, "key2": {
						Type:     storeComm.EnumFieldType_FieldTypeStr,
						StrValue: "val2",
					}},
				}},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			FillDocInfo(tt.args.docInfo, tt.args.f, tt.args.val)
			if !reflect.DeepEqual(tt.args.docInfo, tt.want) {
				t.Errorf("DocInfo() = %v, want %v", tt.args.docInfo, tt.want)
			}
		})
	}
}
