//go:build !darwin || !arm64

// gomonkey包在 Apple Silicon 架构下不支持运行, 这里将使用 Gomonkey 包的测试单独一个测试文件
package main

import (
	"context"
	"fmt"
	"testing"

	pcgmonitor "git.code.oa.com/pcgmonitor/trpc_report_api_go"
	"git.code.oa.com/pcgmonitor/trpc_report_api_go/pb/nmnt"
	"git.code.oa.com/trpc-go/trpc-go/client"
	access "git.code.oa.com/trpcprotocol/storage_service/access_layer"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/access_layer/util/errs"
	"git.code.oa.com/video_media/storage_service/access_layer/util/report"
	"git.code.oa.com/video_media/storage_service/auth"
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"
	"github.com/prashantv/gostub"
)

func initMock(ctrl *gomock.Controller) *gostub.Stubs {
	mockAuthAndFrequency()
	mockAttAAndMonitor()
	mockCache()
	// 初始化放号
	_ = report.InitIDCreator()
	return mockAdaptorLayer(ctrl)
}

func mockAdaptorLayer(ctrl *gomock.Controller) *gostub.Stubs {
	proxy := adaptor.NewMockDataAdaptorClientProxy(ctrl)
	stub := gostub.Stub(&adaptor.NewDataAdaptorClientProxy, func(opts ...client.Option) adaptor.DataAdaptorClientProxy {
		return proxy
	})
	proxy.EXPECT().GetFieldInfos(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, req *adaptor.GetFieldInfosRequest,
			opts ...client.Option,
		) (rsp *adaptor.GetFieldInfosResponse, err error) {
			return &adaptor.GetFieldInfosResponse{
				FieldInfos: []*adaptor.GetFieldInfo{{
					FieldId:    9,
					FieldValue: "testTitle",
				}, {
					FieldId:    3200,
					FieldValue: "cid1+cid2",
					FieldKey:   "testKey",
				}},
			}, nil
		}).AnyTimes()
	proxy.EXPECT().BatchGetFieldInfos(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context,
		req *adaptor.BatchGetFieldsRequest, opts ...client.Option,
	) (rsp *adaptor.BatchGetFieldsResponse, err error) {
		if len(req.GetId()) == 0 {
			return &adaptor.BatchGetFieldsResponse{}, nil
		}
		return &adaptor.BatchGetFieldsResponse{
			DocInfos: map[string]*protocol.DocInfo{req.GetId()[0]: {
				Id: req.GetId()[0],
				Fields: map[string]*protocol.FieldInfo{"titleX": {
					FieldName: "titleX",
					FieldId:   9,
					StrValue:  "testTitle",
				}, "aspect_list": {
					FieldName: "aspect_list",
					FieldId:   3200,
					MapVal:    map[string]*protocol.MapValue{"testKey": {VecStr: []string{"cid1", "cid2"}}},
				}},
			}},
		}, nil
	}).AnyTimes()
	proxy.EXPECT().SetFieldInfos(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&adaptor.SetFieldInfosResponse{}, nil).AnyTimes()
	return stub
}

func mockAuthAndFrequency() {
	gomonkey.ApplyFunc(auth.CheckAuth, func(appID, appKey string) error {
		if appID == "err_auth" {
			return errs.New(auth.ErrAuth)
		}
		return nil
	})
	gomonkey.ApplyFunc(auth.CheckFrequency, func(labels map[string]string) error {
		if labels["appID"] == "over_load" {
			return errs.New(auth.ErrOverLoad)
		}
		return nil
	})
}

func mockAttAAndMonitor() {
	gomonkey.ApplyFunc(tool.ReportToAtta, func(info tool.ReportDataInf) {})
	gomonkey.ApplyFunc(pcgmonitor.ReportCustom, func(_ string, _ []string, _ []*nmnt.StatValue) error {
		return nil
	})
}

func transKey(keys ...interface{}) string {
	var key string
	for _, k := range keys {
		key += fmt.Sprint(k) + "|"
	}
	return key
}

func mockCache() {
	c := map[string]map[string]interface{}{
		item.TBDataSet: {transKey(2001, ""): &item.DataSet{
			DataSetID:     2001,
			Topic:         "test_video_topic",
			DataSourceIDs: "1,2",
			BaseInfo:      "title",
		}}, item.TBAppField: {transKey("testApp", 2001): &item.AppFieldInfo{
			AppID:         "testApp",
			DataSetID:     2001,
			ReadListText:  "title",
			WriteListText: "title",
		}}, item.TBFieldInfo: {transKey("title", 2001): &item.FieldInfo{
			FieldID:      9,
			FieldType:    1,
			DataSetID:    2001,
			DataSourceID: 1,
			FieldName:    "title",
		}, transKey(9, 2001): &item.FieldInfo{
			FieldID:      9,
			FieldType:    1,
			DataSetID:    2001,
			DataSourceID: 1,
			FieldName:    "title",
		}, transKey("aspect_list", 2001): &item.FieldInfo{
			FieldID:      3200,
			FieldType:    5,
			DataSetID:    2001,
			DataSourceID: 1,
			FieldName:    "aspect_list",
		}, transKey(3200, 2001): &item.FieldInfo{
			FieldID:      3200,
			FieldType:    5,
			DataSetID:    2001,
			DataSourceID: 1,
			FieldName:    "aspect_list",
		}},
	}
	gomonkey.ApplyFunc(cache.GetDataSet, func(datasetID int32, tenantID string) *item.DataSet {
		t, ok := c[item.TBDataSet]
		if !ok {
			return nil
		}
		return t[transKey(datasetID, "")].(*item.DataSet)
	})
	gomonkey.ApplyFunc(cache.GetAppFieldInfo, func(appID string, datasetID int32) *item.AppFieldInfo {
		t, ok := c[item.TBAppField]
		if !ok {
			return nil
		}
		return t[transKey(appID, datasetID)].(*item.AppFieldInfo)
	})
	gomonkey.ApplyFunc(cache.GetFieldInfoByName, func(fieldName string, datasetID int32) *item.FieldInfo {
		t, ok := c[item.TBFieldInfo]
		if !ok {
			return nil
		}
		f, _ := t[transKey(fieldName, datasetID)].(*item.FieldInfo)
		return f
	})
	gomonkey.ApplyFunc(cache.GetFieldInfoByID, func(fieldID uint32, datasetID int32) *item.FieldInfo {
		t, ok := c[item.TBFieldInfo]
		if !ok {
			return nil
		}
		f, _ := t[transKey(fieldID, datasetID)].(*item.FieldInfo)
		return f
	})
}

func Test_mediaInterfaceServiceImpl_UpdateMediaInfo(t *testing.T) {
	type args struct {
		ctx context.Context
		req *access.MediaUpdateRequest
		rsp *access.MediaUpdateResponse
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	stub := initMock(ctrl)
	defer stub.Reset()
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "测试鉴权失败",
			args: args{
				ctx: context.TODO(),
				req: &access.MediaUpdateRequest{
					AuthInfo:  &access.AuthInfo{AppId: "err_auth"},
					DataSetId: 2001,
					Id:        "testVid",
				},
				rsp: &access.MediaUpdateResponse{},
			},
			wantErr: errs.New(protocol.EnumMediaErrorCode_RetNoAuth),
		}, {
			name: "测试请求被频控",
			args: args{
				ctx: context.TODO(),
				req: &access.MediaUpdateRequest{
					AuthInfo:  &access.AuthInfo{AppId: "over_load"},
					DataSetId: 2001,
					Id:        "testVid",
				},
				rsp: &access.MediaUpdateResponse{},
			},
			wantErr: errs.New(protocol.EnumMediaErrorCode_RetOverApplyReq),
		}, {
			name: "测试无效请求",
			args: args{
				ctx: context.TODO(),
				req: &access.MediaUpdateRequest{
					AuthInfo:  &access.AuthInfo{AppId: "testApp"},
					DataSetId: 2001,
					Id:        "testVid",
				},
				rsp: &access.MediaUpdateResponse{},
			},
			wantErr: nil,
		}, {
			name: "测试Update请求",
			args: args{
				ctx: context.TODO(),
				req: &access.MediaUpdateRequest{
					AuthInfo:  &access.AuthInfo{AppId: "testApp"},
					DataSetId: 2001,
					Id:        "testVid",
					UpdateFieldInfos: []*protocol.UpdateFieldInfo{{
						FieldInfo: &protocol.FieldInfo{
							FieldName: "title",
							FieldType: protocol.EnumFieldType_FieldTypeStr,
							StrValue:  "测试标题",
						},
						UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
					}, {
						FieldInfo: &protocol.FieldInfo{
							FieldName: "aspect_list",
							FieldType: protocol.EnumFieldType_FieldTypeMapKList,
							MapVal: map[string]*protocol.MapValue{"test": {
								Type:   protocol.EnumFieldType_FieldTypeSet,
								VecStr: []string{"cid1", "cid2"},
							}},
						},
						UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
					}},
				},
				rsp: &access.MediaUpdateResponse{},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &mediaInterfaceServiceImpl{}
			err := s.UpdateMediaInfo(tt.args.ctx, tt.args.req, tt.args.rsp)
			if errs.Code(err) != errs.Code(tt.wantErr) {
				t.Errorf("UpdateMediaInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_mediaInterfaceServiceImpl_GetMediaInfo(t *testing.T) {
	type args struct {
		ctx context.Context
		req *access.MediaGetRequest
		rsp *access.MediaGetResponse
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	stub := initMock(ctrl)
	defer stub.Reset()
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试请求被频控",
			args: args{
				ctx: context.TODO(),
				req: &access.MediaGetRequest{
					AuthInfo:  &access.AuthInfo{AppId: "over_load"},
					DataSetId: 2001,
					Id:        "testVid",
				},
				rsp: &access.MediaGetResponse{},
			},
			wantErr: true,
		}, {
			name: "测试无效请求",
			args: args{
				ctx: context.TODO(),
				req: &access.MediaGetRequest{
					AuthInfo:  &access.AuthInfo{AppId: "testApp"},
					DataSetId: 2001,
					Id:        "testVid",
				},
				rsp: &access.MediaGetResponse{},
			},
			wantErr: false,
		}, {
			name: "测试Get接口",
			args: args{
				ctx: context.TODO(),
				req: &access.MediaGetRequest{
					AuthInfo:   &access.AuthInfo{AppId: "testApp"},
					DataSetId:  2001,
					Id:         "testVid",
					FieldNames: []string{"title", "aspect_list"},
				},
				rsp: &access.MediaGetResponse{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &mediaInterfaceServiceImpl{}
			if err := s.GetMediaInfo(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("GetMediaInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_mediaInterfaceServiceImpl_GetAllMediaInfo(t *testing.T) {
	type args struct {
		ctx context.Context
		req *access.MediaGetAllRequest
		rsp *access.MediaGetAllResponse
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	stub := initMock(ctrl)
	defer stub.Reset()
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试GetAll接口",
			args: args{
				ctx: context.TODO(),
				req: &access.MediaGetAllRequest{
					AuthInfo:  &access.AuthInfo{AppId: "testApp"},
					DataSetId: 2001,
					Id:        "testVid",
				},
				rsp: &access.MediaGetAllResponse{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &mediaInterfaceServiceImpl{}
			if err := s.GetAllMediaInfo(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("GetAllMediaInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
