package model

import (
	"context"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pbu "git.code.oa.com/trpcprotocol/video_media/universal_mgr"
	"git.code.oa.com/video_media/ip_status_update/config"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/dataaccess"
	"git.code.oa.com/video_media/media_go_commlib/mediapkg/universal"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	api "git.woa.com/video_media/media_go_commlib/universal_api"
)

// UpdateInfo IP内容相关更新信息
type UpdateInfo struct {
	CID string

	MediaUnScheduleTime       string // MediaUnScheduleTime 未定档预热时间
	MediaScheduleTime         string // MediaScheduleTime 已定档预热时间
	MediaPrePareTimeText      string // MediaPrePareTimeText 即将上映时间（文本）
	MediaPrePareTime          string // MediaPrePareTime 即将上映时间（时间）
	MediaFirstPlayTime        string
	MediaEndPlayTime          string
	MediaYuReThreeMonthTime   string
	MediaYuReEndTime          string
	MediaThreeYearTailTime    string
	MediaEndTimeForVIP        string // 会员完结
	MediaEndTimeForFree       string
	MediaOperationSegmentTime string // 运营期切段
	MediaIPOnlineStatus       string // MediaIPOnlineStatus 媒资ip实时状态

	PlanFirstTime string // PlanFirstTime 排播首播时间
	PlanEndTime   string // PlanEndTime 排播完结时间

	Category            string // Category 品类
	IsTrailer           bool   // IsTrailer 是否预告片，true为预告片，false为非预告片
	IsPositive          bool   // IsPositive 是否正片，true为正片，false为非正片
	IsSinglePointPay    bool   // IsSinglePointPay 是否单片付费，true为是，false为不是
	ProductTime         string // ProductTime 电视剧出品时间
	EndTime             string // EndTime 电视剧完结时间
	PArea               string // PArea 制片地区
	NetPublishTime      string // NetPublishTime 腾讯首播时间
	LastPubTime         string // LastPubTime 最近一期上线时间
	AnimeUpdateStatus   string // AnimeUpdateStatus 动漫更新状态
	HotLevel            string // HotLevel 版权采买等级
	NatureOfCopyrightID string // NatureOfCopyrightID  版权性质
	VideoCheckupTime    string // VideoCheckupTime 专辑上架时间
	PublishDate         string // PublishDate 播放日期
	SportsColumnType    string // SportsColumnType 体育栏目类型
	SportsMatchID       string // SportsMatchID 比赛ID
	FirstCheckupTime    string //  FirstCheckupTime 首正片上架时间
	PayStatus           string // PayStatus 付费状态
	EditingVersionComic string // EditingVersionComic 动漫剪辑版本
	TotalAuditState     string // TotalAuditState 专辑审核状态
	CheckupState        string // CheckupState 专辑状态

	// 这部分存储计算后待更新数据信息
	UnScheduleTime       string
	ScheduleTime         string
	FirstPlayTime        string
	EndPlayTime          string // EndPlayTime 运营期结束时间
	YuReThreeMonthTime   string
	YuReEndTime          string
	ThreeYearTailTime    string
	EndTimeForVIP        string // 会员完结
	EndTimeForFree       string
	OperationSegmentTime string // 运营期切段
	IPOnlineStatus       string `json:"ip_online_status,omitempty"` // IPOnlineStatus ip实时状态

	UpdateFlag bool
	FieldMap   map[string][]string
	IsUpdate   bool
}

// GetCalculateValue 获取计算结果，如果没在计算范围且当前值不为空则返回@invalid_val
func (u *UpdateInfo) GetCalculateValue(fieldName, val string) string {
	const invalid = "@invalid_val"
	if len(u.FieldMap[fieldName]) == 0 && val != "" {
		return invalid
	}
	return val
}

func makeFieldList(fieldMap map[string][]string, protectFields []string) []*pbu.FieldUpdateEntry {
	var reqFieldList []*pbu.FieldUpdateEntry

	for key, value := range fieldMap {
		if IsContain(protectFields, key) {
			continue
		}
		reqObj := &pbu.FieldUpdateEntry{
			ExternalName: key,
			FieldValues:  value,
			UpdateType:   1, // 更新方式
		}
		reqFieldList = append(reqFieldList, reqObj)
	}
	return reqFieldList
}

// IsContain 判断字符串切片是否包含某字符串
func IsContain(strs []string, str string) bool {
	for _, e := range strs {
		if e == str {
			return true
		}
	}
	return false
}

// UpdateMediaInfo 更新字段
func UpdateMediaInfo(ctx context.Context, mediaInfo msghub.MediaInfo, update *UpdateInfo, dataSet int) error {
	if !update.IsUpdate || len(update.FieldMap) == 0 {
		return nil
	}

	dataID := mediaInfo.Id
	// 获取保护字段，保护字段里的值不更新
	protectFields := strings.Split(parse.GetFieldValue(mediaInfo, parse.OperationProtectionFields), ",")
	reqFieldList := makeFieldList(update.FieldMap, protectFields)
	log.InfoContextf(ctx, "UpdateMediaInfo: cid %v update field is %v", dataID, reqFieldList)
	if len(reqFieldList) == 0 {
		return nil
	}
	var projID, dimID int32
	var ua config.UniAccess
	switch dataSet {
	case parse.CidType:
		projID = universal.ProjIDVideo
		dimID = universal.DimIDCover
		ua = config.GetAccCfg().UniAccUpdate
	case parse.LidType:
		projID = universal.ProjIDVideo
		dimID = universal.DimIDColumn
		ua = config.GetAccCfg().UniAccColumnUpdate
	case parse.ShortDramaType:
		projID = universal.ProjIDVideo
		dimID = universal.DimIDMidCover
		ua = config.GetAccCfg().UniAccMidCoveUpdate
	default:
		return nil
	}
	log.InfoContextf(ctx, "dataID %v update req is %v", dataID, reqFieldList)
	var dataAccess dataaccess.DataAccess
	dataAccess.AccessSetOptions(
		dataaccess.WithProjID(projID),       //参考项目ID与维度ID表格: https://iwiki.woa.com/pages/viewpage.action?pageId=648541669
		dataaccess.WithDimID(dimID),         //参考项目ID与维度ID表格: https://iwiki.woa.com/pages/viewpage.action?pageId=648541669
		dataaccess.WithAppID(ua.UniAppID),   // 统一接入平台申请
		dataaccess.WithAppKey(ua.UniAppKey), // 统一接入平台申请
		dataaccess.WithNamespace("Production"),
		dataaccess.WithCaller(universalPolaris))
	if !ua.CanUpdate {
		log.InfoContextf(ctx, "update dataID %v field success (test mode)", dataID)
		return nil
	}
	if _, err := dataAccess.UpdateMediaData(ctx,
		dataID, reqFieldList, client.WithNamespace("Production"), client.WithDisableServiceRouter()); err != nil {
		log.ErrorContextf(ctx, "update dataID %v field error,err %v", dataID, err)
		return err
	}
	log.InfoContextf(ctx, "update dataID %v field success", dataID)
	return nil
}

// UpdateShortDramaIPStatus 专门更新短剧IP实时状态的函数
func UpdateShortDramaIPStatus(ctx context.Context, dataID string, ipOnlineStatus string) error {
	if ipOnlineStatus == "" {
		log.DebugContextf(ctx, "ipOnlineStatus is empty, skip update")
		return nil
	}

	// 构建更新字段
	reqFieldList := []*pbu.FieldUpdateEntry{
		{
			ExternalName: parse.FieldIPOnlineStatus,
			FieldValues:  []string{ipOnlineStatus},
			UpdateType:   1, // 更新方式
		},
	}
	log.DebugContextf(ctx, "短剧 %v update IP status to %v", dataID, ipOnlineStatus)

	ua := config.GetAccCfg().UniAccMidCoveUpdate
	var dataAccess dataaccess.DataAccess
	dataAccess.AccessSetOptions(
		dataaccess.WithProjID(universal.ProjIDVideo),  // 项目ID
		dataaccess.WithDimID(universal.DimIDMidCover), // 短剧对应的dimID=4
		dataaccess.WithAppID(ua.UniAppID),             // 统一接入平台申请
		dataaccess.WithAppKey(ua.UniAppKey),           // 统一接入平台申请
		dataaccess.WithNamespace("Production"),
		dataaccess.WithCaller(universalPolaris))

	if !config.GetAccCfg().UniAccMidCoveUpdate.CanUpdate {
		log.DebugContextf(ctx, "update 短剧 %v IP status success (test mode)", dataID)
		return nil
	}

	if _, err := dataAccess.UpdateMediaData(ctx,
		dataID, reqFieldList, client.WithNamespace("Production"), client.WithDisableServiceRouter()); err != nil {
		log.ErrorContextf(ctx, "update 短剧 %v IP status error,err %v", dataID, err)
		return err
	}

	log.DebugContextf(ctx, "update 短剧 %v IP status success", dataID)
	return nil
}

// NewUniversalProxy 新建新sdk统一接入平台proxy
func NewUniversalProxy() {
	// 栏目更新配置
	projID, dimID, appID, appKey := config.GetColumnUpdateCfg()
	_ = api.NewProxy("column-update",
		api.WithProject(projID, dimID),           // 项目、应用维度
		api.WithAppKey(appID, appKey),            // appid、appkey
		api.WithOperatorName("ip_statue_update"), // 操作人
	)
}
