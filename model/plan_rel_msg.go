package model

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/ip_status_update/parse"
)

const (
	// planServiceName 排播计划服务名
	planServiceName = "trpc.http.getPlanData"
	// liveServiceName 直播计划服务名
	liveServiceName = "trpc.http.getLiveData"
)

const (
	// NextTimeCal 下一次时间计算标识
	NextTimeCal = 1
	// LastTimeCal 上一次时间计算标识
	LastTimeCal = 2
)
const (
	// LiveType 直播类型
	LiveType = 1
	// PlanType 非直播类型
	PlanType = 2
)

const (
	// PUSHType 推送类型
	PUSHType = 1
	// ETSType ETS定时计算类型
	ETSType = 2
)

// PayType 付费类型
type PayType int

const (
	// VIPCoupon VIP用券
	VIPCoupon PayType = 4
	// MonthSingle 包月单点
	MonthSingle PayType = 5
	// MonthOnly 包月only
	MonthOnly PayType = 6
	// SinglePay 单片付费
	SinglePay PayType = 7
	// Free 免费
	Free PayType = 8
	// SinglePayPlus 单片付费plus
	SinglePayPlus PayType = 9
	// VipLevel 会员等级
	VipLevel PayType = 10
	// AheadPlay 超前点播
	AheadPlay PayType = 12
	// MonthlySubscriptionOnly 时光包月
	MonthlySubscriptionOnly = 13
	// MonthlySinglePayPlus 时光单片付费plus
	MonthlySinglePayPlus = 14
	// MonthSingleSVIP 包月单点plus（SVIP）
	MonthSingleSVIP PayType = 15
	// MonthOnlySVIP 包月only plus
	MonthOnlySVIP PayType = 16
)

var (
	PayTypeMap = map[PayType]PayType{
		VIPCoupon:               VIPCoupon,
		MonthSingle:             MonthSingle,
		MonthOnly:               MonthOnly,
		SinglePay:               SinglePay,
		SinglePayPlus:           SinglePayPlus,
		VipLevel:                VipLevel,
		AheadPlay:               AheadPlay,
		MonthlySubscriptionOnly: MonthlySubscriptionOnly,
		MonthlySinglePayPlus:    MonthlySinglePayPlus,
		MonthSingleSVIP:         MonthSingleSVIP,
		MonthOnlySVIP:           MonthOnlySVIP,
	}
)

// TaskType 任务类型
type TaskType int

const (
	// Update 剧集正片更新任务
	Update TaskType = 3
	// PayChange 付费变更
	PayChange = 2
)

// PlanData 排播计划数据
type PlanData struct {
	ID             string `json:"ID,omitempty"`             // 排播cid
	Type           int    `json:"type"`                     // 直播类型还是排播类型
	StartDate      string `json:"startDate,omitempty"`      // 开始时间
	PayStartDate   string `json:"payStartDate,omitempty"`   // 付费开始时间
	FreeStartDate  string `json:"freeStartDate,omitempty"`  // 免费开始时间
	EndDate        string `json:"endDate"`                  // 结束时间
	PayEndDate     string `json:"payEndDate"`               // 付费开始时间
	FreeEndDate    string `json:"freeEndDate"`              // 免费开始时间
	PushType       int    `json:"pushType"`                 // 用于判断是否是ETS推送过来的消息
	NextUpdate     string `json:"nextUpdate"`               // 下一次更新时间，用于排播数据自动计算
	LastUpdate     string `json:"lastUpdate"`               // 上一次付费更新时间，用于之前排播数据的自动计算
	FreeLastUpdate string `json:"freeLastUpdate"`           // 上一次免费更新时间，用于之前排播数据的自动计算
	FreeNextUpdate string `json:"freeNextUpdate,omitempty"` // 下一次免费更新时间，用于排播数据自动计算
	PayVipTime     string `json:"payVipTime"`               // 单点转包月时间（目前是只有电影品类用到）
}

// ValueData 价值认定相关数据
type ValueData struct {
	MediaID             string `json:"mediaID"`             // MediaID 数据id
	MediaType           string `json:"mediaType"`           // MediaType 媒资大分类
	FreeEndTime         string `json:"freeEndDate"`         // FreeEndTime 免费完结时间
	PayEndTime          string `json:"payEndTime"`          // PayEndTime 付费完结时间
	EndTime             string `json:"endTime"`             // EndTime (同步剧)完结时间
	TVEndTime           string `json:"tvEndTime"`           // TVEndTime 电视剧完结时间（全网）
	PlayEndTime         string `json:"playEndTime"`         // PlayEndTime 运营期结束时间
	PremiereTime        string `json:"premiereTime"`        // PremiereTime 首播时间
	LastPubTime         string `json:"lastPubTime"`         // LastPubTime 最后一期内容上线日
	MediaValueEndTime   string `json:"mediaValueEndTime"`   // MediaValueEndTime 媒资价值认定结束时间
	ValueEndTime        string `json:"valueEndTime"`        // ValueEndTime 计算后的价值认定结束时间
	MediaValueTimeState string `json:"mediaValueTimeState"` // MediaValueTimeState 媒资价值认定状态
	ValueTimeState      string `json:"valueTimeState"`      // ValueTimeState 媒资价值认定状态
	LastUpdateDay       string `json:"lastUpdateDay"`       // LastUpdateDay 排播表最后一集上线时间
	ProductTime         string `json:"productTime"`         // ProductTime 电视剧出品时间
	PushType            int    `json:"pushType"`            // PushType 用于判断是否是ETS推送过来的消息
}

// RetCalData 返回日历信息
type RetCalData struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Calendars []CalTuple `json:"calendars"`
		PlanType  struct {
			Num1 []struct {
				TaskType    int `json:"TaskType"`
				PlanType    int `json:"PlanType"`
				NewPlanType int `json:"NewPlanType"`
			} `json:"1"`
		} `json:"planType"`
	} `json:"data"`
}

// CalTuple 日历信息
type CalTuple struct {
	ID           int    `json:"id"`
	TaskType     int    `json:"taskType"`
	PlanType     int    `json:"planType"`
	NewPlanType  int    `json:"newPlanType"`
	Date         string `json:"date"`
	StartEpisode int    `json:"startEpisode"`
	EndEpisode   int    `json:"endEpisode"`
	Episode      int    `json:"episode"`
	Vid          string `json:"vid"`
	PlanID       int    `json:"planID"`
	PeriodID     int    `json:"periodID"`
	Remark       string `json:"remark"`
	Week         int    `json:"week"`
}

// RetLiveData 返回直播信息
type RetLiveData struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		Pid       string `json:"pid"`
		RelatedID string `json:"relatedID"`
		IDType    string `json:"idType"`
		StartTime string `json:"startTime"`
		EndTime   string `json:"endTime"`
		IsDeleted int    `json:"isDeleted"`
	} `json:"data"`
}

// RetPlanData 返回排播信息
type RetPlanData struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		PlanList []struct {
			ID             int         `json:"id"`
			Name           string      `json:"name"`
			StartDate      string      `json:"startDate"`
			EndDate        string      `json:"endDate"`
			TotalEpisodes  int         `json:"totalEpisodes"`
			Channel        int         `json:"channel"`
			AlbumID        int64       `json:"albumID"`
			Cid            string      `json:"cid"`
			Lid            string      `json:"lid"`
			PayStartDate   string      `json:"payStartDate"`
			PayEndDate     string      `json:"payEndDate"`
			FreeStartDate  string      `json:"freeStartDate"`
			FreeEndDate    string      `json:"freeEndDate"`
			AlarmBlockMode interface{} `json:"alarmBlockMode"`
			Remark         string      `json:"remark"`
			CreatedBy      string      `json:"createdBy"`
			CreatedAt      string      `json:"createdAt"`
			State          string      `json:"state"`
			PlanState      int         `json:"planState"`
		} `json:"planList"`
		TotalSize int `json:"totalSize"`
	} `json:"data"`
}

// CheckTime2 检测时间是否合法 yyyy-mm-dd
func CheckTime2(t string) bool {
	if len(t) != 10 {
		return false
	}
	if t == "0000-00-00" {
		return false
	}
	_, err := time.ParseInLocation("2006-01-02", t, time.Local)
	if err == nil {
		return true
	}
	return false
}

// CheckTime 检测时间是否合法 yyyy-mm-dd hh:mm:ss
func CheckTime(t string) bool {
	if len(t) != 19 {
		return false
	}
	if t == "0000-00-00 00:00:00" {
		return false
	}
	_, err := time.ParseInLocation("2006-01-02 15:04:05", t, time.Local)
	if err == nil {
		return true
	}
	return false
}

// GetLiveTypeData 获取直播类型排播数据
func GetLiveTypeData(ctx context.Context, data PlanData) (*PlanData, error) {
	p, err := GetLiveRelInfo(ctx, data.ID)
	if err != nil {
		return nil, err
	}

	if len(p.Data) > 0 {
		data.FreeStartDate, data.FreeEndDate = getLiveCalTime(p, data.ID)
	}
	return &data, nil
}

// GetPlanTypeData 获取排播类型排播数据
func GetPlanTypeData(ctx context.Context, data PlanData) (*PlanData, error) {
	p, err := GetPlanRelInfo(ctx, data.ID)
	if err != nil {
		return nil, err
	}
	var c *RetCalData
	if len(p.Data.PlanList) > 0 {
		planID := p.Data.PlanList[0].ID
		planState := p.Data.PlanList[0].PlanState
		c, err = GetPlanCalInfo(ctx, planID, planState)
		if err != nil {
			return nil, err
		}
	}
	return unionPlanAndCalTime(data.ID, p, c)
}

// GetPlanData 获取排播数据
func GetPlanData(ctx context.Context, data PlanData) (*PlanData, error) {
	switch data.Type {
	case LiveType:
		return GetLiveTypeData(ctx, data)
	case PlanType:
		return GetPlanTypeData(ctx, data)
	default:
		return nil, nil
	}
}

// GetLessTime 获取两个时间较小的时间
func GetLessTime(time1, time2 string) string {
	if time2 == "" {
		return time1
	}
	if time1 < time2 {
		return time1
	}
	return time2
}

func getLiveCalTime(p *RetLiveData, cid string) (string, string) {
	var startTime string
	var endTime string
	for idx, d := range p.Data {
		if idx == 0 {
			startTime = d.StartTime
			endTime = d.EndTime
			continue
		}
		if d.RelatedID != cid {
			continue
		}
		startTime = GetLessTime(d.StartTime, startTime)
		endTime = GetLessTime(d.EndTime, endTime)
	}
	return startTime, endTime
}

func unionPlanAndCalTime(cid string, p *RetPlanData, c *RetCalData) (*PlanData, error) {
	var retData PlanData
	retData.ID = cid
	retData.Type = PlanType
	if len(p.Data.PlanList) > 0 {
		if p.Data.PlanList[0].PlanState != parse.Configured {
			return &retData, nil
		}
		category := p.Data.PlanList[0].Channel
		retData.StartDate = p.Data.PlanList[0].StartDate
		retData.EndDate = p.Data.PlanList[0].EndDate
		// 从日历当中获取付费开始结束时间
		retData.PayStartDate, retData.PayEndDate, retData.NextUpdate, retData.LastUpdate, retData.PayVipTime = calPayTime(
			convertRelDate(p.Data.PlanList[0].PayStartDate), convertRelDate(p.Data.PlanList[0].PayEndDate), category, c)
		// 从日历当中获取免费开始结束时间
		retData.FreeStartDate, retData.FreeEndDate, retData.FreeLastUpdate, retData.FreeNextUpdate =
			calFreeTime(convertRelDate(p.Data.PlanList[0].FreeStartDate),
				convertRelDate(p.Data.PlanList[0].FreeEndDate), c)
	}
	// 当付费更新时间为空时，使用免费更新时间
	if retData.LastUpdate == "" {
		retData.LastUpdate = retData.FreeLastUpdate
	}
	if retData.NextUpdate == "" {
		retData.NextUpdate = retData.FreeNextUpdate
	}
	return &retData, nil
}

func getCalPlanType(c CalTuple) PayType {
	if TaskType(c.TaskType) == Update {
		return PayType(c.PlanType)
	} else if TaskType(c.TaskType) == PayChange {
		return PayType(c.NewPlanType)
	}
	return 0
}

// GetEqualTime 获取相等的时间
func GetEqualTime(t, payTime string, oldTime string) string {
	// 先判斷
	if t[:10] == payTime {
		if CheckTime(oldTime) && TimeBefore(oldTime, t) {
			// 返回当天第一个时间
			return oldTime
		}
		return t
	}
	return oldTime
}

// TimeBefore 判断两个时间的前后,t1是否在t2之前,true为t1在t2之前，false为t1在t2之后
func TimeBefore(t1, t2 string) bool {
	at1, _ := time.ParseInLocation("2006-01-02 15:04:05", t1, time.Local)
	at2, _ := time.ParseInLocation("2006-01-02 15:04:05", t2, time.Local)
	if !at1.After(at2) {
		return true
	}
	return false
}

func calPayTime(payStart, payEnd string, category int, c *RetCalData) (string, string, string, string, string) {
	var payStartTime, payEndTime, payVipTime string
	var nextTime, lastTime string
	log.Infof("payStart:%s, payEnd:%s, category:%d", payStart, payEnd, category)

	nowTime := time.Now().Format("2006-01-02 15:04:05")
	// 付费计算，取到
	for _, t := range c.Data.Calendars {
		planType := getCalPlanType(t)
		if !CheckTime(t.Date) || !checkPayType(planType) {
			continue
		}
		// 获取单点转包月时间 (电影品类)
		if category == parse.ValueTypeMovie {
			payVipTime = calPayVipTime(t, payVipTime)
		}

		nextTime = getCompareTime(nowTime, t.Date, nextTime, NextTimeCal)
		lastTime = getCompareTime(nowTime, t.Date, lastTime, LastTimeCal)
		payStartTime = GetEqualTime(t.Date, payStart, payStartTime)
		payEndTime = GetEqualTime(t.Date, payEnd, payEndTime)
	}
	log.Infof("payStartTime:%s, payEndTime:%s, nextTime:%s,payVipTime: %s", payStartTime, payEndTime, nextTime,
		payVipTime)
	return payStartTime, payEndTime, nextTime, lastTime, payVipTime
}

func nextTimeComCal(nowTime, date, t string) bool {
	if date >= nowTime {
		if t != "" && date < t {
			return true
		} else if t == "" {
			return true
		}
	}
	return false
}

func lastTimeComCal(nowTime, date, t string) bool {
	if date <= nowTime {
		if t != "" && date > t {
			return true
		} else if t == "" {
			return true
		}
	}
	return false
}

func getCompareTime(nowTime, date, t string, compareFlag int) string {
	switch compareFlag {
	case NextTimeCal:
		if nextTimeComCal(nowTime, date, t) {
			return date
		}
		return t
	case LastTimeCal:
		if lastTimeComCal(nowTime, date, t) {
			return date
		}
		return t
	default:
		return ""
	}
}

func calFreeTime(freeStart, freeEnd string, c *RetCalData) (string, string, string, string) {
	var freeStartTime, freeEndTime, freeLastTime, freeNextTime string
	nowTime := time.Now().Format("2006-01-02 15:04:05")
	for _, t := range c.Data.Calendars {
		planType := getCalPlanType(t)
		if !CheckTime(t.Date) || !checkFreeType(planType) {
			continue
		}
		// 提取出免费时间的时分秒
		freeStartTime = GetEqualTime(t.Date, freeStart, freeStartTime)
		freeEndTime = GetEqualTime(t.Date, freeEnd, freeEndTime)
		freeLastTime = getCompareTime(nowTime, t.Date, freeLastTime, LastTimeCal)
		freeNextTime = getCompareTime(nowTime, t.Date, freeNextTime, NextTimeCal)
	}
	return freeStartTime, freeEndTime, freeLastTime, freeNextTime
}

func checkPayType(payType PayType) bool {
	if _, ok := PayTypeMap[payType]; ok {
		return true
	}
	return false
}

func checkFreeType(payType PayType) bool {
	if payType == Free {
		return true
	}
	return false
}

// GetPlanCalInfo 获取排播日历数据
func GetPlanCalInfo(ctx context.Context, id int, planState int) (*RetCalData, error) {
	if planState != parse.Configured {
		return &RetCalData{}, nil
	}
	proxy := thttp.NewClientProxy(planServiceName)
	reqUrl := fmt.Sprintf("/api/playplan/getCalendars?planID=%d", id)
	var retInfo RetCalData
	log.DebugContextf(ctx, "get plan rel info, req-url:%s", reqUrl)
	err := proxy.Get(ctx, reqUrl, &retInfo, client.WithSerializationType(codec.SerializationTypeJSON))
	if err != nil {
		log.ErrorContextf(ctx, "proxy get err:%+v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "rsp plan data:%+v", retInfo)
	if retInfo.Code != 0 {
		return nil, fmt.Errorf("ret plan Data code not 0:%d, msg:%s", retInfo.Code, retInfo.Msg)
	}
	return &retInfo, nil
}

// GetPlanRelInfo 获取回源cid排播数据
func GetPlanRelInfo(ctx context.Context, cid string) (*RetPlanData, error) {
	proxy := thttp.NewClientProxy(planServiceName)
	reqUrl := fmt.Sprintf("/api/playplan/getPlanList?cid=%s", cid)
	var retInfo RetPlanData
	log.DebugContextf(ctx, "get plan rel info, req-url:%s", reqUrl)
	err := proxy.Get(ctx, reqUrl, &retInfo, client.WithSerializationType(codec.SerializationTypeJSON))
	if err != nil {
		log.ErrorContextf(ctx, "proxy get err:%+v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "rsp plan data:%+v", retInfo)
	if retInfo.Code != 0 {
		return nil, fmt.Errorf("ret plan Data code not 0:%d, msg:%s", retInfo.Code, retInfo.Msg)
	}
	return &retInfo, nil
}

// GetLiveRelInfo 获取回源cid直播数据
func GetLiveRelInfo(ctx context.Context, cid string) (*RetLiveData, error) {
	proxy := thttp.NewClientProxy(liveServiceName)
	reqUrl := fmt.Sprintf("/api/appPlayPlan/getLivePlans?relatedID=%s", cid)
	var retInfo RetLiveData
	log.DebugContextf(ctx, "get plan rel info, req-url:%s", reqUrl)
	err := proxy.Get(ctx, reqUrl, &retInfo, client.WithSerializationType(codec.SerializationTypeJSON))
	if err != nil {
		log.ErrorContextf(ctx, "proxy get err:%+v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "rsp plan data:%+v", retInfo)
	if retInfo.Code != 0 {
		return nil, fmt.Errorf("ret plan Data code not 0:%d, msg:%s", retInfo.Code, retInfo.Msg)
	}
	return &retInfo, nil
}

func convertRelDate(date string) string {
	if len(date) != 8 {
		return ""
	}
	year, _ := strconv.Atoi(date[:4])
	month, _ := strconv.Atoi(date[4:6])
	day, _ := strconv.Atoi(date[6:])
	tDate := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.Local)
	newTime := tDate.Format("2006-01-02")
	return newTime
}

func calPayVipTime(c CalTuple, oldTime string) string {
	// 运营要求：单片付费 转包月only或者转包月单点 都算单点转包月
	if c.PlanType == int(SinglePay) && (c.NewPlanType == int(MonthOnly) || c.NewPlanType == int(MonthSingle)) {
		return c.Date
	}
	return oldTime
}
