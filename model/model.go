// Package model 定义本项目中使用的常量
package model

const (
	// LongVideoListType 代表长视频列表类型
	LongVideoListType = 1
	// ClipVideoListType 代表剪辑视频列表类型
	ClipVideoListType = 2
	// UnpublishVideoListType 代表未发布视频列表类型
	UnpublishVideoListType = 10
	// AspectVideoListType 代表看点列表类型
	AspectVideoListType = 100
	// FollowingPlayList 代表跟播列表类型
	FollowingPlayList = 101
	// Operator 代表操作者
	Operator = "cover_videos_auto_operation"
	// AppID 代表应用ID
	AppID = "cover_videos_auto_operation"
	// AppKey 代表应用密钥
	AppKey = "83d429110c45f30f98796ede01d6e5da"
	// MidVideoContent 代表中等视频内容
	MidVideoContent = 8368972
	// MediaDelimiter 代表媒资分隔符
	MediaDelimiter = "+"
	// MsgHubDelimiter 代表消息中心分隔符
	MsgHubDelimiter = ","
	// TaskWaiting 代表任务状态：等待中
	TaskWaiting = 0
	// TaskSuc 代表任务状态：成功
	TaskSuc = 1
	// TaskFail 代表任务状态：失败
	TaskFail = 2
	// MaxFailNum 异步重试允许的最大失败次数
	MaxFailNum = 20
	// VideoCheckUpState 视频上架状态
	VideoCheckUpState = 4
	// VideoOffState 视频下架状态
	VideoOffState = 8
	// VideoLinkState 链接替换状态
	VideoLinkState = 101
	// SportsType 体育频道
	SportsType = 4
)

// CoverInfo 媒资专辑信息
type CoverInfo struct {
	// LongVideoNum 长视频数
	LongVideoNum int
	// HzPic 专辑横图
	HzPic string
	// VtPic 专辑竖图
	VtPic string
	// CheckupGrade 上下架状态
	CheckupGrade int
}
