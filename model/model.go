// Package model 常量定义
package model

import (
	"fmt"
	"strconv"
	"strings"
)

const (
	// AccountStateOK 账号状态：正常
	AccountStateOK = 1
	// CrawlerRunning 抓取状态：抓取中
	CrawlerRunning = 2
	// CrawlerSuc 抓取状态：抓取成功
	CrawlerSuc = 3
	// CrawlerRetEmpty 抓取状态：抓取返回结果为空
	CrawlerRetEmpty = 4
	// CrawlerFail 抓取状态：抓取失败（抓取侧明确返回）
	CrawlerFail = 5
	// ManualCorrection 人工纠正状态
	ManualCorrection = 2
	// Backtracked 已回溯状态
	Backtracked = 1
)

const (
	// OfficeAccountType 抓取类型：IP官号（发文信息）
	OfficeAccountType = 1
	// TopicType 抓取类型：话题名（发文信息）
	TopicType = 2
	// CPInfoType 抓取类型：CP信息
	CPInfoType = 3
	// TopicFieldType 抓取类型：话题属性
	TopicFieldType = 4
)

const (
	// PushCrawTaskReport 推送抓取任务上报
	PushCrawTaskReport = 1
	// ConsumerReport 消费抓取结果上报
	ConsumerReport = 2
	// NewEnvTag 标识新链路 tag
	NewEnvTag = 1 // NewEnvTag
	// NewEnvTransmitFlag 新链路在Transmit中的标识
	NewEnvTransmitFlag = "newframe"
)

const (
	MsgTypeAddCrawTask = 1
	MsgTypeCraw        = 2
)

const (
	CPOfficeAccountType = 1
	CPMCNType           = 2
)

const (
	// IPAccountOrder IP官号抓取的订单ID
	IPAccountOrder = 1001
	// IPTopicOrder IP话题抓取的订单ID
	IPTopicOrder = 1002
	// CPCrawOrder CP账号抓取的订单ID
	CPCrawOrder = 1003
	// LongTailIPTopicOrder 长尾IP抓取的订单ID
	LongTailIPTopicOrder = 1004
	// PlaylistOrder 排播列表抓取
	PlaylistOrder = 1005
	// MaoYanTxVideoSearchOrder 猫眼腾讯视频IP搜索
	MaoYanTxVideoSearchOrder = 1006
	// MaoYanTxVideoIPDetailOrder 猫眼腾讯视频IP详情抓取
	MaoYanTxVideoIPDetailOrder = 1007
	// MaoYanCompetitorSearchOrder 猫眼竞品IP搜索
	MaoYanCompetitorSearchOrder = 1008
	// MaoYanCompetitorIPDetailOrder 猫眼竞品IP详情抓取
	MaoYanCompetitorIPDetailOrder = 1009
	// YunHeBoxCompetitorSearchOrder 云合工具箱竞品IP搜索
	YunHeBoxCompetitorSearchOrder = 1010
	// YunHeBoxCompetitorIPDetailOrder 云合工具箱竞品IP详情抓取
	YunHeBoxCompetitorIPDetailOrder = 1011

	// DouYinCompetitorSearchOrder 抖音竞品IP搜索
	DouYinCompetitorSearchOrder = 1012
	// DouYinCompetitorIPDetailOrder 抖音竞品IP详情抓取
	DouYinCompetitorIPDetailOrder = 1013
)

// ExtractCrawID 从Transmit结果中提取抓取ID
func ExtractCrawID(transmit string) (int, int, int, error) {
	// CP抓取、IP官号、IP话题抓取；结果回调是同一个通道；我们通过前缀来区分不同的抓取类型
	transmits := strings.Split(transmit, "_")
	crawType := 0
	crawID := 0
	orderID := 0
	if len(transmits) < 3 {
		return 0, 0, 0, fmt.Errorf("err tansmit")
	}
	crawType, _ = strconv.Atoi(transmits[0])
	crawID, _ = strconv.Atoi(transmits[1])
	orderID, _ = strconv.Atoi(transmits[2])
	return crawType, crawID, orderID, nil
}

// IsNewFrameResult 是否为新框架下的结果
func IsNewFrameResult(transmit string) bool {
	transmits := strings.Split(transmit, "_")
	if len(transmits) == 0 {
		return false
	}
	return strings.Contains(transmit, NewEnvTransmitFlag)
}

var mapTypeNameID = map[string]int{
	"电影":  1,
	"电视剧": 2,
	"动漫":  3,
	"纪录片": 9,
	"综艺":  10,
	"少儿":  106,
	"音乐":  22,
	"新闻":  23,
}

// TransTypeName 翻译大分类中文名
func TransTypeName(typeName string) int {
	return mapTypeNameID[typeName]
}

// TransTypeID 翻译大分类ID
func TransTypeID(typeID int) string {
	typeName := ""
	for name, id := range mapTypeNameID {
		if id == typeID {
			typeName = name
		}
	}
	return typeName
}
