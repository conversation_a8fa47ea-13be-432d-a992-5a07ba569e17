// Package model 放置数据适配层服务公共模型结构
package model

import (
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	tool "git.code.oa.com/video_media/storage_service/common"
)

// BatchGetResponse 批量读响应体
type BatchGetResponse struct {
	DocInfos []*protocol.DocInfo
	FailList map[string]string
}

// ModifyInfo 数据变更消息
type ModifyInfo struct {
	// BaseInfo 基础信息
	BaseInfo map[string]*protocol.FieldInfo
	// Infos 变更消息
	Infos []*protocol.ModifyFieldInfo
}

// GetBaseInfo 返回BaseInfo
func (m *ModifyInfo) GetBaseInfo() map[string]*protocol.FieldInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

// GetInfos 返回Infos
func (m *ModifyInfo) GetInfos() []*protocol.ModifyFieldInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

// HbaseFieldVal 字段值类型
type HbaseFieldVal struct {
	// TxtVal 文本类型
	TxtVal string
	// MapKVVal mapKV类型
	MapKVVal map[string]string
	// MapKListVal mapKList类型
	MapKListVal map[string][]string
}

// createStrTypeModifyInfo 根据字符串类型字段生成变更消息
func createStrTypeModifyInfo(field *protocol.FieldInfo, oldStr, newStr string) *protocol.ModifyFieldInfo {
	return &protocol.ModifyFieldInfo{
		FieldName:   field.FieldName,
		FieldId:     field.FieldId,
		FieldType:   field.FieldType,
		OldStrValue: oldStr,
		NewStrValue: newStr,
	}
}

// createVecTypeModifyInfo 根据选项型字段生成变更消息
func createVecTypeModifyInfo(field *protocol.FieldInfo, oldStr, newStr string) *protocol.ModifyFieldInfo {
	return &protocol.ModifyFieldInfo{
		FieldName: field.FieldName,
		FieldId:   field.FieldId,
		FieldType: field.FieldType,
		OldVecInt: tool.ConvertStrToUInt32Vec(oldStr, "+"),
		NewVecInt: tool.ConvertStrToUInt32Vec(newStr, "+"),
	}
}

// createSetTypeModifyInfo 根据set类型字段生成变更消息
func createSetTypeModifyInfo(field *protocol.FieldInfo, oldStr, newStr string) *protocol.ModifyFieldInfo {
	return &protocol.ModifyFieldInfo{
		FieldName: field.FieldName,
		FieldId:   field.FieldId,
		FieldType: field.FieldType,
		OldVecStr: tool.ConvertStrToStrVec(oldStr, "+"),
		NewVecStr: tool.ConvertStrToStrVec(newStr, "+"),
	}
}

// createMapTypeModifyInfo 根据map类型字段生成变更消息
func createMapTypeModifyInfo(field *protocol.FieldInfo, oldVal, newVal map[string]string) *protocol.ModifyFieldInfo {
	return &protocol.ModifyFieldInfo{
		FieldName: field.FieldName,
		FieldId:   field.FieldId,
		FieldType: field.FieldType,
		OldMapVal: tool.NewMapVal(field.FieldType, oldVal),
		NewMapVal: tool.NewMapVal(field.FieldType, newVal),
	}
}

// InitModifyFieldInfo ...
func InitModifyFieldInfo(field *protocol.FieldInfo) *protocol.ModifyFieldInfo {
	return &protocol.ModifyFieldInfo{
		FieldId:   field.FieldId,
		FieldType: field.FieldType,
		FieldName: field.FieldName,
	}
}

// NewModifyFieldInfo 根据字段信息创建变更消息
func NewModifyFieldInfo(field *protocol.FieldInfo, cur, new *tool.FieldVal) *protocol.ModifyFieldInfo {
	var modify *protocol.ModifyFieldInfo
	switch field.FieldType {
	case protocol.EnumFieldType_FieldTypeStr:
		modify = createStrTypeModifyInfo(field, cur.TxtVal, new.TxtVal)
	case protocol.EnumFieldType_FieldTypeIntVec:
		modify = createVecTypeModifyInfo(field, cur.TxtVal, new.TxtVal)
	case protocol.EnumFieldType_FieldTypeSet:
		modify = createSetTypeModifyInfo(field, cur.TxtVal, new.TxtVal)
	case protocol.EnumFieldType_FieldTypeMapKV, protocol.EnumFieldType_FieldTypeMapKList:
		modify = createMapTypeModifyInfo(field, cur.MapVal, new.MapVal)
	default:
	}
	return modify
}

// UpdateBaseFields 使用新的更新值更新baseField
func (m *ModifyInfo) UpdateBaseFields() *ModifyInfo {
	for _, info := range m.Infos {
		if val, ok := m.BaseInfo[info.FieldName]; ok {
			val.StrValue = info.NewStrValue
			val.VecInt = info.NewVecInt
			val.VecStr = info.NewVecStr
			val.MapVal = info.NewMapVal
		}
	}
	return m
}

// BuildModifyInfo 生成通知消息体
func BuildModifyInfo(base []*protocol.FieldInfo, infos map[uint32]*protocol.ModifyFieldInfo,
	val tool.CurVal,
) *ModifyInfo {
	modifyInfo := &ModifyInfo{
		BaseInfo: make(map[string]*protocol.FieldInfo),
		Infos:    make([]*protocol.ModifyFieldInfo, 0, len(infos)),
	}
	// 填充变更消息
	for _, info := range infos {
		modifyInfo.Infos = append(modifyInfo.Infos, info)
	}

	// 填充基础信息
	for _, f := range base {
		if v, ok := val.CurVal(f); ok {
			tool.FillFieldInfo(f, v)
		}
		modifyInfo.BaseInfo[f.FieldName] = f
	}
	return modifyInfo.UpdateBaseFields()
}

// NewHbaseModifyFieldInfo 根据字段信息创建Hbase变更消息
func NewHbaseModifyFieldInfo(field *protocol.FieldInfo, cur, new *HbaseFieldVal) *protocol.ModifyFieldInfo {
	var modify *protocol.ModifyFieldInfo
	switch field.FieldType {
	case protocol.EnumFieldType_FieldTypeStr:
		modify = createStrTypeModifyInfo(field, cur.TxtVal, new.TxtVal)
	case protocol.EnumFieldType_FieldTypeIntVec:
		modify = createVecTypeModifyInfo(field, cur.TxtVal, new.TxtVal)
	case protocol.EnumFieldType_FieldTypeSet:
		modify = createSetTypeModifyInfo(field, cur.TxtVal, new.TxtVal)
	case protocol.EnumFieldType_FieldTypeMapKV:
		modify = createHbaseMapKVModifyInfo(field, cur.MapKVVal, new.MapKVVal)
	case protocol.EnumFieldType_FieldTypeMapKList:
		modify = createHbaseMapKListModifyInfo(field, cur.MapKListVal, new.MapKListVal)
	default:
	}
	return modify
}

// createHbaseMapKVModifyInfo 根据字符串类型字段生成变更消息
func createHbaseMapKVModifyInfo(field *protocol.FieldInfo, oldKVMap,
	newKVMap map[string]string,
) *protocol.ModifyFieldInfo {
	return &protocol.ModifyFieldInfo{
		FieldName: field.FieldName,
		FieldId:   field.FieldId,
		FieldType: field.FieldType,
		OldMapVal: NewKVMapVal(oldKVMap),
		NewMapVal: NewKVMapVal(newKVMap),
	}
}

// createHbaseMapKListModifyInfo 根据字符串类型字段生成变更消息
func createHbaseMapKListModifyInfo(field *protocol.FieldInfo, oldKListMap,
	newKListMap map[string][]string,
) *protocol.ModifyFieldInfo {
	return &protocol.ModifyFieldInfo{
		FieldName: field.FieldName,
		FieldId:   field.FieldId,
		FieldType: field.FieldType,
		OldMapVal: NewKListMapVal(oldKListMap),
		NewMapVal: NewKListMapVal(newKListMap),
	}
}

// NewKVMapVal MapKV转结构体
func NewKVMapVal(val map[string]string) map[string]*protocol.MapValue {
	if len(val) == 0 {
		return nil
	}
	mapVal := make(map[string]*protocol.MapValue)
	for k, v := range val {
		mapValue := &protocol.MapValue{Type: protocol.EnumFieldType_FieldTypeStr}
		mapValue.StrValue = v
		mapVal[k] = mapValue
	}
	return mapVal
}

// NewKListMapVal MapKList转结构体
func NewKListMapVal(val map[string][]string) map[string]*protocol.MapValue {
	if len(val) == 0 {
		return nil
	}
	mapVal := make(map[string]*protocol.MapValue)
	for k, v := range val {
		mapValue := &protocol.MapValue{Type: protocol.EnumFieldType_FieldTypeSet}
		mapValue.VecStr = v
		mapVal[k] = mapValue
	}
	return mapVal
}
