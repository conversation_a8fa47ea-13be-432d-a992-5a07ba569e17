package model

import (
	"context"
	"testing"

	"git.code.oa.com/video_media/ip_status_update/parse"
)

func TestUpdateShortDramaIPStatus(t *testing.T) {
	type args struct {
		ctx            context.Context
		dataID         string
		ipOnlineStatus string
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "正常更新短剧IP状态",
			args: args{
				ctx:            context.Background(),
				dataID:         "test_short_drama_001",
				ipOnlineStatus: parse.ValueSchedule,
			},
			wantErr: false,
		},
		{
			name: "空状态跳过更新",
			args: args{
				ctx:            context.Background(),
				dataID:         "test_short_drama_002",
				ipOnlineStatus: "",
			},
			wantErr: false,
		},
		{
			name: "更新运营期状态",
			args: args{
				ctx:            context.Background(),
				dataID:         "test_short_drama_003",
				ipOnlineStatus: parse.ValueOperatingPeriod,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := UpdateShortDramaIPStatus(tt.args.ctx, tt.args.dataID, tt.args.ipOnlineStatus); (err != nil) != tt.wantErr {
				t.Errorf("UpdateShortDramaIPStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
