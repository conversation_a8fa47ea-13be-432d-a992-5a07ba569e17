package model

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pbu "git.code.oa.com/trpcprotocol/video_media/universal_mgr"
	"git.code.oa.com/video_media/ip_status_update/config"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"git.code.oa.com/video_media/media_go_commlib/dataaccess"
	"git.code.oa.com/video_media/media_go_commlib/mediapkg/universal"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

const (
	universalPolaris = "trpc.video_media.universal_mgr.Universal_mgr" // 媒资接入平台服务
	// DimIDShortDrama 短剧对应的dimID
	DimIDShortDrama = 4
)

// GetIdSetTextFromUniversal 通过传入视频ID查找接入平台的某个字段,获取的是文本或者单选项的值，也可以用于判断这个字段是否存在
func GetIdSetTextFromUniversal(mediaId string, field string, mediaInfos map[string]map[string][]*pbu.ValEntry) (
	string, bool) {
	valEntrys, ok := mediaInfos[mediaId]
	if !ok {
		log.Debugf("getuniversal donesn't have mediaID %v", mediaId)
		return "", false
	}
	valList, ok := valEntrys[field]
	if !ok {
		log.Debugf("getuniversal mediaID %v doesn't have field %v", mediaId, field)
		return "", false
	}
	// 用于获取单选项或者文本的值，或者用于判断字段值是否存在
	fieldVal := valList[0].GetTxt()
	return fieldVal, true
}

// GetAllMediaFieldInfos 获取这批ID下的所有字段值
func GetAllMediaFieldInfos(ctx context.Context, mediaIds []string, dataSet int) (map[string]map[string][]*pbu.
ValEntry, error) {
	var ua config.UniAccess
	var projID, dimID int32
	switch dataSet {
	case parse.CidType:
		ua = config.GetAccCfg().UniAccGet
		projID = universal.ProjIDVideo
		dimID = universal.DimIDCover
	case parse.LidType:
		ua = config.GetAccCfg().UniAccGet
		projID = universal.ProjIDVideo
		dimID = universal.DimIDColumn
	case parse.ShortDramaType:
		ua = config.GetAccCfg().UniAccMidCoveGet
		projID = universal.ProjIDVideo
		dimID = DimIDShortDrama
	default:
		return nil, nil
	}
	var dataAccess dataaccess.DataAccess
	dataAccess.AccessSetOptions(
		dataaccess.WithProjID(projID),       //参考项目ID与维度ID表格: https://iwiki.woa.com/pages/viewpage.action?pageId=648541669
		dataaccess.WithDimID(dimID),         //参考项目ID与维度ID表格: https://iwiki.woa.com/pages/viewpage.action?pageId=648541669
		dataaccess.WithAppID(ua.UniAppID),   // 统一接入平台申请
		dataaccess.WithAppKey(ua.UniAppKey), // 统一接入平台申请
	)
	log.DebugContextf(ctx, "get id info req %v", dataAccess)
	retDataInfo, err := dataAccess.GetMediaInfo(ctx, mediaIds, client.WithNamespace("Production"),
		client.WithDisableServiceRouter())
	if err != nil {
		return retDataInfo, err
	}
	if len(retDataInfo) == 0 {
		log.ErrorContextf(ctx, "ids %v retData is empty", mediaIds)
		return retDataInfo, errors.New("")
	}
	return retDataInfo, nil
}

// MakeMediaInfo 制作相关媒资消息总线结构信息
func MakeMediaInfo(rsp map[string]map[string][]*pbu.ValEntry, id string, dataSet int) (msghub.MediaInfo, error) {
	var retInfo msghub.MediaInfo
	if _, ok := rsp[id]; !ok {
		return retInfo, fmt.Errorf("media id not has")
	}
	retInfo.Id = id
	retInfo.DataSetId = dataSet
	retInfo.FieldInfos = make(map[string]msghub.FieldInfo)
	for field, vInfo := range rsp[id] {
		if len(vInfo) > 0 {
			// 处理字段值，支持多值字段
			var vals []string
			for _, value := range vInfo {
				if value != nil && value.Val != "" {
					vals = append(vals, value.Val)
				}
			}
			
			var fieldValue string
			if len(vals) > 0 {
				// 多值选项型，用+分割
				fieldValue = strings.Join(vals, "+")
			}

			fi := msghub.FieldInfo{
				Value: fieldValue,
			}
			retInfo.FieldInfos[field] = fi
		}
	}
	return retInfo, nil
}
