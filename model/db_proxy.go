package model

import "time"

// IpValueStatusData ip价值认定数据
type IpValueStatusData struct {
	ID           int        `gorm:"column:id;primaryKey;autoIncrement"` // 存储唯一key
	DataID       string     `gorm:"column:c_data_id"`
	PremiereTime *time.Time `gorm:"column:c_premiere_time"`
	ValueEndTime *time.Time `gorm:"column:c_value_endtime" `
	ValueStatus  string     `gorm:"column:c_value_status" `
	CTime        *time.Time `gorm:"column:c_create_time"` // 创建时间
	Mtime        *time.Time `gorm:"column:c_mtime"`       // 修改时间
}

// TableName 分发接入配置表名
func (d *IpValueStatusData) TableName() string {
	return "t_value_status_check"
}

// IpOnlineStatusData ip实时状态数据
type IpOnlineStatusData struct {
	ID                    int        `gorm:"column:id;primaryKey;autoIncrement"`
	DataID                string     `gorm:"column:c_data_id"`
	PremiereTime          *time.Time `gorm:"column:c_premiere_time"`
	OperationEndTime      *time.Time `gorm:"column:c_operation_end_time"`
	AfterheatTimeMoretime *time.Time `gorm:"column:c_afterheat_time_moretime"`
	AfterheatTime         *time.Time `gorm:"column:c_afterheat_time"`
	LongtailTimeMoretime  *time.Time `gorm:"column:c_longtail_time_moretime"`
	IpOnlineStatus        string     `gorm:"column:c_ip_online_status"`
	CTime                 *time.Time `gorm:"column:c_create_time"`
	Mtime                 *time.Time `gorm:"column:c_mtime"`
}

// TableName 分发接入配置表名
func (d *IpOnlineStatusData) TableName() string {
	return "t_value_status_check"
}
