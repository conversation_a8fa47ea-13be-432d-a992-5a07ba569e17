package model

import (
	"testing"

	pbu "git.code.oa.com/trpcprotocol/video_media/universal_mgr"
	"git.code.oa.com/video_media/ip_status_update/parse"
	"github.com/stretchr/testify/assert"
)

func TestGetFieldFromUniversal(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			name:    "test basic logic",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Skip("Skipping integration test - requires refactoring for unit testing")
		})
	}
}

// Test_MakeMediaInfo 测试MakeMediaInfo函数的字段类型处理逻辑
func Test_MakeMediaInfo(t *testing.T) {
	tests := []struct {
		name        string
		rsp         map[string]map[string][]*pbu.ValEntry
		id          string
		dataSet     int
		expectedErr bool
		expected    map[string]string // 期望的字段值
	}{
		{
			name: "单值字段处理",
			rsp: map[string]map[string][]*pbu.ValEntry{
				"test_id": {
					"single_field": []*pbu.ValEntry{
						{Val: "single_value"},
					},
				},
			},
			id:          "test_id",
			dataSet:     parse.CidType,
			expectedErr: false,
			expected: map[string]string{
				"single_field": "single_value",
			},
		},
		{
			name: "多值字段处理（用+分割）",
			rsp: map[string]map[string][]*pbu.ValEntry{
				"test_id": {
					"multi_field": []*pbu.ValEntry{
						{Val: "value1"},
						{Val: "value2"},
						{Val: "value3"},
					},
				},
			},
			id:          "test_id",
			dataSet:     parse.CidType,
			expectedErr: false,
			expected: map[string]string{
				"multi_field": "value1+value2+value3",
			},
		},
		{
			name: "混合字段类型处理",
			rsp: map[string]map[string][]*pbu.ValEntry{
				"test_id": {
					"single_field": []*pbu.ValEntry{
						{Val: "single_value"},
					},
					"multi_field": []*pbu.ValEntry{
						{Val: "value1"},
						{Val: "value2"},
					},
					"empty_field": []*pbu.ValEntry{},
				},
			},
			id:          "test_id",
			dataSet:     parse.CidType,
			expectedErr: false,
			expected: map[string]string{
				"single_field": "single_value",
				"multi_field":  "value1+value2",
				// empty_field 应该不出现在结果中
			},
		},
		{
			name: "包含空值的字段处理",
			rsp: map[string]map[string][]*pbu.ValEntry{
				"test_id": {
					"field_with_empty": []*pbu.ValEntry{
						{Val: "value1"},
						{Val: ""},
						{Val: "value2"},
						nil,
					},
				},
			},
			id:          "test_id",
			dataSet:     parse.CidType,
			expectedErr: false,
			expected: map[string]string{
				"field_with_empty": "value1+value2", // 空值和nil应该被过滤掉
			},
		},
		{
			name: "媒资ID不存在",
			rsp: map[string]map[string][]*pbu.ValEntry{
				"other_id": {
					"field": []*pbu.ValEntry{
						{Val: "value"},
					},
				},
			},
			id:          "test_id",
			dataSet:     parse.CidType,
			expectedErr: true,
			expected:    nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := MakeMediaInfo(tt.rsp, tt.id, tt.dataSet)

			if tt.expectedErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.id, result.Id)
			assert.Equal(t, tt.dataSet, result.DataSetId)

			// 验证字段值
			for expectedField, expectedValue := range tt.expected {
				fieldInfo, exists := result.FieldInfos[expectedField]
				assert.True(t, exists, "字段 %s 应该存在", expectedField)
				assert.Equal(t, expectedValue, fieldInfo.Value, "字段 %s 的值不匹配", expectedField)
			}

			// 验证不应该存在的字段
			for fieldName := range result.FieldInfos {
				_, shouldExist := tt.expected[fieldName]
				assert.True(t, shouldExist, "字段 %s 不应该存在", fieldName)
			}
		})
	}
}

// Test_MakeMediaInfo_BackwardCompatibility 测试向后兼容性
func Test_MakeMediaInfo_BackwardCompatibility(t *testing.T) {
	// 模拟原有的单值字段使用场景
	rsp := map[string]map[string][]*pbu.ValEntry{
		"test_id": {
			"category": []*pbu.ValEntry{
				{Val: "123130251"}, // 短剧预告片
			},
			"checkup_state": []*pbu.ValEntry{
				{Val: "4"}, // 已上架
			},
		},
	}

	result, err := MakeMediaInfo(rsp, "test_id", parse.CidType)
	assert.NoError(t, err)

	// 验证原有逻辑仍然正常工作
	categoryField, exists := result.FieldInfos["category"]
	assert.True(t, exists)
	assert.Equal(t, "123130251", categoryField.Value)

	checkupField, exists := result.FieldInfos["checkup_state"]
	assert.True(t, exists)
	assert.Equal(t, "4", checkupField.Value)
}
