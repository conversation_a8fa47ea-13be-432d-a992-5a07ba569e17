package model

import "testing"

func Test_getCalPlanType(t *testing.T) {
	type args struct {
		c CalTuple
	}
	tests := []struct {
		name string
		args args
		want PayType
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				c: CalTuple{
					TaskType: 2,
					PlanType: 5,
				},
			},
			want: 0,
		}, {
			name: "test2",
			args: args{
				c: CalTuple{
					TaskType: 3,
					PlanType: 5,
				},
			},
			want: 5,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getCalPlanType(tt.args.c); got != tt.want {
				t.Errorf("getCalPlanType() = %v, want %v", got, tt.want)
			}
		})
	}
}
