package model

// TopicList 话题列表返回信息
type TopicList struct {
	Info     *TopicInfo `json:"-"`          // Info 从 data 解析出来的话题信息
	Data     string     `json:"data"`       // Data 话题信息数据
	Transmit string     `json:"transmit"`   // Transmit 透传信息
	BizID    string     `json:"tms_biz_id"` // BizID 透传信息(若transmit为空，可从此字段中取)
}

// TopicInfo 话题信息
type TopicInfo struct {
	TopicID   string `json:"topic_id"`   // TopicID 话题 id
	TopicName string `json:"topic_name"` // TopicName 话题名称
	TopicURL  string `json:"topic_url"`  // TopicURL 话题 url
	Rank      int    `json:"rank"`       // Rank 排名
}
