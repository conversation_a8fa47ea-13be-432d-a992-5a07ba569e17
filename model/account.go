package model

import "encoding/json"

// AccountList 账号列表返回信息
type AccountList struct {
	Info     *AccountInfo `json:"-"`        // Info 账号信息，由 data unmarshal 出
	Data     string       `json:"data"`     // Data 账号信息数据
	Transmit string       `json:"transmit"` // Transmit 透传数据
}

// AccountInfo 账号信息
type AccountInfo struct {
	MID    string `json:"mid"`    // MID 账号 id
	URL    string `json:"url"`    // URL 账号 url
	Author string `json:"author"` // Author 账号名称
	Avatar string `json:"avatar"` // Avatar 头像
	Rank   int    `json:"rank"`   // Rank 排名
}

// String marshal 为字符串
func (a *AccountList) String() string {
	data, _ := json.Marshal(a)
	return string(data)
}
