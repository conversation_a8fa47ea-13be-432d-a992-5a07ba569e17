package common

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"git.code.oa.com/polaris/polaris-go/api"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	storeComm "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/config_cache/dao"

	"github.com/juju/ratelimit"
)

const (
	// SetCut set类型字段分隔符
	SetCut = "+"
	// MapCut map类型字段分隔符
	MapCut = "\u2795"
)

// polarisConsumer 北极星消费者
var polarisConsumer = polarisApi{
	Mutex: sync.Mutex{},
	api:   nil,
}

// ExistInArray list内任一元素存在返回true,否则false
func ExistInArray(array []uint32, list []uint32) bool {
	for _, val := range list {
		if UintInUintVec(array, val) {
			return true
		}
	}
	return false
}

// UintInUintVec 判断uint32是否在uint32数组中
func UintInUintVec(array []uint32, val uint32) bool {
	for _, value := range array {
		if value == val {
			return true
		}
	}
	return false
}

// StringInStringVec 判断string是否在string数组中
func StringInStringVec(strList []string, key string) bool {
	for _, str := range strList {
		if str == key {
			return true
		}
	}

	return false
}

// ConvertStringsToInterface 转换[]string为[]interface{}
func ConvertStringsToInterface(strList []string) []interface{} {
	newList := make([]interface{}, 0)
	for _, str := range strList {
		newList = append(newList, str)
	}
	return newList
}

// ConvertStrToInt32Vec 按照分隔符转化字符串为int32数组
func ConvertStrToInt32Vec(s string, sep string) []int32 {
	numArray := make([]int32, 0, 0)
	strList := strings.Split(s, sep)
	for _, str := range strList {
		if num, err := strconv.Atoi(str); err == nil {
			numArray = append(numArray, int32(num))
		}
	}
	return numArray
}

// ConvertStrToUInt32Vec 按照分隔符转化字符串为uint32数组
func ConvertStrToUInt32Vec(s string, sep string) []uint32 {
	var numArray []uint32
	strList := strings.Split(s, sep)
	for _, str := range strList {
		if num, err := strconv.Atoi(str); err == nil {
			numArray = append(numArray, uint32(num))
		}
	}
	return numArray
}

// ConvertStrToStrVec 按照分隔符转化字符串为string数组
func ConvertStrToStrVec(s string, sep string) []string {
	if s == "" {
		return nil
	}

	s = strings.Trim(s, sep)
	return strings.Split(s, sep)
}

// RevertWord 反转指定范围字符串
func RevertWord(source []rune, begin, end int) {
	for {
		if begin >= end {
			break
		}
		source[begin], source[end] = source[end], source[begin]
		begin++
		end--
	}
}

// ReverseString 反转字符串
func ReverseString(source []rune) {
	for from, to := 0, len(source)-1; from < to; from, to = from+1, to-1 {
		source[from], source[to] = source[to], source[from]
	}
}

func getKeyType(ty storeComm.EnumFieldType) storeComm.EnumFieldType {
	switch ty {
	case storeComm.EnumFieldType_FieldTypeMapKV:
		return storeComm.EnumFieldType_FieldTypeStr
	case storeComm.EnumFieldType_FieldTypeMapKList:
		return storeComm.EnumFieldType_FieldTypeSet
	default:
		return storeComm.EnumFieldType_InValidType
	}
}

// NewSingleMapVal 通过单个kv创建map类型字段值
func NewSingleMapVal(ty storeComm.EnumFieldType, key, val string) map[string]*storeComm.MapValue {
	if key == "" {
		return nil
	}
	mapVal := make(map[string]*storeComm.MapValue)
	AddSingleMapVal(mapVal, ty, key, val)
	return mapVal
}

// NewMapVal 创建新的map类型字段值
func NewMapVal(ty storeComm.EnumFieldType, val map[string]string) map[string]*storeComm.MapValue {
	if len(val) == 0 {
		return nil
	}
	mapVal := make(map[string]*storeComm.MapValue)
	AddMapVal(mapVal, ty, val)
	return mapVal
}

// AddSingleMapVal 通过单个kv添加map类型字段值
func AddSingleMapVal(src map[string]*storeComm.MapValue, ty storeComm.EnumFieldType, key, val string) {
	if key == "" {
		return
	}

	ty = getKeyType(ty)
	mapVal := &storeComm.MapValue{Type: ty}
	switch mapVal.Type {
	case storeComm.EnumFieldType_FieldTypeStr:
		mapVal.StrValue = val
	case storeComm.EnumFieldType_FieldTypeSet:
		mapVal.VecStr = ConvertStrToStrVec(val, MapCut)
	default:
	}
	src[key] = mapVal
}

// AddMapVal 添加map类型字段值
func AddMapVal(source map[string]*storeComm.MapValue, ty storeComm.EnumFieldType, val map[string]string) {
	if len(val) == 0 {
		return
	}

	ty = getKeyType(ty)
	for k, v := range val {
		mapVal := &storeComm.MapValue{Type: ty}
		switch mapVal.Type {
		case storeComm.EnumFieldType_FieldTypeStr:
			mapVal.StrValue = v
		case storeComm.EnumFieldType_FieldTypeSet:
			mapVal.VecStr = ConvertStrToStrVec(v, MapCut)
		default:
		}
		source[k] = mapVal
	}
	return
}

// NewFieldInfo 创建字段对象
func NewFieldInfo(ty storeComm.EnumFieldType, fieldID uint32, fieldName, innerName string,
	val *FieldVal,
) *storeComm.FieldInfo {
	fieldInfo := &storeComm.FieldInfo{
		FieldName:      fieldName,
		FieldType:      ty,
		FieldId:        fieldID,
		InnerFieldName: innerName,
	}
	FillFieldInfo(fieldInfo, val)
	return fieldInfo
}

// FillFieldInfoByGetVal 通过原始db数据填充fieldInfo
func FillFieldInfoByGetVal(f *storeComm.FieldInfo, val *adaptor_layer.GetFieldInfo) {
	f.Mtime = val.Mtime
	switch f.FieldType {
	case storeComm.EnumFieldType_FieldTypeStr:
		f.StrValue = val.FieldValue
	case storeComm.EnumFieldType_FieldTypeIntVec:
		f.VecInt = ConvertStrToUInt32Vec(val.FieldValue, "+")
	case storeComm.EnumFieldType_FieldTypeSet:
		f.VecStr = ConvertStrToStrVec(val.FieldValue, "+")
	case storeComm.EnumFieldType_FieldTypeMapKV, storeComm.EnumFieldType_FieldTypeMapKList:
		if f.MapVal == nil {
			f.MapVal = NewSingleMapVal(f.FieldType, val.FieldKey, val.FieldValue)
			return
		}
		AddSingleMapVal(f.MapVal, f.FieldType, val.FieldKey, val.FieldValue)
	default:
	}
}

// NewFieldInfoByGetVal 通过原始db数据创建fieldInfo
func NewFieldInfoByGetVal(f *dao.FieldInfo, val *adaptor_layer.GetFieldInfo) *storeComm.FieldInfo {
	fieldInfo := &storeComm.FieldInfo{
		FieldName:      f.FieldName,
		InnerFieldName: f.InnerFieldName,
		FieldId:        f.FieldID,
		FieldType:      storeComm.EnumFieldType(f.FieldType),
	}
	FillFieldInfoByGetVal(fieldInfo, val)
	return fieldInfo
}

// IsMapValueEmpty 判断map字段值是否为空
func IsMapValueEmpty(ty storeComm.EnumFieldType, v *storeComm.MapValue) bool {
	if v.Type == storeComm.EnumFieldType_FieldTypeStr || ty == storeComm.EnumFieldType_FieldTypeMapKV {
		return v.StrValue == ""
	}
	if v.Type == storeComm.EnumFieldType_FieldTypeSet || ty == storeComm.EnumFieldType_FieldTypeMapKList {
		return len(v.VecStr) == 0
	}
	return true
}

// IsFieldEmpty 判断字段值是否为空
func IsFieldEmpty(f *storeComm.FieldInfo) bool {
	switch f.FieldType {
	case storeComm.EnumFieldType_FieldTypeStr:
		return f.StrValue == ""
	case storeComm.EnumFieldType_FieldTypeIntVec:
		return len(f.VecInt) == 0
	case storeComm.EnumFieldType_FieldTypeSet:
		return len(f.VecStr) == 0
	case storeComm.EnumFieldType_FieldTypeMapKV, storeComm.EnumFieldType_FieldTypeMapKList:
		for k, v := range f.MapVal {
			if IsMapValueEmpty(f.FieldType, v) {
				// map类型字段如果为空值需要删除掉
				delete(f.MapVal, k)
			}
		}
		return len(f.MapVal) == 0
	default:
	}
	// 异常情况默认判断不为空
	return false
}

// FillFieldInfo 根据传入字段类型填写值
func FillFieldInfo(f *storeComm.FieldInfo, val *FieldVal) {
	switch f.FieldType {
	case storeComm.EnumFieldType_FieldTypeStr:
		f.StrValue = val.TxtVal
	case storeComm.EnumFieldType_FieldTypeIntVec:
		f.VecInt = ConvertStrToUInt32Vec(val.TxtVal, "+")
	case storeComm.EnumFieldType_FieldTypeSet:
		f.VecStr = ConvertStrToStrVec(val.TxtVal, "+")
	case storeComm.EnumFieldType_FieldTypeMapKV, storeComm.EnumFieldType_FieldTypeMapKList:
		if f.MapVal == nil {
			f.MapVal = NewMapVal(f.FieldType, val.MapVal)
			return
		}
		AddMapVal(f.MapVal, f.FieldType, val.MapVal)
	default:
	}
}

// NewDocInfo 通过原始db数据创建文档数据
func NewDocInfo(f *dao.FieldInfo, val *adaptor_layer.GetFieldInfo, err error) *storeComm.DocInfo {
	doc := &storeComm.DocInfo{
		Id:       val.Id,
		Fields:   make(map[string]*storeComm.FieldInfo),
		FailList: make(map[string]*storeComm.FailInfo),
	}
	if err != nil {
		doc.FailList[f.FieldName] = &storeComm.FailInfo{ErrMsg: err.Error()}
		return doc
	}
	doc.Fields[f.FieldName] = NewFieldInfoByGetVal(f, val)
	return doc
}

// FillDocInfo 通过原始db数据填充文档数据
func FillDocInfo(doc *storeComm.DocInfo, f *dao.FieldInfo, val *adaptor_layer.GetFieldInfo, err error) {
	if f == nil {
		return
	}
	fieldName := f.FieldName
	if err != nil {
		doc.FailList[fieldName] = &storeComm.FailInfo{ErrMsg: err.Error()}
		return
	}

	if val == nil {
		// 入参为空直接退出
		return
	}
	if storeComm.EnumFieldType(f.FieldType) < storeComm.EnumFieldType_FieldTypeMapKV {
		// 非map类型字段可以直接new值
		doc.Fields[fieldName] = NewFieldInfoByGetVal(f, val)
		return
	}
	// map类型字段需要判断是否已添加然后进行填充
	field, ok := doc.Fields[fieldName]
	if !ok {
		doc.Fields[fieldName] = NewFieldInfoByGetVal(f, val)
		return
	}
	FillFieldInfoByGetVal(field, val)
}

// ConvertUnixToTime 转换时间戳为格式时间串2006-01-02 15:04:05
func ConvertUnixToTime(timeStamp int64) string {
	return time.Unix(timeStamp, 0).Format("2006-01-02 15:04:05")
}

// EscapeString 对sql特殊字符转义
func EscapeString(source string) string {
	j := 0
	if len(source) == 0 {
		return ""
	}

	tempStr := source[:]
	desc := make([]byte, len(tempStr)*2)
	for i := 0; i < len(tempStr); i++ {
		flag := false
		var escape byte
		switch tempStr[i] {
		case '\r':
			flag = true
			escape = '\r'
			break
		case '\n':
			flag = true
			escape = '\n'
			break
		case '\\':
			flag = true
			escape = '\\'
			break
		case '\'':
			flag = true
			escape = '\''
			break
		case '"':
			flag = true
			escape = '"'
			break
		case '\032':
			flag = true
			escape = 'Z'
			break
		default:
		}
		if flag {
			desc[j] = '\\'
			desc[j+1] = escape
			j = j + 2
		} else {
			desc[j] = tempStr[i]
			j = j + 1
		}
	}
	return string(desc[0:j])
}

// RSHash Rs哈希算法
func RSHash(id string) uint32 {
	var (
		b    uint32 = 378551
		a    uint32 = 63689
		hash uint32 = 0
	)

	for idx := range id {
		hash = hash*a + uint32(id[idx])
		a *= b
	}
	return hash & 0x7FFFFFFF
}

// FilterInvalidUtf8 过滤非法utf8字符
func FilterInvalidUtf8(s string) string {
	if !utf8.ValidString(s) {
		v := make([]rune, 0, len(s))
		for i, r := range s {
			if r == utf8.RuneError {
				_, size := utf8.DecodeRuneInString(s[i:])
				if size == 1 {
					continue
				}
			}
			v = append(v, r)
		}
		s = string(v)
	}
	return s
}

// LocalFreCtl 进程内频控
type LocalFreCtl struct {
	*ratelimit.Bucket
	Qps int64
}

// NewLocalFreCtl 创建新的频控对象
func NewLocalFreCtl(qps int64) *LocalFreCtl {
	if qps <= 0 {
		return &LocalFreCtl{
			Qps: 0,
		}
	}
	return &LocalFreCtl{
		Bucket: ratelimit.NewBucket(time.Duration(1000000/qps)*time.Microsecond, qps),
		Qps:    qps,
	}
}

// CheckQpsWithUpdate 检查频控并根据qps做更新
func (l *LocalFreCtl) CheckQpsWithUpdate(qps int64) {
	if qps <= 0 {
		// qps为0直接跳过
		l.Bucket = nil
		l.Qps = 0
		return
	}

	// qps更新需要更新bucket
	if (l.Qps == 0 && qps > 0) || l.Capacity() != qps {
		l.Bucket = ratelimit.NewBucket(time.Duration(1000000/qps)*time.Microsecond, qps)
		l.Qps = qps
	}
	l.Wait(1)
}

// polarisApi 北极星api结构体
type polarisApi struct {
	sync.Mutex
	api api.ConsumerAPI
}

func getPolarisConsumer() api.ConsumerAPI {
	// 获取北极星单例使用double-check
	if polarisConsumer.api == nil {
		polarisConsumer.Lock()
		defer polarisConsumer.Unlock()

		if polarisConsumer.api == nil {
			var err error
			polarisConsumer.api, err = api.NewConsumerAPI()
			if err != nil {
				log.Errorf("Fail to init polaris consumer, err is %s.", err)
				return nil
			}
		}
	}
	return polarisConsumer.api
}

// GetIpByOns 使用北极星解析ons域名 返回ip:port
func GetIpByOns(onsName string) string {
	return GetIpByPolarisName(onsName, "Production")
}

// GetIpByPolarisName 使用北极星接口解析北极星域名 返回ip:port
func GetIpByPolarisName(serviceName, nameSpace string) string {
	polarisSingleton := getPolarisConsumer()
	if polarisSingleton == nil {
		// 初始化失败返回""
		return ""
	}

	getInstResp, err := polarisSingleton.GetOneInstance(&api.GetOneInstanceRequest{
		GetOneInstanceRequest: model.GetOneInstanceRequest{
			Service:   serviceName,
			Namespace: nameSpace,
		},
	})
	if err != nil || len(getInstResp.Instances) == 0 {
		log.Errorf("Fail to get instance, ons:%s, err is:%s.", serviceName, err)
		return ""
	}
	return fmt.Sprintf("%s:%d", getInstResp.Instances[0].GetHost(), getInstResp.Instances[0].GetPort())
}

// GetClientIp 获取客户端ip
func GetClientIp(ctx context.Context) string {
	msg := trpc.Message(ctx)
	var addr string
	if msg.RemoteAddr() != nil {
		addr = msg.RemoteAddr().String()
		s := strings.Split(addr, ":")
		if len(s) > 0 {
			addr = s[0]
		}
	}
	return addr
}

// CurVal 获取当前值接口
type CurVal interface {
	CurVal(field *storeComm.FieldInfo) (*FieldVal, bool)
}

// FieldVal 字段值类型
type FieldVal struct {
	// TxtVal 文本类型
	TxtVal string
	// MapVal map类型
	MapVal map[string]string
}
