syntax = "proto3";
package trpc.media_msg_hub.media_version_control;
option go_package="git.code.oa.com/trpcprotocol/media_version_control";

message MediaVersion
{
	string vid = 1;
	string fieldName = 2;
	int32  version = 3;
};

message MediaVersionResponse
{
	repeated MediaVersion MediaVersionList = 1;
};

message MediaVersionRequest
{
	repeated MediaVersion MediaVersionList = 1;
};


service MediaVersionControl {
	rpc MediaVersionWrite (MediaVersionRequest) returns (MediaVersionResponse) {}
	rpc MediaVersionRead (MediaVersionRequest) returns (MediaVersionResponse) {}
};

