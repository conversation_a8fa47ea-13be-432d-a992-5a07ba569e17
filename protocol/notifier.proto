syntax = "proto3";
package trpc.media_msg_hub.trpcNotifier;
option go_package="git.code.oa.com/trpcprotocol/trpcNotifier";


message FieldInfo //这个是该视频消息的属性
{
    int32 id = 1;
    string value = 2; //如果value是多个值，例如数组，那么你会得到一个用逗号分割的字符串
};

message ModifyFieldInfo // 这个是该视频消息发生变更的字段
{
    int32 id = 1;
    string old = 2;
    string new = 3;
};

message MediaMessage //媒资消息的对象表示
{
    string vid = 1; //视频的id
    int32 timestamp = 2; //这个是媒资系统产生该消息的时间
    int32 dataSetId = 3; //媒资数据集，一半不需要关心这个
    map<string, FieldInfo> fieldInfos = 4;  //你所订阅的该视频全量字段的集合
    map<string, ModifyFieldInfo> modifyFieldInfos =5; //与该消息相关的变更字段的集合，记录了此次消息中涉及的发生改变的字段
    string operatorName = 6;
};

message errorString
{
    string errorString = 1;
};

service NotifierReceiver {
	rpc Receive (MediaMessage) returns (errorString) {} //如果是基于go实现，将错误返回到error中即可。如果是非go实现的，请给errorString赋值错误消息
};

