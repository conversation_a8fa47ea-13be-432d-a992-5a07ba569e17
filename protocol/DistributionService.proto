syntax = "proto3";
package   trpc.media_msg_hub.distribution_server;

//default go package name
option  go_package ="git.code.oa.com/trpcprotocol/media_msg_hub/distribution_server_distribution_service";

// 不建议使用 google/protobuf/any.proto
// any强依赖package type.googleapis.com/_packagename_._messagename_.
// https://developers.google.com/protocol-buffers/docs/proto3#any

// tRPC-Go数据校验模块（**移除注释使用**）
// 介绍：http://km.oa.com/articles/view/438840
// import "trpc/common/validate.proto";

message MsgString {
	string str = 1;
}
service DistributionService {
	rpc Receive (MsgString) returns (MsgString) {}
}
