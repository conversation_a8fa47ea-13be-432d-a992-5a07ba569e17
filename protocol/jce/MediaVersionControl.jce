module MediaVersionControl
{

	struct MediaVersion
	{
        0 require string vid;            
        1 require string fieldName;                
        2 require int    version; 
	};

	struct MediaVersionResponse 
	{
		0 require vector<MediaVersion> MediaVersionList;
	};

	struct MediaVersionRequest
	{
        0 require vector<MediaVersion> MediaVersionList;
	};


	interface MediaVersionControl
	{
	    MediaVersionResponse MediaVersionWrite(vector<MediaVersion> MediaVersionList);
		MediaVersionResponse MediaVersionRead(vector<MediaVersion> MediaVersionList);
	};
};
