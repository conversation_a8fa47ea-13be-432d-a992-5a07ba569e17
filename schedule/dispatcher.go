package schedule

import (
	"fmt"
	"sync"
)

// Dispatcher 下发结构
type Dispatcher struct {
	name    string
	events  chan interface{}
	wg      sync.WaitGroup
	closed  bool
	workers int
}

// DispatchHandler 下发处理接口interface
type DispatchHandler interface {
	Handle(event interface{}) (err error)
	OnFailure(event interface{})
}

// NewDispatcher 新建分发器
func NewDispatcher(name string, maxGoroutines int, handler DispatchHandler) *Dispatcher {
	// 初始化事件调度器
	dispatcher := Dispatcher{
		name:    name,
		events:  make(chan interface{}),
		workers: maxGoroutines,
		// handler : handler,
	}

	// 等待监听routine结束的信号量
	dispatcher.wg.Add(maxGoroutines)

	// 启动事件监听routione
	for i := 0; i < maxGoroutines; i++ {
		go func(id int) {
			for event := range dispatcher.events {

				func() {

					defer func() {
						if err := recover(); err != nil {
							_ = fmt.Sprintf("recover from : %v", err)
							// log.GetLogger().Error(msg)
						}
					}()

					err := handler.Handle(event)

					if err != nil {
						// log.GetLogger().ErrorFormat("[%s - %d] Event failed to dispatch : %v, (error=%v)", name, id, event, err)
						// 派发失败
						handler.OnFailure(event)

					}
				}()
			}

			dispatcher.wg.Done()
		}(i)
	}
	// log.GetLogger().InfoFormat("Dispatcher %s started with %d workers", name, maxGoroutines)
	// 返回调度器
	return &dispatcher
}

// GetWorkers 获取处理worker
func (d *Dispatcher) GetWorkers() int {
	return d.workers
}

// Dispatch 调度
func (d *Dispatcher) Dispatch(event interface{}) {
	if !d.closed {
		// 发送事件到时间消息通道，没有空闲worker时阻塞
		d.events <- event
	}
}

// Shutdown 关闭
func (d *Dispatcher) Shutdown() {
	// 关闭事件接收通道
	close(d.events)
	d.closed = true
	// 等待事件监听routine结束
	d.wg.Wait()
	// log.GetLogger().InfoFormat("Dispatcher %s shuted down...", d.name)
}
