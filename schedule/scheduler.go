// Package schedule 调度包
package schedule

import (
	"sync"
	"time"
)

// Handler 处理接口
type Handler interface {
	Run()
	Shutdown()
}

// Scheduler 调度结构
type Scheduler struct {
	ticker    *time.Ticker
	interrupt chan bool
	wg        sync.WaitGroup
	delay     time.Duration
	interval  time.Duration
	handler   Handler
}

// NewScheduler 新建调度器
func NewScheduler(delay time.Duration, interval time.Duration, handler Handler) *Scheduler {
	return &Scheduler{
		interrupt: make(chan bool),
		delay:     delay,
		interval:  interval,
		handler:   handler,
	}
}

// Start 启动
func (s *Scheduler) Start() {
	s.wg.Add(1)
	go func() {
		defer func() {
			s.wg.Done()
		}()
		// delay
		time.Sleep(s.delay)
		// run for the first time
		if s.handler != nil {
			s.handler.Run()
		}
		s.ticker = time.NewTicker(s.interval)
		for {
			select {
			case <-s.interrupt:
				return
			case <-s.ticker.C:
				if s.handler != nil {
					s.handler.Run()
				}
			}
		}
	}()
}

// Shutdown 关闭函数
func (s *Scheduler) Shutdown() {
	if s.ticker == nil {
		return
	}
	/* 停止计时器 */
	s.ticker.Stop()
	/* 中断调度 */
	s.interrupt <- true
	/* 等待调度器停止 */
	s.wg.Wait()
	/* 停止Handler */
	s.handler.Shutdown()
}
