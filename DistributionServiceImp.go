package main

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/media_msg_hub/bee_push"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/worker"
	jsoniter "github.com/json-iterator/go"
)

type beePushServerServiceImpl struct{}

// Insert 插入实现函数
func (s *beePushServerServiceImpl) Insert(ctx context.Context, req *pb.InsertRequest, rsp *pb.InsertReply) (err error) {
	for _, appKey := range req.AppKey {
		subscriber := worker.Get(appKey)
		if subscriber == nil {
			return errs.New(10002, "subscriber is not exist or not enabled")
		}
		msg := &worker.MediaInfo{}
		err = jsoniter.Unmarshal([]byte(req.Msg), msg)
		if err != nil {
			return errs.New(10003, "unmarshal req msg error.")
		}
		log.WithContextFields(ctx,
			"mediaSet", trpc.GlobalConfig().Global.FullSetName,
			"serverName", trpc.GlobalConfig().Server.Server,
			"data_id", msg.Id,
			"subscriber_name", subscriber.Name,
		)
		httpInfo, sendResult := subscriber.SendMessageSync(ctx, subscriber.BuildMessageFromMediaInfo(msg))
		log.InfoContextf(ctx,
			"(%s)worker bee Insert %d msgID %s,httpInfo(%s),result(%s),msg(%s)",
			subscriber.Name, subscriber.Id, msg.Id, httpInfo, sendResult, msg)
	}
	rsp.Msg = "success"
	return nil
}
