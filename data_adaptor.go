package main

import (
	"context"
	"sync/atomic"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/dao/inf"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/logic"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
)

// SetFieldInfos 数据适配层set接口
func (s *dataAdaptorServiceImpl) SetFieldInfos(ctx context.Context, req *adaptor.SetFieldInfosRequest,
	rsp *adaptor.SetFieldInfosResponse,
) (opErr error) {
	rsp.RetInfo = logic.NewRetInfo()
	// 初始化map
	rsp.RetInfo.FailList = make(map[string]protocol.EnumMediaErrorCode)

	if len(req.GetFieldInfos()) == 0 {
		return nil
	}
	var obj inf.StoreObj
	obj, opErr = dao.NewStoreObj(ctx, req.GetDataSourceId(), req.GetId())
	if opErr != nil {
		log.Errorf("Fail to get dataItem[dataSourceId:%d], err is %s.", req.DataSourceId, opErr)
		return
	}

	modifyInfo, err := obj.SetFieldInfos(req.GetId(), req.GetBaseFieldIds(), req.GetFieldInfos(), rsp)
	if err != nil {
		log.Errorf("Fail to call setFieldInfos, err is %s.", err)
		// 更新失败场景仍有modifyInfo，触发检查补发消息流程
		logic.AddTimeoutCheckTask(ctx, req.GetDataSourceId(), req.GetId(), req.GetExtraInfo(), modifyInfo)
		return errs.New(int(protocol.EnumMediaErrorCode_RetFailUpdate), "fail to call SetFieldInfos")
	}
	// 填充变更事件列表
	rsp.BaseInfo, rsp.ModifyInfos = modifyInfo.GetBaseInfo(), modifyInfo.GetInfos()
	log.Debugf("Update baseInfo:%+v, modifyInfo:%+v.", rsp.GetBaseInfo(), rsp.GetModifyInfos())
	return
}

// GetFieldInfos 数据适配层get接口
func (s *dataAdaptorServiceImpl) GetFieldInfos(ctx context.Context, req *adaptor.GetFieldInfosRequest,
	rsp *adaptor.GetFieldInfosResponse,
) (opErr error) {
	// 初始化map
	rsp.FailList = make(map[uint32]protocol.EnumMediaErrorCode)

	if len(req.FieldIds)+len(req.FieldKeys) == 0 {
		// Get请求无字段id直接跳过
		log.Debugf("Pass nil request, req is %s.", req.String())
		return nil
	}

	// 根据数据源id和key获取数据连接对象（例如数据库表连接）
	var obj inf.StoreObj
	obj, opErr = dao.NewStoreObj(ctx, req.GetDataSourceId(), req.GetId())
	if opErr != nil {
		log.Errorf("Fail to get dataItem[dataSourceId:%d], err is %s.", req.DataSourceId, opErr)
		return
	}

	fieldKey := &inf.FieldKey{FieldIDs: req.FieldIds, FieldKeys: req.FieldKeys}
	if err := obj.GetFieldInfos([]string{req.Id}, fieldKey, rsp); err != nil {
		log.Errorf("Fail to call GetFieldInfos, err is %s.", err)
		return errs.New(int(protocol.EnumMediaErrorCode_RetFailSelect), "fail to call GetFieldInfos")
	}
	log.Debugf("Get infos:%+v.", rsp.FieldInfos)
	return
}

// InsertFieldInfos 数据适配层insert接口
func (s *dataAdaptorServiceImpl) InsertFieldInfos(ctx context.Context, req *adaptor.InsertFieldInfosRequest,
	rsp *adaptor.InsertFieldInfosResponse,
) (opErr error) {
	rsp.RetInfo = logic.NewRetInfo()

	if len(req.FieldInfos) == 0 {
		return nil
	}

	var obj inf.StoreObj
	obj, opErr = dao.NewStoreObj(ctx, req.GetDataSourceId(), req.GetId())
	if opErr != nil {
		log.Errorf("Fail to get dataItem[dataSourceId:%d], err is %s.", req.DataSourceId, opErr)
		return
	}

	modifyInfo, err := obj.InsertFieldInfos(req.Id, req.Version, req.FieldInfos)
	if err != nil {
		return errs.New(int(protocol.EnumMediaErrorCode_RetFailUpdate), "fail to call InsertFieldInfos")
	}
	if modifyInfo != nil {
		rsp.ModifyInfos = modifyInfo.Infos
	}
	return
}

// transBatchReqToInterfaceReq 将批量读请求转换为接口请求
func transBatchReqToInterfaceReq(req *adaptor.BatchGetFieldsRequest) ([]string, map[string][]string) {
	extra := make(map[string][]string)
	var fields []string
	for _, rawField := range req.GetFieldNames() {
		fieldName, key, hasKey := logic.ParseMapFieldName(rawField)
		if !hasKey {
			fields = append(fields, fieldName)
			continue
		}
		_, ok := extra[fieldName]
		// map类型字段添加到请求extra里面
		extra[fieldName] = append(extra[fieldName], key)
		if !ok {
			fields = append(fields, fieldName)
		}
	}
	return fields, extra
}

func mergeBatchRsps(rsp *adaptor.BatchGetFieldsResponse, rsps []*model.BatchGetResponse) {
	for _, r := range rsps {
		if r == nil {
			continue
		}

		// 合并文档内容
		for _, doc := range r.DocInfos {
			rsp.DocInfos[doc.GetId()] = doc
		}
		// 合并failList
		for id, msg := range r.FailList {
			rsp.FailList[id] = msg
		}
	}
}

// BatchGetFieldInfos 数据适配层批量拉取数据接口
// NOCC:CCN_threshold(设计如此:)
func (s *dataAdaptorServiceImpl) BatchGetFieldInfos(ctx context.Context, req *adaptor.BatchGetFieldsRequest,
	rsp *adaptor.BatchGetFieldsResponse,
) error {
	rsp.DocInfos, rsp.FailList = make(map[string]*protocol.DocInfo), make(map[string]string)
	if len(req.Id) == 0 || len(req.FieldNames) == 0 {
		// 空操作直接跳过
		return nil
	}

	objs, err := dao.NewStoreObjs(ctx, req.DataSourceID, req.GetId())
	if err != nil {
		log.Errorf("Fail to get dataItem[dataSourceId:%d], err is %v.", req.DataSourceID, err)
		return err
	}

	var (
		handles []func() error
		i       int32 = -1
	)
	fields, extra := transBatchReqToInterfaceReq(req)
	rsps := make([]*model.BatchGetResponse, len(objs))
	for _, g := range objs {
		g := g
		handles = append(handles, func() (err error) {
			log.Debugf("Begin to batch getFields, fields is %v, extra is %v, ids is %v.", fields, extra, g.IDs)
			var bRsp *model.BatchGetResponse
			bRsp, err = g.Obj.BatchGetFields(req.DataSetID, g.IDs, fields, extra)
			rsps[atomic.AddInt32(&i, 1)] = bRsp
			return
		})
	}
	if err = trpc.GoAndWait(handles...); err != nil {
		log.Errorf("Fail to go and wait handlers, err is %v.", err)
		rsp.ErrCode = protocol.EnumMediaErrorCode_RetCallAdaptorErr
	}
	mergeBatchRsps(rsp, rsps)
	return nil
}

// SearchMediaInfos 数据适配层服务搜索数据接口
func (s *dataAdaptorServiceImpl) SearchMediaInfos(ctx context.Context, req *adaptor.SearchMediaInfosRequest,
	rsp *adaptor.SearchMediaInfosResponse,
) error {
	log.Debugf("SearchReq:%+v", req)
	rsp.RetInfo = logic.NewRetInfo()
	var err error
	defer func() {
		if err != nil {
			rsp.RetInfo.ErrCode = protocol.EnumMediaErrorCode(errs.Code(err))
			rsp.RetInfo.ErrMsg = errs.Msg(err)
		}
	}()

	var searchObj inf.SearchObj
	searchObj, err = dao.NewSearchObj(ctx, req.GetDataSourceID(), req.GetDataSetID())
	if err != nil {
		log.Errorf("Fail to connect dataItem[dataSourceId:%d], err is %s.", req.GetDataSourceID(), err)
		return err
	}

	total, docs, err := searchObj.Search(req.GetCondGroups(), req.GetLogical(), req.GetExprCond(),
		req.GetIncludes(), req.GetSort(), req.GetPageInfo())
	if err != nil {
		return errs.New(int(protocol.EnumMediaErrorCode_RetFailSelect), err.Error())
	}
	rsp.Docs = docs
	rsp.Total = uint32(total)
	return nil
}
