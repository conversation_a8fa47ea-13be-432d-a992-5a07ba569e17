package main

import (
	"encoding/json"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"

	"github.com/IBM/sarama"
	"google.golang.org/protobuf/proto"
)

// EventData 事件数据结构
type EventData struct {
	CID          string `json:"cid"`
	CheckupState int    `json:"checkup_state"`
	ListType     int    `json:"list_type"`
	VIDList      string `json:"vid_list"`
}

func main() {
	// 1. 创建Kafka生产者配置
	config := sarama.NewConfig()
	config.Producer.Return.Successes = true
	config.Producer.Return.Errors = true
	config.Producer.RequiredAcks = sarama.WaitForAll

	// 2. 创建Kafka生产者
	producer, err := sarama.NewSyncProducer([]string{"21.106.152.8:9092"}, config)
	if err != nil {
		log.Fatalf("创建Kafka生产者失败: %v", err)
	}
	defer producer.Close()

	// 3. 构造测试事件
	data := EventData{
		CID:          "mzc00200v8xid75",
		CheckupState: 4,
		ListType:     1,
		VIDList:      "r4100gi9l2u,t410050ld1q,z41007kwrt3",
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		log.Fatalf("序列化失败: %v", err)
	}
	event := &common_event.ProcessorEvent{
		Event: &common_event.BaseEvent{
			Id:        "test_event_id",
			Subject:   "mzc00200v8xid75",
			BridgeId:  9,
			Type:      "remove_video_from_cover", // 使用配置文件中定义的事件类型
			Timestamp: 1748432109000,             // 使用毫秒时间戳 time.Now().UnixMilli()
			SourceId:  17,
			Data:      string(jsonData),
			Trace:     "fd8b85ff2d5be221121f63efff769a7f",
		},
	}

	// 4. 序列化事件
	eventData, err := proto.Marshal(event)
	if err != nil {
		log.Fatalf("序列化事件失败: %v", err)
	}

	// 5. 发送消息到Kafka
	msg := &sarama.ProducerMessage{
		Topic: "topic_bridge_9", // 使用配置文件中定义的topic
		Key:   sarama.StringEncoder(event.Event.Id),
		Value: sarama.ByteEncoder(eventData),
	}

	partition, offset, err := producer.SendMessage(msg)
	if err != nil {
		log.Fatalf("发送消息失败: %v", err)
	}

	log.Infof("消息发送成功! Topic: %s, Partition: %d, Offset: %d, EventID: %s",
		msg.Topic, partition, offset, event.Event.Id)

	// 6. 等待一段时间，确保消息被处理
	time.Sleep(5 * time.Second)

}
