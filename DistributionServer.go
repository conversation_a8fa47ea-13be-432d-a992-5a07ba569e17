package main

import (
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go"
	pb "git.code.oa.com/trpcprotocol/media_msg_hub/bee_push"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/db"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/extcfg"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/master"
	"git.code.oa.com/video_media/media_msg_hub/distribution_server/worker"

	_ "net/http/pprof"

	_ "git.code.oa.com/trpc-go/trpc-config-tconf"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-go/metrics"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.woa.com/galileo/trpc-go-galileo"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/otelcarriers/otelkafka"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc"
	_ "git.woa.com/video_media/traceplus"
)

// Init servant
func main() {
	s := trpc.NewServer()
	initDb()
	worker.Run()
	startAlarm()
	// trpc worker run
	pb.RegisterBeePushService(s.Service("trpc.media_msg_hub.distribution_server.distribution_server"),
		&beePushServerServiceImpl{})
	if err := s.Serve(); err != nil {
		panic(err)
	}
}

func initDb() {
	config := extcfg.GetExtCfg()
	mysqlConfig := config.Mysql
	dbName, host, port, user, pwd, _, maxOpen := mysqlConfig.GetConfigurations()
	db.InitDB(fmt.Sprintf("%s:%d", host, port), user, pwd, dbName, maxOpen)
}

func startAlarm() {
	alarmManager := master.NewAlarmManager()
	alarmManager.Start()
}
