// Package rule 规则实现 (基于gojq重构)
package rule

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/trpcprotocol/media_event_hub/common_errcode"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/toolkit/logic"
	"git.woa.com/video_media/media_event_hub/toolkit/logic/jsonpath"
)

// NewEventRuleCalculator 事件规则分析实现
var NewEventRuleCalculator = func() logic.EventRuleCalculator {
	return &eventRuleJQCalculator{jsonOperator: jsonpath.New()}
}

type eventRuleJQCalculator struct {
	jsonOperator logic.JsonPathOperator
}

var regexCache sync.Map

func (e *eventRuleJQCalculator) IsEventRuleMatch(ctx context.Context,
	event *common_event.ProcessorEvent, matchRule *metadata_access.EventSubscribeRule) (bool, error) {
	// 1. 检查事件类型是否匹配
	if !e.checkEventTypeMatch(event, matchRule) {
		return false, nil
	}

	// 2. 检查是否需要匹配规则
	if !e.needMatchRules(matchRule) {
		return true, nil
	}

	// 3. 检查主规则是否匹配
	if matched, err := e.checkMainRuleMatch(ctx, event, matchRule); err != nil || !matched {
		return false, err
	}

	// 4. 检查扩展数据规则是否匹配
	return e.checkExtDataRulesMatch(ctx, event, matchRule)
}

// FilterExtData 过滤扩展数据
func (e *eventRuleJQCalculator) FilterExtData(ctx context.Context, event *common_event.ProcessorEvent,
	rule *metadata_access.EventSubscribeRule) error {
	if event == nil || event.ExtData == nil || len(event.ExtData.DataList) == 0 {
		return nil
	}
	if rule == nil || len(rule.ExtDataMatchRule) == 0 {
		return nil
	}
	for _, extCfg := range rule.ExtDataMatchRule {
		if extCfg == nil || len(extCfg.Layers) == 0 {
			continue
		}
		for _, cfgLayer := range extCfg.Layers {
			if cfgLayer == nil || cfgLayer.MatchRule == nil || len(cfgLayer.MatchRule.Groups) == 0 {
				continue
			}
			if int(cfgLayer.Layer) > len(event.ExtData.DataList) {
				continue
			}
			if err := e.filterExtData(ctx, event, event.ExtData.DataList[cfgLayer.Layer], cfgLayer.MatchRule); err != nil {
				return err
			}

		}
	}
	return nil
}

func (e *eventRuleJQCalculator) CutData(ctx context.Context, event *common_event.ProcessorEvent,
	cuts map[string]string) (err error) {
	for key, val := range cuts {
		rst, isHit, err := e.renderVarJsonPath(val, event.Event.Data, event.Event.Subject)
		if err != nil {
			if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
				return errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY),
					fmt.Sprintf("jsonpath渲染异常，查询[%s]的变量为空！", val))
			}
			return err
		}
		if !isHit {
			return errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY),
				fmt.Sprintf("jsonpath渲染异常，查询[%s]的变量为空！", val))
		}
		cuts[key] = rst
	}
	event.Event.Data, err = e.jsonOperator.CutJsonStr(event.Event.Data, cuts)
	if err != nil {
		return err
	}
	return nil
}

// checkEventTypeMatch 检查事件类型是否匹配
func (e *eventRuleJQCalculator) checkEventTypeMatch(event *common_event.ProcessorEvent,
	matchRule *metadata_access.EventSubscribeRule) bool {
	if matchRule.WatchType == "" || matchRule.WatchType == "*" {
		return true
	}
	return event.Event.Type == matchRule.WatchType
}

// needMatchRules 检查是否需要匹配规则
func (e *eventRuleJQCalculator) needMatchRules(
	matchRule *metadata_access.EventSubscribeRule) bool {
	hasMainRule := matchRule.MatchRule != nil && len(matchRule.MatchRule.Groups) > 0
	hasExtRule := len(matchRule.ExtDataMatchRule) > 0
	return hasMainRule || hasExtRule
}

// checkMainRuleMatch 检查主规则是否匹配
func (e *eventRuleJQCalculator) checkMainRuleMatch(ctx context.Context, event *common_event.ProcessorEvent,
	matchRule *metadata_access.EventSubscribeRule) (bool, error) {
	if matchRule.MatchRule == nil || len(matchRule.MatchRule.Groups) == 0 {
		return true, nil
	}
	return e.isMatchRuleGroupsMatch(ctx, event.Event.Data, matchRule.MatchRule, event, event.Event.Subject)
}

// checkExtDataRulesMatch 检查扩展数据规则是否匹配
func (e *eventRuleJQCalculator) checkExtDataRulesMatch(ctx context.Context,
	event *common_event.ProcessorEvent, matchRule *metadata_access.EventSubscribeRule) (bool, error) {
	if len(matchRule.ExtDataMatchRule) == 0 || event.ExtData == nil {
		return true, nil
	}
	for _, extCfg := range matchRule.ExtDataMatchRule {
		if extCfg == nil || len(extCfg.Layers) == 0 {
			continue
		}
		if len(extCfg.Layers) != 0 && extCfg.Layers[0] != nil && extCfg.Layers[0].Layer == 0 {
			if matched, err := e.processLayerMatch(ctx, event, extCfg.Layers[0]); err != nil || !matched {
				return false, err
			}
		}
	}
	return true, nil
}

// processLayerMatch 处理单层匹配逻辑
func (e *eventRuleJQCalculator) processLayerMatch(ctx context.Context, event *common_event.ProcessorEvent,
	layer *metadata_access.ExtDataConfigLayer) (bool, error) {
	// 处理首层匹配
	if layer.Layer == 0 {
		return e.processFirstLayerMatch(ctx, event, layer)
	}
	return true, nil
}

// processFirstLayerMatch 处理首层匹配逻辑
func (e *eventRuleJQCalculator) processFirstLayerMatch(ctx context.Context,
	event *common_event.ProcessorEvent, layer *metadata_access.ExtDataConfigLayer) (bool, error) {
	if layer.MatchRule == nil || len(layer.MatchRule.Groups) == 0 {
		return true, nil
	}
	var firstLayer *common_event.LayerData
	if event.ExtData == nil || len(event.ExtData.DataList) == 0 || event.ExtData.DataList[0] == nil ||
		len(event.ExtData.DataList[0].Nodes) == 0 || event.ExtData.DataList[0].Nodes[0] == nil {
		// 手动初始化，归一化处理
		firstLayer = &common_event.LayerData{Nodes: []*common_event.Node{{}}}
	} else {
		firstLayer = event.ExtData.DataList[0]
	}
	for _, node := range firstLayer.Nodes {
		if node == nil {
			// 手动初始化，归一化处理
			node = &common_event.Node{}
		}
		compareData, _ := json.Marshal(node)
		isMatch, err := e.isMatchRuleGroupsMatch(ctx, string(compareData),
			layer.MatchRule, event, node.DataId)
		if err != nil {
			return false, err
		}
		if !isMatch {
			return false, nil
		}
	}
	return true, nil
}

// filterExtData 处理非首层匹配逻辑
func (e *eventRuleJQCalculator) filterExtData(ctx context.Context, event *common_event.ProcessorEvent,
	dataLayer *common_event.LayerData, cfgLayer *metadata_access.MatchRuleGroupsConfig) error {
	if dataLayer == nil || len(dataLayer.Nodes) == 0 {
		return nil
	}
	if cfgLayer == nil || len(cfgLayer.Groups) == 0 {
		return nil
	}
	var matchedNodes []*common_event.Node
	for _, node := range dataLayer.Nodes {
		if node == nil {
			continue
		}
		compareData, err := json.Marshal(node)
		if err != nil {
			continue
		}
		isMatch, err := e.isMatchRuleGroupsMatch(ctx, string(compareData), cfgLayer, event, node.DataId)
		if err == nil && isMatch {
			matchedNodes = append(matchedNodes, node)
		}
	}
	dataLayer.Nodes = matchedNodes
	return nil
}

func (e *eventRuleJQCalculator) isMatchRuleGroupsMatch(ctx context.Context, data string,
	groups *metadata_access.MatchRuleGroupsConfig, event *common_event.ProcessorEvent, id string) (bool, error) {
	if groups == nil || len(groups.Groups) == 0 {
		return true, nil
	}
	var (
		existSuc   bool
		existFalse bool
	)
	for _, group := range groups.Groups {
		rst, err := e.isMatchRuleGroupMatch(ctx, data, group, event, id)
		if err != nil {
			return false, err
		}
		if rst {
			existSuc = true
		} else {
			existFalse = true
		}
	}
	// 与匹配 存在成功 不存在失败
	if groups.IsAnd && existSuc && !existFalse {
		return true, nil
	}
	// 或匹配，存在成功
	if !groups.IsAnd && existSuc {
		return true, nil
	}
	return false, nil
}

func (e *eventRuleJQCalculator) isMatchRuleGroupMatch(ctx context.Context, data string,
	matchRuleGroup *metadata_access.MatchRuleGroup, event *common_event.ProcessorEvent, id string) (bool, error) {
	// 无规则表示全匹配
	if len(matchRuleGroup.Rules) == 0 {
		return true, nil
	}
	var (
		existSuc   bool
		existFalse bool
	)
	for _, rule := range matchRuleGroup.Rules {
		rst, err := e.isMatchRuleMatch(ctx, data, rule, event, id)
		if err != nil {
			return false, err
		}
		if rst {
			existSuc = true
		} else {
			existFalse = true
		}
	}
	// 与匹配 存在成功 不存在失败
	if matchRuleGroup.IsAnd && existSuc && !existFalse {
		return true, nil
	}
	// 或匹配，存在成功
	if !matchRuleGroup.IsAnd && existSuc {
		return true, nil
	}
	return false, nil
}

// isMatchRuleMatch 匹配规则是否匹配
func (e *eventRuleJQCalculator) isMatchRuleMatch(ctx context.Context, data string,
	matchRule *metadata_access.MatchRule, event *common_event.ProcessorEvent, id string) (bool, error) {
	jp, isHit, err := e.renderVarJsonPath(matchRule.Jsonpath, event.Event.Data, id)
	if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
		return false, nil
	}
	if !isHit {
		return false, nil
	}
	matchRule.Jsonpath = jp
	// step2 匹配逻辑
	switch matchRule.Op {
	case metadata_access.MatchRuleOp_NOT_MATCH:
		if len(matchRule.Vals) == 0 {
			return false, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("compare vals empty"))
		}
		val, err := e.jsonOperator.GetString(matchRule.Jsonpath, data)
		if err != nil {
			if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
				if emptyStr[matchRule.Vals[0]] {
					return false, nil
				}
				return true, nil
			}
			return false, err
		}
		return !(matchRule.Vals[0] == val), nil
	case metadata_access.MatchRuleOp_NOT_IN:
		if len(matchRule.Vals) == 0 {
			return false, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("compare vals empty"))
		}
		val, err := e.jsonOperator.GetString(matchRule.Jsonpath, data)
		if err != nil {
			if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
				if inOperation("", matchRule.Vals) {
					return false, nil
				}
				return true, nil
			}
			return false, err
		}
		return !inOperation(val, matchRule.Vals), nil
	case metadata_access.MatchRuleOp_NOT_EXIST:
		val, err := e.jsonOperator.GetString(matchRule.Jsonpath, data)
		if err != nil {
			if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
				return true, nil
			}
			return false, err
		}
		return emptyStr[val], nil
	case metadata_access.MatchRuleOp_EXIST:
		val, err := e.jsonOperator.GetString(matchRule.Jsonpath, data)
		if err != nil {
			if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
				return false, nil
			}
			return false, err
		}
		return !emptyStr[val], nil
	case metadata_access.MatchRuleOp_MATCH:
		if len(matchRule.Vals) == 0 {
			return false, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("compare vals empty"))
		}
		val, err := e.jsonOperator.GetString(matchRule.Jsonpath, data)
		if err != nil {
			if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
				if matchRule.Vals[0] == "" {
					return true, nil
				}
				return false, nil
			}
			return false, err
		}
		return matchRule.Vals[0] == val, nil
	case metadata_access.MatchRuleOp_FUZZ_MATCH:
		if len(matchRule.Vals) == 0 {
			return false, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("compare vals empty"))
		}
		val, err := e.jsonOperator.GetString(matchRule.Jsonpath, data)
		if err != nil {
			if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
				if matchRule.Vals[0] == "" {
					return true, nil
				}
				return false, nil
			}
			return false, err
		}
		return strings.Contains(val, matchRule.Vals[0]), nil
	case metadata_access.MatchRuleOp_REG_MATCH:
		if len(matchRule.Vals) == 0 {
			return false, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("compare vals empty"))
		}
		val, err := e.jsonOperator.GetString(matchRule.Jsonpath, data)
		if err != nil {
			if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
				if matchRule.Vals[0] == "" {
					return true, nil
				}
				return false, nil
			}
			return false, err
		}
		return regexMatch(val, matchRule.Vals[0])
	case metadata_access.MatchRuleOp_GT,
		metadata_access.MatchRuleOp_GTE,
		metadata_access.MatchRuleOp_LT,
		metadata_access.MatchRuleOp_LTE:
		if len(matchRule.Vals) == 0 {
			return false, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("compare vals empty"))
		}
		val, err := e.jsonOperator.GetFloat(matchRule.Jsonpath, data)
		if err != nil {
			return false, err
		}
		return compareNumbers(val, matchRule.Vals[0], matchRule.Op)
	case metadata_access.MatchRuleOp_IN:
		if len(matchRule.Vals) == 0 {
			return false, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("compare vals empty"))
		}
		val, err := e.jsonOperator.GetString(matchRule.Jsonpath, data)
		if err != nil {
			if errs.Code(err) == int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY) {
				return inOperation("", matchRule.Vals), nil
			}
			return false, err
		}
		return inOperation(val, matchRule.Vals), nil
	default:
		return false, fmt.Errorf("unsupported op: %v", matchRule.Op)
	}
}

func (e *eventRuleJQCalculator) renderVarJsonPath(jsonpath string, data string, id string) (string, bool, error) {
	vars := logic.GetVariables(jsonpath)
	if len(vars) == 0 {
		return jsonpath, true, nil
	}
	in := map[string]string{}
	for _, v := range vars {
		// 如果v == .id，取ID
		if v == ".id" {
			in[v] = id
			continue
		}
		str, err := e.jsonOperator.GetString(v, data)
		if err != nil {
			return "", false, err
		}
		in[v] = str
	}
	return logic.RenderTemplate(in, jsonpath), true, nil
}

func regexMatch(val string, pattern string) (bool, error) {
	var re *regexp.Regexp
	if cached, ok := regexCache.Load(pattern); ok {
		re = cached.(*regexp.Regexp)
	} else {
		compiled, err := regexp.Compile(pattern)
		if err != nil {
			return false, fmt.Errorf("invalid regex pattern: %v", err)
		}
		regexCache.Store(pattern, compiled)
		re = compiled
	}
	return re.MatchString(val), nil
}

func compareNumbers(extractedNum float64, target string, op metadata_access.MatchRuleOp) (bool, error) {
	targetNum, err := strconv.ParseFloat(target, 64)
	if err != nil {
		return false, fmt.Errorf("target value is not a number: %v", err)
	}
	switch op {
	case metadata_access.MatchRuleOp_GT:
		return extractedNum > targetNum, nil
	case metadata_access.MatchRuleOp_GTE:
		return extractedNum >= targetNum, nil
	case metadata_access.MatchRuleOp_LT:
		return extractedNum < targetNum, nil
	case metadata_access.MatchRuleOp_LTE:
		return extractedNum <= targetNum, nil
	default:
		return false, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("unsupported comparison op: %v", op))
	}
}

func inOperation(item string, targets []string) bool {
	for _, t := range targets {
		if t == item {
			return true
		}
	}
	return false
}

var emptyStr = map[string]bool{"": true, "{}": true, "[]": true, "nil": true, "null": true, "Null": true, " ": true}
