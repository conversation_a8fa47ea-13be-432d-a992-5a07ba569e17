package rule

import (
	"context"
	"testing"

	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
)

func TestIsEventRuleMatch(t *testing.T) {
	calculator := NewEventRuleCalculator()
	ctx := context.Background()

	tests := []struct {
		name    string
		event   *common_event.ProcessorEvent
		rule    *metadata_access.EventSubscribeRule
		want    bool
		wantErr bool
	}{
		// 测试事件类型匹配
		{
			name: "事件类型匹配-通配符",
			event: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Type: "video.create",
				},
			},
			rule: &metadata_access.EventSubscribeRule{
				WatchType: "*",
			},
			want: true,
		},
		{
			name: "事件类型匹配-精确匹配",
			event: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Type: "video.create",
				},
			},
			rule: &metadata_access.EventSubscribeRule{
				WatchType: "video.create",
			},
			want: true,
		},
		{
			name: "事件类型不匹配",
			event: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Type: "video.delete",
				},
			},
			rule: &metadata_access.EventSubscribeRule{
				WatchType: "video.create",
			},
			want: false,
		},

		// 测试主规则匹配
		{
			name: "主规则-精确匹配",
			event: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Type:    "video.create",
					Data:    `{"status": "published"}`,
					Subject: "video_123",
				},
			},
			rule: &metadata_access.EventSubscribeRule{
				WatchType: "video.create",
				MatchRule: &metadata_access.MatchRuleGroupsConfig{
					Groups: []*metadata_access.MatchRuleGroup{
						{
							IsAnd: true,
							Rules: []*metadata_access.MatchRule{
								{
									Jsonpath: ".status",
									Op:       metadata_access.MatchRuleOp_MATCH,
									Vals:     []string{"published"},
								},
							},
						},
					},
				},
			},
			want: true,
		},
		{
			name: "主规则-模糊匹配",
			event: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Type:    "video.create",
					Data:    `{"title": "测试视频标题"}`,
					Subject: "video_123",
				},
			},
			rule: &metadata_access.EventSubscribeRule{
				WatchType: "video.create",
				MatchRule: &metadata_access.MatchRuleGroupsConfig{
					Groups: []*metadata_access.MatchRuleGroup{
						{
							IsAnd: true,
							Rules: []*metadata_access.MatchRule{
								{
									Jsonpath: ".title",
									Op:       metadata_access.MatchRuleOp_FUZZ_MATCH,
									Vals:     []string{"视频"},
								},
							},
						},
					},
				},
			},
			want: true,
		},
		{
			name: "主规则-正则匹配",
			event: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Type:    "video.create",
					Data:    `{"id": "video_12345"}`,
					Subject: "video_12345",
				},
			},
			rule: &metadata_access.EventSubscribeRule{
				WatchType: "video.create",
				MatchRule: &metadata_access.MatchRuleGroupsConfig{
					Groups: []*metadata_access.MatchRuleGroup{
						{
							IsAnd: true,
							Rules: []*metadata_access.MatchRule{
								{
									Jsonpath: ".id",
									Op:       metadata_access.MatchRuleOp_REG_MATCH,
									Vals:     []string{"^video_\\d+$"},
								},
							},
						},
					},
				},
			},
			want: true,
		},
		{
			name: "主规则-数值比较",
			event: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Type:    "video.create",
					Data:    `{"duration": 300}`,
					Subject: "video_123",
				},
			},
			rule: &metadata_access.EventSubscribeRule{
				WatchType: "video.create",
				MatchRule: &metadata_access.MatchRuleGroupsConfig{
					Groups: []*metadata_access.MatchRuleGroup{
						{
							IsAnd: true,
							Rules: []*metadata_access.MatchRule{
								{
									Jsonpath: ".duration",
									Op:       metadata_access.MatchRuleOp_GT,
									Vals:     []string{"200"},
								},
							},
						},
					},
				},
			},
			want: true,
		},
		{
			name: "主规则-包含判断",
			event: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Type:    "video.create",
					Data:    `{"video_123": {"tags": "教育"}}`,
					Subject: "video_123",
				},
			},
			rule: &metadata_access.EventSubscribeRule{
				WatchType: "video.create",
				MatchRule: &metadata_access.MatchRuleGroupsConfig{
					Groups: []*metadata_access.MatchRuleGroup{
						{
							IsAnd: true,
							Rules: []*metadata_access.MatchRule{
								{
									Jsonpath: ".${.id}.tags",
									Op:       metadata_access.MatchRuleOp_IN,
									Vals:     []string{"教育", "科技", "娱乐"},
								},
							},
						},
					},
				},
			},
			want: true,
		},

		// 测试扩展数据规则匹配
		{
			name: "扩展数据规则-过滤和匹配",
			event: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Type:    "video.create",
					Data:    `{"id": "video_123"}`,
					Subject: "video_123",
				},
				ExtData: &common_event.ExtData{
					DataList: []*common_event.LayerData{
						{
							Level: 0,
							Nodes: []*common_event.Node{
								{
									DataId: "node_1",
									Values: map[string]*common_event.ValueContainer{
										"2:1_2": {
											Fields: map[string]*common_event.FieldValue{
												"title": {
													FieldType: common_event.EnumFieldType_STR_FIELD,
													Str:       "测试视频",
												},
												"long_video_list": {
													FieldType: common_event.EnumFieldType_STR_FIELD,
													StrList:   []string{""},
												},
												"positive_content": {
													FieldType: common_event.EnumFieldType_INT_VEC_FIELD,
													IntList:   []int64{1543606},
												},
											},
										},
									},
								},
							},
						},
						{
							Level: 1,
							Nodes: []*common_event.Node{
								{
									DataId: "node_2",
									Values: map[string]*common_event.ValueContainer{
										"2_1_2": {
											ViewType: common_event.EnumViewType_UNION_VIEW,
											Fields: map[string]*common_event.FieldValue{
												"state":            {Int: 4},
												"positive_content": {IntList: nil},
											},
										},
									},
								},
								{
									DataId: "node_3",
									Values: map[string]*common_event.ValueContainer{
										"2_1_2": {
											ViewType: common_event.EnumViewType_UNION_VIEW,
											Fields: map[string]*common_event.FieldValue{
												"state":            {Int: 8},
												"positive_content": {IntList: []int64{1}},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			rule: &metadata_access.EventSubscribeRule{
				WatchType: "video.create",
				ExtDataMatchRule: []*metadata_access.ExtDataConfig{
					{
						Layers: []*metadata_access.ExtDataConfigLayer{
							{
								Layer: 0,
								MatchRule: &metadata_access.MatchRuleGroupsConfig{
									Groups: []*metadata_access.MatchRuleGroup{
										{
											IsAnd: true,
											Rules: []*metadata_access.MatchRule{
												{
													Jsonpath: ".values.[\"2:1_2\"].fields.title.str",
													Op:       metadata_access.MatchRuleOp_FUZZ_MATCH,
													Vals:     []string{"视频"},
												},
												{
													Jsonpath: ".values.[\"2:1_2\"].fields.long_video_list.str_list",
													Op:       metadata_access.MatchRuleOp_NOT_EXIST,
												},
												{
													Jsonpath: ".values.[\"2:1_2\"].fields.positive_content.int_list[0]",
													Op:       metadata_access.MatchRuleOp_MATCH,
													Vals:     []string{"1543606"},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := calculator.IsEventRuleMatch(ctx, tt.event, tt.rule)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsEventRuleMatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsEventRuleMatch() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_eventRuleJQCalculator_FilterExtData(t *testing.T) {
	calculator := NewEventRuleCalculator().(*eventRuleJQCalculator)
	ctx := context.Background()

	// 基础测试数据
	baseEvent := &common_event.ProcessorEvent{
		Event: &common_event.BaseEvent{
			Data: `{"id": "video_123"}`,
		},
		ExtData: &common_event.ExtData{
			DataList: []*common_event.LayerData{
				{ // Layer 0
					Nodes: []*common_event.Node{
						{
							DataId: "node1",
							Values: map[string]*common_event.ValueContainer{
								"view1": {
									Fields: map[string]*common_event.FieldValue{
										"title": {Str: "测试视频"},
										"state": {Int: 1},
									},
								},
							},
						},
						{
							DataId: "node2",
							Values: map[string]*common_event.ValueContainer{
								"view1": {
									Fields: map[string]*common_event.FieldValue{
										"title": {Str: "无效数据"},
										"state": {Int: 2},
									},
								},
							},
						},
					},
				},
				{ // Layer 1
					Nodes: []*common_event.Node{
						{
							DataId: "child1",
							Values: map[string]*common_event.ValueContainer{
								"view2": {
									Fields: map[string]*common_event.FieldValue{
										"score":            {Float: 8.5},
										"positive_content": {IntList: []int64{1}},
									},
								},
							},
						},
						{
							DataId: "child2",
							Values: map[string]*common_event.ValueContainer{
								"view2": {
									Fields: map[string]*common_event.FieldValue{
										"positive_content": {IntList: []int64{1}},
										"score":            {Float: 6.0},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	tests := []struct {
		name     string
		event    *common_event.ProcessorEvent
		rule     *metadata_access.EventSubscribeRule
		wantErr  bool
		wantLens []int // 各层nodes期望长度
	}{
		{
			name:  "正常过滤多层数据",
			event: baseEvent,
			rule: &metadata_access.EventSubscribeRule{
				ExtDataMatchRule: []*metadata_access.ExtDataConfig{
					{
						Layers: []*metadata_access.ExtDataConfigLayer{
							{
								Layer: 0,
								MatchRule: &metadata_access.MatchRuleGroupsConfig{
									Groups: []*metadata_access.MatchRuleGroup{
										{
											IsAnd: true,
											Rules: []*metadata_access.MatchRule{
												{
													Jsonpath: ".values.view1.fields.title.str",
													Op:       metadata_access.MatchRuleOp_FUZZ_MATCH,
													Vals:     []string{"测试"},
												},
											},
										},
									},
								},
							},
							{
								Layer: 1,
								MatchRule: &metadata_access.MatchRuleGroupsConfig{
									Groups: []*metadata_access.MatchRuleGroup{
										{
											IsAnd: true,
											Rules: []*metadata_access.MatchRule{
												{
													Jsonpath: ".values.view2.fields.positive_content.int_list[0]",
													Op:       metadata_access.MatchRuleOp_MATCH,
													Vals:     []string{"1"},
												},
												{
													Jsonpath: ".values.view2.fields.score.float",
													Op:       metadata_access.MatchRuleOp_GT,
													Vals:     []string{"7.0"},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			wantLens: []int{1, 1}, // Layer0保留1个，Layer1保留1个
		},
		{
			name: "空数据不处理",
			event: &common_event.ProcessorEvent{
				ExtData: &common_event.ExtData{},
			},
			rule:     &metadata_access.EventSubscribeRule{},
			wantLens: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := calculator.FilterExtData(ctx, tt.event, tt.rule)

			if (err != nil) != tt.wantErr {
				t.Errorf("FilterExtData() error = %v, wantErr %v", err, tt.wantErr)
			}

			if tt.wantLens != nil && tt.event.ExtData != nil {
				for i, wantLen := range tt.wantLens {
					if len(tt.event.ExtData.DataList[i].Nodes) != wantLen {
						t.Errorf("第%d层数据过滤错误，期望长度%d，实际长度%d",
							i, wantLen, len(tt.event.ExtData.DataList[i].Nodes))
					}
				}
			}
		})
	}
}
