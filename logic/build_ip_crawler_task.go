package logic

import (
	"context"
	"regexp"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

// mergeDupCoverInfo 合并重复专辑信息（多个标题相同的cid，合并成一条，以免生成多条任务）
func mergeDupCoverInfo(coverInfos []*dao.CoverInfo) map[string]*dao.CoverInfo {
	resCoverInfos := make(map[string]*dao.CoverInfo)
	for _, info := range coverInfos {
		oriCover, ok := resCoverInfos[info.Title]
		if ok { // 已经存在同名专辑, 则更新同名专辑的covers，把cid放在一个字段(有优先标记的cid放在首位)
			if info.PriorityFlag == 1 {
				oriCover.CoverID = info.CoverID + "+" + oriCover.CoverID
			} else {
				oriCover.CoverID = oriCover.CoverID + "+" + info.CoverID
			}
		} else {
			resCoverInfos[info.Title] = info
		}
	}
	return resCoverInfos
}

func getAllCoverInfo(ctx context.Context) ([]*dao.CoverInfo, error) {
	var c dao.CoverInfo
	return c.GetAllCoverInfo(ctx)
}

func generateCrawTasks(coverInfos []*dao.CoverInfo) ([]*dao.IPCrawTask, error) {
	var crawInfos []*dao.IPCrawTask
	for _, info := range mergeDupCoverInfo(coverInfos) {
		crawInfos = append(crawInfos, genCrawInfo(model.IPAccountOrder, model.OfficeAccountType, info)...)
		crawInfos = append(crawInfos, genCrawInfo(model.IPTopicOrder, model.TopicType, info)...)
	}
	return crawInfos, nil
}

func insertIPCrawInfos(ctx context.Context, crawInfos []*dao.IPCrawTask) error {
	for _, info := range crawInfos {
		if err := info.InsertIPCrawInfo(ctx); err != nil {
			return err
		}
	}
	log.InfoContextf(ctx, "insertIPCrawInfos suc")
	return nil
}

// HandleBuildIPCrawTask 构建IP抓取任务池；每天凌晨 更新t_ip_crawler_conf表
func HandleBuildIPCrawTask(ctx context.Context, _, _ string, _ int32) error {
	coverInfos, err := getAllCoverInfo(ctx)
	if err != nil {
		return err
	}
	crawInfos, err := generateCrawTasks(coverInfos)
	if err != nil {
		return err
	}
	return insertIPCrawInfos(ctx, crawInfos)
}

// isOnline 是运营期
func isOnline(status int) bool {
	return status == 8377974 || status == 8389611 // 可预热-定档 还有 运营期的数据
}

// NOCC:CCN_threshold(设计如此:)
func createCrawInfo(orderID, crawType int, platform string, info *dao.CoverInfo) *dao.IPCrawTask {
	officeAccName, topicName := calcCrawName(info.CoverType, info.Title)
	crawInfo := &dao.IPCrawTask{
		OrderID:      orderID,
		CrawType:     crawType,
		Platform:     platform,
		CoverID:      info.CoverID,
		CoverType:    info.CoverType,
		PriorityFlag: info.PriorityFlag,
		Valid:        0,
		CoverLevel:   info.CoverLevel,
	}

	if crawType == model.OfficeAccountType {
		crawInfo.CrawKey = officeAccName
		if info.OfficeAccFlag == 1 && isOnline(info.IPOnlineStatus) { // 相应的抓取开关打开，才可以抓取
			crawInfo.Valid = 1
		}
	} else if crawType == model.TopicType {
		crawInfo.CrawKey = topicName
		if info.TopicFlag == 1 && isOnline(info.IPOnlineStatus) {
			crawInfo.Valid = 1
		}
	}
	return crawInfo
}

func genCrawInfo(orderID, crawID int, info *dao.CoverInfo) []*dao.IPCrawTask {
	var crawInfos []*dao.IPCrawTask
	for _, platform := range conf.GetCrawPlatforms(orderID) {
		crawInfos = append(crawInfos, createCrawInfo(orderID, crawID, platform, info))
	}
	return crawInfos
}

func createTopicName(coverType int, title string) string {
	switch coverType {
	case movies:
		return "电影" + title
	case teleplay, documentaryFilm, children:
		return title
	case anime:
		return title + "动画"
	case variety:
		re, _ := regexp.Compile(`第.季$`)
		return strings.TrimSpace(re.ReplaceAllString(title, ""))
	default:
		return ""
	}
}

// calcCrawName 计算IP的抓取名（IP官号、IP话题）
func calcCrawName(coverType int, title string) (string, string) {
	officeAccName := title
	topicName := createTopicName(coverType, title)
	// 去除抓取关键词中所有空格
	return strings.ReplaceAll(officeAccName, " ", ""),
		strings.ReplaceAll(topicName, " ", "")
}
