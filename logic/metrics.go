package logic

import (
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/metrics"
	metadata "git.woa.com/trpcprotocol/media_event_hub/metadata_access"
)

// MetricsReporter 监控数据上报器
type MetricsReporter struct {
	bridgeID   int64
	bridgeInfo *metadata.EventBridge
}

// NewMetricsReporter 创建监控数据上报器
func NewMetricsReporter(bridgeID int64, bridgeInfo *metadata.EventBridge) *MetricsReporter {
	return &MetricsReporter{
		bridgeID:   bridgeID,
		bridgeInfo: bridgeInfo,
	}
}

// ReportMetrics 上报监控数据
// reqCount: 总请求量
// errCount: 消费失败量
// successCount: 消费成功量
// delay: 消费延迟（秒）
// cost: 总花费时间（毫秒）
func (r *MetricsReporter) ReportMetrics(reqCount, errCount, successCount, delay, cost float64) {
	// 非法数据不上报，避免干扰统计
	if delay < 0 || cost <= 0 {
		return
	}

	// 获取事件总线名称，如果为空则使用ID
	bridgeName := "unknown"
	if r.bridgeInfo != nil && r.bridgeInfo.Name != "" {
		bridgeName = r.bridgeInfo.Name
	}

	_ = metrics.ReportMultiDimensionMetricsX(
		"media_event_hub.processor_server",
		[]*metrics.Dimension{
			{Name: "bridgeID", Value: strconv.FormatInt(r.bridgeID, 10)},
			{Name: "bridgeName", Value: bridgeName},
		},
		[]*metrics.Metrics{
			metrics.NewMetrics("req", reqCount, metrics.PolicySUM),
			metrics.NewMetrics("err", errCount, metrics.PolicySUM),
			metrics.NewMetrics("suc", successCount, metrics.PolicySUM),
			metrics.NewMetrics("delay_s", delay, metrics.PolicyHistogram),
			metrics.NewMetrics("cost_ms", cost, metrics.PolicyHistogram),
		},
	)
}
