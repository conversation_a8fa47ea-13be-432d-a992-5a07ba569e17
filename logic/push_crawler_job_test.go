package logic

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"github.com/agiledragon/gomonkey/v2"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
)

func Test_fetchAllJobs(t *testing.T) {
	ctx := trpc.BackgroundContext()
	if _, err := fetchAllJobs(ctx, 0); err == nil {
		t.Fatal("expect error for invalid orderID")
	}

	// Mock the GetAllCrawJobs method to avoid database connection
	patch := gomonkey.ApplyMethod(reflect.TypeOf(&dao.CrawJob{}), "GetAllCrawJobs",
		func(_ *dao.CrawJob, _ context.Context, _ string) ([]*dao.CrawJob, error) {
			return []*dao.CrawJob{{OrderID: 1006}}, nil
		})
	defer patch.Reset()

	jobs, err := fetchAllJobs(ctx, 1006)
	if err != nil || len(jobs) != 1 {
		t.Fatalf("unexpected: %v %d", err, len(jobs))
	}
}

func Test_processCrawJobs(t *testing.T) {
	ctx := trpc.BackgroundContext()
	jobs := []*dao.CrawJob{{ID: 1, OrderID: 1006}, {ID: 2, OrderID: 1006}}
	// Patch IsPushCraw by wrapping process path
	var jPtr *dao.CrawJob
	setStatePatch := gomonkey.ApplyMethod(reflect.TypeOf(jPtr), "SetCrawJobState",
		func(_ *dao.CrawJob, _ context.Context) error { return nil })
	defer setStatePatch.Reset()

	startPatch := gomonkey.ApplyFunc(StartCrawOrder, func(_ context.Context, _ int, _ int, _ string) error { return nil })
	defer startPatch.Reset()

	// Case: IsPushCraw returns true for first, false for second
	jobs[0].DayCrawlerState = 0
	jobs[0].DayCrawlerEndTime = jobs[0].DayCrawlerEndTime.AddDate(0, 0, -1)
	jobs[1].DayCrawlerState = dao.CrawJob{DayCrawlerState: 3}.DayCrawlerState
	jobs[1].DayCrawlerEndTime = jobs[1].DayCrawlerEndTime
	if err := processCrawJobs(ctx, jobs); err != nil {
		t.Fatalf("unexpected: %v", err)
	}
}

func Test_HandlePushCrawJobs_Success(t *testing.T) {
	ctx := trpc.BackgroundContext()

	// Mock for the success case
	getPatch := gomonkey.ApplyMethod(reflect.TypeOf(&dao.CrawJob{}), "GetAllCrawJobs",
		func(_ *dao.CrawJob, _ context.Context, _ string) ([]*dao.CrawJob, error) {
			t.Log("Mock GetAllCrawJobs called with success")
			return []*dao.CrawJob{{ID: 1, OrderID: 1006}}, nil
		})
	defer getPatch.Reset()

	setStatePatch := gomonkey.ApplyMethod(reflect.TypeOf(&dao.CrawJob{}), "SetCrawJobState",
		func(_ *dao.CrawJob, _ context.Context) error { return nil })
	defer setStatePatch.Reset()
	startPatch := gomonkey.ApplyFunc(StartCrawOrder, func(_ context.Context, _ int, _ int, _ string) error { return nil })
	defer startPatch.Reset()

	t.Log("Testing success case...")
	if err := HandlePushCrawJobs(ctx, "", "1006", 0); err != nil {
		t.Fatalf("unexpected: %v", err)
	}
	t.Log("Success case passed")
}

func Test_HandlePushCrawJobs_Error(t *testing.T) {
	ctx := trpc.BackgroundContext()

	// Mock the fetchAllJobs function to return error
	fetchPatch := gomonkey.ApplyFunc(fetchAllJobs, func(_ context.Context, _ int) ([]*dao.CrawJob, error) {
		t.Log("Mock fetchAllJobs called with error")
		return nil, errors.New("db")
	})
	defer fetchPatch.Reset()

	// Test that error is returned when fetchAllJobs fails
	t.Log("Testing error case...")
	if err := HandlePushCrawJobs(ctx, "", "1006", 0); err == nil {
		t.Fatalf("expect error")
	}
	t.Log("Error case passed")
}
