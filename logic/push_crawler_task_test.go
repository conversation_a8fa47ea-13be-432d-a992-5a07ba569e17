package logic

import (
	"testing"
	"time"
)

func TestCalcBacktraceDays(t *testing.T) {
	type args struct {
		crawTaskType    int
		confirmStatus   int
		backtrackStatus int
		createTime      time.Time
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "IP Trace Days",
			args: args{
				crawTaskType: 1,
			},
			want: 365 * 10,
		},
		{
			name: "CP Trace Days",
			args: args{
				crawTaskType: 3,
			},
			want: 30,
		},
		{
			name: "IP Topic Trace Days",
			args: args{
				crawTaskType: 2,
			},
			want: 365 * 10,
		},
		{
			name: "IP Trace Days but first time",
			args: args{
				crawTaskType:  1,
				confirmStatus: 2,
			},
			want: 365 * 10,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CalcBacktraceDays(tt.args.crawTaskType, tt.args.confirmStatus, tt.args.backtrackStatus, tt.args.createTime); got != tt.want {
				t.<PERSON><PERSON>("CalcBacktraceDays() = %v, want %v", got, tt.want)
			}
		})
	}
}
