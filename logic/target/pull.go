package target

import (
	"context"

	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/toolkit/logic"
)

func init() {
	logic.RegisterTarget(metadata_access.EventTargetType_PULL, &pullTarget{})
}

type pullTarget struct {
}

func (p pullTarget) PostEvent(ctx context.Context, target *metadata_access.EventTarget, event *common_event.PostEvent,
	param *logic.TargetExtParam) error {
	return nil
}

func (p pullTarget) ValidateTarget(ctx context.Context, target *metadata_access.EventTarget) error {
	return nil
}
