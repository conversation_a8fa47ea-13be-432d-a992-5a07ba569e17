package target

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/video_media/media_go_commlib/mediapkg/thirdparty/sender"
	"git.woa.com/trpcprotocol/media_event_hub/common_errcode"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/toolkit/logic"
	"git.woa.com/video_media/media_event_hub/toolkit/logic/jsonpath"
	"github.com/xeipuuv/gojsonschema"
)

// NewEventTargetEnterpriseWeChat 企业微信事件目标实现
var NewEventTargetEnterpriseWeChat = func() logic.Target {
	return &eventTargetEnterpriseWeChat{
		jsonOp: jsonpath.New(),
	}
}

func init() {
	logic.RegisterTarget(metadata_access.EventTargetType_ENTERPRISE_WECHAT_ROBOT,
		&eventTargetEnterpriseWeChat{jsonpath.New()})
}

type eventTargetEnterpriseWeChat struct {
	jsonOp logic.JsonPathOperator
}

func (e eventTargetEnterpriseWeChat) PostEvent(ctx context.Context, target *metadata_access.EventTarget,
	event *common_event.PostEvent, param *logic.TargetExtParam) error {
	if err := e.ValidateTarget(ctx, target); err != nil {
		return err
	}
	var cfg metadata_access.EventTargetEnterpriseWeChatRobot
	json.Unmarshal([]byte(target.Target), &cfg)
	// step1 根据jsonpath从data中取值
	renderVariable := make(map[string]string)
	extRenderVariable := make(map[string]string)
	for key, jp := range cfg.Variable {
		val, _ := e.jsonOp.GetString(jp, event.Event.Data)
		renderVariable[key] = val
	}
	extData, _ := json.Marshal(event.ExtData)
	for key, jp := range cfg.ExtDataVariable {
		val, _ := e.jsonOp.GetString(jp, string(extData))
		extRenderVariable[key] = val
	}
	// step2 根据模板渲染
	sendReqStr := logic.RenderTemplate(renderVariable, cfg.Template)
	sendReqStr = logic.RenderTemplate(extRenderVariable, sendReqStr)
	var sendReq sender.RobotMsg
	json.Unmarshal([]byte(sendReqStr), &sendReq)
	sendReq.ChatID = cfg.ChatId
	// step3 发送消息
	proxy := sender.NewSender(cfg.RobotUrl)
	if err := proxy.SendByRobot(ctx, &sendReq); err != nil {
		return errs.New(int(common_errcode.ErrCode_API_INVOKE_ERR), fmt.Sprintf("推送企微失败：%+v", err))
	}
	return nil
}

const enterpriseWeChatJsonSchemaStr = `{
    "type": "object",
    "required": ["msgtype"],
    "properties": {
        "msgtype": {
            "type": "string",
            "enum": ["text", "markdown"]
        },
        "text": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "mentioned_list": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "mentioned_mobile_list": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            },
            "additionalProperties": true
        },
        "markdown": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                }
            },
            "additionalProperties": true
        }
    },
    "additionalProperties": true
}
`

var enterpriseWeChatJsonSchema = gojsonschema.NewStringLoader(enterpriseWeChatJsonSchemaStr)

func (e eventTargetEnterpriseWeChat) ValidateTarget(ctx context.Context, target *metadata_access.EventTarget) error {
	if target == nil {
		return errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("事件目标-企微配置为空"))
	}
	var cfg metadata_access.EventTargetEnterpriseWeChatRobot
	json.Unmarshal([]byte(target.Target), &cfg)
	if cfg.RobotUrl == "" {
		return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL), fmt.Sprintf("企微机器人RobotURL为空"))
	}
	// 变量取值校验
	for _, jsonpath := range cfg.Variable {
		if err := e.jsonOp.ValidateJsonPath(jsonpath); err != nil {
			return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL), fmt.Sprintf("变量取值方式校验错误：%+v", err))
		}
	}
	for _, jsonpath := range cfg.ExtDataVariable {
		if err := e.jsonOp.ValidateJsonPath(jsonpath); err != nil {
			return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL), fmt.Sprintf("变量取值方式校验错误：%+v", err))
		}
	}
	// 对传入的json做一次校验
	// 加载Schema
	doc := gojsonschema.NewStringLoader(cfg.Template)
	result, err := gojsonschema.Validate(enterpriseWeChatJsonSchema, doc)
	if err != nil {
		return errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("模板未通过json schema校验，"+
			"协议参见：https://developer.work.weixin.qq.com/document/path/91770"))
	}
	if !result.Valid() {
		return errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("模板未通过json schema校验，"+
			"协议参见：https://developer.work.weixin.qq.com/document/path/91770"))
	}
	if cfg.DupDurationMill == 0 {
		cfg.DupDurationMill = (time.Minute * 3).Milliseconds()
	}
	return nil
}
