package target

import (
	"encoding/json"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/media_go_commlib/mediapkg/thirdparty/sender"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/toolkit/logic/jsonpath"
)

func Test_test(t *testing.T) {
	op := jsonpath.New()
	str := "{\"temp\": false}"
	val, err := op.GetString(".temp", str)
	log.Infof("%s, err: %+v", val, err)
}

func Test_eventTargetEnterpriseWeChat_ValidateTarget(t *testing.T) {
	var markdownTemp, _ = json.Marshal(&sender.RobotMsg{
		MsgType: "markdown",
		Markdown: struct {
			Content string `json:"content,omitempty"`
		}{
			Content: "test",
		},
	})
	var markdown, _ = json.Marshal(metadata_access.EventTargetEnterpriseWeChatRobot{
		RobotUrl:        "cc",
		Template:        string(markdownTemp),
		DupDurationMill: 0,
	})
	var textTemplate, _ = json.Marshal(&sender.RobotMsg{
		MsgType: "text",
		Text: struct {
			Content             string   `json:"content,omitempty"`
			MentionedList       []string `json:"mentioned_list,omitempty"`
			MentionedMobileList []string `json:"mentioned_mobile_list,omitempty"`
		}{Content: "test"},
	})
	var text, _ = json.Marshal(metadata_access.EventTargetEnterpriseWeChatRobot{
		RobotUrl:        "cc",
		Template:        string(textTemplate),
		DupDurationMill: 0,
	})
	var otherTemplate, _ = json.Marshal(&sender.RobotMsg{MsgType: "other"})
	var other, _ = json.Marshal(metadata_access.EventTargetEnterpriseWeChatRobot{
		RobotUrl:        "cc",
		Template:        string(otherTemplate),
		DupDurationMill: 0,
	})
	var emptyTemplate, _ = json.Marshal(&sender.RobotMsg{})
	var empty, _ = json.Marshal(metadata_access.EventTargetEnterpriseWeChatRobot{
		RobotUrl:        "cc",
		Template:        string(emptyTemplate),
		DupDurationMill: 0,
	})
	type fields struct {
		EventTargetEnterpriseWeChatRobot *metadata_access.EventTargetEnterpriseWeChatRobot
	}
	type args struct {
		target *metadata_access.EventTarget
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "markdown",
			args: args{target: &metadata_access.EventTarget{
				Target: string(markdown),
			}},
		},
		{
			name: "text",
			args: args{target: &metadata_access.EventTarget{
				Target: string(text),
			}},
		},
		{
			name: "other",
			args: args{target: &metadata_access.EventTarget{
				Target: string(other),
			}},
			wantErr: true,
		},
		{
			name:    "empty",
			args:    args{target: &metadata_access.EventTarget{Target: string(empty)}},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := eventTargetEnterpriseWeChat{
				jsonOp: jsonpath.New(),
			}
			if err := e.ValidateTarget(trpc.BackgroundContext(), tt.args.target); (err != nil) != tt.wantErr {
				t.Errorf("ValidateTarget() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
