// Package main TODO
package main

import (
	"encoding/json"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/selector"
	"git.code.oa.com/video_media/media_go_commlib/mediapkg/thirdparty/sender"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/toolkit/logic"
	_ "git.woa.com/video_media/media_event_hub/toolkit/logic/target"
)

func main() {
	selector.RegisterDefault()
	testEnterpriseWechat()
}

func testEnterpriseWechat() {
	template, _ := json.Marshal(&sender.RobotMsg{
		ChatID:  "",
		MsgType: sender.Markdown,
		Markdown: struct {
			Content string `json:"content,omitempty"`
		}{
			Content: fmt.Sprintf("测试变量渲染：${cid}，${vid}，${vid}，${not_exist}, ${ vid }, ${op}, ${ts}"),
		},
	})
	var cfg, _ = json.Marshal(&metadata_access.EventTargetEnterpriseWeChatRobot{
		RobotUrl: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1fc788d4-7edd-49f5-a5aa-b5c53a81f562",
		ChatId:   "wrkSFfCgAAteNo-XISbalBt3DGr_SXuw",
		Variable: map[string]string{
			"cid": ".cid",
			"vid": ".vid",
			"op":  ".op",
			"ts":  ".ts",
		},
		Template:        string(template),
		DupDurationMill: 0,
	})
	cfgTarget := &metadata_access.EventTarget{
		Target: string(cfg),
	}
	imp := logic.GetTarget(metadata_access.EventTargetType_ENTERPRISE_WECHAT_ROBOT)
	err := imp.PostEvent(trpc.BackgroundContext(), cfgTarget, &common_event.PostEvent{
		Event: &common_event.BaseEvent{
			Data: `{"cid": "c1", "vid": "ahahah", "ts": 20123123123}`,
		},
	}, nil)
	log.ErrorContextf(trpc.BackgroundContext(), "err: %+v", err)
}
