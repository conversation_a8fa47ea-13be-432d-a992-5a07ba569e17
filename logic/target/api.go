package target

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/trpcprotocol/media_event_hub/common_consumer"
	"git.woa.com/trpcprotocol/media_event_hub/common_errcode"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/toolkit/logic"
)

func init() {
	logic.RegisterTarget(metadata_access.EventTargetType_API, &api{})
}

type api struct {
}

func (a api) PostEvent(ctx context.Context, target *metadata_access.EventTarget, event *common_event.PostEvent,
	param *logic.TargetExtParam) error {
	if err := a.ValidateTarget(ctx, target); err != nil {
		return err
	}
	var targetAPI metadata_access.EventTargetAPI
	json.Unmarshal([]byte(target.Target), &targetAPI)
	proxy := common_consumer.NewEventHandlerClientProxy(
		client.WithNamespace(targetAPI.Addr.Namespace),
		client.WithProtocol(targetAPI.Addr.Protocol),
		client.WithTarget(targetAPI.Addr.Target),
		client.WithTimeout(time.Duration(targetAPI.Addr.Timeout)*time.Millisecond))
	req := &common_consumer.HandleEventReq{Event: event}
	if param != nil {
		req.RetryCount = int32(param.RetryCount)
		req.RetryInterval = int32(param.RetryInterval)
	}
	rsp, err := proxy.HandleEvent(ctx, &common_consumer.HandleEventReq{
		Event: event,
	})
	if err != nil {
		return err
	}
	if rsp.ErrorCode != 0 {
		return errs.New(int(rsp.ErrorCode), rsp.ErrorMsg)
	}
	return nil
}

func (a api) ValidateTarget(ctx context.Context, target *metadata_access.EventTarget) error {
	if target == nil {
		return errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("事件目标-API配置为空"))
	}
	var targetAPI metadata_access.EventTargetAPI
	json.Unmarshal([]byte(target.Target), &targetAPI)
	if targetAPI.Addr == nil {
		return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL), fmt.Sprintf("事件目标地址配置为空！"))
	}
	if targetAPI.Addr.Target == "" {
		return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL), fmt.Sprintf("事件目标地址为空！"))
	}
	if targetAPI.Addr.Namespace == "" {
		return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL), fmt.Sprintf("事件目标名称空间为空！"))
	}
	if targetAPI.Addr.Protocol == "" {
		targetAPI.Addr.Protocol = "trpc"
	}

	return nil
}
