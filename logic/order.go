package logic

// 处理通用抓取订单逻辑

import (
	"context"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/hydra/tms"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
)

// StartCrawOrder 触发抓取订单（一般是由定时任务触发）
func StartCrawOrder(ctx context.Context, orderID int, crawJobID int, JobArguments string) error {
	var crawOrderInfo dao.CrawOrder
	if err := crawOrderInfo.GetCrawOrderInfo(ctx, orderID); err != nil {
		log.ErrorContextf(ctx, "GetCrawOrderInfo ERR:%+v", err)
		return err
	}
	if JobArguments == "" { // 若没有指定抓取参数，则使用DB中配置的参数
		JobArguments = crawOrderInfo.JobArguments
	}

	var err error
	var rsp *tms.AddJobReply
	req := &tms.AddJobRequest{
		Caller: crawOrderInfo.CrawAPICaller,
		Job: &tms.Job{
			TaskID: crawOrderInfo.CrawTaskID, // 抓取侧是根据这个参数，来区分不同类型的抓取任务
			BizID: genTransmit(crawOrderInfo.CrawType, crawJobID, orderID,
				model.NewEnvTransmitFlag+"_"+time.Now().Format("20060102150405")), // 带上新链路标识
			JobArguments: JobArguments,
		},
		Auth: &tms.Auth{
			Caller: crawOrderInfo.CrawAPICaller,
			Type:   tms.AuthType_app,
			Secret: crawOrderInfo.CrawAPISecret,
		},
	}
	log.InfoContextf(ctx, "StartCrawOrder orderID:%d crawJobID:%d JobArguments:%s", orderID, crawJobID, JobArguments)
	if rsp, err = dao.InvokeCrawlerAddTask(ctx, req); err != nil {
		return err
	}
	log.InfoContextf(ctx, "StartCrawOrder suc:%+v", *rsp)
	return nil
}
