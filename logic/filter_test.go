package logic

import (
	"context"
	"testing"

	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	toolkitlogic "git.woa.com/video_media/media_event_hub/toolkit/logic"
)

// Mock implementations for testing Filter

// mockEventRuleCalculator 用于测试的事件规则计算器mock
type mockEventRuleCalculator struct {
	isMatchResult bool
	isMatchError  bool
	filterError   bool
	cutDataError  bool
}

func (m *mockEventRuleCalculator) IsEventRuleMatch(ctx context.Context, event *common_event.ProcessorEvent, rule *metadata_access.EventSubscribeRule) (bool, error) {
	if m.isMatchError {
		return false, &testError{"规则匹配失败"}
	}
	return m.isMatchResult, nil
}

func (m *mockEventRuleCalculator) FilterExtData(ctx context.Context, event *common_event.ProcessorEvent, rule *metadata_access.EventSubscribeRule) error {
	if m.filterError {
		return &testError{"过滤扩展数据失败"}
	}
	return nil
}

func (m *mockEventRuleCalculator) CutData(ctx context.Context, event *common_event.ProcessorEvent, cutJsonpath map[string]string) error {
	if m.cutDataError {
		return &testError{"裁剪数据失败"}
	}
	return nil
}

// createFilterEvent 创建过滤器测试事件
func createFilterEvent(id, eventType, subject string, withExtData bool) *common_event.ProcessorEvent {
	event := &common_event.ProcessorEvent{
		Event: &common_event.BaseEvent{Id: id, Type: eventType, Subject: subject},
	}
	if withExtData {
		event.ExtData = &common_event.ExtData{
			DataList: []*common_event.LayerData{{
				Level: 0,
				Nodes: []*common_event.Node{{
					DataId: "test_data_id",
					Values: map[string]*common_event.ValueContainer{
						"test_view": {
							ViewType: common_event.EnumViewType_UNION_VIEW, ViewId: "test_view_id",
							Fields: map[string]*common_event.FieldValue{
								"test_field": {FieldType: common_event.EnumFieldType_STR_FIELD, Str: "test_value"},
							},
						},
					},
				}},
			}},
		}
	}
	return event
}

// createSubscriberRule 创建订阅者规则
func createSubscriberRule(id int64, watchType string, withExtData bool, cutJsonpath map[string]string) *metadata_access.EventSubscribeRule {
	rule := &metadata_access.EventSubscribeRule{SubscribeId: id, WatchType: watchType}
	if withExtData {
		rule.ExtDataMatchRule = []*metadata_access.ExtDataConfig{{
			Layers: []*metadata_access.ExtDataConfigLayer{{
				Layer: 0,
				ReadCfgs: []*metadata_access.ExtDataReadCfg{{
					ViewType: common_event.EnumViewType_UNION_VIEW,
					ViewId:   "test_view_id", Fields: []string{"test_field"},
				}},
			}},
		}}
	}
	if cutJsonpath != nil {
		rule.CutJsonpath = cutJsonpath
	}
	return rule
}

// runFilterTest 执行过滤器测试的通用逻辑
func runFilterTest(t *testing.T, event *common_event.ProcessorEvent, subscribers []*metadata_access.EventSubscribeRule,
	ruleCalculator *mockEventRuleCalculator, wantErr bool, expectedResultSize int) {

	testFilter := &testFilter{ruleCalculator: ruleCalculator}
	result, err := testFilter.ProcessSubscriberEvents(context.Background(), event, subscribers)

	if (err != nil) != wantErr {
		t.Errorf("ProcessSubscriberEvents() error = %v, wantErr %v", err, wantErr)
	}
	if len(result) != expectedResultSize {
		t.Errorf("Expected result size %d, got %d", expectedResultSize, len(result))
	}
}

func TestFilter_ProcessSubscriberEvents(t *testing.T) {
	tests := []struct {
		name               string
		event              *common_event.ProcessorEvent
		subscribers        []*metadata_access.EventSubscribeRule
		ruleCalculator     *mockEventRuleCalculator
		wantErr            bool
		expectedResultSize int
	}{
		{
			name: "事件为空", event: nil, subscribers: nil,
			ruleCalculator: &mockEventRuleCalculator{isMatchResult: true}, wantErr: true, expectedResultSize: 0,
		},
		{
			name: "没有订阅者", event: createFilterEvent("test_event", "test_type", "test_subject", false),
			subscribers:    []*metadata_access.EventSubscribeRule{},
			ruleCalculator: &mockEventRuleCalculator{isMatchResult: true}, expectedResultSize: 0,
		},
		{
			name: "处理订阅者事件成功", event: createFilterEvent("test_event", "test_type", "test_subject", true),
			subscribers:    []*metadata_access.EventSubscribeRule{createSubscriberRule(1, "test_type", true, nil)},
			ruleCalculator: &mockEventRuleCalculator{isMatchResult: true}, expectedResultSize: 1,
		},
		{
			name: "规则匹配失败", event: createFilterEvent("test_event", "test_type", "test_subject", false),
			subscribers:    []*metadata_access.EventSubscribeRule{createSubscriberRule(1, "test_type", false, nil)},
			ruleCalculator: &mockEventRuleCalculator{isMatchError: true}, expectedResultSize: 0,
		},
		{
			name: "规则不匹配", event: createFilterEvent("test_event", "test_type", "test_subject", false),
			subscribers:    []*metadata_access.EventSubscribeRule{createSubscriberRule(1, "test_type", false, nil)},
			ruleCalculator: &mockEventRuleCalculator{isMatchResult: false}, expectedResultSize: 0,
		},
		{
			name: "过滤扩展数据失败", event: createFilterEvent("test_event", "test_type", "test_subject", false),
			subscribers:    []*metadata_access.EventSubscribeRule{createSubscriberRule(1, "test_type", false, nil)},
			ruleCalculator: &mockEventRuleCalculator{isMatchResult: true, filterError: true}, expectedResultSize: 0,
		},
		{
			name: "裁剪数据失败", event: createFilterEvent("test_event", "test_type", "test_subject", false),
			subscribers:    []*metadata_access.EventSubscribeRule{createSubscriberRule(1, "test_type", false, map[string]string{"root": "$.data"})},
			ruleCalculator: &mockEventRuleCalculator{isMatchResult: true, cutDataError: true}, expectedResultSize: 0,
		},
		{
			name: "包含nil订阅者", event: createFilterEvent("test_event", "test_type", "test_subject", false),
			subscribers:    []*metadata_access.EventSubscribeRule{createSubscriberRule(1, "test_type", false, nil), nil, createSubscriberRule(2, "test_type", false, nil)},
			ruleCalculator: &mockEventRuleCalculator{isMatchResult: true}, expectedResultSize: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			runFilterTest(t, tt.event, tt.subscribers, tt.ruleCalculator, tt.wantErr, tt.expectedResultSize)
		})
	}
}

// testFilter 用于测试的过滤器，复制原始ProcessSubscriberEvents逻辑但使用mock组件
type testFilter struct {
	ruleCalculator toolkitlogic.EventRuleCalculator
}

func (f *testFilter) ProcessSubscriberEvents(ctx context.Context,
	event *common_event.ProcessorEvent,
	subscribers []*metadata_access.EventSubscribeRule) (map[int64]*common_event.ProcessorEvent, error) {

	if event == nil {
		return nil, &testError{"事件数据为空"}
	}
	if len(subscribers) == 0 {
		return nil, nil
	}

	// 按订阅方分组处理结果
	result := make(map[int64]*common_event.ProcessorEvent)

	// 为每个订阅方处理事件
	for _, subscriber := range subscribers {
		if subscriber == nil {
			continue
		}

		// 1. 生成订阅方专属的ProcessorEvent
		subscriberEvent := &common_event.ProcessorEvent{
			Event: event.Event, // 简化实现，直接复制事件
		}

		// 2. 判断事件是否满足订阅方配置的条件
		matched, err := f.ruleCalculator.IsEventRuleMatch(ctx, subscriberEvent, subscriber)
		if err != nil {
			continue // 跳过规则匹配出错的订阅方
		}
		if !matched {
			continue // 跳过不匹配的订阅方
		}

		// 按订阅规则，过滤订阅消息
		if err := f.ruleCalculator.FilterExtData(ctx, subscriberEvent, subscriber); err != nil {
			continue // 跳过处理出错的订阅方
		}

		// 3. 裁剪事件数据，生成订阅方专属的ProcessorEvent
		if err := f.ruleCalculator.CutData(ctx, subscriberEvent, subscriber.CutJsonpath); err != nil {
			continue // 跳过处理出错的订阅方
		}

		result[subscriber.SubscribeId] = subscriberEvent
	}
	return result, nil
}

func TestFilter_tailorEventForSubscriber(t *testing.T) {
	tests := []struct {
		name        string
		originEvent *common_event.ProcessorEvent
		subscriber  *metadata_access.EventSubscribeRule
		wantErr     bool
		wantNil     bool
	}{
		{
			name:        "原始事件为空",
			originEvent: nil,
			subscriber:  &metadata_access.EventSubscribeRule{},
			wantErr:     true,
			wantNil:     true,
		},
		{
			name: "订阅者为空",
			originEvent: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{},
			},
			subscriber: nil,
			wantErr:    true,
			wantNil:    true,
		},
		{
			name: "裁剪事件成功",
			originEvent: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{
					Id:      "test_event",
					Type:    "test_type",
					Subject: "test_subject",
				},
			},
			subscriber: &metadata_access.EventSubscribeRule{
				SubscribeId: 1,
				WatchType:   "test_type",
			},
			wantErr: false,
			wantNil: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := NewFilter()
			result, err := filter.tailorEventForSubscriber(tt.originEvent, tt.subscriber)

			if (err != nil) != tt.wantErr {
				t.Errorf("tailorEventForSubscriber() error = %v, wantErr %v", err, tt.wantErr)
			}

			if (result == nil) != tt.wantNil {
				t.Errorf("tailorEventForSubscriber() result = %v, wantNil %v", result, tt.wantNil)
			}

			if !tt.wantErr && !tt.wantNil {
				if result.Event != tt.originEvent.Event {
					t.Errorf("Event not copied correctly")
				}
			}
		})
	}
}

func TestFilter_extractExtData(t *testing.T) {
	tests := []struct {
		name          string
		sourceExtData *common_event.ExtData
		extRules      []*metadata_access.ExtDataConfig
		wantNil       bool
	}{
		{
			name:          "源扩展数据为空",
			sourceExtData: nil,
			extRules:      []*metadata_access.ExtDataConfig{},
			wantNil:       true,
		},
		{
			name: "源扩展数据为空数组",
			sourceExtData: &common_event.ExtData{
				DataList: []*common_event.LayerData{},
			},
			extRules: []*metadata_access.ExtDataConfig{},
			wantNil:  true,
		},
		{
			name: "规则为空",
			sourceExtData: &common_event.ExtData{
				DataList: []*common_event.LayerData{
					{
						Level: 0,
						Nodes: []*common_event.Node{},
					},
				},
			},
			extRules: []*metadata_access.ExtDataConfig{},
			wantNil:  true,
		},
		{
			name: "提取扩展数据成功",
			sourceExtData: &common_event.ExtData{
				DataList: []*common_event.LayerData{
					{
						Level: 0,
						Nodes: []*common_event.Node{
							{
								DataId: "test_data_id",
								Values: map[string]*common_event.ValueContainer{
									"test_view": {
										ViewType: common_event.EnumViewType_UNION_VIEW,
										ViewId:   "test_view_id",
										Fields: map[string]*common_event.FieldValue{
											"test_field": {
												FieldType: common_event.EnumFieldType_STR_FIELD,
												Str:       "test_value",
											},
										},
									},
								},
							},
						},
					},
				},
			},
			extRules: []*metadata_access.ExtDataConfig{
				{
					Layers: []*metadata_access.ExtDataConfigLayer{
						{
							Layer: 0,
							ReadCfgs: []*metadata_access.ExtDataReadCfg{
								{
									ViewType: common_event.EnumViewType_UNION_VIEW,
									ViewId:   "test_view_id",
									Fields:   []string{"test_field"},
								},
							},
						},
					},
				},
			},
			wantNil: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := NewFilter()
			result := filter.extractExtData(tt.sourceExtData, tt.extRules)

			if (result == nil) != tt.wantNil {
				t.Errorf("extractExtData() result = %v, wantNil %v", result, tt.wantNil)
			}

			if !tt.wantNil && result != nil {
				if len(result.DataList) != len(tt.sourceExtData.DataList) {
					t.Errorf("Expected %d layers, got %d", len(tt.sourceExtData.DataList), len(result.DataList))
				}
			}
		})
	}
}

func TestFilter_filterNodeFields(t *testing.T) {
	tests := []struct {
		name       string
		sourceNode *common_event.Node
		readCfgs   []*metadata_access.ExtDataReadCfg
		wantNil    bool
	}{
		{
			name:       "源节点为空",
			sourceNode: nil,
			readCfgs:   []*metadata_access.ExtDataReadCfg{},
			wantNil:    true,
		},
		{
			name: "读取配置为空",
			sourceNode: &common_event.Node{
				DataId: "test_data_id",
			},
			readCfgs: []*metadata_access.ExtDataReadCfg{},
			wantNil:  true,
		},
		{
			name: "过滤节点字段成功",
			sourceNode: &common_event.Node{
				DataId: "test_data_id",
				Values: map[string]*common_event.ValueContainer{
					"1:test_view_id": {
						ViewType: common_event.EnumViewType_UNION_VIEW,
						ViewId:   "test_view_id",
						Fields: map[string]*common_event.FieldValue{
							"test_field": {
								FieldType: common_event.EnumFieldType_STR_FIELD,
								Str:       "test_value",
							},
							"other_field": {
								FieldType: common_event.EnumFieldType_STR_FIELD,
								Str:       "other_value",
							},
						},
					},
				},
			},
			readCfgs: []*metadata_access.ExtDataReadCfg{
				{
					ViewType: common_event.EnumViewType_UNION_VIEW,
					ViewId:   "test_view_id",
					Fields:   []string{"test_field"},
				},
			},
			wantNil: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := NewFilter()
			result := filter.filterNodeFields(tt.sourceNode, tt.readCfgs)

			if (result == nil) != tt.wantNil {
				t.Errorf("filterNodeFields() result = %v, wantNil %v", result, tt.wantNil)
			}

			if !tt.wantNil && result != nil {
				if result.DataId != tt.sourceNode.DataId {
					t.Errorf("DataId not copied correctly")
				}
			}
		})
	}
}

func TestNewFilter(t *testing.T) {
	filter := NewFilter()

	if filter == nil {
		t.Errorf("NewFilter() returned nil")
	}

	if filter.ruleCalculator == nil {
		t.Errorf("NewFilter() ruleCalculator is nil")
	}
}
