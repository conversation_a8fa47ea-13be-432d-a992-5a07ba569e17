// Package logic 逻辑层
package logic

import (
	"context"
	"sync"

	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
)

// EventRuleCalculator 事件规则计算接口
type EventRuleCalculator interface {
	IsEventRuleMatch(ctx context.Context,
		event *common_event.ProcessorEvent, rule *metadata_access.EventSubscribeRule) (bool, error) // 事件和事件规则是否匹配
	FilterExtData(ctx context.Context, event *common_event.ProcessorEvent,
		rule *metadata_access.EventSubscribeRule) error
	CutData(ctx context.Context, event *common_event.ProcessorEvent, cuts map[string]string) error
}

// JsonPathOperator jsonPath接口定义
type JsonPathOperator interface {
	ValidateJsonPath(jsonpath string) error
	GetString(jsonpath, data string) (string, error)
	GetStrings(jsonpath, data string) ([]string, error)
	GetInt(jsonpath, data string) (int, error)
	GetIntArr(jsonpath, data string) ([]int, error)
	GetFloat(jsonpath, data string) (float64, error)
	GetFloatArr(jsonpath, data string) ([]float64, error)
	GetBool(jsonpath, data string) (bool, error)
	GetBoolArr(jsonpath, data string) ([]bool, error)
	CutJsonStr(str string, rules map[string]string) (string, error) // 根据gojq规则，对json进行加工计算
}

// Target 事件目标接口定义
type Target interface {
	PostEvent(ctx context.Context, target *metadata_access.EventTarget, event *common_event.PostEvent,
		param *TargetExtParam) error // 投递事件至事件目标
	ValidateTarget(
		ctx context.Context, target *metadata_access.EventTarget) error // 校验事件目标元数据配置
}

// TargetExtParam 事件目标的扩展参数，不需要可不填
type TargetExtParam struct {
	RetryCount    int // 已经重试的次数
	RetryInterval int // 当前重试的间隔
}

var targetImp sync.Map

// RegisterTarget 注册事件目标实现
func RegisterTarget(tp metadata_access.EventTargetType, t Target) {
	targetImp.Store(tp, t)
}

// GetTarget 获取事件目标实现
func GetTarget(tp metadata_access.EventTargetType) Target {
	tmp, exist := targetImp.Load(tp)
	if !exist || tmp == nil {
		return nil
	}
	return tmp.(Target)
}
