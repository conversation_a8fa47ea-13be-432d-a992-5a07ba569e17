package logic

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/galileo/trpc-go-galileo/logs"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/galileo/eco/go/sdk/base"
	metadata "git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/processor/config"
	toolkitrepo "git.woa.com/video_media/media_event_hub/toolkit/repo"

	"github.com/IBM/sarama"
	"github.com/avast/retry-go"
	"go.opentelemetry.io/otel/trace"

	// 监控上报包
	_ "git.code.oa.com/trpc-go/trpc-go/metrics"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
)

// eventConsumer 实现sarama.ConsumerGroupHandler接口
type eventConsumer struct {
	ctx             context.Context
	bridgeID        int64
	topic           string
	processor       *EventBridgeProcessor
	bridgeInfo      *metadata.EventBridge
	metaReader      toolkitrepo.EventMetaReader
	config          *config.Config
	metricsReporter *MetricsReporter
}

// Setup 消费者组会话建立时的钩子函数
func (c *eventConsumer) Setup(sarama.ConsumerGroupSession) error {
	return nil
}

// Cleanup 消费者组会话清理时的钩子函数
func (c *eventConsumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim 处理分配给消费者的消息分区
func (c *eventConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for message := range claim.Messages() {
		// 记录ConsumeClaim开始时间
		startTime := time.Now()

		_ = base.WithSpan(
			trpc.CloneContext(c.ctx), "ConsumeClaim",
			func(ctx context.Context) error {
				// 监控数据统计变量
				var reqCount, errCount, successCount float64 = 1, 0, 0
				var delay float64 = 0

				// 解析事件消息获取traceID
				event, traceID, err := c.processor.ParseEventMessage(ctx, message)
				if err != nil {
					log.ErrorContextf(ctx, "解析事件消息失败，事件总线ID: %d, 错误: %v", c.bridgeID, err)
					errCount = 1
					c.markMessageProcessed(ctx, session, message)
					c.reportMetrics(reqCount, errCount, successCount, delay, startTime)
					return nil
				}
				if event == nil {
					successCount = 1 // 空事件也算成功处理
					c.markMessageProcessed(ctx, session, message)
					c.reportMetrics(reqCount, errCount, successCount, delay, startTime)
					return nil
				}

				// 计算消费延迟（当前时间戳 - 事件中的时间戳 秒）
				if event.Event != nil && event.Event.Timestamp > 0 {
					delay = float64(time.Now().Unix() - event.Event.Timestamp)
				}

				// 设置trace context并处理消息
				newCtx := c.createTraceContext(ctx, traceID)
				logs.SetContextLogger(newCtx)

				// 处理事件消息
				processErr := base.WithSpan(newCtx, "ProcessEventMessage", func(ctx context.Context) error {
					logs.SetContextLogger(ctx)
					return c.processEventWithRetry(ctx, message)
				})
				if processErr != nil {
					errCount = 1
				} else {
					successCount = 1
				}

				c.markMessageProcessed(ctx, session, message) // 无论是否成功处理，都标记消息已处理
				c.reportMetrics(reqCount, errCount, successCount, delay, startTime)
				return nil
			},
		)
	}
	return nil
}

// createTraceContext 创建带有traceID的context
func (c *eventConsumer) createTraceContext(cx context.Context, traceID string) context.Context {
	if traceID == "" {
		log.WarnContextf(cx, "事件中没有traceID，使用原始context")
		return cx
	}

	traceIDBytes, err := trace.TraceIDFromHex(traceID)
	if err != nil {
		log.WarnContextf(cx, "解析traceID失败，使用原始context，traceID: %s, 错误: %v", traceID, err)
		return cx
	}

	sc := trace.NewSpanContext(
		trace.SpanContextConfig{
			TraceID:    traceIDBytes,
			SpanID:     trace.SpanID([8]byte{255}),
			TraceFlags: trace.FlagsSampled,
		},
	)
	return trace.ContextWithRemoteSpanContext(cx, sc)
}

// processEventWithRetry 带重试的事件处理
func (c *eventConsumer) processEventWithRetry(ctx context.Context, message *sarama.ConsumerMessage) error {
	// 从配置中获取重试参数，如果获取失败则使用默认值
	retryCount, retryInterval := c.getRetryConfig(ctx)

	err := retry.Do(
		func() error {
			return c.processor.ProcessEventMessage(ctx, message)
		},
		retry.Attempts(uint(retryCount)),
		retry.Delay(retryInterval),
		retry.LastErrorOnly(true),
		retry.OnRetry(func(n uint, err error) {
			log.InfoContextf(ctx, "处理事件消息重试中(%d/%d)，事件总线ID: %d, 错误: %v",
				n, retryCount, c.bridgeID, err)
		}),
	)
	if err != nil {
		log.ErrorContextf(ctx, "消息丢弃：处理事件消息重试%d次后仍然失败，事件总线ID: %d, 错误: %v",
			retryCount, c.bridgeID, err)
		return err
	}
	return nil
}

// markMessageProcessed 标记消息已处理
func (c *eventConsumer) markMessageProcessed(ctx context.Context, session sarama.ConsumerGroupSession, message *sarama.ConsumerMessage) {
	session.MarkMessage(message, "")
	log.InfoContextf(ctx, "### 消息已提交, 消息ID: %s, 分区: %d, 偏移量: %d",
		string(message.Key), message.Partition, message.Offset)
}

// getRetryConfig 从配置中获取重试参数，如果获取失败则使用默认值
func (c *eventConsumer) getRetryConfig(ctx context.Context) (int, time.Duration) {
	// 默认值
	const defaultRetryCount = 10
	const defaultRetryInterval = 100 * time.Millisecond

	// 如果没有配置，使用默认值
	if c.config == nil {
		log.WarnContextf(ctx, "配置为空，使用默认重试参数：重试次数=%d, 重试间隔=%v", defaultRetryCount, defaultRetryInterval)
		return defaultRetryCount, defaultRetryInterval
	}

	// 从配置中获取重试参数
	retryConfig := c.config.GetRetryConfig(c.bridgeID)
	if retryConfig == nil {
		log.WarnContextf(ctx, "获取事件总线 %d 的重试配置失败，使用默认重试参数", c.bridgeID)
		return defaultRetryCount, defaultRetryInterval
	}

	// 读取配置中的重试次数
	retryCount := retryConfig.RetryCount
	if retryCount <= 0 {
		log.WarnContextf(ctx, "配置中的重试次数无效 (%d)，使用默认值 %d", retryCount, defaultRetryCount)
		retryCount = defaultRetryCount
	}

	// 读取配置中的重试间隔（毫秒）
	retryIntervalMs := retryConfig.RetryIntervalMs
	if retryIntervalMs <= 0 {
		log.WarnContextf(ctx, "配置中的重试间隔无效 (%d ms)，使用默认值 %v", retryIntervalMs, defaultRetryInterval)
		return retryCount, defaultRetryInterval
	}

	retryInterval := time.Duration(retryIntervalMs) * time.Millisecond
	log.InfoContextf(ctx, "从配置读取重试参数：重试次数=%d, 重试间隔=%v", retryCount, retryInterval)
	return retryCount, retryInterval
}

// reportMetrics 上报监控数据
func (c *eventConsumer) reportMetrics(reqCount, errCount, successCount, delay float64, startTime time.Time) {
	// 计算总花费时间（毫秒）
	cost := float64(time.Since(startTime).Milliseconds())

	// 上报监控数据
	if c.metricsReporter != nil {
		c.metricsReporter.ReportMetrics(reqCount, errCount, successCount, delay, cost)
	}
}
