package logic

// 本模块实现事件数据的过滤逻辑（把经过Extender字段扩展后的事件，再次裁剪、过滤）

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/trpcprotocol/media_event_hub/common_errcode"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/processor/utils"
	toolkitlogic "git.woa.com/video_media/media_event_hub/toolkit/logic"
	toolkitrule "git.woa.com/video_media/media_event_hub/toolkit/logic/rule"
)

// Filter 过滤处理器，实现订阅方事件过滤功能
type Filter struct {
	ruleCalculator toolkitlogic.EventRuleCalculator
}

// NewFilter 创建新的过滤处理器
func NewFilter() *Filter {
	return &Filter{
		ruleCalculator: toolkitrule.NewEventRuleCalculator(),
	}
}

// ProcessSubscriberEvents 处理订阅方事件，为每个订阅方生成专属ProcessorEvent
// 参数：event 事件基础数据（包含订阅该事件的所有订阅方关注的扩展字段信息）
// 参数：subscribers 订阅该事件的所有订阅方信息
// 返回值：每个订阅方关注的事件基础数据
func (f *Filter) ProcessSubscriberEvents(ctx context.Context,
	event *common_event.ProcessorEvent,
	subscribers []*metadata_access.EventSubscribeRule) (map[int64]*common_event.ProcessorEvent, error) {
	if event == nil {
		return nil, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), "事件数据为空")
	}
	if len(subscribers) == 0 {
		return nil, nil
	}

	// 按订阅方分组处理结果
	result := make(map[int64]*common_event.ProcessorEvent)

	// 为每个订阅方处理事件
	for _, subscriber := range subscribers {
		if subscriber == nil {
			continue
		}

		// 1. 生成订阅方专属的ProcessorEvent
		subscriberEvent, err := f.tailorEventForSubscriber(event, subscriber)
		if err != nil {
			log.ErrorContextf(ctx, "裁剪事件数据失败, subscriberID: %d, error: %v",
				subscriber.SubscribeId, err)
			continue // 跳过处理出错的订阅方
		}

		// 2. 判断事件是否满足订阅方配置的条件;
		subscriberEventJson, _ := json.Marshal(subscriberEvent)
		subscriberJson, _ := json.Marshal(subscriber)
		log.InfoContextf(ctx, "subscriberEvent: %s;", subscriberEventJson)
		log.InfoContextf(ctx, "subscriberJson: %s;", subscriberJson)
		matched, err := f.ruleCalculator.IsEventRuleMatch(ctx, subscriberEvent, subscriber)
		if err != nil {
			log.ErrorContextf(ctx, "判断事件是否满足订阅方配置的过滤条件失败, subscriberID: %d, error: %v",
				subscriber.SubscribeId, err)
			continue // 跳过规则匹配出错的订阅方
		}
		if !matched {
			log.InfoContextf(ctx, "事件不满足订阅方配置的过滤条件, subscriberID: %d, eventID: %s",
				subscriber.SubscribeId, subscriberEvent.Event.Id)
			continue // 跳过不匹配的订阅方
		}

		// 按订阅规则，过滤订阅消息
		if err := f.ruleCalculator.FilterExtData(ctx, subscriberEvent, subscriber); err != nil {
			log.ErrorContextf(ctx, "过滤订阅消息失败, subscriberID: %d, error: %v",
				subscriber.SubscribeId, err)
			continue // 跳过处理出错的订阅方
		}

		// 3. 裁剪事件数据，生成订阅方专属的ProcessorEvent
		if err := f.ruleCalculator.CutData(ctx, subscriberEvent, subscriber.CutJsonpath); err != nil {
			log.ErrorContextf(ctx, "裁剪事件数据失败, subscriberID: %d, error: %v, CutJsonpath:%+v",
				subscriber.SubscribeId, err, subscriber.CutJsonpath)
			continue // 跳过处理出错的订阅方
		}
		subscriberEventJson, _ = json.Marshal(subscriberEvent)
		log.InfoContextf(ctx, "after subscriberEvent: %s;", subscriberEventJson)
		result[subscriber.SubscribeId] = subscriberEvent
	}
	return result, nil
}

// tailorEventForSubscriber 根据订阅方配置裁剪事件数据
func (f *Filter) tailorEventForSubscriber(
	originEvent *common_event.ProcessorEvent,
	subscriber *metadata_access.EventSubscribeRule) (*common_event.ProcessorEvent, error) {
	if originEvent == nil || subscriber == nil {
		return nil, fmt.Errorf("原始事件或订阅方配置为空")
	}

	// 深度复制原始事件的基本信息
	subscriberEvent := &common_event.ProcessorEvent{
		Event: originEvent.Event, // 事件基础信息保持不变
	}

	// 处理扩展数据，根据订阅方规则裁剪
	if originEvent.ExtData != nil && len(subscriber.ExtDataMatchRule) > 0 {
		subscriberEvent.ExtData = f.extractExtData(originEvent.ExtData, subscriber.ExtDataMatchRule)
	}
	return subscriberEvent, nil
}

// extractExtData 根据订阅方配置的扩展数据规则提取相关字段
// 参数：sourceExtData 为原始的包含系统所有扩展字段的扩展数据
// 参数：extRules 为某个订阅方的所有层级配置
func (f *Filter) extractExtData(
	sourceExtData *common_event.ExtData,
	extRules []*metadata_access.ExtDataConfig) *common_event.ExtData {
	if sourceExtData == nil || len(sourceExtData.DataList) == 0 || len(extRules) == 0 {
		return nil
	}

	// 创建新的扩展数据对象
	result := &common_event.ExtData{
		DataList: make([]*common_event.LayerData, len(sourceExtData.DataList)),
	}

	// 为每一层创建空的LayerData
	for i := range result.DataList {
		result.DataList[i] = &common_event.LayerData{
			Level: int32(i),
			Nodes: []*common_event.Node{},
		}
	}

	// 根据规则处理每一层数据
	for _, rule := range extRules {
		if rule == nil || len(rule.Layers) == 0 {
			continue
		}

		for _, layer := range rule.Layers {
			if layer == nil || int(layer.Layer) >= len(sourceExtData.DataList) {
				continue
			}

			// 获取源数据和目标数据的当前层
			sourceLayer := sourceExtData.DataList[layer.Layer]
			targetLayer := result.DataList[layer.Layer]
			if sourceLayer == nil || len(sourceLayer.Nodes) == 0 {
				continue
			}

			// 处理当前层的节点数据
			for _, sourceNode := range sourceLayer.Nodes {
				if sourceNode == nil {
					continue
				}

				// 只保留需要的字段
				if filteredNode := f.filterNodeFields(sourceNode, layer.ReadCfgs); filteredNode != nil {
					targetLayer.Nodes = append(targetLayer.Nodes, filteredNode)
				}
			}
		}
	}
	return result
}

// filterNodeFields 根据配置提取节点中需要的字段
func (f *Filter) filterNodeFields(
	sourceNode *common_event.Node,
	readCfgs []*metadata_access.ExtDataReadCfg) *common_event.Node {
	if sourceNode == nil || len(readCfgs) == 0 {
		return nil
	}

	// 创建新节点，只保留数据ID
	filteredNode := &common_event.Node{
		DataId: sourceNode.DataId,
		Values: make(map[string]*common_event.ValueContainer),
	}

	// 如果有父引用信息，也要保留
	if sourceNode.ParentRef != nil {
		filteredNode.ParentRef = sourceNode.ParentRef
	}

	// 处理每个读取配置
	for _, readCfg := range readCfgs {
		if readCfg == nil {
			continue
		}

		// 构建视图Key
		viewKey := utils.FormatViewKey(readCfg.ViewType, readCfg.ViewId)

		// 从源节点中获取对应视图的值容器
		sourceContainer, exists := sourceNode.Values[viewKey]
		if !exists || sourceContainer == nil {
			continue
		}

		// 创建新的值容器，保留视图信息
		filteredContainer := &common_event.ValueContainer{
			ViewType: sourceContainer.ViewType,
			ViewId:   sourceContainer.ViewId,
			Fields:   make(map[string]*common_event.FieldValue),
		}

		// 只保留配置中指定的字段
		if len(readCfg.Fields) > 0 {
			for _, fieldName := range readCfg.Fields {
				if fieldValue, exists := sourceContainer.Fields[fieldName]; exists && fieldValue != nil {
					filteredContainer.Fields[fieldName] = fieldValue
				}
			}
		}

		// 只有当有字段被保留时才添加到结果中
		if len(filteredContainer.Fields) > 0 {
			filteredNode.Values[viewKey] = filteredContainer
		}
	}

	// 只有当有值容器被保留时才返回节点
	if len(filteredNode.Values) > 0 {
		return filteredNode
	}
	return nil
}
