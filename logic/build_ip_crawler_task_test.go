package logic

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"github.com/agiledragon/gomonkey/v2"
)

func Test_calcCrawName(t *testing.T) {
	type args struct {
		coverType int
		title     string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
	}{
		{
			name: "case1",
			args: args{
				coverType: 10,
				title:     "恕我直言",
			},
			want:  "恕我直言",
			want1: "恕我直言",
		},
		{
			name: "case2",
			args: args{
				coverType: 10,
				title:     "令人心动的offer第2季",
			},
			want:  "令人心动的offer第2季",
			want1: "令人心动的offer",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := calcCrawName(tt.args.coverType, tt.args.title)
			if got != tt.want {
				t.Errorf("calcCrawName() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("calcCrawName() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestHandleBuildIPCrawTask(t *testing.T) {
	var coverInfo *dao.CoverInfo
	gomonkey.ApplyMethod(reflect.TypeOf(coverInfo), "GetAllCoverInfo",
		func(_ *dao.CoverInfo, ctx context.Context) ([]*dao.CoverInfo, error) {
			return []*dao.CoverInfo{
				{
					CoverType:      1,
					Title:          "titleTest",
					OfficeAccFlag:  1,
					TopicFlag:      1,
					IPOnlineStatus: 8377974,
				},
			}, nil
		})
	var ipCrawTask *dao.IPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(ipCrawTask), "InsertIPCrawInfo",
		func(_ *dao.IPCrawTask, ctx context.Context) error {
			return nil
		})

	type args struct {
		ctx context.Context
		in1 string
		in2 string
		in3 int32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := HandleBuildIPCrawTask(tt.args.ctx, tt.args.in1, tt.args.in2, tt.args.in3); (err != nil) != tt.wantErr {
				t.Errorf("HandleBuildIPCrawTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

type GetCrawPlatformsFunc func(int) []string

// 定义自定义变量并分配初始值
var getCrawPlatforms GetCrawPlatformsFunc = conf.GetCrawPlatforms

func TestGenCrawInfo(t *testing.T) {
	type args struct {
		orderID  int
		crawType int
		info     *dao.CoverInfo
	}
	tests := []struct {
		name              string
		args              args
		want              []*dao.IPCrawTask
		mockCrawPlatforms GetCrawPlatformsFunc
	}{
		{
			name: "Case 1: Example of crawType and CoverInfo",
			args: args{
				orderID:  model.IPAccountOrder,
				crawType: model.OfficeAccountType,
				info: &dao.CoverInfo{
					CoverType:      1,
					Title:          "ExampleTitle",
					IPOnlineStatus: 8377974,
					PriorityFlag:   1,
				},
			},
			want: func() []*dao.IPCrawTask {
				crawInfos := make([]*dao.IPCrawTask, 0)
				// ... 您的期望 isValid 和 isPreValid 的实现 ...
				return crawInfos
			}(),
			mockCrawPlatforms: func(crawType int) []string {
				return []string{"platform1", "platform2"}
			},
		},
		// TODO: 添加其他穷举
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			temp := getCrawPlatforms // 备份原始函数
			getCrawPlatforms = tt.mockCrawPlatforms
			genCrawInfo(tt.args.orderID, tt.args.crawType, tt.args.info)
			getCrawPlatforms = temp // 恢复原始函数
		})
	}
}

func Test_createCrawInfo(t *testing.T) {
	type args struct {
		orderID  int
		crawType int
		platform string
		info     *dao.CoverInfo
	}
	tests := []struct {
		name string
		args args
		want *dao.IPCrawTask
	}{
		{
			name: "Case 1: Office Account Type, info.OfficeAccFlag is 1 and isOnline is true",
			args: args{
				orderID:  1001,
				crawType: model.OfficeAccountType,
				platform: "test_platform",
				info: &dao.CoverInfo{
					CoverID:        "aaaaaa",
					CoverType:      2,
					Title:          "Cover Title",
					OfficeAccFlag:  1,
					IPOnlineStatus: 1,
					PriorityFlag:   3,
				},
			},
			want: &dao.IPCrawTask{
				OrderID:      1001,
				CrawType:     model.OfficeAccountType,
				Platform:     "test_platform",
				CoverID:      "aaaaaa",
				CoverType:    2,
				PriorityFlag: 3,
				Valid:        0,
				CrawKey:      "CoverTitle",
			},
		},
		// 添加其他测试用例
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := createCrawInfo(tt.args.orderID, tt.args.crawType,
				tt.args.platform, tt.args.info); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("createCrawInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_createTopicName(t *testing.T) {
	type args struct {
		coverType int
		title     string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Case 1: movies",
			args: args{
				coverType: movies,
				title:     "MyMovie",
			},
			want: "电影MyMovie",
		},
		{
			name: "Case 2: teleplay",
			args: args{
				coverType: teleplay,
				title:     "MyTeleplay",
			},
			want: "MyTeleplay",
		},
		{
			name: "Case 3: anime",
			args: args{
				coverType: anime,
				title:     "MyAnime",
			},
			want: "MyAnime动画",
		},
		// 添加其他测试用例
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := createTopicName(tt.args.coverType, tt.args.title); got != tt.want {
				t.Errorf("createTopicName() = %v, want %v", got, tt.want)
			}
		})
	}
}
