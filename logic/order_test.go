package logic

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpcprotocol/hydra/tms"
	"github.com/agiledragon/gomonkey/v2"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
)

func TestStartCrawOrder(t *testing.T) {
	ctx := context.Background()

	// patch CrawOrder.GetCrawOrderInfo
	var co *dao.CrawOrder
	p1 := gomonkey.ApplyMethod(reflect.TypeOf(co), "GetCrawOrderInfo",
		func(_ *dao.CrawOrder, _ context.Context, _ int) error {
			// set minimal fields via return object address not accessible, so simulate by
			// filling a package-level variable through subsequent patches using the req
			return nil
		})
	defer p1.Reset()

	// patch InvokeCrawlerAddTask and assert JobArguments defaults
	called := false
	p2 := gomonkey.ApplyFunc(dao.InvokeCrawlerAddTask,
		func(_ context.Context, req *tms.AddJobRequest) (*tms.AddJobReply, error) {
			called = true
			if req.Job.JobArguments == "" {
				t.Fatalf("expect default JobArguments applied")
			}
			return &tms.AddJobReply{Code: 0}, nil
		})
	defer p2.Reset()

	// Patch CrawOrder.GetCrawOrderInfo again but fill fields on the receiver
	p1.Reset()
	p1 = gomonkey.ApplyMethod(reflect.TypeOf(co), "GetCrawOrderInfo",
		func(c *dao.CrawOrder, _ context.Context, _ int) error {
			c.CrawAPICaller = "caller"
			c.CrawTaskID = "task"
			c.CrawAPISecret = "secret"
			c.JobArguments = "default-args"
			c.CrawType = 1
			return nil
		})
	defer p1.Reset()

	if err := StartCrawOrder(ctx, 1006, 1, ""); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
	if !called {
		t.Fatalf("InvokeCrawlerAddTask not called")
	}
}
