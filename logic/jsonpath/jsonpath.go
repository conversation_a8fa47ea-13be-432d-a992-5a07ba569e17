// Package jsonpath jsonpath操作接口实现
package jsonpath

import (
	"encoding/json"
	"fmt"
	"math"
	"reflect"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/trpcprotocol/media_event_hub/common_errcode"
	"git.woa.com/video_media/media_event_hub/toolkit/logic"
	"github.com/itchyny/gojq"
)

// operators 实现 JsonPathOperator 接口，并使用缓存优化
type operators struct{}

// New 创建JsonPathOperator实例
func New() logic.JsonPathOperator {
	return &operators{}
}

// ValidateJsonPath 验证jsonpath语法
func (o *operators) ValidateJsonPath(jsonpath string) error {
	_, err := o.compileJsonPath(jsonpath)
	return err
}

// getResult 执行查询并返回结果
func (o *operators) getResult(query *gojq.Query, data string) ([]interface{}, error) {
	var input interface{}
	if err := json.Unmarshal([]byte(data), &input); err != nil {
		return nil, fmt.Errorf("invalid json data: %w", err)
	}
	iter := query.Run(input)
	var results []interface{}
	for {
		v, ok := iter.Next()
		if !ok {
			break
		}
		if err, ok := v.(error); ok {
			return nil, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM),
				fmt.Sprintf("jsonpath query err: %+v", err))
		}
		results = append(results, v)
	}
	if len(results) == 0 || results[0] == nil {
		return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY),
			"query result empty")
	}
	return results, nil
}

// GetString 获取单个字符串结果
func (o *operators) GetString(jsonpath, data string) (string, error) {
	if data == "" {
		return data, nil
	}
	query, err := o.compileJsonPath(jsonpath)
	if err != nil {
		return "", err
	}
	results, err := o.getResult(query, data)
	if err != nil {
		return "", err
	}
	switch v := results[0].(type) {
	case string:
		return v, nil
	case float64:
		if v == math.Trunc(v) {
			return fmt.Sprintf("%d", int(v)), nil
		}
		return fmt.Sprintf("%g", v), nil
	case int:
		return fmt.Sprintf("%d", v), nil
	default:
		return fmt.Sprintf("%v", v), nil
	}
}

func (o *operators) GetBool(jsonpath, data string) (bool, error) {
	if data == "" {
		return false, nil
	}
	query, err := o.compileJsonPath(jsonpath)
	if err != nil {
		return false, err
	}
	results, err := o.getResult(query, data)
	if err != nil {
		return false, err
	}
	rst, ok := results[0].(bool)
	if !ok {
		return false, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
			fmt.Sprintf("except type: bool, autual: %s", reflect.TypeOf(results[0]).Name()))
	}
	return rst, nil
}

func (o *operators) CutJsonStr(str string, rules map[string]string) (string, error) {
	if str == "" {
		return str, nil
	}
	// 解析原始JSON
	var input interface{}
	if err := json.Unmarshal([]byte(str), &input); err != nil {
		return "", errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("json解析失败：%+v", err))
	}
	output := make(map[string]interface{})
	// 遍历处理每条规则
	for key, queryStr := range rules {
		query, err := o.compileJsonPath(queryStr)
		if err != nil {
			return "", err
		}
		// 执行查询
		iter := query.Run(input)
		// 收集结果（处理可能的多值情况）
		var results []interface{}
		for {
			v, ok := iter.Next()
			if !ok {
				break
			}
			if err, ok := v.(error); ok {
				return "", errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("执行查询[%s]异常：%+v", queryStr, err))
			}
			results = append(results, v)
		}
		// 根据结果数量决定存储格式
		switch len(results) {
		case 0:
			output[key] = nil // 路径不存在设为null
		case 1:
			output[key] = results[0] // 单值直接存储
		default:
			output[key] = results // 多值转为数组
		}
	}
	resultJSON, _ := json.Marshal(output)
	return string(resultJSON), nil
}

func (o *operators) renderJsonpath(jp, id, data string) (string, bool, error) {
	vars := logic.GetVariables(jp)
	if len(vars) == 0 {
		return jp, true, nil
	}
	in := map[string]string{}
	for _, v := range vars {
		// 如果v == .id，取ID
		if v == ".id" {
			in[v] = id
			continue
		}
		str, err := o.GetString(v, data)
		if err != nil {
			return "", false, err
		}
		in[v] = str
	}
	return logic.RenderTemplate(in, jp), true, nil
}

// compileJsonPath 编译jsonpath并缓存结果
func (o *operators) compileJsonPath(jsonpath string) (*gojq.Query, error) {
	query, err := gojq.Parse(jsonpath)
	if err != nil {
		return nil, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), fmt.Sprintf("compile jsonpath err: %+v",
			err))
	}
	return query, nil
}

func (o *operators) GetBoolArr(jsonpath, data string) ([]bool, error) {
	if data == "" {
		return nil, nil
	}
	query, err := o.compileJsonPath(jsonpath)
	if err != nil {
		return nil, err
	}
	results, err := o.getResult(query, data)
	if err != nil {
		return nil, err
	}
	var bools []bool
	for _, item := range results {
		if v, ok := item.(bool); ok {
			bools = append(bools, v)
		} else {
			return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
				fmt.Sprintf("value is not bool: %v (%T)", item, item))
		}
	}
	if len(bools) == 0 {
		return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY),
			"query result empty")
	}
	return bools, nil
}

// GetStrings 获取字符串数组结果
func (o *operators) GetStrings(jsonpath, data string) ([]string, error) {
	if data == "" {
		return nil, nil
	}
	query, err := o.compileJsonPath(jsonpath)
	if err != nil {
		return nil, err
	}
	results, err := o.getResult(query, data)
	if err != nil {
		return nil, err
	}
	var strs []string
	for _, item := range results {
		switch v := item.(type) {
		case string:
			strs = append(strs, v)
		case float64:
			if v == math.Trunc(v) {
				strs = append(strs, fmt.Sprintf("%d", int(v)))
			} else {
				strs = append(strs, fmt.Sprintf("%g", v))
			}
		case int:
			strs = append(strs, fmt.Sprintf("%d", v))
		case bool:
			strs = append(strs, fmt.Sprintf("%t", v))
		case json.Number:
			strs = append(strs, v.String())
		default:
			return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
				fmt.Sprintf("unsupported type conversion: %T", v))
		}
	}

	if len(strs) == 0 {
		return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY),
			"query result empty")
	}
	return strs, nil
}

// GetInt 获取单个整数结果
func (o *operators) GetInt(jsonpath, data string) (int, error) {
	if data == "" {
		return 0, nil
	}
	query, err := o.compileJsonPath(jsonpath)
	if err != nil {
		return 0, err
	}
	results, err := o.getResult(query, data)
	if err != nil {
		return 0, err
	}
	switch v := results[0].(type) {
	case int:
		return v, nil
	case float64:
		if math.IsNaN(v) || math.IsInf(v, 0) || v != math.Trunc(v) {
			return 0, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
				fmt.Sprintf("value is not valid integer: %v", v))
		}
		return int(v), nil
	case json.Number:
		intVal, err := v.Int64()
		if err != nil {
			return 0, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
				fmt.Sprintf("convert json.Number to int error: %v", err))
		}
		return int(intVal), nil
	default:
		return 0, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
			fmt.Sprintf("except type: int, actual: %s (%T)", reflect.TypeOf(v).String(), v))
	}
}

// GetIntArr 获取整数数组结果
func (o *operators) GetIntArr(jsonpath, data string) ([]int, error) {
	if data == "" {
		return nil, nil
	}
	query, err := o.compileJsonPath(jsonpath)
	if err != nil {
		return nil, err
	}
	results, err := o.getResult(query, data)
	if err != nil {
		return nil, err
	}
	var ints []int
	for _, item := range results {
		switch v := item.(type) {
		case int:
			ints = append(ints, v)
		case float64:
			if math.IsNaN(v) || math.IsInf(v, 0) || v != math.Trunc(v) {
				return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
					fmt.Sprintf("value is not valid integer: %v", v))
			}
			ints = append(ints, int(v))
		case json.Number:
			intVal, err := v.Int64()
			if err != nil {
				return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
					fmt.Sprintf("convert json.Number to int error: %v", err))
			}
			ints = append(ints, int(intVal))
		default:
			return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
				fmt.Sprintf("unsupported type conversion: %T", v))
		}
	}

	if len(ints) == 0 {
		return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY),
			"query result empty")
	}
	return ints, nil
}

// GetFloat 获取单个浮点数结果
func (o *operators) GetFloat(jsonpath, data string) (float64, error) {
	if data == "" {
		return 0, nil
	}
	query, err := o.compileJsonPath(jsonpath)
	if err != nil {
		return 0, err
	}
	results, err := o.getResult(query, data)
	if err != nil {
		return 0, err
	}
	switch v := results[0].(type) {
	case int:
		return float64(v), nil
	case float64:
		return v, nil
	case json.Number:
		floatVal, err := v.Float64()
		if err != nil {
			return 0, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
				fmt.Sprintf("convert json.Number to float error: %v", err))
		}
		return floatVal, nil
	default:
		return 0, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
			fmt.Sprintf("except type: float, actual: %s (%T)", reflect.TypeOf(v).String(), v))
	}
}

// GetFloatArr 获取浮点数数组结果
func (o *operators) GetFloatArr(jsonpath, data string) ([]float64, error) {
	if data == "" {
		return nil, nil
	}
	query, err := o.compileJsonPath(jsonpath)
	if err != nil {
		return nil, err
	}
	results, err := o.getResult(query, data)
	if err != nil {
		return nil, err
	}
	var floats []float64
	for _, item := range results {
		switch v := item.(type) {
		case int:
			floats = append(floats, float64(v))
		case float64:
			floats = append(floats, v)
		case json.Number:
			floatVal, err := v.Float64()
			if err != nil {
				return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
					fmt.Sprintf("convert json.Number to float error: %v", err))
			}
			floats = append(floats, floatVal)
		default:
			return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_ERR),
				fmt.Sprintf("unsupported type conversion: %T", v))
		}
	}

	if len(floats) == 0 {
		return nil, errs.New(int(common_errcode.ErrCode_JSONPATH_QUERY_EMPTY),
			"query result empty")
	}
	return floats, nil
}

func (o *operators) unmarshalResults(items []interface{}, m any) error {
	if len(items) == 0 {
		return nil
	}
	// 如果第一个元素本身就是数组，则直接使用
	if arr, ok := items[0].([]interface{}); ok {
		data, _ := json.Marshal(arr)
		return json.Unmarshal(data, m)
	}
	// 否则将所有结果项合并为数组
	data, _ := json.Marshal(items)
	return json.Unmarshal(data, m)
}
