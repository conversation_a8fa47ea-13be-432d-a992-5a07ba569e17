package jsonpath

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

var testData = `{
	"user": {
		"name": "<PERSON>",
		"age": 30,
		"height": 175.5,
		"tags": ["developer", "gamer"],
		"scores": [90, 95, 100],
		"mixed": [10, "twenty", 30.5],
		"floatArr": [10.1, 95.123,30.5],
		"float": 1023.2442,
		"active": true,
		"boolFalse": false,
		"boolArr": [true, true, false, false, true]
	}
}`

const testJSON = `{
	"store": {
		"book": [
			{
				"title": "The Catcher in the Rye",
				"price": 15.99,
				"tags": ["classic", "fiction"],
				"available": true
			},
			{
				"title": "To Kill a Mockingbird",
				"price": 12.50,
				"tags": ["novel", "drama"],
				"available": false
			}
		],
		"numbers": [1, 2, 3.5, 4],
		"flags": [true, false, true]
	}
}`

func TestGetStrings(t *testing.T) {
	op := New().(*operators)

	t.Run("直接获取字符串数组", func(t *testing.T) {
		// 测试直接获取数组
		result, err := op.GetStrings(".store.book[0].tags[]", testJSON)
		assert.NoError(t, err)
		assert.Equal(t, []string{"classic", "fiction"}, result)
	})

	t.Run("获取多个字符串元素", func(t *testing.T) {
		// 测试收集多个元素
		result, err := op.GetStrings(".store.book[].title", testJSON)
		assert.NoError(t, err)
		assert.Equal(t, []string{
			"The Catcher in the Rye",
			"To Kill a Mockingbird",
		}, result)
	})

	t.Run("混合类型转换字符串", func(t *testing.T) {
		// 测试数字和布尔值转换
		result, err := op.GetStrings(".store.numbers[]", testJSON)
		assert.NoError(t, err)
		assert.Equal(t, []string{"1", "2", "3.5", "4"}, result)
	})
}

func TestOperators(t *testing.T) {
	op := New().(*operators)

	t.Run("ValidateJsonPath", func(t *testing.T) {
		tt := []struct {
			path   string
			valid  bool
			cached bool
		}{
			{".user.name", true, false},     // 不缓存
			{"invalid.path!", false, false}, // 无效路径
		}

		for _, tc := range tt {
			err := op.ValidateJsonPath(tc.path)
			if tc.valid && err != nil {
				t.Errorf("预期有效路径 %q 验证通过，但得到错误: %v", tc.path, err)
			}
			if !tc.valid && err == nil {
				t.Errorf("预期无效路径 %q 验证失败，但验证通过", tc.path)
			}
		}
	})

	t.Run("GetString", func(t *testing.T) {
		tt := []struct {
			path     string
			expected string
			hasError bool
		}{
			{".user.name", "John", false},
			{".user.age", "30", false},       // 数字自动转换
			{".user.height", "175.5", false}, // 浮点数转换
			{".user.active", "true", false},  // 布尔值转换
			//	{".user.missing", "", true},        // 路径不存在
			{".user.tags", "[developer gamer]", false}, // 取数组第一个元素?
			{".user.tags[1]", "gamer", false},          // 数组索引
		}

		for _, tc := range tt {
			got, err := op.GetString(tc.path, testData)
			if tc.hasError {
				if err == nil {
					t.Errorf("路径 %q 预期错误但成功返回: %v", tc.path, got)
				}
				continue
			}

			if err != nil {
				t.Errorf("路径 %q 预期成功但返回错误: %v", tc.path, err)
				continue
			}

			if got != tc.expected {
				t.Errorf("路径 %q 结果不符\n预期: %+v\n实际: %+v", tc.path, tc.expected, got)
			}
		}
	})

	t.Run("GetStrings", func(t *testing.T) {
		tt := []struct {
			path     string
			expected []string
			hasError bool
		}{
			{".user.tags[]", []string{"developer", "gamer"}, false},
			{".user.mixed[]", []string{"10", "twenty", "30.5"}, false},
			{".user.scores[]", []string{"90", "95", "100"}, false},
		}

		for _, tc := range tt {
			got, err := op.GetStrings(tc.path, testData)
			if tc.hasError {
				if err == nil {
					t.Errorf("路径 %q 预期错误但成功返回: %v", tc.path, got)
				}
				continue
			}
			if err != nil {
				t.Errorf("路径 %q 预期成功但返回错误: %v", tc.path, err)
				continue
			}
			if len(got) != len(tc.expected) {
				t.Errorf("路径 %q 结果数量不符\n预期: %v\n实际: %v", tc.path, tc.expected, got)
				continue
			}
			for i := range got {
				if got[i] != tc.expected[i] {
					t.Errorf("路径 %q 结果不符\n预期: %v\n实际: %v", tc.path, tc.expected, got)
					break
				}
			}
		}
	})

	t.Run("GetInt", func(t *testing.T) {
		tt := []struct {
			path     string
			expected int
			hasError bool
		}{
			{".user.age", 30, false},
			{".user.scores[0]", 90, false},
			{".user.height", 0, true}, // 浮点数不能直接转int
			{".user.active", 0, true}, // 布尔值转换失败
		}

		for _, tc := range tt {
			got, err := op.GetInt(tc.path, testData)
			if tc.hasError {
				if err == nil {
					t.Errorf("路径 %q 预期错误但成功返回: %v", tc.path, got)
				}
				continue
			}

			if err != nil {
				t.Errorf("路径 %q 预期成功但返回错误: %v", tc.path, err)
				continue
			}

			if got != tc.expected {
				t.Errorf("路径 %q 结果不符\n预期: %d\n实际: %d", tc.path, tc.expected, got)
			}
		}
	})

	t.Run("GetIntArr", func(t *testing.T) {
		tt := []struct {
			path     string
			expected []int
			hasError bool
		}{
			{".user.scores[]", []int{90, 95, 100}, false}, // 注意95.5会被拒绝
			{".user.mixed", nil, true},                    // 包含字符串元素
		}

		for _, tc := range tt {
			got, err := op.GetIntArr(tc.path, testData)
			if tc.hasError {
				if err == nil {
					t.Errorf("路径 %q 预期错误但成功返回: %v", tc.path, got)
				}
				continue
			}

			if err != nil {
				t.Errorf("路径 %q 预期成功但返回错误: %v", tc.path, err)
				continue
			}

			if len(got) != len(tc.expected) {
				t.Errorf("路径 %q 结果数量不符\n预期: %v\n实际: %v", tc.path, tc.expected, got)
				continue
			}

			for i := range got {
				if got[i] != tc.expected[i] {
					t.Errorf("路径 %q 结果不符\n预期: %v\n实际: %v", tc.path, tc.expected, got)
					break
				}
			}
		}
	})

	t.Run("GetFloat", func(t *testing.T) {
		tt := []struct {
			path     string
			expected float64
			hasError bool
		}{
			{".user.height", 175.5, false},
			{".user.age", 30.0, false},
			{".user.float", 1023.2442, false},
			{".user.floatArr[1]", 95.123, false},
			{".user.active", 0, true}, // 布尔值转换失败
		}

		for _, tc := range tt {
			got, err := op.GetFloat(tc.path, testData)
			if tc.hasError {
				if err == nil {
					t.Errorf("路径 %q 预期错误但成功返回: %v", tc.path, got)
				}
				continue
			}

			if err != nil {
				t.Errorf("路径 %q 预期成功但返回错误: %v", tc.path, err)
				continue
			}

			if got != tc.expected {
				t.Errorf("路径 %q 结果不符\n预期: %f\n实际: %f", tc.path, tc.expected, got)
			}
		}
	})

	t.Run("GetFloatArr", func(t *testing.T) {
		tt := []struct {
			path     string
			expected []float64
			hasError bool
		}{
			{".user.height", []float64{175.5}, false},
			{".user.floatArr[]", []float64{10.1, 95.123, 30.5}, false},
		}

		for _, tc := range tt {
			got, err := op.GetFloatArr(tc.path, testData)
			if tc.hasError {
				if err == nil {
					t.Errorf("路径 %q 预期错误但成功返回: %v", tc.path, got)
				}
				continue
			}

			if err != nil {
				t.Errorf("路径 %q 预期成功但返回错误: %v", tc.path, err)
				continue
			}
			if len(got) != len(tc.expected) {
				t.Errorf("路径%q获取数组元素不相等！", tc.path)
				continue
			}
			for idx := range got {
				if got[idx] != tc.expected[idx] {
					t.Errorf("路径 %q 结果不符\n预期: %f\n实际: %f", tc.path, tc.expected, got)
					break
				}
			}
		}
	})

	t.Run("GetBool", func(t *testing.T) {
		tt := []struct {
			path     string
			expected bool
			hasError bool
		}{
			{".user.active", true, false},
			{".user.boolFalse", false, false},
		}

		for _, tc := range tt {
			got, err := op.GetBool(tc.path, testData)
			if tc.hasError {
				if err == nil {
					t.Errorf("路径 %q 预期错误但成功返回: %v", tc.path, got)
				}
				continue
			}

			if err != nil {
				t.Errorf("路径 %q 预期成功但返回错误: %v", tc.path, err)
				continue
			}
			if (got) != (tc.expected) {
				t.Errorf("路径%q获取数组元素不相等！", tc.path)
				continue
			}
		}
	})

	t.Run("GetBoolArr", func(t *testing.T) {
		tt := []struct {
			path     string
			expected []bool
			hasError bool
		}{
			{".user.boolArr[]", []bool{true, true, false, false, true}, false},
		}

		for _, tc := range tt {
			got, err := op.GetBoolArr(tc.path, testData)
			if tc.hasError {
				if err == nil {
					t.Errorf("路径 %q 预期错误但成功返回: %v", tc.path, got)
				}
				continue
			}

			if err != nil {
				t.Errorf("路径 %q 预期成功但返回错误: %v", tc.path, err)
				continue
			}
			if len(got) != len(tc.expected) {
				t.Errorf("路径%q获取数组元素不相等！", tc.path)
				continue
			}
			for idx := range got {
				if got[idx] != tc.expected[idx] {
					t.Errorf("路径%q获取数组元素不相等！", tc.path)
				}
			}
		}
	})

	t.Run("ErrorScenarios", func(t *testing.T) {
		t.Run("InvalidJSON", func(t *testing.T) {
			_, err := op.GetString(".user.name", "{invalid}")
			if err == nil {
				t.Errorf("预期JSON解析错误，实际得到: %v", err)
			}
		})
		t.Run("missing", func(t *testing.T) {
			_, err := op.GetString(".user.abc", testData)
			if err == nil {
				t.Errorf("预期查询为空，实际得到: %v", err)
			}
		})
	})
}

func TestCutJsonStr_Basic(t *testing.T) {
	o := New() // 需实现初始化方法

	t.Run("基本字段提取", func(t *testing.T) {
		input := `{"name":"Alice","age":25}`
		rules := map[string]string{
			"username": ".name",
			"userage":  ".age",
		}

		result, err := o.CutJsonStr(input, rules)
		assert.NoError(t, err)
		assert.JSONEq(t, `{"username":"Alice","userage":25}`, result)
	})

	t.Run("嵌套结构处理", func(t *testing.T) {
		input := `{"user":{"profile":{"name":"Bob"}},"depart":"IT"}`
		rules := map[string]string{
			"name":   ".user.profile.name",
			"depart": ".depart",
		}

		result, err := o.CutJsonStr(input, rules)
		assert.NoError(t, err)
		assert.JSONEq(t, `{"name":"Bob","depart":"IT"}`, result)
	})
}

func TestCutJsonStr_ArrayHandling(t *testing.T) {
	o := New()
	t.Run("数组元素提取", func(t *testing.T) {
		input := `{"scores":[85,92,78]}`
		rules := map[string]string{
			"first_score": ".scores[0]",
			"all_scores":  ".scores[]",
		}

		result, err := o.CutJsonStr(input, rules)
		assert.NoError(t, err)
		assert.JSONEq(t, `{"first_score":85,"all_scores":[85,92,78]}`, result)
	})

	t.Run("空数组处理", func(t *testing.T) {
		input := `{"items":[]}`
		rules := map[string]string{
			"first_item": ".items[0]",
		}

		result, err := o.CutJsonStr(input, rules)
		assert.NoError(t, err)
		assert.JSONEq(t, `{"first_item":null}`, result)
	})
}

func TestCutJsonStr_ErrorHandling(t *testing.T) {
	o := New()
	t.Run("无效JSON输入", func(t *testing.T) {
		input := `{invalid json}`
		rules := map[string]string{"test": ".any"}

		_, err := o.CutJsonStr(input, rules)
		assert.ErrorContains(t, err, "json解析失败")
	})

	t.Run("错误查询语法", func(t *testing.T) {
		input := `{}`
		rules := map[string]string{
			"err_field": "{invalid",
		}

		_, err := o.CutJsonStr(input, rules)
		assert.ErrorContains(t, err, "compile jsonpath err")
	})
}

func TestCutJsonStr_SpecialCases(t *testing.T) {
	o := New()

	t.Run("路径不存在返回null", func(t *testing.T) {
		input := `{}`
		rules := map[string]string{
			"missing": ".not.exist",
		}

		result, err := o.CutJsonStr(input, rules)
		assert.NoError(t, err)
		assert.JSONEq(t, `{"missing":null}`, result)
	})

	t.Run("特殊字符key处理", func(t *testing.T) {
		input := `{"strange.key":"value"}`
		rules := map[string]string{
			"special": `."strange.key"`,
		}

		result, err := o.CutJsonStr(input, rules)
		assert.NoError(t, err)
		assert.JSONEq(t, `{"special":"value"}`, result)
	})

	t.Run("空规则处理", func(t *testing.T) {
		input := `{"data":1}`
		rules := map[string]string{}

		result, err := o.CutJsonStr(input, rules)
		assert.NoError(t, err)
		assert.JSONEq(t, `{}`, result)
	})
}
