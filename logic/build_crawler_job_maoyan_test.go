package logic

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"github.com/agiledragon/gomonkey/v2"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
)

func Test_generateMaoyanSearchCrawJobs(t *testing.T) {
	infos := []*dao.CoverInfo{{Title: "A", CoverID: "cid1", CoverType: 1}, {Title: "A", CoverID: "cid2", CoverType: 1}}
	jobs, err := generateMaoyanSearchCrawJobs(infos, 1006)
	if err != nil || len(jobs) != 1 {
		t.Fatalf("unexpected: %v %d", err, len(jobs))
	}
	var m map[string]any
	_ = json.Unmarshal([]byte(jobs[0].JobArguments), &m)
	if m["query"].(string) != "A" || jobs[0].Platform != "maoyan" {
		t.Fatalf("unexpected job: %+v", jobs[0])
	}
}

func Test_genYunHeBoxSearchCrawJobs(t *testing.T) {
	infos := []*dao.CoverInfo{{Title: "A", CoverID: "cid1", CoverType: 2}, {Title: "B", CoverID: "cid2", CoverType: 1}}
	jobs, err := genYunHeBoxSearchCrawJobs(infos, 1010)
	if err != nil || len(jobs) != 1 || jobs[0].Platform != "yunhe" {
		t.Fatalf("unexpected: %v %d %+v", err, len(jobs), jobs)
	}
}

func Test_standardizationCategory(t *testing.T) {
	if standardizationCategory("网络剧") != "电视剧" {
		t.Fatal("fail")
	}
	if standardizationCategory("movie") != "电影" {
		t.Fatal("fail")
	}
	if standardizationCategory("art") != "综艺" {
		t.Fatal("fail")
	}
}

func Test_transSearch2DetailOrderID_and_Back(t *testing.T) {
	if transSearch2DetailOrderID(1006) != 1007 {
		t.Fatal("fail")
	}
	if transDetail2SearchOrderID(1007) != 1006 {
		t.Fatal("fail")
	}
	if transSearch2DetailOrderID(1012) != 1013 {
		t.Fatal("fail")
	}
	if transDetail2SearchOrderID(1013) != 1012 {
		t.Fatal("fail")
	}
}

func Test_checkCoverType(t *testing.T) {
	if !checkCoverType("电影") || !checkCoverType("电视剧") || !checkCoverType("动漫") {
		t.Fatal("fail")
	}
	if checkCoverType("其他") {
		t.Fatal("fail")
	}
}

func Test_genMaoYanDetailJobs(t *testing.T) {
	job := genMaoYanDetailJobs(1006, &MaoyanSearchCrawRet{OriCrawID: 1, MaoyanID: "m1", Title: "T"}, "maoyan")
	if job == nil || job.Platform != "maoyan" || job.Title != "T" {
		t.Fatalf("unexpected job: %+v", job)
	}
	if genMaoYanDetailJobs(1006, nil, "maoyan") != nil {
		t.Fatal("expect nil for nil input")
	}
}

func Test_generateMaoyanCrawJobByTitle(t *testing.T) {
	ctx := trpc.BackgroundContext()
	var cd *dao.CrawDetail

	// Test success case
	patch := gomonkey.ApplyMethod(reflect.TypeOf(cd), "GetCrawDetailByTitle",
		func(_ *dao.CrawDetail, _ context.Context, _ string) ([]*dao.CrawDetail, error) {
			ret := []*dao.CrawDetail{
				{ID: 1, Title: "X|电影", Detail: `{"category":"movie","id":"m1","name":"X","rank":3}`, Ext: "e"},
				{ID: 2, Title: "X|电影", Detail: `{"category":"movie","id":"m1","name":"X","rank":1}`},
				{ID: 3, Title: "X|电影", Detail: `bad_json`},
			}
			return ret, nil
		})
	defer patch.Reset()

	job, err := generateMaoyanCrawJobByTitle(ctx, 1007, "X", "maoyan")
	if err != nil || job == nil || job.Title != "X" {
		t.Fatalf("unexpected: %v %+v", err, job)
	}
}

func Test_generateMaoyanCrawJobByTitle_Error(t *testing.T) {
	ctx := trpc.BackgroundContext()
	var cd *dao.CrawDetail

	// Test error case
	patch := gomonkey.ApplyMethod(reflect.TypeOf(cd), "GetCrawDetailByTitle",
		func(_ *dao.CrawDetail, _ context.Context, _ string) ([]*dao.CrawDetail, error) {
			return nil, errors.New("db")
		})
	defer patch.Reset()

	if _, err := generateMaoyanCrawJobByTitle(ctx, 1007, "X", "maoyan"); err == nil {
		t.Fatalf("expect error")
	}
}

func Test_build_and_Handle_MaoYan_Jobs(t *testing.T) {
	ctx := trpc.BackgroundContext()
	var job *dao.CrawJob
	cleanPatch := gomonkey.ApplyMethod(reflect.TypeOf(job), "CleanCrawJobs",
		func(_ *dao.CrawJob, _ context.Context, _ string) error { return nil })
	defer cleanPatch.Reset()
	insertPatch := gomonkey.ApplyMethod(reflect.TypeOf(job), "InsertCrawJob",
		func(_ *dao.CrawJob, _ context.Context) error { return nil })
	defer insertPatch.Reset()
	getAllPatch := gomonkey.ApplyMethod(reflect.TypeOf(job), "GetAllCrawJobs",
		func(_ *dao.CrawJob, _ context.Context, _ string) ([]*dao.CrawJob, error) {
			return []*dao.CrawJob{{Title: "T1", Platform: "maoyan"}}, nil
		})
	defer getAllPatch.Reset()

	getTxVideoPatch := gomonkey.ApplyFunc(getTxVideoFocusCoverInfo,
		func(_ context.Context) ([]*dao.CoverInfo, error) {
			return []*dao.CoverInfo{{Title: "Tx", CoverID: "c1", CoverType: 1}}, nil
		})
	defer getTxVideoPatch.Reset()
	getPlanPatch := gomonkey.ApplyFunc(dao.GetAllPlanInfo,
		func(_ context.Context, _ []string) ([]*dao.CoverInfo, error) {
			return []*dao.CoverInfo{{Title: "C1", CoverID: "c2", CoverType: 2}}, nil
		})
	defer getPlanPatch.Reset()

	if err := HandleBuildMaoYanSearchJob(ctx, "", "", 0); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
	// detail
	var cd *dao.CrawDetail
	getDetailPatch := gomonkey.ApplyMethod(reflect.TypeOf(cd), "GetCrawDetailByTitle",
		func(_ *dao.CrawDetail, _ context.Context, _ string) ([]*dao.CrawDetail, error) {
			return []*dao.CrawDetail{{ID: 11, Title: "T1|电影", Detail: `{"category":"movie","id":"m1","name":"T1","rank":1}`}}, nil
		})
	defer getDetailPatch.Reset()
	if err := HandleBuildMaoYanDetailJob(ctx, "", "", 0); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
}
