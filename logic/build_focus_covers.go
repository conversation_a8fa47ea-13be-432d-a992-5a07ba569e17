package logic

import (
	"context"
	"io/ioutil"
	"net/http"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

const (
	movies          = 1
	teleplay        = 2
	anime           = 3
	documentaryFilm = 9
	variety         = 10
	children        = 106
	cinemaMovies    = 92972 // cinema_flag 的属性值
	SPlusLevel      = 1525777
	SLevel          = 224425
	ALevel          = 224426
	BLevel          = 224427
	PositiveFilm    = 10001   // 正片
	NewStructure    = 8371548 // 综艺新结构值
)

func getMapValInt(m map[string]msghub.FieldInfo, key string) int {
	res := 0
	if fieldInfo, ok := m[key]; ok {
		res, _ = strconv.Atoi(fieldInfo.Value)
	}
	return res
}

func getMapValStr(m map[string]msghub.FieldInfo, key string) string {
	var res string
	if fieldInfo, ok := m[key]; ok {
		res = fieldInfo.Value
	}
	return res
}

func getCoverLevel(m *msghub.MediaInfo) string {
	mapCoverLevel := map[int]string{
		SPlusLevel: "S+",
		SLevel:     "S",
		ALevel:     "A",
		BLevel:     "B",
	}
	hotLevel := getMapValInt(m.FieldInfos, "hot_level")
	if val, ok := mapCoverLevel[hotLevel]; ok {
		return val
	}
	return ""
}

// isMonitorLifecycle 判断是否在监控周期中
func isMonitorLifecycle(ipOnlineStatus int) bool {
	if ipOnlineStatus == 8377974 || ipOnlineStatus == 8389611 { //可预热-定档；运营期；内的数据纳入监控
		return true
	}
	return false
}

// isNetworkFilm 是否为网络电影；
func isNetworkFilm(m *msghub.MediaInfo) bool {
	coverCateID := getMapValInt(m.FieldInfos, "category_value")
	if coverCateID == 10890 || coverCateID == 10979 { // 网络电影-正片；网络电影-预告片
		return true
	}
	return false
}

// isPremiere 是否为云首发
func isPremiere(m *msghub.MediaInfo) bool {
	return getMapValInt(m.FieldInfos, "is_premiere") == 1543606
}

// isSelfMade 是否自制剧
func isSelfMade(m *msghub.MediaInfo) bool {
	return getMapValInt(m.FieldInfos, "nature_of_the_content_id") == 1585431
}

// isHighALevel 是否高于（包括等于）A级
func isHighALevel(m *msghub.MediaInfo) bool {
	hotLevel := getMapValInt(m.FieldInfos, "hot_level")
	return hotLevel == SPlusLevel || hotLevel == SLevel || hotLevel == ALevel
}

// isHighSLevel 是否高于（包括等于）S级
func isHighSLevel(m *msghub.MediaInfo) bool {
	hotLevel := getMapValInt(m.FieldInfos, "hot_level")
	return hotLevel == SLevel || hotLevel == SPlusLevel
}

// isValidData 是否合法数据；不满足条件的数据，我们不处理
func isValidData(m *msghub.MediaInfo) bool {
	if getMapValStr(m.FieldInfos, "ip_online_status") == "" ||
		getMapValInt(m.FieldInfos, "data_type_id") == 1571345 { // "IP实时状态"要有值; 1571345 测试数据
		return false
	}
	return true
}

// isPositiveFilm 是否为正片
func isPositiveFilm(m *msghub.MediaInfo) bool {
	filmCategory := getMapValInt(m.FieldInfos, "category_value")
	return filmCategory == PositiveFilm
}

// isNewStructure 是否为新结构综艺
func isNewStructure(m *msghub.MediaInfo) bool {
	structure := getMapValInt(m.FieldInfos, "cid_content_structure")
	return structure == NewStructure
}

// NOCC:CCN_threshold(设计如此:)
func isNeedCrawForMovies(m *msghub.MediaInfo, cinemaFlag int) bool {
	if cinemaFlag == cinemaMovies && isHighALevel(m) {
		return true
	}
	return false
}

// NOCC:CCN_threshold(设计如此:)
func isNeedCrawForOtherCategories(m *msghub.MediaInfo, coverTypeID int) bool {
	if (coverTypeID == teleplay && isHighALevel(m)) ||
		// 综艺: A级以上新结构正片
		(coverTypeID == variety && isHighALevel(m) && isPositiveFilm(m) && isNewStructure(m)) ||
		// 动漫: S级以上
		(coverTypeID == anime && isHighSLevel(m)) ||
		// 少儿: A级以上自制
		(coverTypeID == children && isHighALevel(m) && isSelfMade(m)) {
		return true
	}
	return false
}

func isNeedCrawForDocumentaryFilm(m *msghub.MediaInfo, coverTypeID, hotLevel int) bool {
	return coverTypeID == documentaryFilm && (isHighSLevel(m) || (hotLevel == ALevel && isSelfMade(m)))
}

// calcCrawFlag 计算剧集监控周期；是否纳入监控， 是否监听IP官号
func calcCrawFlag(m *msghub.MediaInfo) (bool, bool) {
	isCrawOfficeAcc := true
	isNeedCraw := false
	coverTypeID := getMapValInt(m.FieldInfos, "type")
	cinemaFlag := getMapValInt(m.FieldInfos, "cinema_flag")
	if coverTypeID == movies {
		isNeedCraw = isNeedCrawForMovies(m, cinemaFlag)
		if isNeedCraw {
			isCrawOfficeAcc = false
		}
	} else {
		isNeedCraw = isNeedCrawForOtherCategories(m, coverTypeID) ||
			isNeedCrawForDocumentaryFilm(m, coverTypeID, getMapValInt(m.FieldInfos, "hot_level"))
	}
	return isNeedCraw, isCrawOfficeAcc
}

// updateFocusCoversPool 更新重点剧池子
func updateFocusCoversPool(ctx context.Context, c dao.CoverInfo) error {
	// 对于非监控期内的数据：
	// 1. 若不在"重点剧池"中，则丢弃;
	// 2. 在"重点剧池"中，若在则更新ip_online_status，以让抓取表中的任务停止;
	if !isMonitorLifecycle(c.IPOnlineStatus) {
		IsExist, err := c.IsExistFocusCoverLoop(ctx)
		if err != nil {
			return err
		}
		if !IsExist { // 当前不在重点剧池，丢弃
			return nil
		}
	}

	// 对于在监控期内的数据，则直接写入
	if err := c.InsertCoverInfo(ctx); err != nil {
		return err
	}
	return nil
}

// isPriority 是否优先数据（用于同一个标题数据，有优先标识的 用作主数据）
func isPriority(m *msghub.MediaInfo) bool {
	contentStructure := getMapValInt(m.FieldInfos, "cid_content_structure") // 综艺的内容结构
	// 综艺新CID结构
	return contentStructure == 8371548
}

func parseRequestData(ctx context.Context, reqData string) (*msghub.MediaInfo, error) {
	reqBody, err := msghub.ParseMsghubJson(reqData)
	if err != nil {
		log.ErrorContextf(ctx, "Unmarshal data error:%v; reqData:%s", err, reqData)
		return reqBody, err
	}
	return reqBody, nil
}

func processCoverData(ctx context.Context, reqBody *msghub.MediaInfo) error {
	if !isValidData(reqBody) {
		return nil
	}
	isNeedCraw, isCrawOfficeAcc := calcCrawFlag(reqBody)
	if !isNeedCraw {
		return nil
	}
	c := dao.CoverInfo{
		CoverID:                 reqBody.Id,
		CoverType:               getMapValInt(reqBody.FieldInfos, "type"),
		Title:                   getMapValStr(reqBody.FieldInfos, "title"),
		IPOnlineStatus:          getMapValInt(reqBody.FieldInfos, "ip_online_status"),
		TopicFlag:               1,
		PriorityFlag:            0,
		CoverLevel:              getCoverLevel(reqBody),
		CopyrightPlaybackStatus: getMapValInt(reqBody.FieldInfos, "copyright_playback_status_id"),
	}
	if isCrawOfficeAcc {
		c.OfficeAccFlag = 1
	}
	if isPriority(reqBody) {
		c.PriorityFlag = 1
	}
	return updateFocusCoversPool(ctx, c)
}

// HandleCoverMsg 监听数据总线专辑变更，维护重点剧池，并更新抓取IP信息表
func HandleCoverMsg(w http.ResponseWriter, r *http.Request) error {
	ctx := r.Context()
	reqData, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.ErrorContextf(ctx, "ioutil error:%v; ", err)
		return err
	}

	reqBody, err := parseRequestData(ctx, string(reqData))
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return err
	}

	err = processCoverData(ctx, reqBody)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return err
	}
	return nil
}
