package logic

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"github.com/agiledragon/gomonkey/v2"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
)

func Test_IPCrawler_GetArgs_And_IsPush(t *testing.T) {
	c := dao.IPCrawTask{ID: 1, OrderID: model.IPAccountOrder, CrawType: model.OfficeAccountType, Platform: "shipinghao", ConfirmStatus: model.ManualCorrection, CorrectKey: "ckey", Valid: model.AccountStateOK}
	crawler := NewIPCrawler(c, report.ReportArticleInfo{}, report.ReportAccountInfo{}, report.ReportTopicFieldInfo{})
	args := crawler.getIPCrawTaskArgs()
	if args.CrawTxt == "" && args.URL == "" {
		t.Fatal("expect fields")
	}
	if !crawler.IsPushCraw() {
		t.Fatal("should push")
	}
}

func Test_CrawlerRetArticleCallBack_and_Account(t *testing.T) {
	ctx := trpc.BackgroundContext()
	c := dao.IPCrawTask{ID: 1, OrderID: model.IPAccountOrder, CrawType: model.OfficeAccountType}
	crawler := NewIPCrawler(c, report.ReportArticleInfo{}, report.ReportAccountInfo{}, report.ReportTopicFieldInfo{})
	var tsk *dao.IPCrawTask
	p1 := gomonkey.ApplyMethod(reflect.TypeOf(tsk), "GetCrawTaskByID", func(_ *dao.IPCrawTask, _ context.Context, _ int) error { return nil })
	defer p1.Reset()
	p2 := gomonkey.ApplyMethod(reflect.TypeOf(tsk), "SetCrawTaskState", func(_ *dao.IPCrawTask, _ context.Context) error { return nil })
	defer p2.Reset()
	p3 := gomonkey.ApplyMethod(reflect.TypeOf(tsk), "SetCrawTaskBackTrack", func(_ *dao.IPCrawTask, _ context.Context) error { return nil })
	defer p3.Reset()
	if err := crawler.CrawlerRetArticleCallBack(ctx, 1); err != nil {
		t.Fatalf("err: %v", err)
	}
	if err := crawler.CrawlerRetAccountCallBack(ctx, 1); err != nil {
		t.Fatalf("err: %v", err)
	}
}

func Test_pushIPInfoCrawTask(t *testing.T) {
	ctx := trpc.BackgroundContext()
	var tsk *dao.IPCrawTask
	// supply two tasks: one searched, one to search
	pGet := gomonkey.ApplyMethod(reflect.TypeOf(tsk), "GetAllCrawTasks",
		func(_ *dao.IPCrawTask, _ context.Context) ([]*dao.IPCrawTask, error) {
			return []*dao.IPCrawTask{
				{ID: 1, CrawType: model.OfficeAccountType, Platform: "shipinhao", Nick: "n"},
				{ID: 2, CrawType: model.TopicType, Platform: "douyin", CrawKey: "k"},
			}, nil
		})
	defer pGet.Reset()

	pAdd := gomonkey.ApplyFunc(dao.AddCrawTask, func(_ context.Context, _ *dao.CrawTaskArgs, _ bool) error { return nil })
	defer pAdd.Reset()
	pSet := gomonkey.ApplyMethod(reflect.TypeOf(tsk), "SetCrawTaskState", func(_ *dao.IPCrawTask, _ context.Context) error { return nil })
	defer pSet.Reset()
	if err := pushIPInfoCrawTask(ctx, 0); err != nil {
		t.Fatalf("err: %v", err)
	}
}

// NOTE: The following additional tests share the same package and imports above.

func TestIPCrawler_CrawlerRetArticleCallBack(t *testing.T) {
	// 打上补丁模拟 GetCrawTaskByID, SetCrawTaskEmptyState, SetCrawTaskSucState
	var ipCrawTask *dao.IPCrawTask
	patches := gomonkey.ApplyMethod(reflect.TypeOf(ipCrawTask), "GetCrawTaskByID",
		func(_ *dao.IPCrawTask, _ context.Context, _ int) error {
			return nil
		})
	defer patches.Reset()

	var ipCrawler *IPCrawler
	patches.ApplyMethod(reflect.TypeOf(ipCrawler), "SetCrawTaskEmptyState",
		func(_ *IPCrawler, _ context.Context) error {
			return nil
		})

	patches.ApplyMethod(reflect.TypeOf(ipCrawler), "SetCrawTaskSucState",
		func(_ *IPCrawler, _ context.Context) error {
			return nil
		})

	type fields struct {
		CrawTask       dao.IPCrawTask
		ArticleInfo    report.ReportArticleInfo
		AccountInfo    report.ReportAccountInfo
		TopicFieldInfo report.ReportTopicFieldInfo
	}
	type args struct {
		ctx    context.Context
		crawID int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				CrawTask:       dao.IPCrawTask{},
				ArticleInfo:    report.ReportArticleInfo{},
				AccountInfo:    report.ReportAccountInfo{},
				TopicFieldInfo: report.ReportTopicFieldInfo{},
			},
			args: args{
				ctx:    trpc.BackgroundContext(),
				crawID: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &IPCrawler{
				CrawTask:       tt.fields.CrawTask,
				ArticleInfo:    tt.fields.ArticleInfo,
				AccountInfo:    tt.fields.AccountInfo,
				TopicFieldInfo: tt.fields.TopicFieldInfo,
			}
			if err := c.CrawlerRetArticleCallBack(tt.args.ctx, tt.args.crawID); (err != nil) != tt.wantErr {
				t.Errorf("CrawlerRetArticleCallBack() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestIPCrawler_CrawlerRetAccountCallBack(t *testing.T) {
	// 打上补丁模拟 GetCrawTaskByID, SetCrawTaskEmptyState, SetCrawTaskSucState
	var ipCrawTask *dao.IPCrawTask
	patches := gomonkey.ApplyMethod(reflect.TypeOf(ipCrawTask), "GetCrawTaskByID",
		func(_ *dao.IPCrawTask, _ context.Context, _ int) error {
			return nil
		})
	defer patches.Reset()

	var ipCrawler *IPCrawler
	patches.ApplyMethod(reflect.TypeOf(ipCrawler), "SetCrawTaskEmptyState",
		func(_ *IPCrawler, _ context.Context) error {
			return nil
		})

	patches.ApplyMethod(reflect.TypeOf(ipCrawler), "SetCrawTaskSucState",
		func(_ *IPCrawler, _ context.Context) error {
			return nil
		})

	type fields struct {
		CrawTask       dao.IPCrawTask
		ArticleInfo    report.ReportArticleInfo
		AccountInfo    report.ReportAccountInfo
		TopicFieldInfo report.ReportTopicFieldInfo
	}
	type args struct {
		ctx    context.Context
		crawID int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				CrawTask:       dao.IPCrawTask{},
				ArticleInfo:    report.ReportArticleInfo{},
				AccountInfo:    report.ReportAccountInfo{},
				TopicFieldInfo: report.ReportTopicFieldInfo{},
			},
			args: args{
				ctx:    trpc.BackgroundContext(),
				crawID: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &IPCrawler{
				CrawTask:       tt.fields.CrawTask,
				ArticleInfo:    tt.fields.ArticleInfo,
				AccountInfo:    tt.fields.AccountInfo,
				TopicFieldInfo: tt.fields.TopicFieldInfo,
			}
			if err := i.CrawlerRetAccountCallBack(tt.args.ctx, tt.args.crawID); (err != nil) != tt.wantErr {
				t.Errorf("CrawlerRetAccountCallBack() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestIPCrawler_CrawlerRetTopicFieldCallBack(t *testing.T) {
	// 打上补丁模拟 GetCrawTaskByID, SetCrawTaskEmptyState, SetCrawTaskSucState
	var ipCrawTask *dao.IPCrawTask
	patches := gomonkey.ApplyMethod(reflect.TypeOf(ipCrawTask), "GetCrawTaskByID",
		func(_ *dao.IPCrawTask, _ context.Context, _ int) error {
			return nil
		})
	defer patches.Reset()

	var ipCrawler *IPCrawler
	patches.ApplyMethod(reflect.TypeOf(ipCrawler), "SetCrawTaskFailState",
		func(_ *IPCrawler, _ context.Context) error {
			return nil
		})

	patches.ApplyMethod(reflect.TypeOf(ipCrawler), "SetCrawTaskSucState",
		func(_ *IPCrawler, _ context.Context) error {
			return nil
		})

	type fields struct {
		CrawTask       dao.IPCrawTask
		ArticleInfo    report.ReportArticleInfo
		AccountInfo    report.ReportAccountInfo
		TopicFieldInfo report.ReportTopicFieldInfo
	}
	type args struct {
		ctx    context.Context
		crawID int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				CrawTask:       dao.IPCrawTask{},
				ArticleInfo:    report.ReportArticleInfo{},
				AccountInfo:    report.ReportAccountInfo{},
				TopicFieldInfo: report.ReportTopicFieldInfo{},
			},
			args: args{
				ctx:    trpc.BackgroundContext(),
				crawID: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &IPCrawler{
				CrawTask:       tt.fields.CrawTask,
				ArticleInfo:    tt.fields.ArticleInfo,
				AccountInfo:    tt.fields.AccountInfo,
				TopicFieldInfo: tt.fields.TopicFieldInfo,
			}
			if err := i.CrawlerRetTopicFieldCallBack(tt.args.ctx, tt.args.crawID); (err != nil) != tt.wantErr {
				t.Errorf("CrawlerRetTopicFieldCallBack() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestHandlePushIPInfoCrawTask(t *testing.T) {
	// 打上补丁模拟 GetAllCrawTasks 方法
	var ipCrawTask *dao.IPCrawTask
	patches := gomonkey.ApplyMethod(reflect.TypeOf(ipCrawTask), "GetAllCrawTasks",
		func(_ *dao.IPCrawTask, _ context.Context) ([]*dao.IPCrawTask, error) {
			return []*dao.IPCrawTask{
				{},
				{},
				{},
			}, nil
		})
	defer patches.Reset()

	type args struct {
		ctx context.Context
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := HandlePushIPInfoCrawTask(tt.args.ctx, "", "", 0); (err != nil) != tt.wantErr {
				t.Errorf("HandlePushIPInfoCrawTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
