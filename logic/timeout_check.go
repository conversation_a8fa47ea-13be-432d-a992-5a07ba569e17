package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/kafka"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/trpcprotocol/storage_service/access_layer"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/config"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	tool "git.code.oa.com/video_media/storage_service/common"

	"github.com/Shopify/sarama"
	"google.golang.org/protobuf/proto"
)

// checkTaskFailCounter 超时检查失败任务计数器
var checkTaskFailCounter = metrics.Counter("TimeoutCheckTaskError")

func init() {
	// 重写异步发送变更消息失败
	kafka.AsyncProducerErrorCallback = func(err error, topic string, key, value []byte, headers []sarama.RecordHeader) {
		log.Errorf("fail to send message of timeout check task, err is %v.", err)
		produceErrHandle(err.Error(), topic, key, value)
	}
}

func produceErrHandle(errInfo, topic string, key, value []byte) {
	// 上报特性告警
	checkTaskFailCounter.Incr()
	var notify access_layer.ModifyNotify
	if err := proto.Unmarshal(value, &notify); err != nil {
		log.Errorf("fail to unmarshal data, key is %s, err is %v.", string(key), err)
	}
	// 发送消息失败，上报流水记录
	tool.NewRemoteLog(context.Background(), "report").AddField("clientIp", notify.RemoteIp).
		AddField("appId", notify.AppId).AddIntField("dataSetId", int(notify.DataSetId)).
		AddField("id", notify.Id).AddIntField("callId", int(notify.SequenceId)).
		AddField("scane", "TimeoutCheck").AddIntField("step", endStep).
		AddField("namespace", trpc.GlobalConfig().Global.Namespace).ReportDebug(errInfo)

	if !config.GetConfig().CheckTask.Enable {
		// 极端情况下可关闭失败重试机制
		return
	}
	proxy := kafka.NewClientProxy("trpc.kafka.timeout.checktask")
	for i := 0; i < config.GetConfig().CheckTask.Retry; i++ {
		_, _, err := proxy.SendMessage(context.Background(), topic, key, value)
		if err == nil {
			return
		}
		log.Errorf("fail to send message, key is %s, err is %v.", string(key), err)
	}
}

// updateInfo 更新请求信息
type updateInfo struct {
	DatasetID    int32  `json:"datasetID"`
	AppID        string `json:"appID"`
	OperatorName string `json:"operatorName"`
	LocalIP      string `json:"localIP"`
	RemoteIP     string `json:"remoteIP"`
	ExtInfo      string `json:"extInfo"`
	SequenceID   int64  `json:"sequenceID"`
	TenantID     string `json:"tenantID"`
}

const (
	beginStep = 1
	endStep   = 2
)

func sendCheckTaskMsg(ctx context.Context, datasourceID int32, id string, info *updateInfo,
	modifyInfo *model.ModifyInfo,
) {
	notify := &access_layer.ModifyNotify{
		Id:               id,
		DataSetId:        info.DatasetID,
		TimeStamp:        int32(time.Now().Unix()),
		ModifyFieldInfos: modifyInfo.GetInfos(),
		AppId:            info.AppID,
		OperatorName:     info.OperatorName,
		LocalIp:          info.LocalIP,
		RemoteIp:         info.RemoteIP,
		ExtInfo:          info.ExtInfo,
		SequenceId:       info.SequenceID,
		BaseInfo:         modifyInfo.GetBaseInfo(),
		TenantID:         info.TenantID,
	}
	tool.NewRemoteLog(context.Background(), "report").AddField("clientIp", info.RemoteIP).
		AddField("appId", info.AppID).AddIntField("dataSetId", int(info.DatasetID)).AddField("id", id).
		AddIntField("callId", int(info.SequenceID)).AddField("scane", "TimeoutCheck").
		AddIntField("step", beginStep).AddField("namespace", trpc.GlobalConfig().Global.Namespace).
		ReportDebug(notify.String())
	data, err := proto.Marshal(notify)
	if err != nil {
		log.ErrorContextf(ctx, "fail to marshal ModifyNotify, err is %v.", err)
		return
	}

	key := []byte(fmt.Sprintf("timeout_%d_%s", datasourceID, id))
	topic := config.GetConfig().CheckTask.Topic
	// 必须使用context.Background()，否则会因为超过整体链路时间而一定失败
	if err = kafka.NewClientProxy("trpc.kafka.timeout.checktask").AsyncSendMessage(context.Background(), topic,
		key, data); err != nil {
		log.ErrorContextf(ctx, "fail to send kafka message of %s, err is %v.", key, err)
		produceErrHandle(err.Error(), topic, key, data)
	}
}

// AddTimeoutCheckTask 创建更新超时检查任务
func AddTimeoutCheckTask(ctx context.Context, datasourceID int32, id, extraInfo string, modifyInfo *model.ModifyInfo) {
	if modifyInfo == nil {
		return
	}

	var info updateInfo
	if err := json.Unmarshal([]byte(extraInfo), &info); err != nil {
		log.ErrorContextf(ctx, "fail to unmarshal, data is %s, err is %v.", extraInfo, err)
		return
	}
	sendCheckTaskMsg(ctx, datasourceID, id, &info, modifyInfo)
}
