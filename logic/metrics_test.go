package logic

import (
	"testing"

	metadata "git.woa.com/trpcprotocol/media_event_hub/metadata_access"
)

func TestNewMetricsReporter(t *testing.T) {
	bridgeID := int64(123)
	bridgeInfo := &metadata.EventBridge{
		Id:   bridgeID,
		Name: "test_bridge",
	}

	reporter := NewMetricsReporter(bridgeID, bridgeInfo)

	if reporter == nil {
		t.Fatal("NewMetricsReporter() returned nil")
	}

	if reporter.bridgeID != bridgeID {
		t.<PERSON><PERSON>("NewMetricsReporter() bridgeID = %d, want %d", reporter.bridgeID, bridgeID)
	}

	if reporter.bridgeInfo != bridgeInfo {
		t.Errorf("NewMetricsReporter() bridgeInfo = %v, want %v", reporter.bridgeInfo, bridgeInfo)
	}
}

func TestMetricsReporter_ReportMetrics(t *testing.T) {
	tests := []struct {
		name         string
		bridgeInfo   *metadata.EventBridge
		reqCount     float64
		errCount     float64
		successCount float64
		delay        float64
		cost         float64
		shouldReport bool
	}{
		{
			name: "正常上报",
			bridgeInfo: &metadata.EventBridge{
				Id:   123,
				Name: "test_bridge",
			},
			reqCount:     1,
			errCount:     0,
			successCount: 1,
			delay:        100,
			cost:         50,
			shouldReport: true,
		},
		{
			name: "延迟为负数不上报",
			bridgeInfo: &metadata.EventBridge{
				Id:   123,
				Name: "test_bridge",
			},
			reqCount:     1,
			errCount:     0,
			successCount: 1,
			delay:        -100,
			cost:         50,
			shouldReport: false,
		},
		{
			name: "花费时间为0不上报",
			bridgeInfo: &metadata.EventBridge{
				Id:   123,
				Name: "test_bridge",
			},
			reqCount:     1,
			errCount:     0,
			successCount: 1,
			delay:        100,
			cost:         0,
			shouldReport: false,
		},
		{
			name:         "bridgeInfo为空",
			bridgeInfo:   nil,
			reqCount:     1,
			errCount:     0,
			successCount: 1,
			delay:        100,
			cost:         50,
			shouldReport: true,
		},
		{
			name: "bridgeName为空",
			bridgeInfo: &metadata.EventBridge{
				Id:   123,
				Name: "",
			},
			reqCount:     1,
			errCount:     0,
			successCount: 1,
			delay:        100,
			cost:         50,
			shouldReport: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reporter := NewMetricsReporter(123, tt.bridgeInfo)
			
			// 这里我们只是调用方法，确保不会panic
			// 在实际环境中，这会上报到监控系统
			reporter.ReportMetrics(tt.reqCount, tt.errCount, tt.successCount, tt.delay, tt.cost)
			
			// 测试通过表示没有panic
		})
	}
}
