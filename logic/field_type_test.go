package logic

import (
	"reflect"
	"testing"

	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/common"
)

func TestBuildStrFieldOpStr(t *testing.T) {
	type args struct {
		ty     protocol.EnumUpdateType
		curVal string
		newVal string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 bool
	}{
		{
			name: "测试set",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeSet,
				curVal: "oldVal",
				newVal: "newVal",
			},
			want:  "newVal",
			want1: true,
		}, {
			name: "测试del",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeDel,
				curVal: "oldVal",
				newVal: "",
			},
			want:  "",
			want1: true,
		}, {
			name: "测试append",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeAppend,
				curVal: "oldVal",
				newVal: "newVal",
			},
			want:  "oldValnewVal",
			want1: true,
		}, {
			name: "测试其他",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeReverse,
				curVal: "oldVal",
				newVal: "newVal",
			},
			want:  "",
			want1: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := BuildStrFieldOpStr(tt.args.ty, tt.args.curVal, tt.args.newVal)
			if got != tt.want {
				t.Errorf("BuildStrFieldOpStr() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("BuildStrFieldOpStr() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestBuildVecFieldOpStr(t *testing.T) {
	type args struct {
		ty     protocol.EnumUpdateType
		curVal string
		newVal []uint32
	}
	tests := []struct {
		name       string
		args       args
		wantRetStr string
		wantOk     bool
	}{
		{
			name: "测试set",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeSet,
				curVal: "123",
				newVal: []uint32{234},
			},
			wantRetStr: "234",
			wantOk:     true,
		}, {
			name: "测试del",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeDel,
				curVal: "123+234",
				newVal: []uint32{234},
			},
			wantRetStr: "123",
			wantOk:     true,
		}, {
			name: "测试del不存在的",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeDel,
				curVal: "123+234",
				newVal: []uint32{123, 1234},
			},
			wantRetStr: "234",
			wantOk:     true,
		}, {
			name: "测试全部del",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeDel,
				curVal: "123+234",
				newVal: []uint32{234, 123},
			},
			wantRetStr: "",
			wantOk:     true,
		}, {
			name: "测试append",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeAppend,
				curVal: "123",
				newVal: []uint32{234},
			},
			wantRetStr: "123+234",
			wantOk:     true,
		}, {
			name: "测试不支持的更新类型",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeReverse,
				curVal: "123",
				newVal: nil,
			},
			wantRetStr: "",
			wantOk:     false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRetStr, gotOk := BuildVecFieldOpStr(tt.args.ty, tt.args.curVal, tt.args.newVal)
			if gotRetStr != tt.wantRetStr {
				t.Errorf("BuildVecFieldOpStr() gotRetStr = %v, want %v", gotRetStr, tt.wantRetStr)
			}
			if gotOk != tt.wantOk {
				t.Errorf("BuildVecFieldOpStr() gotOk = %v, want %v", gotOk, tt.wantOk)
			}
		})
	}
}

func Test_RemoveRepeat(t *testing.T) {
	type args struct {
		source []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "单个字符串",
			args: args{source: []string{"123"}},
			want: []string{"123"},
		}, {
			name: "去重正常场景带空字符串",
			args: args{source: []string{"123", "", "234", "123"}},
			want: []string{"123", "234"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveRepeat(tt.args.source); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("removeRepeat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBuildSetFieldOpStr1(t *testing.T) {
	type args struct {
		ty     protocol.EnumUpdateType
		pos    int
		cut    string
		curVal string
		newVal []string
	}
	tests := []struct {
		name       string
		args       args
		wantResult string
		wantOk     bool
		wantErr    bool
	}{
		{
			name: "测试insert",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeInsert,
				pos:    2,
				cut:    "+",
				curVal: "123+234+345",
				newVal: []string{"456", "789"},
			},
			wantResult: "123+234+456+789+345",
			wantOk:     true,
			wantErr:    false,
		}, {
			name: "测试insert到末尾",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeInsert,
				pos:    -1,
				cut:    "+",
				curVal: "123+234+345",
				newVal: []string{"345", "789"},
			},
			wantResult: "123+234+345+789",
			wantOk:     true,
			wantErr:    false,
		}, {
			name: "测试insert失败",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeInsert,
				pos:    2,
				cut:    "+",
				curVal: "",
				newVal: []string{"456"},
			},
			wantResult: "",
			wantOk:     false,
			wantErr:    true,
		}, {
			name: "测试reorder",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeReorder,
				pos:    2,
				cut:    "+",
				curVal: "123+234+345+4567",
				newVal: []string{"123", "345"},
			},
			wantResult: "234+4567+123+345",
			wantOk:     true,
			wantErr:    false,
		}, {
			name: "测试reorder失败-1",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeReorder,
				pos:    2,
				cut:    "+",
				curVal: "123+234+345",
				newVal: []string{"1234"},
			},
			wantResult: "123+234+345",
			wantOk:     false,
			wantErr:    true,
		}, {
			name: "测试reorder失败-2",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeReorder,
				pos:    100,
				cut:    "+",
				curVal: "123+234+345",
				newVal: []string{"123"},
			},
			wantResult: "123+234+345",
			wantOk:     false,
			wantErr:    true,
		}, {
			name: "测试reverse",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeReverse,
				cut:    "+",
				curVal: "123+234",
			},
			wantResult: "234+123",
			wantOk:     true,
			wantErr:    false,
		}, {
			name: "测试reverse-1",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeReverse,
				cut:    "+",
				curVal: "123",
			},
			wantResult: "123",
			wantOk:     false,
			wantErr:    false,
		}, {
			name: "测试非法更新类型",
			args: args{
				ty: protocol.EnumUpdateType_UpInValidType,
			},
			wantResult: "",
			wantOk:     false,
			wantErr:    true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotResult, gotOk, err := BuildSetFieldOpStr(tt.args.ty, tt.args.pos, tt.args.cut, tt.args.curVal, tt.args.newVal)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildSetFieldOpStr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotResult != tt.wantResult {
				t.Errorf("BuildSetFieldOpStr() gotResult = %v, want %v", gotResult, tt.wantResult)
			}
			if gotOk != tt.wantOk {
				t.Errorf("BuildSetFieldOpStr() gotOk = %v, want %v", gotOk, tt.wantOk)
			}
		})
	}
}

func TestBuildSetFieldOpStr(t *testing.T) {
	type args struct {
		ty     protocol.EnumUpdateType
		pos    int
		cut    string
		curVal string
		newVal []string
	}
	tests := []struct {
		name       string
		args       args
		wantResult string
		wantOk     bool
		wantErr    bool
	}{
		{
			name: "测试set",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeSet,
				cut:    "+",
				curVal: "123+234",
				newVal: []string{"345", "456"},
			},
			wantResult: "345+456",
			wantOk:     true,
			wantErr:    false,
		}, {
			name: "测试del",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeDel,
				cut:    "+",
				curVal: "123+234+345",
				newVal: []string{"123", "345"},
			},
			wantResult: "234",
			wantOk:     true,
			wantErr:    false,
		}, {
			name: "测试del不存在",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeDel,
				cut:    "+",
				curVal: "123+234+345",
				newVal: []string{"1234"},
			},
			wantResult: "123+234+345",
			wantOk:     false,
			wantErr:    false,
		}, {
			name: "测试del失败",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeDel,
				cut:    "+",
				curVal: "123+234+345",
				newVal: []string{"1234", "345"},
			},
			wantResult: "123+234",
			wantOk:     true,
			wantErr:    false,
		}, {
			name: "测试append",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeAppend,
				cut:    "+",
				curVal: "123+234",
				newVal: []string{"345"},
			},
			wantResult: "123+234+345",
			wantOk:     true,
			wantErr:    false,
		}, {
			name: "测试append变为调序",
			args: args{
				ty:     protocol.EnumUpdateType_UpdateTypeAppend,
				cut:    "+",
				curVal: "123+234",
				newVal: []string{"123"},
			},
			wantResult: "234+123",
			wantOk:     true,
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotResult, gotOk, err := BuildSetFieldOpStr(tt.args.ty, tt.args.pos, tt.args.cut, tt.args.curVal, tt.args.newVal)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildSetFieldOpStr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotResult != tt.wantResult {
				t.Errorf("BuildSetFieldOpStr() gotResult = %v, want %v", gotResult, tt.wantResult)
			}
			if gotOk != tt.wantOk {
				t.Errorf("BuildSetFieldOpStr() gotOk = %v, want %v", gotOk, tt.wantOk)
			}
		})
	}
}

func TestBuildMapFieldVal(t *testing.T) {
	type args struct {
		update *protocol.UpdateFieldInfo
		curVal map[string]string
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]string
		want1   bool
		wantErr bool
	}{
		{
			name: "测试k-v",
			args: args{
				update: &protocol.UpdateFieldInfo{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "title",
						FieldType: protocol.EnumFieldType_FieldTypeMapKV,
						MapVal:    map[string]*protocol.MapValue{"cn": {StrValue: "中文标题"}},
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeSet,
				},
				curVal: map[string]string{},
			},
			want:    map[string]string{"cn": "中文标题"},
			want1:   true,
			wantErr: false,
		}, {
			name: "测试k-list",
			args: args{
				update: &protocol.UpdateFieldInfo{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "video_list",
						FieldType: protocol.EnumFieldType_FieldTypeMapKList,
						MapVal:    map[string]*protocol.MapValue{"cn": {VecStr: []string{"vid1"}}},
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeAppend,
				},
				curVal: map[string]string{"cn": "vid2"},
			},
			want:    map[string]string{"cn": "vid2" + common.MapCut + "vid1"},
			want1:   true,
			wantErr: false,
		}, {
			name: "测试删除k-list",
			args: args{
				update: &protocol.UpdateFieldInfo{
					FieldInfo: &protocol.FieldInfo{
						FieldName: "video_list",
						FieldType: protocol.EnumFieldType_FieldTypeMapKList,
						MapVal:    map[string]*protocol.MapValue{"cn": {VecStr: []string{"vid1"}}},
					},
					UpdateType: protocol.EnumUpdateType_UpdateTypeDel,
				},
				curVal: map[string]string{"cn": "vid2" + common.MapCut + "vid1"},
			},
			want:    map[string]string{"cn": "vid2"},
			want1:   true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := BuildMapFieldVal(tt.args.update, tt.args.curVal)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildMapFieldVal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BuildMapFieldVal() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("BuildMapFieldVal() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestDelVecFieldInfoStr(t *testing.T) {
	type args struct {
		intVec    []uint32
		oldVecStr string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 bool
	}{
		{
			name: "测试正常场景-1",
			args: args{
				intVec:    []uint32{123, 456},
				oldVecStr: "123",
			},
			want:  "",
			want1: true,
		}, {
			name: "测试正常场景-2",
			args: args{
				intVec:    []uint32{123, 456},
				oldVecStr: "+123",
			},
			want:  "",
			want1: true,
		}, {
			name: "测试正常场景-3",
			args: args{
				intVec:    []uint32{123, 456},
				oldVecStr: "+123+",
			},
			want:  "",
			want1: true,
		}, {
			name: "测试正常场景-4",
			args: args{
				intVec:    []uint32{123},
				oldVecStr: "+789+123+456",
			},
			want:  "789+456",
			want1: true,
		}, {
			name: "测试删除不存在的值",
			args: args{
				intVec:    []uint32{123, 456},
				oldVecStr: "12345+23456",
			},
			want:  "12345+23456",
			want1: false,
		}, {
			name: "异常场景-1",
			args: args{
				intVec:    []uint32{123, 456},
				oldVecStr: "+",
			},
			want:  "",
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := DelVecFieldInfoStr(tt.args.intVec, tt.args.oldVecStr)
			if got != tt.want {
				t.Errorf("DelVecFieldInfoStr() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("DelVecFieldInfoStr() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestAddVecFieldInfoStr(t *testing.T) {
	type args struct {
		intVec    []uint32
		oldVecStr string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 bool
	}{
		{
			name: "正常场景-1",
			args: args{
				intVec:    []uint32{567, 789},
				oldVecStr: "123+456",
			},
			want:  "123+456+567+789",
			want1: true,
		}, {
			name: "正常场景-2",
			args: args{
				intVec:    []uint32{567, 789},
				oldVecStr: "+123+456+",
			},
			want:  "123+456+567+789",
			want1: true,
		}, {
			name: "正常场景-3",
			args: args{
				intVec:    []uint32{123, 789},
				oldVecStr: "+123+456+",
			},
			want:  "123+456+789",
			want1: true,
		}, {
			name: "插入选项都已经存在",
			args: args{
				intVec:    []uint32{123, 789},
				oldVecStr: "+123+789+",
			},
			want:  "123+789",
			want1: false,
		}, {
			name: "异常场景-1",
			args: args{
				intVec:    []uint32{567, 789},
				oldVecStr: "+",
			},
			want:  "567+789",
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := AddVecFieldInfoStr(tt.args.intVec, tt.args.oldVecStr)
			if got != tt.want {
				t.Errorf("AddVecFieldInfoStr() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("AddVecFieldInfoStr() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestParseMapFieldName(t *testing.T) {
	type args struct {
		fieldName string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
		want2 bool
	}{
		{
			name:  "测试只有一个.",
			args:  args{fieldName: "."},
			want:  "",
			want1: "",
			want2: true,
		}, {
			name:  "测试字段为空",
			args:  args{fieldName: ""},
			want:  "",
			want1: "",
			want2: false,
		}, {
			name:  "测试正常map字段请求",
			args:  args{fieldName: "testMap.testKey"},
			want:  "testMap",
			want1: "testKey",
			want2: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := ParseMapFieldName(tt.args.fieldName)
			if got != tt.want {
				t.Errorf("ParseMapFieldName() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("ParseMapFieldName() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("ParseMapFieldName() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}
