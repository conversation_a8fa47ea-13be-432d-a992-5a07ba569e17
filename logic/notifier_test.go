package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"github.com/agiledragon/gomonkey/v2"
)

func TestHandleNotifier(t *testing.T) {
	var ipCraw *dao.IPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(ipCraw), "GetIPCount",
		func(_ *dao.IPCrawTask, ctx context.Context, typeID int) (uint64, error) {
			return 1, nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(ipCraw), "GetIPSucCount",
		func(_ *dao.IPCrawTask, ctx context.Context,
			crawType int, createDate string, typeID int) (uint64, error) {
			return 1, nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(ipCraw), "GetIPUnconfirmedCount",
		func(_ *dao.IPCrawTask, ctx context.Context,
			crawType int, createDate string,
			typeID int) (uint64, error) {
			return 1, nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(ipCraw), "GetUnconfirmedCoreCover",
		func(_ *dao.IPCrawTask, ctx context.Context,
			createDate string, typeID int) ([]*dao.IPCrawTask,
			error) {
			return nil, nil
		})

	var cpCraw *dao.CPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(cpCraw), "GetCPCount",
		func(_ *dao.CPCrawTask, ctx context.Context, crawType int) (uint64, error) {
			return 2, nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(cpCraw), "GetCPSucCount",
		func(_ *dao.CPCrawTask, ctx context.Context, createDate string, crawType int) (uint64, error) {
			return 2, nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(ipCraw), "GetManualCheckEmptyKeyTask",
		func(_ *dao.IPCrawTask, ctx context.Context) ([]*dao.IPCrawTask, error) {
			return []*dao.IPCrawTask{
				{ID: 123, CrawType: model.OfficeAccountType, Platform: "shipinhao"},
			}, nil
		})

	patches := gomonkey.ApplyFunc(dao.SendQyWxMsg, func(ctx context.Context, content string, msgType string) error {
		return nil
	})
	defer patches.Reset()

	type args struct {
		ctx context.Context
		in1 string
		in2 string
		in3 int32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := HandleNotifier(tt.args.ctx, tt.args.in1, tt.args.in2, tt.args.in3); (err != nil) != tt.wantErr {
				t.Errorf("HandleNotifier() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGenJumpWUJILink(t *testing.T) {
	type filter struct {
		FieldID    string `json:"id"`
		FieldVal   int    `json:"s"`
		Op         string `json:"o"`
		SearchType int    `json:"t"`
	}

	tests := []struct {
		name    string
		typeID  int
		wantURL string
	}{
		{
			name:   "Test case 1",
			typeID: 1,
			wantURL: "https://wuji.woa.com/p/edit?appid=ovbu_crawler_data&schemaid=t_ip_crawler_conf" +
				"&sort=&order=&filterPreset=default&nsid=_all&filterArr=",
		},
		{
			name:   "Test case 2",
			typeID: 2,
			wantURL: "https://wuji.woa.com/p/edit?appid=ovbu_crawler_data&schemaid=t_ip_crawler_conf" +
				"&sort=&order=&filterPreset=default&nsid=_all&filterArr=",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filterArr := []filter{
				{
					FieldID:    "c_cover_type",
					FieldVal:   tt.typeID,
					Op:         "=",
					SearchType: 5,
				},
				{
					FieldID:    "c_valid",
					FieldVal:   1,
					Op:         "=",
					SearchType: 5,
				},
			}
			filterStr, _ := json.Marshal(filterArr)
			tt.wantURL += url.QueryEscape(string(filterStr))
			fmt.Println(tt.wantURL)

			if gotURL := genJumpWUJILink(tt.typeID); gotURL != tt.wantURL {
				t.Errorf("genJumpWUJILink() = %v, want %v", gotURL, tt.wantURL)
			}
		})
	}
}
