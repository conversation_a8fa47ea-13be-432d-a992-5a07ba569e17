package logic

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

// 触发抓取job
// job的定义为：一个原子的抓取任务，例如一个剧集信息的抓取；
// 一般是扫描job存储表（t_crawler_job_detail_X），找到所有未抓取的job，然后执行抓取

func processCrawJobs(ctx context.Context, crawJobs []*dao.CrawJob) error {
	for _, job := range crawJobs {
		if !job.IsPushCraw() {
			continue
		}
		if err := StartCrawOrder(ctx, job.OrderID, job.ID, job.JobArguments); err != nil {
			log.ErrorContextf(ctx, "StartCrawOrder ERR:%+v", err)
			continue
		}
		job.DayCrawlerState = model.CrawlerRunning
		if err := job.SetCrawJobState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawJobState ERR:%+v", err)
		}
	}
	return nil
}

func fetchAllJobs(ctx context.Context, orderID int) ([]*dao.CrawJob, error) {
	if orderID <= 0 {
		log.ErrorContextf(ctx, "orderID is invalid! %d", orderID)
		return nil, fmt.Errorf("orderID is invalid")
	}
	crawJob := &dao.CrawJob{}
	crawJob.OrderID = orderID
	return crawJob.GetAllCrawJobs(ctx, time.Now().Format("2006-01-02"))
}

// HandlePushCrawJobs 触发抓取job
func HandlePushCrawJobs(ctx context.Context, _, params string, _ int32) error {
	log.InfoContextf(ctx, "HandlePushCrawJobs enter:%s", params)
	orderID, _ := strconv.Atoi(params)
	crawJobs, err := fetchAllJobs(ctx, orderID)
	if err != nil {
		return err
	}

	if err = processCrawJobs(ctx, crawJobs); err != nil {
		return err
	}
	log.InfoContextf(ctx, "HandlePushCrawJobs suc")
	return nil
}
