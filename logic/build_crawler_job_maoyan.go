package logic

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

// 生成抓取job【一般和业务逻辑相关】
// job的定义为：一个原子的抓取任务，例如一个剧集信息的抓取；
// 一般是根据产品业务逻辑，以及爬虫的抓取规则，生成job 写入job存储表（t_crawler_job_detail_X）

func generateMaoyanSearchCrawJobs(coverInfos []*dao.CoverInfo, orderID int) ([]*dao.CrawJob, error) {
	var crawJobs []*dao.CrawJob
	for _, info := range mergeDupCoverInfo(coverInfos) {
		jobArguments := map[string]interface{}{
			"query": info.Title,
		}
		bs, _ := json.Marshal(jobArguments)
		crawJobs = append(crawJobs, &dao.CrawJob{
			OrderID:      orderID,
			Platform:     "maoyan",
			TransmitKey:  genTransmit(0, 0, orderID, info.CoverID),
			Title:        info.Title + "|" + model.TransTypeID(info.CoverType), // 把频道也带入，方便后续流程查频道信息
			JobArguments: string(bs),
			Valid:        1,
			Ext:          info.Ext,
		})
	}
	return crawJobs, nil
}

func genYunHeBoxSearchCrawJobs(coverInfos []*dao.CoverInfo, orderID int) ([]*dao.CrawJob, error) {
	var crawJobs []*dao.CrawJob
	for _, info := range mergeDupCoverInfo(coverInfos) {
		if info.CoverType != 2 { // 非电视剧的都不需要
			continue
		}
		jobArguments := map[string]interface{}{
			"query": info.Title,
			"type":  "tv", // 目前只需要电视剧，tv为电视剧 | movie为电影 | art为综艺
		}
		bs, _ := json.Marshal(jobArguments)
		crawJobs = append(crawJobs, &dao.CrawJob{
			OrderID:      orderID,
			Platform:     "yunhe",
			TransmitKey:  genTransmit(0, 0, orderID, info.CoverID),
			Title:        info.Title + "|" + model.TransTypeID(info.CoverType), // 把频道也带入，方便后续流程查频道信息
			JobArguments: string(bs),
			Valid:        1,
			Ext:          info.Ext,
		})
	}
	return crawJobs, nil
}

type extInfo struct {
	CoverID string `json:"cover_id"`
	Title   string `json:"name"`
}

// getTxVideoFocusCoverInfo 拉取腾讯视频运营期重点剧信息
func getTxVideoFocusCoverInfo(ctx context.Context) ([]*dao.CoverInfo, error) {
	var validCovers []*dao.CoverInfo
	allCoverInfos, err := getAllCoverInfo(ctx)
	if err != nil {
		log.Error(ctx, "getAllCoverInfo error", err)
		return nil, err
	}
	for _, info := range allCoverInfos {
		// 仅四大频道(电影、电视剧、综艺、动漫)的触发抓取
		if info.CoverType != 1 && info.CoverType != 2 && info.CoverType != 3 && info.CoverType != 10 {
			continue
		}
		ext := extInfo{
			CoverID: info.CoverID,
			Title:   info.Title,
		}
		tmp, _ := json.Marshal(ext)
		info.Ext = string(tmp)
		if isMonitorLifecycle(info.IPOnlineStatus) {
			validCovers = append(validCovers, info)
		}
	}
	return validCovers, nil
}

// HandleBuildMaoYanSearchJob 生成猫眼搜索的job（根据重点剧抓取池中的数据）,定时触发（在重点剧抓取池构造之后）
func HandleBuildMaoYanSearchJob(ctx context.Context, _, _ string, _ int32) error {
	// 先清空当前的任务池（这是抓取源头任务池，要保持干净）
	for _, orderID := range []int{model.MaoYanTxVideoSearchOrder, model.MaoYanCompetitorSearchOrder,
		model.YunHeBoxCompetitorSearchOrder} {
		var c dao.CrawJob
		c.OrderID = orderID
		if err := c.CleanCrawJobs(ctx, time.Now().AddDate(-1, 0, 0).Format("2006-01-02")); err != nil {
			return err
		}
	}

	// 腾讯视频IP抓取任务
	txVideoCoverInfos, err := getTxVideoFocusCoverInfo(ctx)
	if err != nil {
		return err
	}
	var crawInfos []*dao.CrawJob
	txVideoCrawInfos, err := generateMaoyanSearchCrawJobs(txVideoCoverInfos, model.MaoYanTxVideoSearchOrder)
	if err != nil {
		return err
	}
	crawInfos = append(crawInfos, txVideoCrawInfos...)

	// 竞品排播IP抓取任务
	competitorCoverInfos, err := dao.GetAllPlanInfo(ctx, []string{"电影", "电视剧", "综艺"}) // 数据过滤逻辑都在GetAllPlanInfo中
	if err != nil {
		return err
	}
	competitorCrawInfos, err := generateMaoyanSearchCrawJobs(competitorCoverInfos, model.MaoYanCompetitorSearchOrder)
	if err != nil {
		return err
	}
	crawInfos = append(crawInfos, competitorCrawInfos...)
	// 云合工具箱IP抓取任务
	yunHeCrawInfos, err := genYunHeBoxSearchCrawJobs(competitorCoverInfos, model.YunHeBoxCompetitorSearchOrder)
	if err != nil {
		return err
	}
	crawInfos = append(crawInfos, yunHeCrawInfos...)

	for _, info := range crawInfos {
		if err = info.InsertCrawJob(ctx); err != nil {
			return err
		}
	}
	return nil
}

// standardizationCategory 把猫眼品类归一化到媒资分类
func standardizationCategory(maoyanType string) string {
	if maoyanType == "网络剧" || maoyanType == "tv" {
		maoyanType = "电视剧"
	}
	if maoyanType == "网络电影" || maoyanType == "movie" {
		maoyanType = "电影"
	}
	if maoyanType == "art" {
		maoyanType = "综艺"
	}
	return maoyanType
}

// MaoyanSearchCrawRet 猫眼搜索返回结果
type MaoyanSearchCrawRet struct {
	OriCrawID int    // 触发抓取时的抓取ID（标识一次抓取任务）
	Category  string `json:"category"` // 剧集品类
	MaoyanID  string `json:"id"`       // 猫眼ID
	Title     string `json:"name"`     // 剧集名
	Rank      int    `json:"rank"`     // 排名
	Ext       string // 扩展信息
}

// transSearch2DetailOrderID 转换订单ID，把搜索订单ID转成详情订单ID
func transSearch2DetailOrderID(searchOrderID int) int {
	detailOrderID := searchOrderID
	if searchOrderID == model.MaoYanTxVideoSearchOrder {
		detailOrderID = model.MaoYanTxVideoIPDetailOrder
	} else if searchOrderID == model.MaoYanCompetitorSearchOrder {
		detailOrderID = model.MaoYanCompetitorIPDetailOrder
	} else if searchOrderID == model.YunHeBoxCompetitorSearchOrder {
		detailOrderID = model.YunHeBoxCompetitorIPDetailOrder
	} else if searchOrderID == model.DouYinCompetitorSearchOrder {
		detailOrderID = model.DouYinCompetitorIPDetailOrder
	}
	return detailOrderID
}

func transDetail2SearchOrderID(detailOrderID int) int {
	searchOrderID := detailOrderID
	if detailOrderID == model.MaoYanTxVideoIPDetailOrder {
		searchOrderID = model.MaoYanTxVideoSearchOrder
	} else if detailOrderID == model.MaoYanCompetitorIPDetailOrder {
		searchOrderID = model.MaoYanCompetitorSearchOrder
	} else if detailOrderID == model.YunHeBoxCompetitorIPDetailOrder {
		searchOrderID = model.YunHeBoxCompetitorSearchOrder
	} else if detailOrderID == model.DouYinCompetitorIPDetailOrder {
		searchOrderID = model.DouYinCompetitorSearchOrder
	}
	return searchOrderID
}

// id映射-不同的平台去获取详情的时候id不一样
var mapQueryID = map[string]string{
	"maoyan": "maoyan_id",
	"yunhe":  "id",
}

func genMaoYanDetailJobs(orderID int, maoyanSearchDetail *MaoyanSearchCrawRet, platform string) *dao.CrawJob {
	if maoyanSearchDetail == nil {
		return nil
	}
	orderID = transSearch2DetailOrderID(orderID) // 确保是详情抓取订单

	jobArguments := map[string]interface{}{
		mapQueryID[platform]: maoyanSearchDetail.MaoyanID,
	}
	bs, _ := json.Marshal(jobArguments)
	return &dao.CrawJob{
		OrderID:  orderID,
		Platform: platform,
		TransmitKey: genTransmit(0, maoyanSearchDetail.OriCrawID, orderID,
			time.Now().Format("20060102150405")),
		Title:        maoyanSearchDetail.Title,
		JobArguments: string(bs),
		Valid:        1,
		Ext:          maoyanSearchDetail.Ext,
	}
}

// 针对具体的品类才进行校验
func checkCoverType(coverType string) bool {
	if coverType == "电影" {
		return true
	}
	if coverType == "电视剧" {
		return true
	}
	if coverType == "动漫" {
		return true
	}
	return false
}

// generateMaoyanCrawJobByTitle 根据剧集标题，生成猫眼剧集抓取任务
func generateMaoyanCrawJobByTitle(ctx context.Context, orderID int, crawTitle, platform string) (*dao.CrawJob, error) {
	log.InfoContextf(ctx, "generateMaoyanCrawJobByTitle enter:%d-%s", orderID, crawTitle)
	var c dao.CrawDetail
	c.OrderID = transDetail2SearchOrderID(orderID) // 从猫眼搜索结果表中获取数据
	maoyanSearchDetail, err := c.GetCrawDetailByTitle(ctx, crawTitle)
	if err != nil {
		return nil, err
	}

	// 取搜索返回剧集名与原剧集名相同，且搜索返回频道与原剧集频道相同的
	maoyanInfos := make(map[string]*MaoyanSearchCrawRet)
	for _, info := range maoyanSearchDetail {
		var coverType string
		coverInfo := strings.Split(info.Title, "|")
		if len(coverInfo) >= 2 {
			// coverTitle = coverInfo[0]
			coverType = coverInfo[1]
		}

		var maoyanSearchDetail MaoyanSearchCrawRet
		if err = json.Unmarshal([]byte(info.Detail), &maoyanSearchDetail); err != nil {
			log.ErrorContextf(ctx, "Unmarshal err-%+v", err)
			continue
		}
		// 具体品类才进行品类校验
		if checkCoverType(coverType) && coverType != standardizationCategory(maoyanSearchDetail.Category) {
			continue
		}
		maoyanSearchDetail.OriCrawID = info.ID
		m, exists := maoyanInfos[crawTitle]
		if info.Ext != "" {
			maoyanSearchDetail.Ext = info.Ext
		}
		if !exists || (exists && maoyanSearchDetail.Rank < m.Rank) { // 有多个重名的剧集，保留排名靠前的
			maoyanInfos[crawTitle] = &maoyanSearchDetail
		}
	}
	return genMaoYanDetailJobs(orderID, maoyanInfos[crawTitle], platform), nil
}

func buildMaoYanDetailJob(ctx context.Context, detailOrderID int) error {
	// 先清空 详情抓取任务表（由于抓取范围不同，每天要清空一次）
	var c dao.CrawJob
	c.OrderID = detailOrderID
	if err := c.CleanCrawJobs(ctx, time.Now().AddDate(-1, 0, 0).Format("2006-01-02")); err != nil {
		return err
	}

	// 扫描猫眼搜索job表（此表即为猫眼项目要处理的数据）
	c.OrderID = transDetail2SearchOrderID(detailOrderID)                      // 从猫眼搜索job表（即抓取源头）中获取数据
	maoyanJobs, err := c.GetAllCrawJobs(ctx, time.Now().Format("2006-01-02")) // 拉取今天所有需要抓取的数据
	if err != nil {
		return err
	}

	// 生成当天详情抓取任务(根据搜索结果)
	for _, job := range maoyanJobs { // maoyanJobs 中的title是唯一的
		// 根据剧集标题 生成猫眼详情抓取任务
		crawJob, err := generateMaoyanCrawJobByTitle(ctx, detailOrderID, job.Title, job.Platform)
		if err != nil {
			log.ErrorContextf(ctx, "generateMaoyanCrawJobByTitle ERR:%+v", err)
			return err // contiue
		}
		if crawJob == nil {
			log.InfoContextf(ctx, "%s not generate job", job.Title)
			continue
		}
		// 写入猫眼详情抓取表
		if err = crawJob.InsertCrawJob(ctx); err != nil {
			return err
		}
	}
	return nil
}

// HandleBuildMaoYanDetailJob 生成猫眼IP详情抓取的job（根据猫眼搜索结果数据）
func HandleBuildMaoYanDetailJob(ctx context.Context, _, _ string, _ int32) error {
	// 猫眼有两类数据：腾讯视频站内的、竞品的 详情抓取
	for _, orderID := range []int{model.MaoYanTxVideoIPDetailOrder, model.MaoYanCompetitorIPDetailOrder,
		model.YunHeBoxCompetitorIPDetailOrder} {
		if err := buildMaoYanDetailJob(ctx, orderID); err != nil {
			log.ErrorContextf(ctx, "buildMaoYanDetailJob ERR:%+v", err)
			return err
		}
	}
	return nil
}
