package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

// TypeName 媒资大分类id-中文名
type TypeName struct {
	ID   int
	Name string
}

var AllMonitorTypes = []TypeName{
	{ID: 2, Name: "电视剧"},
	{ID: 3, Name: "动漫"},
	{ID: 10, Name: "综艺"},
	{ID: 1, Name: "电影"},
	{ID: 9, Name: "纪录片"},
}

func getCPStatistics(ctx context.Context, curDate string, crawType int) (uint64, uint64, error) {
	var cpCraw *dao.CPCrawTask
	cpCount, err := cpCraw.GetCPCount(ctx, crawType)
	if err != nil {
		return 0, 0, err
	}
	cpCrawSucCount, err := cpCraw.GetCPSucCount(ctx, curDate, crawType)
	if err != nil {
		return 0, 0, err
	}
	return cpCount, cpCrawSucCount, nil
}

func generateCPStatistics(ctx context.Context, curDate string) (string, error) {
	// 获取CP账号数据
	cpCount, cpCrawSucCount, err := getCPStatistics(ctx, curDate, model.CPOfficeAccountType)
	if err != nil {
		return "", err
	}
	// 获取MCN账号数据
	mcnCount, mcnCrawSucCount, err := getCPStatistics(ctx, curDate, model.CPMCNType)
	if err != nil {
		return "", err
	}
	return "**【CP维度】**" + fmt.Sprintf("已录入平台官号%d个，抓取成功%d个;MCN账号%d个，抓取成功%d个；点击查看详情："+
		"[CP抓取任务](https://wuji.oa.com/p/edit?appid=ovbu_crawler_data&schemaid=t_cp_crawler_conf)\n "+
		" \n",
		cpCount, cpCrawSucCount, mcnCount, mcnCrawSucCount), nil
}

func getIPStatistics(ctx context.Context, curDate string, ipCraw dao.IPCrawTask, typeID int,
	crawType int) (uint64, uint64, error) {
	topicCount, err := ipCraw.GetIPSucCount(ctx, crawType, curDate, typeID)
	if err != nil {
		return 0, 0, err
	}
	unconfirmedTopicCount, err := ipCraw.GetIPUnconfirmedCount(ctx, crawType, curDate, typeID)
	if err != nil {
		return 0, 0, err
	}
	return topicCount, unconfirmedTopicCount, nil
}

func genJumpWUJILink(typeID int) string {
	type filter struct {
		FieldID    string `json:"id"`
		FieldVal   int    `json:"s"`
		Op         string `json:"o"`
		SearchType int    `json:"t"`
	}
	filterArr := []filter{
		{
			FieldID:    "c_cover_type",
			FieldVal:   typeID,
			Op:         "=",
			SearchType: 5,
		},
		{
			FieldID:    "c_valid",
			FieldVal:   1,
			Op:         "=",
			SearchType: 5,
		},
	}
	filterStr, _ := json.Marshal(filterArr)
	return "https://wuji.woa.com/p/edit?appid=ovbu_crawler_data&schemaid=t_ip_crawler_conf" +
		"&sort=&order=&filterPreset=default&nsid=_all&filterArr=" + url.QueryEscape(string(filterStr))
}

func generateIPStatistics(ctx context.Context, curDate string, t TypeName) (string, error) {
	var ipCraw dao.IPCrawTask
	typeID := t.ID
	typeName := t.Name
	IPCount, err := ipCraw.GetIPCount(ctx, typeID)
	if err != nil {
		return "", err
	}
	// 获取IP话题数据
	topicCount, unconfirmedTopicCount, err := getIPStatistics(ctx,
		curDate, ipCraw, typeID, model.TopicType)
	if err != nil {
		return "", err
	}
	// 获取IP官号数据
	officeAccountCount, unconfirmedOfficeCount, err := getIPStatistics(ctx,
		curDate, ipCraw, typeID, model.OfficeAccountType)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("**【%s-监控期IP-%d个】**\nIP官号：五大平台抓取<font color=\"#3333ff\">成功%d个</font>，"+
		"<font color=\"warning\">待确认%d个</font>；"+
		"IP话题：五大平台抓取 <font color=\"#3333ff\">成功%d个</font>，<font color=\"warning\">待确认%d个</font>；\n",
		typeName, IPCount, officeAccountCount,
		unconfirmedOfficeCount, topicCount, unconfirmedTopicCount), nil
}

// generateContents 生成需要发送的文字分段
func generateContents(ctx context.Context, curDate string) ([]string, error) {
	var contents []string
	headContxt := fmt.Sprintf("<font color=\"info\">**【%s站外数据抓取通知】**</font> \n",
		time.Now().AddDate(0, 0, -1).Format("0102"))
	contents = append(contents, headContxt)

	// CP维度的统计
	cpStatistics, err := generateCPStatistics(ctx, curDate)
	if err != nil {
		return nil, err
	}
	contents = append(contents, cpStatistics)

	// IP维度的统计
	ipContents, err := generateIPContents(ctx, curDate)
	if err != nil {
		return nil, err
	}
	contents = append(contents, ipContents...)

	// 底部信息
	tailContxt := generateTailContxt()
	contents = append(contents, tailContxt)

	manualCheckEmptyKeyContxt, err := getManualCheckContext(ctx)
	if err != nil {
		return nil, err
	}
	if manualCheckEmptyKeyContxt != "" {
		contents = append(contents, manualCheckEmptyKeyContxt)
	}
	return contents, nil
}

// generateIPContents 生成IP维度的统计内容
func generateIPContents(ctx context.Context, curDate string) ([]string, error) {
	var ipContents []string
	for _, t := range AllMonitorTypes {
		ipStatistics, err := generateIPStatistics(ctx, curDate, t)
		if err != nil {
			return nil, err
		}
		coreCoverStats, err := generateCoreCoverStatistics(ctx, curDate, t)
		if err != nil {
			return nil, err
		}
		ipStatistics += coreCoverStats + " \n"
		ipContents = append(ipContents, ipStatistics)
	}
	if len(ipContents) > 0 {
		ipContents[0] = "**【IP维度】**\n" + ipContents[0]
	}
	return ipContents, nil
}

// generateTailContxt 生成底部信息
func generateTailContxt() string {
	return "因各平台运营行为存在差异，详情请查看：" +
		"[IP数据监控](https://beacon.woa.com/datatalk/txsp_video_ds/dashboard/216534?menuIds=menu_ikcpw32e)\n" +
		"如需对IP官号、IP话题进行确认矫正，请于任务录入系统进行操作：" +
		"[IP抓取任务](https://wuji.woa.com/p/edit?appid=ovbu_crawler_data&schemaid=t_ip_crawler_conf)\n" +
		"如有疑问请联系媒资产品rossieyang."
}

// HandleNotifier 发送企微通知入口
// NOCC:CCN_threshold(设计如此:)
func HandleNotifier(ctx context.Context, _, _ string, _ int32) error {
	// 生成需要发送的文字分段
	curDate := time.Now().Format("2006-01-02")
	contents, err := generateContents(ctx, curDate)
	if err != nil {
		return err
	}

	// 分批发送(企微对文本长度有限制，大文本需要截断成多条发送)
	batchNum := 4 // 一批发送batchNum条
	var alarmMsgs []string
	for i := 0; i < len(contents); i += batchNum {
		if i+batchNum <= len(contents) {
			alarmMsgs = append(alarmMsgs, strings.Join(contents[i:i+batchNum], "\n"))
		} else {
			alarmMsgs = append(alarmMsgs, strings.Join(contents[i:], "\n"))
		}
	}
	if len(alarmMsgs) == 0 {
		alarmMsgs = append(alarmMsgs, "")
	}
	rbPath := conf.GetConfig().RobotCallbackPath
	for i, msg := range alarmMsgs {
		log.InfoContextf(ctx, "SendQyWxMsg:%s;%d", msg, i)
		if err = dao.SendQyWxMsg(ctx, rbPath, msg+fmt.Sprintf("(%d/%d)", i+1, len(alarmMsgs))); err != nil {
			log.ErrorContextf(ctx, "SendQyWxMsg err:%+v", err)
			return err
		}
	}
	return maoyanNotifier(ctx)
}

// 猫眼数据监控
func maoyanNotifier(ctx context.Context) error {
	path := conf.GetConfig().MaoyanRobotCallbackPath
	rawStr := "【开始抓取时间】%s\n【监控渠道】猫眼IP数据\n【下发抓取任务数量】%d个\n【抓取成功数量】%d个\n【未成功明细】%s"
	ts, fs, pushedCnt, sucCnt := getMaoStat(ctx)
	msg := fmt.Sprintf(rawStr, ts, pushedCnt, sucCnt, fs)
	if err := dao.SendQyWxMsg(ctx, path, msg); err != nil {
		log.ErrorContextf(ctx, "SendQyWxMsg err:%+v", err)
		return err
	}
	return nil
}

// getMaoStat获取猫眼统计数据
// ts:开始抓取时间，fs: 失败列表名，pushedCnt:下发数，sucCnt:成功数
func getMaoStat(ctx context.Context) (ts, fs string, pushedCnt, sucCnt int) {
	var c dao.CrawJob
	c.OrderID = model.MaoYanCompetitorSearchOrder
	startTime := time.Now().Format("2006-01-02")
	jobs, err := c.GetAllCrawJobs(ctx, startTime)
	if err != nil {
		log.ErrorContextf(ctx, `GetAllCrawJobs failed err =%+v`, err)
		return
	}
	var d dao.CrawDetail
	d.OrderID = model.MaoYanCompetitorIPDetailOrder
	ds, err := d.GetCrawDetail(ctx, startTime)
	if err != nil {
		log.ErrorContextf(ctx, `GetCrawDetail failed err =%+v`, err)
		return
	}
	pushedCnt = len(jobs)
	if len(jobs) > 0 {
		ts = jobs[0].DayCrawlerStartTime.Format("2006-01-02 15:04:05")
	}
	fs, sucCnt = getFailedJob(jobs, ds)
	return
}

// 获取失败任务的标题集合:res，并返回抓取成功的数量:sucCnt
func getFailedJob(jobs []*dao.CrawJob, ds []*dao.CrawDetail) (res string, sucCnt int) {
	dsMap := make(map[string]string)
	for _, d := range ds {
		ext := dao.PlanInfo{}
		if e := json.Unmarshal([]byte(d.Ext), &ext); e != nil {
			log.Errorf(`unmarshal err =%+v`, e)
			continue
		}
		dsMap[ext.Name] = ext.Name
	}
	sucCnt = len(dsMap)
	for _, j := range jobs {
		titles := strings.Split(j.Title, "|")
		if len(titles) == 0 {
			continue
		}
		key := titles[0]
		if v, ok := dsMap[key]; !ok {
			// 模糊匹配： 冰雪谣（如月）和 冰雪谣  名字不一致的问题
			if strings.Contains(key, v) || strings.Contains(v, key) {
				continue
			}
			res += fmt.Sprintf(`《%s》`, key)
		}
	}
	return
}

// generateCoreCoverStatistics 核心专辑单独告警
func generateCoreCoverStatistics(ctx context.Context, date string, t TypeName) (string, error) {
	var ipCrawTask dao.IPCrawTask
	coreCoverInfos, err := ipCrawTask.GetUnconfirmedCoreCover(ctx, date, t.ID)
	if err != nil {
		return "", err
	}
	coverInfoMap := make(map[string]bool)
	var coreCoverList []string
	for _, info := range coreCoverInfos {
		if _, ok := coverInfoMap[info.CoverID]; ok {
			continue
		}
		coverInfoMap[info.CoverID] = true
		coreCoverList = append(coreCoverList, "《"+info.CrawKey+"》")
	}
	if len(coreCoverList) == 0 {
		return "", nil
	}
	return fmt.Sprintf("重点剧集<font color=\"info\">%s</font>尚未全部确认，请前往管理台 <u>[查询并确认](%s)</u> 抓取结果是否正确。\n",
		strings.Join(coreCoverList, "、"), genJumpWUJILink(t.ID)), nil
}

func getManualCheckContext(ctx context.Context) (string, error) {
	var ipCraw dao.IPCrawTask
	manualCheckEmptyKeyTasks, err := ipCraw.GetManualCheckEmptyKeyTask(ctx)
	if err != nil {
		return "", err
	}
	if len(manualCheckEmptyKeyTasks) == 0 {
		return "", nil
	}
	manualCheckEmptyKeyContxt := "【人工纠正未填写抓取 key】\n"
	for _, item := range manualCheckEmptyKeyTasks {
		manualCheckEmptyKeyContxt += fmt.Sprintf("自增ID:[%d] 抓取类型:[%d] 抓取平台:[%s]\n",
			item.ID, item.CrawType, item.Platform)
	}
	return manualCheckEmptyKeyContxt, nil
}
