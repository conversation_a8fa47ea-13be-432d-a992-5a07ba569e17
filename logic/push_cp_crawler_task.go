package logic

// 推送抓取CP信息，及CP下发文及视频信息（早期的接口，触发一次任务即可）
import (
	"context"
	"fmt"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
)

// CPCrawler 抓取CP的爬虫
type CPCrawler struct {
	CrawTask    dao.CPCrawTask
	ArticleInfo report.ReportArticleInfo
	AccountInfo report.ReportAccountInfo
}

// NewCPCrawler 生成CP爬虫代理
func NewCPCrawler(c dao.CPCrawTask, atl report.ReportArticleInfo, acc report.ReportAccountInfo) *CPCrawler {
	return &CPCrawler{CrawTask: c, ArticleInfo: atl, AccountInfo: acc}
}

// genTransmit 生成transmit
func genTransmit(crawType, crawID, orderID int, remark string) string {
	return fmt.Sprintf("%d_%d_%d_%s", crawType, crawID, orderID, remark)
}

// PushCrawTasks 推送抓取任务
func (c *CPCrawler) PushCrawTasks(ctx context.Context) error {
	crawKey := c.CrawTask.LinkURL // 默认通过URL抓取
	if c.CrawTask.Platform == "shipinhao" {
		crawKey = c.CrawTask.AccountName // 视频号通过账户名抓
	}
	transmit := genTransmit(model.CPInfoType, c.CrawTask.ID, model.CPCrawOrder, "cp")
	if err := dao.AddCrawTask(ctx, &dao.CrawTaskArgs{
		CrawType:      model.CPInfoType,
		Transmit:      transmit,
		Source:        c.CrawTask.Platform,
		URL:           crawKey,
		AccountName:   c.CrawTask.AccountName, // 账号名，主要为了上报统计（实际抓取侧不用这个信息）
		BacktraceDays: CalcBacktraceDays(model.CPInfoType, 0, 0, c.CrawTask.CreateTime),
	}, false); err != nil {
		log.ErrorContextf(ctx, "AddCPInfoCrawTask ERR:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "PushCrawTasks suc:%s", transmit)
	return nil
}

// SetCrawTaskRunningState 设置抓取任务为抓取中
func (c *CPCrawler) SetCrawTaskRunningState(ctx context.Context) error {
	c.CrawTask.DayCrawlerState = model.CrawlerRunning
	return c.CrawTask.SetCrawTaskState(ctx)
}

// SetCrawTaskSucState 设置抓取任务为抓取成功
func (c *CPCrawler) SetCrawTaskSucState(ctx context.Context) error {
	c.CrawTask.DayCrawlerState = model.CrawlerSuc
	return c.CrawTask.SetCrawTaskState(ctx)
}

// SetCrawTaskFailState 设置抓取任务为抓取失败
func (c *CPCrawler) SetCrawTaskFailState(ctx context.Context) error {
	c.CrawTask.DayCrawlerState = model.CrawlerSuc
	return c.CrawTask.SetCrawTaskState(ctx)
}

// SetCrawTaskEmptyState 设置抓取任务为 空状态
func (c *CPCrawler) SetCrawTaskEmptyState(ctx context.Context) error {
	c.CrawTask.DayCrawlerState = model.CrawlerRetEmpty
	return c.CrawTask.SetCrawTaskState(ctx)
}

// IsPushCraw 判断是否应该推送抓取
func (c *CPCrawler) IsPushCraw() bool {
	return isPushCraw(c.CrawTask.Platform, c.CrawTask.AccountState, model.CPInfoType,
		c.CrawTask.DayCrawlerEndTime, c.CrawTask.DayCrawlerState)
}

// CrawlerRetArticleCallBack 爬虫文章结果回调处理
func (c *CPCrawler) CrawlerRetArticleCallBack(ctx context.Context, crawID int) error {
	// 查询账号ID
	if err := c.CrawTask.GetCrawTaskByID(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "cannot find %d account id.", crawID)
		return nil
	}
	if c.ArticleInfo.StaticInfo.CrawlSource == "" { // 返回结果为空
		if err := c.SetCrawTaskEmptyState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawTaskEmptyState ERR:%+v", err)
			return nil
		}
	}

	// 上报ATTa
	c.ArticleInfo.OrderID = model.CPCrawOrder
	c.ArticleInfo.AccountID = c.CrawTask.AccountID
	c.ArticleInfo.CrawType = model.CPInfoType
	c.ArticleInfo.EnvFlag = model.NewEnvTag
	(&c.ArticleInfo).DoReport(ctx)

	// 更新抓取状态为"抓取成功"
	if err := c.SetCrawTaskSucState(ctx); err != nil {
		log.ErrorContextf(ctx, "SetCrawTaskSucState ERR:%+v", err)
	}
	return nil
}

// CrawlerRetAccountCallBack 爬虫账号结果回调处理
func (c *CPCrawler) CrawlerRetAccountCallBack(ctx context.Context, crawID int) error {
	// 查询账号ID
	if err := c.CrawTask.GetCrawTaskByID(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "cannot find %d account id.", crawID)
		return nil
	}
	if c.AccountInfo.StaticInfo.CrawlSource == "" { // 返回结果为空，可能是这个账号不存在
		if err := c.SetCrawTaskEmptyState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawTaskEmptyState ERR:%+v", err)
			return nil
		}
	}

	// 上报ATTa
	c.AccountInfo.OrderID = model.CPCrawOrder
	c.AccountInfo.AccountID = c.CrawTask.AccountID
	c.AccountInfo.CrawType = model.CPInfoType
	c.AccountInfo.CrawlerState = model.CrawlerSuc
	c.AccountInfo.AccountType = strconv.Itoa(c.CrawTask.AccountType)
	c.AccountInfo.CrawlerStartTime = c.CrawTask.DayCrawlerStartTime.Format("2006-01-02 15:04:05")
	(&c.AccountInfo).DoReport(ctx)

	// 更新抓取状态为"抓取成功"
	if err := c.SetCrawTaskSucState(ctx); err != nil {
		log.ErrorContextf(ctx, "SetCrawTaskSucState ERR:%+v", err)
	}
	return nil
}

// CrawlerRetTopicFieldCallBack 爬虫话题属性结果回调处理
func (c *CPCrawler) CrawlerRetTopicFieldCallBack(_ context.Context, _ int) error {
	return nil
}

func fetchAllCPTasks(ctx context.Context) ([]*dao.CPCrawTask, error) {
	var cpCrawTask dao.CPCrawTask
	return cpCrawTask.GetAllCrawTasks(ctx)
}

// NOCC:CCN_threshold(设计如此:)
func processCPTasks(ctx context.Context, cpCrawTasks []*dao.CPCrawTask, maxPushNum int) error {
	i := 0
	for _, t := range cpCrawTasks {
		crawler := NewCPCrawler(*t, report.ReportArticleInfo{}, report.ReportAccountInfo{})
		if !crawler.IsPushCraw() {
			continue
		}
		if i > maxPushNum {
			break
		}
		if err := crawler.PushCrawTasks(ctx); err != nil {
			log.ErrorContextf(ctx, "PushCrawTasks ERR:%+v", err)
			continue
		}
		if err := crawler.SetCrawTaskRunningState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawTaskRunningState ERR:%+v", err)
		}
		i++
	}
	return nil
}

// HandlePushCPInfoCrawTask 推送CP抓取任务
func HandlePushCPInfoCrawTask(ctx context.Context, _, _ string, _ int32) error {
	log.InfoContextf(ctx, "HandlePushCPInfoCrawTask enter")

	CPCrawTasks, err := fetchAllCPTasks(ctx)
	if err != nil {
		return err
	}

	maxPushNum := conf.GetConfig().MaxPushNum
	if err = processCPTasks(ctx, CPCrawTasks, maxPushNum); err != nil {
		return err
	}

	log.InfoContextf(ctx, "HandlePushCPInfoCrawTask suc")
	return nil
}
