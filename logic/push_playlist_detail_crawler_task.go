package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/hydra/tms"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

// HandlePushPlaylistDetailCrawTask 根据排播列表，抓取排播中的剧集详情页（每隔5分钟触发；在更新时间前1小时+后半小时内，每5分钟抓一次）
func HandlePushPlaylistDetailCrawTask(ctx context.Context, _, _ string, _ int32) error {
	log.InfoContextf(ctx, "HandlePushPlaylistDetailCrawTask enter")
	needCrawTasks, err := getNeedCrawTasks(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getNeedCrawTasks err:%+v", err)
		return err
	}
	for _, task := range needCrawTasks {
		crawID := genTransmit(0, 0, model.PlaylistOrder,
			fmt.Sprintf("%d_%s", task.ID, time.Now().Format("20060102150405")))
		if e := pushPlaylistDetailCrawTask(ctx, crawID, task.URL); e != nil {
			return e
		}
	}
	log.InfoContextf(ctx, "HandlePushPlaylistDetailCrawTask suc")
	return nil
}

func getNeedCrawTasks(ctx context.Context) ([]*dao.PlaylistInfo, error) {
	var p dao.PlaylistInfo
	curDatePlaylist, err := p.GetDatePlaylistInfo(ctx)
	if err != nil {
		return nil, err
	}

	var validPlaylistInfos []*dao.PlaylistInfo
	for _, info := range curDatePlaylist {
		if isWithinRange(time.Now(), info.UpdateTime) {
			validPlaylistInfos = append(validPlaylistInfos, info)
		}
	}
	return validPlaylistInfos, nil
}

// isWithinRange 当前时间在：更新时间前1小时+后半小时内
func isWithinRange(now time.Time, updateTime string) bool {
	updateTimeParsed, err := time.Parse("15:04:05", updateTime)
	if err != nil {
		return false
	}

	// 将更新时间设置为当前日期
	updateTimeWithCurrentDate := time.Date(now.Year(), now.Month(), now.Day(),
		updateTimeParsed.Hour(), updateTimeParsed.Minute(), updateTimeParsed.Second(), 0, now.Location())

	// 计算更新时间前1小时和后半小时的时间范围
	startTime := updateTimeWithCurrentDate.Add(-1 * time.Hour)
	endTime := updateTimeWithCurrentDate.Add(30 * time.Minute)

	// 判断当前时间是否在范围内
	return now.After(startTime) && now.Before(endTime)
}

func pushPlaylistDetailCrawTask(ctx context.Context, crawID string, detailURL string) error {
	if crawID == "" || detailURL == "" {
		return nil
	}

	type jobArgs struct {
		URL string `json:"url"`
	}
	jobArguments, _ := json.Marshal(&jobArgs{
		URL: detailURL,
	})
	var err error
	var rsp *tms.AddJobReply
	req := &tms.AddJobRequest{
		Caller: dao.AppName,
		Job: &tms.Job{
			TaskID:       conf.GetConfig().PlaylistDetailTaskID, // 抓取侧是根据这个参数，来区分不同类型的抓取任务
			BizID:        crawID,
			JobArguments: string(jobArguments),
		},
		Auth: &tms.Auth{
			Caller: dao.AppName,
			Type:   tms.AuthType_app,
			Secret: conf.GetConfig().CrawAPISecret,
		},
	}
	if rsp, err = dao.InvokeCrawlerAddTask(ctx, req); err != nil {
		return err
	}
	log.InfoContextf(ctx, "pushPlaylistCrawTask suc:%+v", *rsp)
	return nil
}
