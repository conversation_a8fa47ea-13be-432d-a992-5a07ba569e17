package logic

import (
	"fmt"

	"git.woa.com/video_media/media_event_hub/processor/config"
)

// ProcessorService 服务总接口
type ProcessorService interface {
	// Start 启动事件处理服务
	Start()
	// Stop 停止事件处理服务
	Stop()
}

// NewProcessorService 新建事件处理服务
func NewProcessorService() (ProcessorService, error) {
	cfg, err := config.New()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %+v", err)
	}
	// 创建EventBridgeManager作为服务总入口
	manager, err := NewEventBridgeManager(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建事件处理服务失败: %+v", err)
	}

	// 返回EventBridgeManager作为ProcessorService实现
	return manager, nil
}
