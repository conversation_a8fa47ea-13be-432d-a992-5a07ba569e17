package logic

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpcprotocol/hydra/tms"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"github.com/agiledragon/gomonkey/v2"
)

func Test_pushPlaylistCrawTask(t *testing.T) {
	type args struct {
		ctx         context.Context
		channelType string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx:         trpc.BackgroundContext(),
				channelType: "dianshiju",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 使用 gomonkey 将 InvokeCrawlerAddTask 函数替换为空返回函数
			patch := gomonkey.ApplyFunc(dao.InvokeCrawlerAddTask,
				func(ctx context.Context, req *tms.AddJobRequest) (*tms.AddJobReply, error) {
					return &tms.AddJobReply{Code: 0}, nil
				})
			defer patch.Reset()

			if err := pushPlaylistCrawTask(tt.args.ctx, tt.args.channelType); (err != nil) != tt.wantErr {
				t.Errorf("pushPlaylistCrawTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
