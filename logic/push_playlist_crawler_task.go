package logic

import (
	"context"
	"encoding/json"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/hydra/tms"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

// HandlePushPlaylistCrawTask 推送竞品排播列表抓取任务
func HandlePushPlaylistCrawTask(ctx context.Context, _, _ string, _ int32) error {
	log.InfoContextf(ctx, "HandlePushPlaylistCrawTask enter")
	for _, channelType := range []string{"zongyi", "dianshiju"} {
		if err := pushPlaylistCrawTask(ctx, channelType); err != nil {
			return err
		}
	}
	log.InfoContextf(ctx, "HandlePushCPInfoCrawTask suc")
	return nil
}

func pushPlaylistCrawTask(ctx context.Context, channelType string) error {
	type jobArgs struct {
		Channel string `json:"channel"`
	}
	jobArguments, _ := json.Marshal(&jobArgs{
		Channel: channelType,
	})
	var err error
	var rsp *tms.AddJobReply
	req := &tms.AddJobRequest{
		Caller: dao.AppName,
		Job: &tms.Job{
			TaskID:       conf.GetConfig().PlaylistTaskID, // 抓取侧是根据这个参数，来区分不同类型的抓取任务
			BizID:        genTransmit(0, 0, model.PlaylistOrder, channelType),
			JobArguments: string(jobArguments),
		},
		Auth: &tms.Auth{
			Caller: dao.AppName,
			Type:   tms.AuthType_app,
			Secret: conf.GetConfig().CrawAPISecret,
		},
	}
	if rsp, err = dao.InvokeCrawlerAddTask(ctx, req); err != nil {
		return err
	}
	log.InfoContextf(ctx, "pushPlaylistCrawTask suc:%+v", *rsp)
	return nil
}
