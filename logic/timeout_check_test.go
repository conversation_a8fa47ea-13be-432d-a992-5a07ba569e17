package logic

import (
	"context"
	"encoding/json"
	"testing"

	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	"github.com/stretchr/testify/assert"
)

// TestAddTimeoutCheckTask 测试添加超时检查任务
func TestAddTimeoutCheckTask(t *testing.T) {
	tests := []struct {
		name         string
		ctx          context.Context
		datasourceID int32
		id           string
		extraInfo    string
		modifyInfo   *model.ModifyInfo
		expectPanic  bool
	}{
		{
			name:         "modifyInfo为nil",
			ctx:          context.Background(),
			datasourceID: 1,
			id:           "test-vid",
			extraInfo:    "",
			modifyInfo:   nil,
			expectPanic:  false,
		},
		{
			name:         "有效的extraInfo",
			ctx:          context.Background(),
			datasourceID: 1,
			id:           "test-vid",
			extraInfo:    createValidExtraInfo(t),
			modifyInfo: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{
					"title": {
						FieldName: "title",
						FieldType: protocol.EnumFieldType_FieldTypeStr,
						StrValue:  "test title",
					},
				},
				Infos: []*protocol.ModifyFieldInfo{
					{
						FieldName:   "title",
						FieldType:   protocol.EnumFieldType_FieldTypeStr,
						OldStrValue: "old title",
						NewStrValue: "new title",
					},
				},
			},
			expectPanic: false,
		},
		{
			name:         "无效的extraInfo JSON",
			ctx:          context.Background(),
			datasourceID: 1,
			id:           "test-vid",
			extraInfo:    "invalid-json",
			modifyInfo: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos:    []*protocol.ModifyFieldInfo{},
			},
			expectPanic: false,
		},
		{
			name:         "空的extraInfo",
			ctx:          context.Background(),
			datasourceID: 1,
			id:           "test-vid",
			extraInfo:    "",
			modifyInfo: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{},
				Infos:    []*protocol.ModifyFieldInfo{},
			},
			expectPanic: false,
		},
		{
			name:         "完整的测试用例",
			ctx:          context.Background(),
			datasourceID: 123,
			id:           "complete-test-vid",
			extraInfo:    createCompleteExtraInfo(t),
			modifyInfo: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{
					"title": {
						FieldName: "title",
						FieldType: protocol.EnumFieldType_FieldTypeStr,
						StrValue:  "完整测试标题",
					},
					"category": {
						FieldName: "category",
						FieldType: protocol.EnumFieldType_FieldTypeStr,
						StrValue:  "测试分类",
					},
				},
				Infos: []*protocol.ModifyFieldInfo{
					{
						FieldName:   "title",
						FieldType:   protocol.EnumFieldType_FieldTypeStr,
						OldStrValue: "旧标题",
						NewStrValue: "新标题",
					},
					{
						FieldName:   "category",
						FieldType:   protocol.EnumFieldType_FieldTypeStr,
						OldStrValue: "旧分类",
						NewStrValue: "新分类",
					},
				},
			},
			expectPanic: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.expectPanic {
				assert.Panics(t, func() {
					AddTimeoutCheckTask(tt.ctx, tt.datasourceID, tt.id, tt.extraInfo, tt.modifyInfo)
				})
			} else {
				assert.NotPanics(t, func() {
					AddTimeoutCheckTask(tt.ctx, tt.datasourceID, tt.id, tt.extraInfo, tt.modifyInfo)
				})
			}
		})
	}
}

// TestUpdateInfoStruct 测试updateInfo结构体
func TestUpdateInfoStruct(t *testing.T) {
	info := updateInfo{
		DatasetID:    123,
		AppID:        "test-app",
		OperatorName: "test-operator",
		LocalIP:      "127.0.0.1",
		RemoteIP:     "***********",
		ExtInfo:      "test-ext-info",
		SequenceID:   456,
		TenantID:     "test-tenant",
	}

	// 测试JSON序列化和反序列化
	jsonData, err := json.Marshal(info)
	assert.NoError(t, err)
	assert.NotEmpty(t, jsonData)

	var parsedInfo updateInfo
	err = json.Unmarshal(jsonData, &parsedInfo)
	assert.NoError(t, err)
	assert.Equal(t, info, parsedInfo)
}

// TestConstants 测试常量
func TestConstants(t *testing.T) {
	assert.Equal(t, 1, beginStep)
	assert.Equal(t, 2, endStep)
}

// TestSendCheckTaskMsg 测试发送检查任务消息函数
func TestSendCheckTaskMsg(t *testing.T) {
	tests := []struct {
		name         string
		ctx          context.Context
		datasourceID int32
		id           string
		info         *updateInfo
		modifyInfo   *model.ModifyInfo
	}{
		{
			name:         "基本测试",
			ctx:          context.Background(),
			datasourceID: 123,
			id:           "test-vid",
			info: &updateInfo{
				DatasetID:    456,
				AppID:        "test-app",
				OperatorName: "test-operator",
				LocalIP:      "127.0.0.1",
				RemoteIP:     "***********",
				ExtInfo:      "test-ext-info",
				SequenceID:   789,
				TenantID:     "test-tenant",
			},
			modifyInfo: &model.ModifyInfo{
				BaseInfo: map[string]*protocol.FieldInfo{
					"title": {
						FieldName: "title",
						FieldType: protocol.EnumFieldType_FieldTypeStr,
						StrValue:  "test title",
					},
				},
				Infos: []*protocol.ModifyFieldInfo{
					{
						FieldName:   "title",
						FieldType:   protocol.EnumFieldType_FieldTypeStr,
						OldStrValue: "old title",
						NewStrValue: "new title",
					},
				},
			},
		},
		{
			name:         "空的modifyInfo",
			ctx:          context.Background(),
			datasourceID: 123,
			id:           "test-vid",
			info: &updateInfo{
				DatasetID: 456,
				AppID:     "test-app",
			},
			modifyInfo: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试函数是否能正常执行而不崩溃
			assert.NotPanics(t, func() {
				sendCheckTaskMsg(tt.ctx, tt.datasourceID, tt.id, tt.info, tt.modifyInfo)
			})
		})
	}
}

// TestProduceErrHandle 测试错误处理函数
func TestProduceErrHandle(t *testing.T) {
	tests := []struct {
		name    string
		errInfo string
		topic   string
		key     []byte
		value   []byte
	}{
		{
			name:    "基本错误处理",
			errInfo: "kafka send error",
			topic:   "test-topic",
			key:     []byte("test-key"),
			value:   []byte("test-value"),
		},
		{
			name:    "空值处理",
			errInfo: "",
			topic:   "",
			key:     []byte(""),
			value:   []byte(""),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试函数是否能正常执行而不崩溃
			assert.NotPanics(t, func() {
				produceErrHandle(tt.errInfo, tt.topic, tt.key, tt.value)
			})
		})
	}
}

// TestMetricsCounter 测试指标计数器
func TestMetricsCounter(t *testing.T) {
	// 测试计数器是否存在且可用
	assert.NotNil(t, checkTaskFailCounter)

	// 测试计数器调用
	assert.NotPanics(t, func() {
		checkTaskFailCounter.Incr()
	})
}

// 辅助函数：创建有效的extraInfo JSON字符串
func createValidExtraInfo(t *testing.T) string {
	info := updateInfo{
		DatasetID:    123,
		AppID:        "test-app",
		OperatorName: "test-operator",
		LocalIP:      "127.0.0.1",
		RemoteIP:     "***********",
		ExtInfo:      "test-ext-info",
		SequenceID:   456,
		TenantID:     "test-tenant",
	}

	data, err := json.Marshal(info)
	assert.NoError(t, err)
	return string(data)
}

// 辅助函数：创建完整的extraInfo JSON字符串
func createCompleteExtraInfo(t *testing.T) string {
	info := updateInfo{
		DatasetID:    999,
		AppID:        "complete-test-app",
		OperatorName: "complete-test-operator",
		LocalIP:      "********",
		RemoteIP:     "********",
		ExtInfo:      "complete-test-ext-info",
		SequenceID:   888777,
		TenantID:     "complete-test-tenant",
	}

	data, err := json.Marshal(info)
	assert.NoError(t, err)
	return string(data)
}
