// Package logic 公共的逻辑函数，比如字段类型相关逻辑
package logic

import (
	"bytes"
	"fmt"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/media_go_commlib/utils"
	"git.code.oa.com/video_media/storage_service/adaptor_layer/model"
	tool "git.code.oa.com/video_media/storage_service/common"
	cache "git.code.oa.com/video_media/storage_service/config_cache"
	item "git.code.oa.com/video_media/storage_service/config_cache/dao"
)

// GetAllFieldID *号场景指定特殊的fieldID
const GetAllFieldID = 0

// BuildInsertValue 生成插入字段值
func BuildInsertValue(update *protocol.UpdateFieldInfo, cur *tool.FieldVal) (*tool.FieldVal, *protocol.ModifyFieldInfo,
	error,
) {
	var (
		val = new(tool.FieldVal)
		ok  bool
		err error
	)
	switch update.GetFieldInfo().GetFieldType() {
	case protocol.EnumFieldType_FieldTypeStr:
		val.TxtVal, ok = BuildStrFieldOpStr(update.UpdateType, cur.TxtVal, update.FieldInfo.StrValue)
	case protocol.EnumFieldType_FieldTypeIntVec:
		val.TxtVal, ok = BuildVecFieldOpStr(update.UpdateType, cur.TxtVal, update.FieldInfo.VecInt)
	case protocol.EnumFieldType_FieldTypeSet:
		val.TxtVal, ok, err = BuildSetFieldOpStr(update.UpdateType, int(update.Pos), "+", cur.TxtVal,
			update.FieldInfo.VecStr)
	case protocol.EnumFieldType_FieldTypeMapKV, protocol.EnumFieldType_FieldTypeMapKList:
		val.MapVal, ok, err = BuildMapFieldVal(update, cur.MapVal)
	default:
	}

	var modifyInfo *protocol.ModifyFieldInfo
	if ok {
		modifyInfo = model.NewModifyFieldInfo(update.FieldInfo, cur, val)
	}
	return val, modifyInfo, err
}

// BuildStrFieldOpStr 生成字符类型字段字符串 return:true(需要更新/删除)false(不需要更新/删除)
func BuildStrFieldOpStr(ty protocol.EnumUpdateType, curVal, newVal string) (string, bool) {
	switch ty {
	case protocol.EnumUpdateType_UpdateTypeSet:
		return newVal, curVal != newVal
	case protocol.EnumUpdateType_UpdateTypeDel:
		return "", curVal != ""
	case protocol.EnumUpdateType_UpdateTypeAppend:
		return curVal + newVal, true
	default:
		// 异常更新类型不更新
		return "", false
	}
}

// BuildESVecFieldOp 生成选项类型字段字符串 return:true(需要更新/删除)false(不需要更新/删除)
func BuildESVecFieldOp(ty protocol.EnumUpdateType, oldVal, updateVal []uint32) (result []uint32, ok bool) {
	switch ty {
	case protocol.EnumUpdateType_UpdateTypeSet:
		result = updateVal
	case protocol.EnumUpdateType_UpdateTypeDel:
		result = utils.Uint32SliceSub(oldVal, updateVal)
	case protocol.EnumUpdateType_UpdateTypeAppend:
		result = append(oldVal, updateVal...)
	default:
	}
	// 不相等 modify=true
	ok = !utils.Uint32SliceEqual(oldVal, result)
	return
}

// BuildESSetFieldOp 生成Set类型字段字符串 return:true(需要更新/删除)false(不需要更新/删除)
// NOCC:CCN_threshold(设计如此:)
func BuildESSetFieldOp(ty protocol.EnumUpdateType, pos int32, oldVal, updateVal []string) ([]string,
	bool, error,
) {
	var err error
	result := make([]string, len(updateVal)+len(oldVal))
	ok, resultStr, cut := false, "", "+"
	switch ty {
	case protocol.EnumUpdateType_UpdateTypeSet:
		result = updateVal
		ok = !utils.StringSliceEqual(oldVal, result)
	case protocol.EnumUpdateType_UpdateTypeDel:
		result = utils.Subtraction(oldVal, updateVal)
		ok = !utils.StringSliceEqual(oldVal, result)
	case protocol.EnumUpdateType_UpdateTypeAppend, protocol.EnumUpdateType_UpdateTypeInsert:
		if ty == protocol.EnumUpdateType_UpdateTypeAppend {
			pos = -1 // append是特殊的insert
		}
		// 复用原MySQL函数
		resultStr, ok, err = AddSetFieldInfoStr(updateVal, cut, strings.Join(oldVal, cut), int(pos))
		result = strings.Split(resultStr, cut)
	case protocol.EnumUpdateType_UpdateTypeReorder:
		resultStr, ok, err = ReorderSetField(updateVal, cut, strings.Join(oldVal, cut), int(pos))
		result = strings.Split(resultStr, cut)
	case protocol.EnumUpdateType_UpdateTypeReverse:
		copy(result, oldVal)
		utils.ReverseStrSlice(result)
		ok = true // set没有重复值,不会出现反转前后一样的情况
	default:
		return nil, ok, fmt.Errorf("invalid update type")
	}
	return result, ok, err
}

// BuildVecFieldOpStr 生成选项类型字段字符串 return:true(需要更新/删除)false(不需要更新/删除)
func BuildVecFieldOpStr(ty protocol.EnumUpdateType, curVal string, newVal []uint32) (retStr string, ok bool) {
	switch ty {
	case protocol.EnumUpdateType_UpdateTypeSet:
		retStr = BuildVecFieldInfoStr(newVal)
		ok = retStr != curVal
	case protocol.EnumUpdateType_UpdateTypeDel:
		retStr, ok = DelVecFieldInfoStr(newVal, curVal)
	case protocol.EnumUpdateType_UpdateTypeAppend:
		retStr, ok = AddVecFieldInfoStr(newVal, curVal)
	default:
		return "", false
	}
	return
}

// RemoveRepeat 去重set类型字段值，丢弃传入空字符
func RemoveRepeat(source []string) []string {
	if len(source) <= 1 {
		return source
	}

	strList := make([]string, 0)
	strSet := make(map[string]bool)
	for _, s := range source {
		if s == "" || strSet[s] {
			continue
		}
		strList = append(strList, s)
		strSet[s] = true
	}
	return strList
}

// BuildSetFieldOpStr 生成Set类型字段字符串 return:true(需要更新/删除)false(不需要更新/删除)
func BuildSetFieldOpStr(ty protocol.EnumUpdateType, pos int, cut, curVal string, newVal []string) (result string,
	ok bool, err error,
) {
	newVal = RemoveRepeat(newVal)
	switch ty {
	case protocol.EnumUpdateType_UpdateTypeSet:
		result = BuildSetFieldInfoStr(cut, newVal)
		ok = result != curVal
	case protocol.EnumUpdateType_UpdateTypeDel:
		result, ok, _ = DelSetFieldInfoStr(newVal, cut, curVal)
	case protocol.EnumUpdateType_UpdateTypeAppend:
		result, ok, err = AddSetFieldInfoStr(newVal, cut, curVal, -1)
	case protocol.EnumUpdateType_UpdateTypeInsert:
		result, ok, err = AddSetFieldInfoStr(newVal, cut, curVal, pos)
	case protocol.EnumUpdateType_UpdateTypeReorder:
		result, ok, err = ReorderSetField(newVal, cut, curVal, pos)
	case protocol.EnumUpdateType_UpdateTypeReverse:
		result, ok = ReverseSetField(cut, curVal)
	default:
		return "", false, fmt.Errorf("invalid update type")
	}
	return
}

// BuildMapFieldVal 生成Map类型字段字符串
func BuildMapFieldVal(update *protocol.UpdateFieldInfo, curVal map[string]string) (map[string]string, bool, error) {
	mapVal := make(map[string]string)
	for k, v := range update.FieldInfo.MapVal {
		var (
			ok     bool
			newVal string
		)
		cur := curVal[k]
		switch update.FieldInfo.FieldType {
		case protocol.EnumFieldType_FieldTypeMapKV:
			newVal, ok = BuildStrFieldOpStr(update.UpdateType, cur, v.StrValue)
		case protocol.EnumFieldType_FieldTypeMapKList:
			newVal, ok, _ = BuildSetFieldOpStr(update.UpdateType, int(update.Pos), tool.MapCut, cur, v.VecStr)
		default:
		}
		if ok {
			mapVal[k] = newVal
		}
	}
	return mapVal, len(mapVal) > 0, nil
}

// BuildVecFieldInfoStr 将uint32切片转换为选项型字段字符串
func BuildVecFieldInfoStr(intVec []uint32) string {
	// 用于去重
	idSet := make(map[uint32]bool)
	var build strings.Builder
	for _, vecVal := range intVec {
		if !idSet[vecVal] {
			build.WriteString("+" + strconv.Itoa(int(vecVal)))
			idSet[vecVal] = true
		}
	}
	return strings.TrimLeft(build.String(), "+")
}

// AddVecFieldInfoStr 增加选项型字段 return:true(需要增加)false(不需要增加)
func AddVecFieldInfoStr(intVec []uint32, oldVecStr string) (string, bool) {
	if len(intVec) == 0 {
		return oldVecStr, false
	}
	oldVecStr = strings.TrimRight(oldVecStr, "+")
	if oldVecStr == "" {
		return BuildVecFieldInfoStr(intVec), true
	}

	addNum := 0
	tmp := "+" + oldVecStr
	var build strings.Builder
	for _, n := range intVec {
		num := "+" + strconv.Itoa(int(n))
		if !strings.Contains(tmp, num) {
			addNum++
			build.WriteString(num)
		}
	}
	return strings.TrimLeft(oldVecStr+build.String(), "+"), addNum != 0
}

// DelVecFieldInfoStr 删除选项型字段 return:true(需要删除)false(不需要删除)
func DelVecFieldInfoStr(intVec []uint32, oldVecStr string) (string, bool) {
	if len(intVec) == 0 || oldVecStr == "" {
		return oldVecStr, false
	}

	idSet := utils.TransNumArrayToStrSet(intVec)
	vec := strings.Split(oldVecStr, "+")

	var noHit []string
	for _, id := range vec {
		if id == "" || idSet[id] {
			continue
		}
		noHit = append(noHit, id)
	}
	if len(noHit) == len(vec) {
		return oldVecStr, false
	}
	return strings.Join(noHit, "+"), true
}

// BuildSetFieldInfoStr 将string切片转换为Set型字段字符串
func BuildSetFieldInfoStr(cut string, strVec []string) string {
	buffer := bytes.NewBuffer(nil)
	for idx := range strVec {
		buffer.WriteString(cut + strVec[idx])
	}
	return strings.TrimLeft(buffer.String(), cut)
}

// DelSetFieldInfoStr 删除Set型字段指定的值
// return:true(需要删除)false(不需要删除) true(有不存在的)false(全hit)
func DelSetFieldInfoStr(strVec []string, cut, curVal string) (string, bool, error) {
	curVal = cut + curVal + cut
	result := curVal
	var err error
	for idx := range strVec {
		if !strings.Contains(result, cut+strVec[idx]+cut) {
			err = fmt.Errorf("not all hit")
			continue
		}
		result = strings.Replace(result, cut+strVec[idx]+cut, cut, 1)
	}
	return strings.Trim(result, cut), result != curVal, err
}

// AddSetFieldInfoStr 增加Set型字段值 return:true(需要增加)false(不需要增加)
func AddSetFieldInfoStr(strVec []string, cut, old string, pos int) (string, bool, error) {
	if len(strVec) == 0 {
		return old, false, nil
	}

	var newStr string
	if len(old) > 0 {
		// 清除原串准备添加字段
		newStr, _, _ = DelSetFieldInfoStr(strVec, cut, old)
	}
	return addNewSetFields(strVec, cut, old, newStr, pos)
}

// ReorderSetField 重排序Set型字段值 return:true(需要增加)false(不需要增加)
func ReorderSetField(strVec []string, cut, cur string, pos int) (string, bool, error) {
	if len(strVec) == 0 || len(cur) == 0 {
		return cur, false, fmt.Errorf("nil set")
	}

	// 清除原串准备添加字段
	newStr, _, err := DelSetFieldInfoStr(strVec, cut, cur)
	if err != nil {
		log.Errorf("Not all field %v hit in %s.", strVec, cur)
		return cur, false, err
	}
	return addNewSetFields(strVec, cut, cur, newStr, pos)
}

// ReverseSetField 将set类型字段元素顺序反转
func ReverseSetField(cut, cur string) (string, bool) {
	if cur == "" || !strings.Contains(cur, cut) {
		return cur, false
	}

	begin := 0
	end := 0
	runes := []rune(strings.Trim(cur, cut))
	wordNum := len(runes)
	s := []rune(cut)
	for end < wordNum {
		if runes[end] == s[0] {
			tool.RevertWord(runes, begin, end-1)
			end++
			begin = end
		} else {
			end++
		}
	}
	tool.RevertWord(runes, begin, end-1)
	tool.ReverseString(runes)
	return string(runes), true
}

func addFrontSetFields(strList []string, cut, new string) string {
	buff := bytes.NewBuffer(nil)
	for _, setStr := range strList {
		buff.WriteString(setStr + cut)
	}

	resultStr := buff.String() + new
	if new == "" {
		resultStr = strings.TrimRight(resultStr, cut)
	}
	return resultStr
}

func addEndSetFields(strList []string, cut, newSetStr string) string {
	buff := bytes.NewBuffer(nil)
	for _, setStr := range strList {
		buff.WriteString(cut + setStr)
	}

	resultStr := newSetStr + buff.String()
	if newSetStr == "" {
		resultStr = strings.TrimLeft(resultStr, cut)
	}
	return resultStr
}

func addNewSetFields(strVec []string, cut, old, new string, pos int) (string, bool, error) {
	// 针对首尾添加进行优化处理
	s := []rune(cut)
	if pos == -1 {
		result := addEndSetFields(strVec, cut, new)
		return result, result != old, nil
	} else if pos == 0 {
		result := addFrontSetFields(strVec, cut, new)
		return result, result != old, nil
	}

	// 字符串中间插入
	cnt := 0
	idx := strings.IndexFunc(new, func(r rune) bool {
		if r == s[0] {
			cnt++
		}
		if cnt == pos {
			return true
		}
		return false
	})
	// 正好pos为最后一位
	if cnt == pos-1 {
		result := addEndSetFields(strVec, cut, new)
		return result, result != old, nil
	}

	if idx == -1 {
		log.Errorf("Invalid pos:%d, index:%d.", pos, idx)
		return old, false, fmt.Errorf("invalid pos")
	}
	buff := bytes.NewBuffer(nil)
	for i := range strVec {
		buff.WriteString(cut + strVec[i])
	}
	result := new[0:idx] + buff.String() + new[idx:]
	return result, result != old, nil
}

// HandleAllFailList 处理失败列表
func HandleAllFailList(fieldIds []uint32, failList map[uint32]protocol.EnumMediaErrorCode,
	errCode protocol.EnumMediaErrorCode,
) {
	for _, fieldId := range fieldIds {
		failList[fieldId] = errCode
	}
}

// BatchHandleFailList 批量处理更新响应错误列表
func BatchHandleFailList(fieldList []*protocol.UpdateFieldInfo, failList map[string]protocol.EnumMediaErrorCode,
	errCode protocol.EnumMediaErrorCode,
) {
	for _, field := range fieldList {
		failList[field.FieldInfo.FieldName] = errCode
	}
}

// ClassifyFieldsByType 按照是否需要加锁对更新字段进行分类
func ClassifyFieldsByType(fields []*protocol.UpdateFieldInfo) (unlock, lock []*protocol.UpdateFieldInfo) {
	for _, f := range fields {
		switch f.FieldInfo.FieldType {
		case protocol.EnumFieldType_FieldTypeStr, protocol.EnumFieldType_FieldTypeMapKV:
			unlock = append(unlock, f)
		case protocol.EnumFieldType_FieldTypeIntVec, protocol.EnumFieldType_FieldTypeSet,
			protocol.EnumFieldType_FieldTypeMapKList:
			if f.UpdateType == protocol.EnumUpdateType_UpdateTypeSet {
				unlock = append(unlock, f)
			} else {
				lock = append(lock, f)
			}
		default:
		}
	}
	return
}

// ParseMapFieldName 解析map类型请求eg:${fieldName}.${key}
func ParseMapFieldName(fieldName string) (string, string, bool) {
	if strings.Contains(fieldName, ".") {
		fieldNameVec := strings.Split(fieldName, ".")
		return fieldNameVec[0], fieldNameVec[1], true
	}
	return fieldName, "", false
}

// IsGetAllRequest 判断是否为getAll请求
func IsGetAllRequest(fieldID []uint32) bool {
	// fieldID只传0相当于getAll
	return len(fieldID) == 1 && fieldID[0] == GetAllFieldID
}

// NewRetInfo 创建默认响应信息
func NewRetInfo() *protocol.CommRetInfo {
	return &protocol.CommRetInfo{
		ErrCode:  protocol.EnumMediaErrorCode_RetSuccess,
		ErrMsg:   "RetSuccess",
		FailList: make(map[string]protocol.EnumMediaErrorCode),
	}
}

// NewFieldCacheList 根据字段列表创建缓存列表
func NewFieldCacheList(datasetID int32, fields []string) []*item.FieldInfo {
	fieldInfos := make([]*item.FieldInfo, 0, len(fields))
	for _, field := range fields {
		if f := cache.GetFieldInfoByName(field, datasetID); f != nil {
			fieldInfos = append(fieldInfos, f)
		}
	}
	return fieldInfos
}

// DeleteStrSliceElms 删除切片中指定元素，返回结果切片
func DeleteStrSliceElms(sl, elms []string) []string {
	if len(sl) == 0 || len(elms) == 0 {
		return sl
	}
	// 先将元素转为 set
	m := make(map[string]bool)
	for _, v := range elms {
		m[v] = true
	}
	// 过滤掉指定元素
	res := make([]string, 0, len(sl))
	for _, v := range sl {
		if _, ok := m[v]; !ok {
			res = append(res, v)
		}
	}
	return res
}
