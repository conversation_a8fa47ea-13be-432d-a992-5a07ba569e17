package logic

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	metadata "git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/processor/config"
	toolkitrepo "git.woa.com/video_media/media_event_hub/toolkit/repo"

	"github.com/IBM/sarama"
)

// EventBridgeManager 管理所有事件总线的消费者（消费原始事件总线的消息）
// 作为服务的总入口，实现ProcessorService接口
type EventBridgeManager struct {
	lock            sync.RWMutex
	checkInterval   time.Duration
	eventMetaReader toolkitrepo.EventMetaReader
	config          *config.Config                  // 全局配置
	processors      map[int64]*EventBridgeProcessor // 每个事件总线对应一个processor
	bridgeInfos     map[int64]*metadata.EventBridge
	consumerGroups  map[int64]sarama.ConsumerGroup
}

// NewEventBridgeManager 创建事件总线管理器
func NewEventBridgeManager(cfg *config.Config) (*EventBridgeManager, error) {
	// 创建元数据读取器
	metaReader, err := toolkitrepo.NewEventMetaReader()
	if err != nil {
		return nil, fmt.Errorf("创建元数据读取器失败: %w", err)
	}

	return &EventBridgeManager{
		lock:            sync.RWMutex{},
		checkInterval:   time.Second * 15,
		config:          cfg,
		eventMetaReader: metaReader,
		processors:      make(map[int64]*EventBridgeProcessor),
		bridgeInfos:     make(map[int64]*metadata.EventBridge),
		consumerGroups:  make(map[int64]sarama.ConsumerGroup),
	}, nil
}

// Start 启动事件总线管理器，实现ProcessorService接口
func (m *EventBridgeManager) Start() {
	go m.syncEventBridges()
	log.Infof("事件总线管理器已启动，开始监听事件变更")
}

// Stop 停止事件总线管理器
func (m *EventBridgeManager) Stop() {
	m.lock.Lock()
	defer m.lock.Unlock()

	// 关闭所有消费者组
	for id, group := range m.consumerGroups {
		if err := group.Close(); err != nil {
			log.Errorf("关闭消费者组失败, 事件总线ID: %d, 错误: %v", id, err)
		} else {
			log.Infof("关闭消费者组成功, 事件总线ID: %d", id)
		}
	}
}

// syncEventBridges 定时同步事件总线配置
func (m *EventBridgeManager) syncEventBridges() {
	ticker := time.NewTicker(m.checkInterval)
	defer ticker.Stop()

	for range ticker.C {
		ctx := trpc.BackgroundContext()
		m.updateEventBridges(ctx, m.eventMetaReader.GetAllEventBridges(ctx))
	}
}

// updateEventBridges 更新事件总线消费者，只处理新增的事件总线
func (m *EventBridgeManager) updateEventBridges(ctx context.Context, bridges []*metadata.EventBridge) {
	m.lock.Lock()
	defer m.lock.Unlock()

	// 处理新增的事件总线
	for _, bridge := range bridges {
		// 检查是否已存在
		if _, exists := m.bridgeInfos[bridge.Id]; !exists {
			log.InfoContextf(ctx, "start 创建新的事件总线消费者: %d, Topic: %+v", bridge.Id, bridge.Consumer)
			m.bridgeInfos[bridge.Id] = bridge
			go m.startConsumer(trpc.CloneContext(ctx), bridge)
			log.InfoContextf(ctx, "success 创建新的事件总线消费者: %d, Topic: %+v", bridge.Id, bridge.Consumer)
		}
	}
}

// createProcessor 为特定事件总线创建处理器
func (m *EventBridgeManager) createProcessor(ctx context.Context, bridgeID int64) (*EventBridgeProcessor, error) {
	processor := &EventBridgeProcessor{
		bridgeID: bridgeID,
	}

	if err := processor.InitComponents(m.config, bridgeID); err != nil {
		log.ErrorContextf(ctx, "为事件总线 %d 初始化处理器组件失败: %v", bridgeID, err)
		return nil, err
	}
	return processor, nil
}

// startConsumer 启动消费者协程
func (m *EventBridgeManager) startConsumer(ctx context.Context, bridge *metadata.EventBridge) {
	// 为该事件总线创建专用处理器
	processor, err := m.createProcessor(ctx, bridge.Id)
	if err != nil {
		log.ErrorContextf(ctx, "创建事件处理器失败，事件总线ID: %d, 错误: %v", bridge.Id, err)
		return
	}

	// 保存处理器引用
	m.lock.Lock()
	m.processors[bridge.Id] = processor
	m.lock.Unlock()

	// 配置 Kafka 消费者
	conf := sarama.NewConfig()
	conf.Consumer.Return.Errors = true
	conf.Consumer.Offsets.Initial = sarama.OffsetOldest // 从上次提交的offset开始消费

	// 创建消费者组
	group, err := sarama.NewConsumerGroup([]string{bridge.Consumer.Topic.Addr}, bridge.Consumer.Name, conf)
	if err != nil {
		log.ErrorContextf(ctx, "创建消费者组失败，事件总线ID: %d, 错误: %v", bridge.Id, err)
		return
	}

	// 保存消费者组引用
	m.lock.Lock()
	m.consumerGroups[bridge.Id] = group
	m.lock.Unlock()

	// 启动消费循环
	go func() {
		defer func() {
			if err := group.Close(); err != nil {
				log.ErrorContextf(ctx, "关闭消费者组失败: %v", err)
			}
		}()

		// 循环消费消息（具体的消费逻辑可查看consumer.go）
		for {
			consumer := &eventConsumer{
				ctx:             ctx,
				bridgeID:        bridge.Id,
				topic:           bridge.Consumer.Topic.TopicName,
				processor:       processor,
				bridgeInfo:      bridge,
				metaReader:      m.eventMetaReader,
				config:          m.config,
				metricsReporter: NewMetricsReporter(bridge.Id, bridge),
			}
			if err := group.Consume(ctx, []string{bridge.Consumer.Topic.TopicName}, consumer); err != nil {
				log.ErrorContextf(ctx, "事件总线ID:%d 主题:%s 消费错误: %v", bridge.Id, bridge.Consumer.Topic.TopicName, err)
			}
		}
	}()
}
