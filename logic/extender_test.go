package logic

import (
	"context"
	"testing"

	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/processor/config"
	"git.woa.com/video_media/media_event_hub/processor/dao"
	toolkitrepo "git.woa.com/video_media/media_event_hub/toolkit/repo"
)

// Mock implementations for testing Extender

// mockEventMetaReaderForExtender 用于Extender测试的元数据读取器mock
type mockEventMetaReaderForExtender struct {
	subscribers []*metadata_access.EventSubscribe
	returnError bool
}

func (m *mockEventMetaReaderForExtender) GetEventBridge(ctx context.Context, eventBridgeID int64) *metadata_access.EventBridge {
	return nil
}

func (m *mockEventMetaReaderForExtender) GetAllEventBridges(ctx context.Context) []*metadata_access.EventBridge {
	return nil
}

func (m *mockEventMetaReaderForExtender) GetEventSources(ctx context.Context, eventBridgeID int64) []*metadata_access.EventSource {
	return nil
}

func (m *mockEventMetaReaderForExtender) GetEventSource(ctx context.Context, eventBridgeID int64) *metadata_access.EventSource {
	return nil
}

func (m *mockEventMetaReaderForExtender) GetEventType(ctx context.Context, eventBridgeID int64) []*metadata_access.EventType {
	return nil
}

func (m *mockEventMetaReaderForExtender) GetEventSubscribes(ctx context.Context, eventBridgeID int64, eventType string) []*metadata_access.EventSubscribe {
	if m.returnError {
		return nil
	}
	return m.subscribers
}

func (m *mockEventMetaReaderForExtender) GetEventSubscribeByAppID(ctx context.Context, appID string) *metadata_access.EventSubscribe {
	return nil
}

func (m *mockEventMetaReaderForExtender) GetEventSubscribeByID(ctx context.Context, id int64) *metadata_access.EventSubscribe {
	return nil
}

// mockDaoFactory 用于测试的DAO工厂mock
type mockDaoFactory struct {
	shouldError bool
}

func (m *mockDaoFactory) GetDataAccessor(ctx context.Context, bridgeID int64, viewType common_event.EnumViewType) (dao.DataAccessor, error) {
	if m.shouldError {
		return nil, &testError{"获取数据访问器失败"}
	}
	return &mockDataAccessor{}, nil
}

// mockDataAccessor 用于测试的数据访问器mock
type mockDataAccessor struct {
	shouldError bool
	returnNodes []*common_event.Node
}

func (m *mockDataAccessor) BatchGetFields(ctx context.Context, viewId string, dataIds []string, fields []string, timestamp int64) ([]*common_event.Node, error) {
	if m.shouldError {
		return nil, &testError{"批量获取字段失败"}
	}
	return m.returnNodes, nil
}

func (m *mockDataAccessor) Connect() error {
	if m.shouldError {
		return &testError{"初始化数据访问实例失败"}
	}
	return nil
}

// createExtenderEvent 创建扩展器测试事件
func createExtenderEvent(id, eventType, subject string) *common_event.ProcessorEvent {
	if id == "" && eventType == "" && subject == "" {
		return &common_event.ProcessorEvent{Event: nil}
	}
	return &common_event.ProcessorEvent{
		Event: &common_event.BaseEvent{Id: id, Type: eventType, Subject: subject},
	}
}

// createSubscribeRule 创建订阅规则
func createSubscribeRule(id int64, watchType string, hasConfig bool) *metadata_access.EventSubscribe {
	subscriber := &metadata_access.EventSubscribe{Id: id}
	if hasConfig {
		subscriber.Rules = map[string]*metadata_access.EventSubscribeRule{
			watchType: {
				SubscribeId: id, WatchType: watchType,
				ExtDataMatchRule: []*metadata_access.ExtDataConfig{{
					Layers: []*metadata_access.ExtDataConfigLayer{{
						Layer: 0,
						ReadCfgs: []*metadata_access.ExtDataReadCfg{{
							ViewType: common_event.EnumViewType_UNION_VIEW,
							ViewId:   "test_view", Fields: []string{"test_field"},
						}},
					}},
				}},
			},
		}
	}
	return subscriber
}

// runExtenderTest 执行扩展器测试的通用逻辑
func runExtenderTest(t *testing.T, event *common_event.ProcessorEvent, metaReader *mockEventMetaReaderForExtender,
	daoFactory *mockDaoFactory, eventBridgeID int64, eventBridgeType string, wantErr bool) {

	testExtender := &testExtender{
		eventMetaReader: metaReader, daoFactory: daoFactory, bridgeID: eventBridgeID,
	}

	err := testExtender.ExtendEventData(context.Background(), eventBridgeID, eventBridgeType, event)

	if (err != nil) != wantErr {
		t.Errorf("ExtendEventData() error = %v, wantErr %v", err, wantErr)
	}

	// 如果成功且事件不为空，验证ExtData被初始化
	if !wantErr && event != nil && event.Event != nil {
		if event.ExtData == nil {
			t.Errorf("ExtData should be initialized after successful ExtendEventData()")
		}
	}
}

func TestExtender_ExtendEventData(t *testing.T) {
	tests := []struct {
		name            string
		event           *common_event.ProcessorEvent
		metaReader      *mockEventMetaReaderForExtender
		daoFactory      *mockDaoFactory
		eventBridgeID   int64
		eventBridgeType string
		wantErr         bool
	}{
		{
			name: "事件为空", event: nil,
			metaReader: &mockEventMetaReaderForExtender{}, daoFactory: &mockDaoFactory{},
			eventBridgeID: 1, eventBridgeType: "test_type", wantErr: true,
		},
		{
			name: "事件Event为空", event: createExtenderEvent("", "", ""),
			metaReader: &mockEventMetaReaderForExtender{}, daoFactory: &mockDaoFactory{},
			eventBridgeID: 1, eventBridgeType: "test_type", wantErr: true,
		},
		{
			name: "没有订阅信息", event: createExtenderEvent("test_event", "test_type", "test_subject"),
			metaReader: &mockEventMetaReaderForExtender{subscribers: []*metadata_access.EventSubscribe{}},
			daoFactory: &mockDaoFactory{}, eventBridgeID: 1, eventBridgeType: "test_type",
		},
		{
			name: "扩展事件数据成功", event: createExtenderEvent("test_event", "test_type", "test_subject"),
			metaReader: &mockEventMetaReaderForExtender{subscribers: []*metadata_access.EventSubscribe{createSubscribeRule(1, "test_type", true)}},
			daoFactory: &mockDaoFactory{}, eventBridgeID: 1, eventBridgeType: "test_type",
		},
		{
			name: "事件主题为空", event: createExtenderEvent("test_event", "test_type", ""),
			metaReader: &mockEventMetaReaderForExtender{subscribers: []*metadata_access.EventSubscribe{createSubscribeRule(1, "test_type", true)}},
			daoFactory: &mockDaoFactory{}, eventBridgeID: 1, eventBridgeType: "test_type", wantErr: true,
		},
		{
			name: "获取数据访问器失败", event: createExtenderEvent("test_event", "test_type", "test_subject"),
			metaReader: &mockEventMetaReaderForExtender{subscribers: []*metadata_access.EventSubscribe{createSubscribeRule(1, "test_type", true)}},
			daoFactory: &mockDaoFactory{shouldError: true}, eventBridgeID: 1, eventBridgeType: "test_type", wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			runExtenderTest(t, tt.event, tt.metaReader, tt.daoFactory, tt.eventBridgeID, tt.eventBridgeType, tt.wantErr)
		})
	}
}

// testExtender 用于测试的扩展器，简化实现但保持核心逻辑
type testExtender struct {
	eventMetaReader toolkitrepo.EventMetaReader
	daoFactory      *mockDaoFactory
	bridgeID        int64
}

func (e *testExtender) ExtendEventData(ctx context.Context, eventBridgeID int64, eventBridgeType string,
	event *common_event.ProcessorEvent) error {
	if event == nil || event.Event == nil {
		return &testError{"事件为空"}
	}

	// 初始化ExtData
	if event.ExtData == nil {
		event.ExtData = &common_event.ExtData{
			DataList: make([]*common_event.LayerData, 0),
		}
	}

	// 获取事件总线的所有订阅eventBridgeType事件的信息
	subscribes := e.eventMetaReader.GetEventSubscribes(ctx, eventBridgeID, eventBridgeType)
	if len(subscribes) == 0 {
		return nil
	}

	// 简化的实现：检查是否有需要扩展的字段配置
	hasExtendableConfig := false
	for _, subscribe := range subscribes {
		if subscribe != nil && subscribe.Rules != nil {
			if rule, exists := subscribe.Rules[event.Event.Type]; exists && rule != nil {
				if len(rule.ExtDataMatchRule) > 0 {
					hasExtendableConfig = true
					break
				}
			}
		}
	}

	if !hasExtendableConfig {
		return nil
	}

	// 检查事件主题
	if event.Event.Subject == "" {
		return &testError{"事件主题为空，无法进行第一层数据扩展"}
	}

	// 简化的处理第一层数据
	accessor, err := e.daoFactory.GetDataAccessor(ctx, eventBridgeID, common_event.EnumViewType_UNION_VIEW)
	if err != nil {
		return err
	}

	// 模拟调用数据访问器
	_, err = accessor.BatchGetFields(ctx, "test_view", []string{event.Event.Subject}, []string{"test_field"}, event.Event.Timestamp)
	if err != nil {
		return err
	}

	return nil
}

func TestExtender_collectSubscriberConfigs(t *testing.T) {
	tests := []struct {
		name        string
		subscribes  []*metadata_access.EventSubscribe
		eventType   string
		expectedLen int
	}{
		{
			name:        "空订阅列表",
			subscribes:  []*metadata_access.EventSubscribe{},
			eventType:   "test_type",
			expectedLen: 0,
		},
		{
			name: "包含nil订阅",
			subscribes: []*metadata_access.EventSubscribe{
				nil,
				{
					Id: 1,
					Rules: map[string]*metadata_access.EventSubscribeRule{
						"test_type": {
							SubscribeId: 1,
							WatchType:   "test_type",
							ExtDataMatchRule: []*metadata_access.ExtDataConfig{
								{
									Layers: []*metadata_access.ExtDataConfigLayer{
										{
											Layer: 0,
										},
									},
								},
							},
						},
					},
				},
			},
			eventType:   "test_type",
			expectedLen: 1,
		},
		{
			name: "没有匹配的事件类型",
			subscribes: []*metadata_access.EventSubscribe{
				{
					Id: 1,
					Rules: map[string]*metadata_access.EventSubscribeRule{
						"other_type": {
							SubscribeId: 1,
							WatchType:   "other_type",
						},
					},
				},
			},
			eventType:   "test_type",
			expectedLen: 0,
		},
		{
			name: "收集订阅配置成功",
			subscribes: []*metadata_access.EventSubscribe{
				{
					Id: 1,
					Rules: map[string]*metadata_access.EventSubscribeRule{
						"test_type": {
							SubscribeId: 1,
							WatchType:   "test_type",
							ExtDataMatchRule: []*metadata_access.ExtDataConfig{
								{
									Layers: []*metadata_access.ExtDataConfigLayer{
										{
											Layer: 0,
										},
									},
								},
							},
						},
					},
				},
				{
					Id: 2,
					Rules: map[string]*metadata_access.EventSubscribeRule{
						"test_type": {
							SubscribeId: 2,
							WatchType:   "test_type",
							ExtDataMatchRule: []*metadata_access.ExtDataConfig{
								{
									Layers: []*metadata_access.ExtDataConfigLayer{
										{
											Layer: 0,
										},
									},
								},
							},
						},
					},
				},
			},
			eventType:   "test_type",
			expectedLen: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			extender := &Extender{}
			result := extender.collectSubscriberConfigs(tt.subscribes, tt.eventType)

			if len(result) != tt.expectedLen {
				t.Errorf("Expected %d configs, got %d", tt.expectedLen, len(result))
			}
		})
	}
}

func TestExtender_ensureLayerExists(t *testing.T) {
	tests := []struct {
		name         string
		initialEvent *common_event.ProcessorEvent
		layerIndex   int32
		expectedLen  int
	}{
		{
			name: "添加第一层",
			initialEvent: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{},
				ExtData: &common_event.ExtData{
					DataList: []*common_event.LayerData{},
				},
			},
			layerIndex:  0,
			expectedLen: 1,
		},
		{
			name: "添加多层",
			initialEvent: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{},
				ExtData: &common_event.ExtData{
					DataList: []*common_event.LayerData{},
				},
			},
			layerIndex:  2,
			expectedLen: 3,
		},
		{
			name: "已有足够层数",
			initialEvent: &common_event.ProcessorEvent{
				Event: &common_event.BaseEvent{},
				ExtData: &common_event.ExtData{
					DataList: []*common_event.LayerData{
						{Level: 0},
						{Level: 1},
					},
				},
			},
			layerIndex:  1,
			expectedLen: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			extender := &Extender{}
			extender.ensureLayerExists(tt.initialEvent, tt.layerIndex)

			if len(tt.initialEvent.ExtData.DataList) != tt.expectedLen {
				t.Errorf("Expected %d layers, got %d", tt.expectedLen, len(tt.initialEvent.ExtData.DataList))
			}
		})
	}
}

func TestExtender_extractDataIDsFromField(t *testing.T) {
	tests := []struct {
		name         string
		fieldValue   *common_event.FieldValue
		parentRef    *metadata_access.ExtDataGetCfgParentRef
		expectedLen  int
		expectedData []string
	}{
		{
			name:         "字段值为空",
			fieldValue:   nil,
			parentRef:    &metadata_access.ExtDataGetCfgParentRef{},
			expectedLen:  0,
			expectedData: []string{},
		},
		{
			name: "字符串字段无分隔符",
			fieldValue: &common_event.FieldValue{
				FieldType: common_event.EnumFieldType_STR_FIELD,
				Str:       "test_id",
			},
			parentRef: &metadata_access.ExtDataGetCfgParentRef{
				SplitSymbol: "",
			},
			expectedLen:  1,
			expectedData: []string{"test_id"},
		},
		{
			name: "字符串字段有分隔符",
			fieldValue: &common_event.FieldValue{
				FieldType: common_event.EnumFieldType_STR_FIELD,
				Str:       "id1,id2,id3",
			},
			parentRef: &metadata_access.ExtDataGetCfgParentRef{
				SplitSymbol: ",",
			},
			expectedLen:  3,
			expectedData: []string{"id1", "id2", "id3"},
		},
		{
			name: "SET字段类型",
			fieldValue: &common_event.FieldValue{
				FieldType: common_event.EnumFieldType_SET_FIELD,
				StrList:   []string{"id1", "id2", "id3"},
			},
			parentRef:    &metadata_access.ExtDataGetCfgParentRef{},
			expectedLen:  3,
			expectedData: []string{"id1", "id2", "id3"},
		},
		{
			name: "整数向量字段类型",
			fieldValue: &common_event.FieldValue{
				FieldType: common_event.EnumFieldType_INT_VEC_FIELD,
				IntList:   []int64{1, 2, 3},
			},
			parentRef:    &metadata_access.ExtDataGetCfgParentRef{},
			expectedLen:  3,
			expectedData: []string{"1", "2", "3"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			extender := &Extender{}
			result := extender.extractDataIDsFromField(tt.fieldValue, tt.parentRef)

			if len(result) != tt.expectedLen {
				t.Errorf("Expected %d data IDs, got %d", tt.expectedLen, len(result))
			}

			if tt.expectedLen > 0 {
				for i, expected := range tt.expectedData {
					if i < len(result) && result[i] != expected {
						t.Errorf("Expected data ID %s at index %d, got %s", expected, i, result[i])
					}
				}
			}
		})
	}
}

func TestNewExtender(t *testing.T) {
	// 测试NewExtender构造函数，但由于依赖外部组件，主要测试参数传递
	cfg := &config.Config{}
	bridgeID := int64(123)

	// 由于NewExtender内部创建EventMetaReader和DAO工厂可能失败，我们只能进行基本测试
	extender, err := NewExtender(cfg, bridgeID)
	if err != nil {
		// 在测试环境中，由于外部依赖，这可能会失败
		t.Logf("NewExtender() returned error (expected in test environment): %v", err)
	} else if extender != nil {
		if extender.bridgeID != bridgeID {
			t.Errorf("Expected bridgeID %d, got %d", bridgeID, extender.bridgeID)
		}
		if extender.cfg != cfg {
			t.Errorf("Config not set correctly")
		}
	}
}
