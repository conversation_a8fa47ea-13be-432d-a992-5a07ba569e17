package logic

import (
	"context"
	"testing"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpcprotocol/hydra/tms"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"github.com/agiledragon/gomonkey/v2"
)

func TestIsWithinRange(t *testing.T) {
	testCases := []struct {
		now        string
		updateTime string
		expected   bool
	}{
		{"2023-09-13 11:30:00", "10:30:00", false},
		{"2023-09-13 12:00:00", "11:00:00", false},
		{"2023-09-13 12:30:00", "11:30:00", false},
		{"2023-09-13 12:00:00", "12:00:00", true},
		{"2023-09-13 12:00:00", "invalid", false},
	}

	for _, testCase := range testCases {
		now, _ := time.Parse("2006-01-02 15:04:05", testCase.now)
		result := isWithinRange(now, testCase.updateTime)
		if result != testCase.expected {
			t.Errorf("isWithinRange(%v, %q) = %v; want %v", now, testCase.updateTime, result, testCase.expected)
		}
	}
}

func Test_pushPlaylistDetailCrawTask(t *testing.T) {
	type args struct {
		ctx       context.Context
		crawID    string
		detailURL string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx:       trpc.BackgroundContext(),
				crawID:    "1004_100_2",
				detailURL: "https://www.iqiyi.com/v_11ptpczctlo.html?vfrm=jmd_zongyi&vfrmblk=menu_pre_7&vfrmrst=jmd_zongyi_image1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 使用 gomonkey 将 InvokeCrawlerAddTask 函数替换为空返回函数
			patch := gomonkey.ApplyFunc(dao.InvokeCrawlerAddTask,
				func(ctx context.Context, req *tms.AddJobRequest) (*tms.AddJobReply, error) {
					return &tms.AddJobReply{Code: 0}, nil
				})
			defer patch.Reset()

			if err := pushPlaylistDetailCrawTask(tt.args.ctx, tt.args.crawID, tt.args.detailURL); (err != nil) != tt.wantErr {
				t.Errorf("pushPlaylistCrawTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
