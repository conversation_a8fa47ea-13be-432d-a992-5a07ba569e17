package logic

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/andybalholm/brotli"
	"github.com/golang/snappy"
	"github.com/klauspost/compress/gzip"
	"github.com/klauspost/compress/zstd"
	"github.com/klauspost/pgzip"
	"github.com/pierrec/lz4"
	"github.com/tealeg/xlsx"
)

// AlgorithmStats 压缩算法统计结构
type AlgorithmStats struct {
	TotalCompressTime   time.Duration
	TotalDecompressTime time.Duration
	TotalOriginalSize   int64
	TotalCompressedSize int64
	SuccessCount        int
}

// CategoryStats 分类统计结构
type CategoryStats struct {
	TotalFiles int
	Algorithms map[string]AlgorithmStats
}

const (
	// KB TODO
	KB = 1024
	// MB TODO
	MB = 1024 * 1024
)

var categories = map[string]*CategoryStats{
	"0-1MB":    {Algorithms: make(map[string]AlgorithmStats)},
	"1-10MB":   {Algorithms: make(map[string]AlgorithmStats)},
	"10-100MB": {Algorithms: make(map[string]AlgorithmStats)},
	"100MB+":   {Algorithms: make(map[string]AlgorithmStats)},
}

// Task TODO
type Task struct {
	Path string
	Info os.FileInfo
}

// FileResult TODO
type FileResult struct {
	Category       string
	AlgorithmStats map[string]AlgorithmResult
}

// AlgorithmResult TODO
type AlgorithmResult struct {
	Success        bool
	CompressTime   time.Duration
	DecompressTime time.Duration
	OriginalSize   int64
	CompressedSize int64
}

func main() {
	rootDir := "../data/"
	outputExcel := "result.xlsx"
	workers := 1

	algorithms := []struct {
		Name       string
		Compress   func([]byte) ([]byte, error)
		Decompress func([]byte) ([]byte, error)
	}{
		{
			Name: "Gzip",
			Compress: func(data []byte) ([]byte, error) {
				return compressGzip(data, gzip.BestSpeed)
			},
			Decompress: decompressGzip,
		},
		{
			Name: "Pgzip",
			Compress: func(data []byte) ([]byte, error) {
				return compressPgzip(data, runtime.NumCPU())
			},
			Decompress: decompressPgzip,
		},
		{
			Name: "Zstd",
			Compress: func(data []byte) ([]byte, error) {
				return compressZstd(data, zstd.SpeedFastest)
			},
			Decompress: decompressZstd,
		},
		{
			Name: "Brotli-1",
			Compress: func(data []byte) ([]byte, error) {
				return compressBrotli(data, 1)
			},
			Decompress: decompressBrotli,
		},
		{
			Name:       "Snappy",
			Compress:   compressSnappy,
			Decompress: decompressSnappy,
		},
		{
			Name:       "Lz4",
			Compress:   compressLz4,
			Decompress: deCompressLz4,
		},
	}

	taskCh := make(chan Task, 100)
	resultCh := make(chan FileResult, 100)

	var wg sync.WaitGroup

	// 启动worker池
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for task := range taskCh {
				result := processFile(task, algorithms)
				resultCh <- result
			}
		}()
	}

	// 遍历目录生成任务
	go func() {
		filepath.Walk(rootDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				log.Printf("Error accessing path %s: %v", path, err)
				return nil
			}
			if !info.IsDir() {
				taskCh <- Task{Path: path, Info: info}
			}
			return nil
		})
		close(taskCh)
	}()

	// 结果处理
	go func() {
		wg.Wait()
		close(resultCh)
	}()

	// 合并结果
	for res := range resultCh {
		cat := categories[res.Category]
		cat.TotalFiles++

		for algoName, algoRes := range res.AlgorithmStats {
			if !algoRes.Success {
				continue
			}

			stats := cat.Algorithms[algoName]
			stats.TotalCompressTime += algoRes.CompressTime
			stats.TotalDecompressTime += algoRes.DecompressTime
			stats.TotalOriginalSize += algoRes.OriginalSize
			stats.TotalCompressedSize += algoRes.CompressedSize
			stats.SuccessCount++
			cat.Algorithms[algoName] = stats
		}
	}

	// 生成Excel报表
	generateExcel(outputExcel)
}

var count int

func processFile(task Task, algorithms []struct {
	Name       string
	Compress   func([]byte) ([]byte, error)
	Decompress func([]byte) ([]byte, error)
}) FileResult {
	count++
	data, err := os.ReadFile(task.Path)
	if err != nil {
		log.Printf("Error reading %s: %v", task.Path, err)
		return FileResult{}
	}

	originalSize := int64(len(data))
	category := getCategory(originalSize)

	result := FileResult{
		Category:       category,
		AlgorithmStats: make(map[string]AlgorithmResult),
	}

	// var pbData DataBlob
	// pbData.Data = data
	// data, _ = proto.Marshal(&pbData)

	for _, algo := range algorithms {
		var algoResult AlgorithmResult
		algoResult.OriginalSize = originalSize

		start := time.Now()
		compressed, err := algo.Compress(data)
		if err != nil {
			log.Printf("%s compression failed for %s: %v", algo.Name, task.Path, err)
			result.AlgorithmStats[algo.Name] = algoResult
			continue
		}
		compressTime := time.Since(start)

		start = time.Now()
		decompressed, err := algo.Decompress(compressed)
		if err != nil {
			log.Printf("%s decompression failed for %s: %v", algo.Name, task.Path, err)
			result.AlgorithmStats[algo.Name] = algoResult
			continue
		}
		decompressTime := time.Since(start)

		if !bytes.Equal(data, decompressed) {
			log.Printf("%s data mismatch for %s", algo.Name, task.Path)
			result.AlgorithmStats[algo.Name] = algoResult
			continue
		}

		algoResult.Success = true
		algoResult.CompressTime = compressTime
		algoResult.DecompressTime = decompressTime
		algoResult.CompressedSize = int64(len(compressed))
		result.AlgorithmStats[algo.Name] = algoResult
	}
	log.Println(count)
	return result
}

func getCategory(size int64) string {
	switch {
	case size <= 1*MB:
		return "0-1MB"
	case size <= 10*MB:
		return "1-10MB"
	case size <= 100*MB:
		return "10-100MB"
	default:
		return "100MB+"
	}
}

func generateExcel(filename string) {
	file := xlsx.NewFile()

	for category, stats := range categories {
		if stats.TotalFiles == 0 {
			continue
		}

		sheet, err := file.AddSheet(category)
		if err != nil {
			log.Printf("Error creating sheet %s: %v", category, err)
			continue
		}

		// 添加标题行
		header := sheet.AddRow()
		header.AddCell().SetString("算法")
		header.AddCell().SetString("压缩1MB所需毫秒")
		header.AddCell().SetString("解压1MB所需毫秒")
		header.AddCell().SetString("压缩比")
		header.AddCell().SetString("处理文件")

		// 添加数据行
		for algoName, algoStats := range stats.Algorithms {
			if algoStats.SuccessCount == 0 {
				continue
			}

			// 处理速率：xxx MB/ms
			avgCompress := (float64(algoStats.TotalOriginalSize) / 1024 / 1024) / float64(
				algoStats.TotalCompressTime.Milliseconds())
			avgDecompress := float64(algoStats.TotalOriginalSize/1024/1024) / float64(algoStats.TotalDecompressTime.Milliseconds())
			compressionRatio := float64(algoStats.TotalOriginalSize) / float64(algoStats.TotalCompressedSize)

			row := sheet.AddRow()
			row.AddCell().SetString(algoName)
			row.AddCell().SetString(fmt.Sprintf("%.2f", 1/avgCompress))
			row.AddCell().SetString(fmt.Sprintf("%.2f", 1/avgDecompress))
			row.AddCell().SetString(fmt.Sprintf("%.2f", compressionRatio))
			row.AddCell().SetInt(algoStats.SuccessCount)
		}

		// 添加总计行
		totalRow := sheet.AddRow()
		totalRow.AddCell().SetString("Total Files")
		totalRow.AddCell().SetInt(stats.TotalFiles)
	}

	if err := file.Save(filename); err != nil {
		log.Fatal("Error saving Excel file:", err)
	}
}

// 以下是您提供的压缩/解压函数，保持原样不变
// [此处插入您提供的所有压缩/解压函数]

// 以下是原有的压缩/解压函数（保持原样）
// [原有压缩函数保持不变...]

// compressGzip TODO
// GZIP压缩（标准库实现）
func compressGzip(src []byte, level int) ([]byte, error) {
	var buf bytes.Buffer
	gz, err := gzip.NewWriterLevel(&buf, level)
	if err != nil {
		return nil, err
	}
	if _, err := gz.Write(src); err != nil {
		return nil, err
	}
	if err := gz.Close(); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// decompressGzip TODO
// GZIP解压（标准库实现）
func decompressGzip(src []byte) ([]byte, error) {
	gr, err := gzip.NewReader(bytes.NewReader(src))
	if err != nil {
		return nil, err
	}
	defer gr.Close()
	return io.ReadAll(gr)
}

// compressPgzip TODO
// PGZIP并行压缩
func compressPgzip(src []byte, concurrency int) ([]byte, error) {
	var buf bytes.Buffer
	gz, _ := pgzip.NewWriterLevel(&buf, gzip.BestSpeed)
	gz.SetConcurrency(1<<20, concurrency) // 1MB块大小
	if _, err := gz.Write(src); err != nil {
		return nil, err
	}
	if err := gz.Close(); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// decompressPgzip TODO
// PGZIP解压
func decompressPgzip(src []byte) ([]byte, error) {
	gr, err := pgzip.NewReader(bytes.NewReader(src))
	if err != nil {
		return nil, err
	}
	defer gr.Close()
	return io.ReadAll(gr)
}

// compressZstd TODO
// ZSTD压缩[3](@ref)
func compressZstd(src []byte, level zstd.EncoderLevel) ([]byte, error) {
	encoder, _ := zstd.NewWriter(nil, zstd.WithEncoderLevel(level))
	return encoder.EncodeAll(src, nil), nil
}

// decompressZstd TODO
// ZSTD解压
func decompressZstd(src []byte) ([]byte, error) {
	decoder, _ := zstd.NewReader(nil)
	return decoder.DecodeAll(src, nil)
}

// compressBrotli TODO
// BROTLI压缩[3](@ref)
func compressBrotli(src []byte, level int) ([]byte, error) {
	var buf bytes.Buffer
	// 创建带压缩级别的写入器（level范围0-11）
	writer := brotli.NewWriterLevel(&buf, level)
	defer writer.Close() // 确保资源释放

	// 分块写入（适合大文件场景）
	if _, err := writer.Write(src); err != nil {
		return nil, fmt.Errorf("压缩写入失败: %w", err)
	}

	// 必须显式关闭以完成压缩流程
	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("压缩关闭失败: %w", err)
	}
	return buf.Bytes(), nil
}

// decompressBrotli TODO
// BROTLI解压
func decompressBrotli(src []byte) ([]byte, error) {
	// 创建Brotli阅读器
	reader := brotli.NewReader(bytes.NewReader(src))
	// 使用缓冲读取提升大文件解压效率
	var buf bytes.Buffer
	if _, err := io.Copy(&buf, reader); err != nil {
		return nil, fmt.Errorf("解压读取失败: %w", err)
	}
	return buf.Bytes(), nil
}

// compressSnappy TODO
// SNAPPY压缩
func compressSnappy(src []byte) ([]byte, error) {
	return snappy.Encode(nil, src), nil
}

// decompressSnappy TODO
// SNAPPY解压
func decompressSnappy(src []byte) ([]byte, error) {
	return snappy.Decode(nil, src)
}

func compressLz4(src []byte) ([]byte, error) {
	var buffer bytes.Buffer
	srcReader := bytes.NewReader(src)
	zw := lz4.NewWriter(&buffer)
	io.Copy(zw, srcReader)
	zw.Close()
	return buffer.Bytes(), nil
}

func deCompressLz4(src []byte) ([]byte, error) {
	var buffer bytes.Buffer
	zr := lz4.NewReader(bytes.NewReader(src))
	io.Copy(&buffer, zr)
	return buffer.Bytes(), nil
}
