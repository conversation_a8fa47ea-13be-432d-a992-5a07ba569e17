// Package plugin TODO
package plugin

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/trpcprotocol/media_event_hub/common_consumer"
	"git.woa.com/trpcprotocol/media_event_hub/common_errcode"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
)

// PostEvent 向插件投递事件
func PostEvent(ctx context.Context, plugin *metadata_access.EventTargetPlugin,
	event *common_event.PostEvent) (*common_event.PostEvent, error) {
	if plugin == nil || plugin.Addr == nil || plugin.Addr.Target == "" {
		return event, nil
	}
	if plugin.Addr.Protocol == "" || plugin.Addr.Namespace == "" {
		return nil, errs.New(int(common_errcode.ErrCode_ILLEGAL_PARAM), "插件配置信息为空！")
	}
	proxy := common_consumer.NewEventHandlerClientProxy(
		client.WithNamespace(plugin.Addr.Namespace),
		client.WithProtocol(plugin.Addr.Protocol),
		client.WithTarget(plugin.Addr.Target),
		client.WithTimeout(time.Duration(plugin.Addr.Timeout)*time.Millisecond))
	rsp, err := proxy.HandleEvent(ctx, &common_consumer.HandleEventReq{Event: event})
	if err != nil {
		return nil, err
	}
	if rsp.ErrorCode != 0 {
		return nil, errs.New(int(rsp.ErrorCode), rsp.ErrorMsg)
	}
	return rsp.Event, nil
}
