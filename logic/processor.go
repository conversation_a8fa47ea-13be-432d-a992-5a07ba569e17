package logic

import (
	"context"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	metadata "git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/processor/config"
	toolkitrepo "git.woa.com/video_media/media_event_hub/toolkit/repo"

	"github.com/IBM/sarama"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// EventBridgeProcessor 用于处理单个事件总线的消息处理器
type EventBridgeProcessor struct {
	// extender 扩展字段处理器
	extender *Extender
	// filter 过滤处理器
	filter *Filter
	// dispatcher 事件分发器
	dispatcher *Dispatcher
	// metaReader 元数据读取器
	metaReader toolkitrepo.EventMetaReader
	// bridgeID 关联的事件总线ID
	bridgeID int64
}

// InitComponents 初始化处理器组件
func (p *EventBridgeProcessor) InitComponents(cfg *config.Config, bridgeID int64) error {
	log.Infof("初始化处理器组件 InitComponents: %v; bridgeID: %d", cfg, bridgeID)
	// 保存事件总线ID
	p.bridgeID = bridgeID

	// 初始化元数据读取器
	metaReader, err := toolkitrepo.NewEventMetaReader()
	if err != nil {
		return err
	}
	p.metaReader = metaReader

	// 初始化各字段扩展器
	p.extender, err = NewExtender(cfg, bridgeID)
	if err != nil {
		return err
	}
	p.filter = NewFilter()

	// 初始化Dispatcher
	p.dispatcher, err = NewDispatcher(NewPulsarSender())
	if err != nil {
		return err
	}
	return nil
}

// ParseEventMessage 解析事件消息，返回解析后的事件和traceID
func (p *EventBridgeProcessor) ParseEventMessage(ctx context.Context, msg *sarama.ConsumerMessage) (*common_event.ProducerEvent, string, error) {
	// 解析原始消息
	var event common_event.ProducerEvent
	if err := proto.Unmarshal(msg.Value, &event); err != nil {
		log.ErrorContextf(ctx, "解析消息失败:%+v", err)
		return nil, "", err
	}
	if event.Event == nil {
		log.InfoContextf(ctx, "收到空事件消息，Topic: %s, Partition: %d, Offset: %d，将忽略处理",
			msg.Topic, msg.Partition, msg.Offset)
		return nil, "", nil
	}

	// 提取traceID
	traceID := event.Event.Trace
	if traceID == "" {
		log.WarnContextf(ctx, "事件中没有traceID，事件ID: %s", event.Event.Id)
	}

	eventJSON, _ := protojson.Marshal(&event)
	log.InfoContextf(ctx, "成功解析事件总线 %d 的消息，事件ID: %s, 事件类型: %s, traceID: %s, event:%s",
		p.bridgeID, event.Event.Id, event.Event.Type, traceID, string(eventJSON))

	return &event, traceID, nil
}

// ProcessEventMessage 处理事件消息（单个事件的处理入口）
func (p *EventBridgeProcessor) ProcessEventMessage(ctx context.Context, msg *sarama.ConsumerMessage) error {
	startTime := time.Now()
	defer func() {
		log.InfoContextf(ctx, "处理事件总耗时: %v ms, Topic: %s, Partition: %d, Offset: %d",
			time.Since(startTime).Milliseconds(), msg.Topic, msg.Partition, msg.Offset)
	}()

	// 1. 解析原始消息
	event, traceID, err := p.ParseEventMessage(ctx, msg)
	if err != nil {
		return err
	}
	if event == nil {
		return nil
	}

	ctx = log.WithContextFields(ctx, "event_id", event.Event.Id, "event_type", event.Event.Type,
		"bridge_id", strconv.FormatInt(p.bridgeID, 10), "trace_id", traceID)

	// 2. 获取该事件类型关联的所有订阅规则
	subscribers := p.metaReader.GetEventSubscribes(ctx, p.bridgeID, event.Event.Type)
	if len(subscribers) == 0 {
		log.InfoContextf(ctx, "没有找到订阅该事件的订阅方，事件ID: %s, 事件类型: %s",
			event.Event.Id, event.Event.Type)
		return nil
	}

	// 提取所有订阅规则
	var eventRules []*metadata.EventSubscribeRule
	for _, subscriber := range subscribers {
		log.InfoContextf(ctx, "GetEventSubscribes res: %+v", subscriber)
		if subscriber == nil {
			continue
		}
		if rule, exists := subscriber.Rules[event.Event.Type]; exists && rule != nil {
			eventRules = append(eventRules, rule)
		}
	}
	if len(eventRules) == 0 {
		log.InfoContextf(ctx, "没有找到订阅该事件类型的规则，事件ID: %s, 事件类型: %s",
			event.Event.Id, event.Event.Type)
		return nil
	}

	// 3. 调用Extender.ExtendEventData完成字段扩展
	extendStartTime := time.Now()
	processorEvent := common_event.ProcessorEvent{
		Event: event.Event,
	}
	err = p.extender.ExtendEventData(ctx, p.bridgeID, event.Event.Type, &processorEvent)
	if err != nil {
		log.ErrorContextf(ctx, "扩展事件失败: %v, 事件ID: %s", err, event.Event.Id)
		return err
	}
	log.InfoContextf(ctx, "扩展事件字段成功，耗时: %v ms, 事件ID: %s",
		time.Since(extendStartTime).Milliseconds(), event.Event.Id)

	// 4. 调用Filter.ProcessSubscriberEvents完成过滤和裁剪
	filterStartTime := time.Now()
	filteredEvents, err := p.filter.ProcessSubscriberEvents(ctx, &processorEvent, eventRules)
	if err != nil {
		log.ErrorContextf(ctx, "过滤和裁剪事件失败: %v, 事件ID: %s", err, event.Event.Id)
		return err
	}
	log.InfoContextf(ctx, "过滤和裁剪事件成功，耗时: %v ms, 事件ID: %s, 匹配订阅方数量: %d, 订阅方IDs: %v",
		time.Since(filterStartTime).Milliseconds(), event.Event.Id, len(filteredEvents), filteredEvents)
	if len(filteredEvents) == 0 {
		log.InfoContextf(ctx, "过滤后没有匹配的订阅方，事件ID: %s", event.Event.Id)
		return nil
	}

	// 5. 调用Dispatcher.PublishEvents将数据发送到订阅方队列
	publishStartTime := time.Now()
	err = p.dispatcher.PublishEvents(ctx, filteredEvents)
	if err != nil {
		log.ErrorContextf(ctx, "发布事件到订阅方队列失败: %v, 事件ID: %s", err, event.Event.Id)
		return err
	}
	log.InfoContextf(ctx, "发布事件到订阅方队列成功，耗时: %v ms, 事件ID: %s, 订阅方数量: %d",
		time.Since(publishStartTime).Milliseconds(), event.Event.Id, len(filteredEvents))
	return nil
}
