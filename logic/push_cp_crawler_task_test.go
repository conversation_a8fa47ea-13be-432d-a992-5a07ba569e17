package logic

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
	"github.com/agiledragon/gomonkey/v2"
)

func TestCPCrawler_PushCrawTasks(t *testing.T) {
	patches := gomonkey.ApplyFunc(dao.AddCrawTask, func(ctx context.Context,
		args *dao.CrawTaskArgs, isSearch bool) error {
		return nil
	})
	defer patches.Reset()

	type fields struct {
		CrawTask    dao.CPCrawTask
		ArticleInfo report.ReportArticleInfo
		AccountInfo report.ReportAccountInfo
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				CrawTask:    dao.CPCrawTask{},
				ArticleInfo: report.ReportArticleInfo{},
				AccountInfo: report.ReportAccountInfo{},
			},
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CPCrawler{
				CrawTask:    tt.fields.CrawTask,
				ArticleInfo: tt.fields.ArticleInfo,
				AccountInfo: tt.fields.AccountInfo,
			}
			if err := c.PushCrawTasks(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("PushCrawTasks() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCPCrawler_SetCrawTaskRunningState(t *testing.T) {
	var cpCrawTask *dao.CPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawTask), "SetCrawTaskState",
		func(_ *dao.CPCrawTask, ctx context.Context) error {
			return nil
		})

	type fields struct {
		CrawTask    dao.CPCrawTask
		ArticleInfo report.ReportArticleInfo
		AccountInfo report.ReportAccountInfo
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				CrawTask:    dao.CPCrawTask{},
				ArticleInfo: report.ReportArticleInfo{},
				AccountInfo: report.ReportAccountInfo{},
			},
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CPCrawler{
				CrawTask:    tt.fields.CrawTask,
				ArticleInfo: tt.fields.ArticleInfo,
				AccountInfo: tt.fields.AccountInfo,
			}
			if err := c.SetCrawTaskRunningState(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("SetCrawTaskRunningState() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCPCrawler_SetCrawTaskSucState(t *testing.T) {
	var cpCrawTask *dao.CPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawTask), "SetCrawTaskState",
		func(_ *dao.CPCrawTask, ctx context.Context) error {
			return nil
		})

	type fields struct {
		CrawTask    dao.CPCrawTask
		ArticleInfo report.ReportArticleInfo
		AccountInfo report.ReportAccountInfo
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				CrawTask:    dao.CPCrawTask{},
				ArticleInfo: report.ReportArticleInfo{},
				AccountInfo: report.ReportAccountInfo{},
			},
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CPCrawler{
				CrawTask:    tt.fields.CrawTask,
				ArticleInfo: tt.fields.ArticleInfo,
				AccountInfo: tt.fields.AccountInfo,
			}
			if err := c.SetCrawTaskSucState(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("SetCrawTaskSucState() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCPCrawler_SetCrawTaskEmptyState(t *testing.T) {
	var cpCrawTask *dao.CPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawTask), "SetCrawTaskState",
		func(_ *dao.CPCrawTask, ctx context.Context) error {
			return nil
		})

	type fields struct {
		CrawTask    dao.CPCrawTask
		ArticleInfo report.ReportArticleInfo
		AccountInfo report.ReportAccountInfo
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				CrawTask:    dao.CPCrawTask{},
				ArticleInfo: report.ReportArticleInfo{},
				AccountInfo: report.ReportAccountInfo{},
			},
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CPCrawler{
				CrawTask:    tt.fields.CrawTask,
				ArticleInfo: tt.fields.ArticleInfo,
				AccountInfo: tt.fields.AccountInfo,
			}
			if err := c.SetCrawTaskEmptyState(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("SetCrawTaskEmptyState() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCPCrawler_CrawlerRetArticleCallBack(t *testing.T) {
	var cpCrawTask *dao.CPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawTask), "GetCrawTaskByID",
		func(_ *dao.CPCrawTask, ctx context.Context, id int) error {
			return nil
		})
	var cpCrawler *CPCrawler
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawler), "SetCrawTaskEmptyState",
		func(_ *CPCrawler, ctx context.Context) error {
			return nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawler), "SetCrawTaskSucState",
		func(_ *CPCrawler, ctx context.Context) error {
			return nil
		})
	var articleInfo *report.ReportArticleInfo
	gomonkey.ApplyMethod(reflect.TypeOf(articleInfo), "DoReport",
		func(_ *report.ReportArticleInfo, ctx context.Context) {
			return
		})

	type fields struct {
		CrawTask    dao.CPCrawTask
		ArticleInfo report.ReportArticleInfo
		AccountInfo report.ReportAccountInfo
	}
	type args struct {
		ctx    context.Context
		crawID int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				CrawTask:    dao.CPCrawTask{},
				ArticleInfo: report.ReportArticleInfo{},
				AccountInfo: report.ReportAccountInfo{},
			},
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CPCrawler{
				CrawTask:    tt.fields.CrawTask,
				ArticleInfo: tt.fields.ArticleInfo,
				AccountInfo: tt.fields.AccountInfo,
			}
			if err := c.CrawlerRetArticleCallBack(tt.args.ctx, tt.args.crawID); (err != nil) != tt.wantErr {
				t.Errorf("CrawlerRetArticleCallBack() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCPCrawler_CrawlerRetAccountCallBack(t *testing.T) {
	var cpCrawTask *dao.CPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawTask), "GetCrawTaskByID",
		func(_ *dao.CPCrawTask, ctx context.Context, id int) error {
			return nil
		})
	var cpCrawler *CPCrawler
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawler), "SetCrawTaskEmptyState",
		func(_ *CPCrawler, ctx context.Context) error {
			return nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawler), "SetCrawTaskSucState",
		func(_ *CPCrawler, ctx context.Context) error {
			return nil
		})

	type fields struct {
		CrawTask    dao.CPCrawTask
		ArticleInfo report.ReportArticleInfo
		AccountInfo report.ReportAccountInfo
	}
	type args struct {
		ctx    context.Context
		crawID int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				CrawTask:    dao.CPCrawTask{},
				ArticleInfo: report.ReportArticleInfo{},
				AccountInfo: report.ReportAccountInfo{},
			},
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CPCrawler{
				CrawTask:    tt.fields.CrawTask,
				ArticleInfo: tt.fields.ArticleInfo,
				AccountInfo: tt.fields.AccountInfo,
			}
			if err := c.CrawlerRetAccountCallBack(tt.args.ctx, tt.args.crawID); (err != nil) != tt.wantErr {
				t.Errorf("CrawlerRetAccountCallBack() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestHandlePushCPInfoCrawTask(t *testing.T) {
	var cpCrawTask *dao.CPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(cpCrawTask), "GetAllCrawTasks",
		func(_ *dao.CPCrawTask, ctx context.Context) ([]*dao.CPCrawTask, error) {
			return []*dao.CPCrawTask{}, nil
		})

	type args struct {
		ctx context.Context
		in1 string
		in2 string
		in3 int32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := HandlePushCPInfoCrawTask(tt.args.ctx, tt.args.in1, tt.args.in2, tt.args.in3); (err != nil) != tt.wantErr {
				t.Errorf("HandlePushCPInfoCrawTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
