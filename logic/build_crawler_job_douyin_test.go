package logic

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"github.com/agiledragon/gomonkey/v2"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
)

func Test_generateDoyinSearchCrawJobs(t *testing.T) {
	infos := []*dao.CoverInfo{{Title: "A", CoverID: "cid1"}, {Title: "A", CoverID: "cid2"}}
	jobs, err := generateDoyinSearchCrawJobs(infos, 1012)
	if err != nil || len(jobs) != 1 {
		t.Fatalf("unexpected: %v %d", err, len(jobs))
	}
	var m map[string]any
	_ = json.Unmarshal([]byte(jobs[0].JobArguments), &m)
	if m["query"].(string) != "A" || jobs[0].Platform != "douyin" {
		t.Fatalf("unexpected job: %+v", jobs[0])
	}
}

func Test_genDouYinDetailJobs(t *testing.T) {
	job := genDouYinDetailJobs(1012, &DouyinSearchCrawRet{TopicID: "tid", TopicName: "nm", OriCrawID: 2}, "douyin")
	if job == nil || job.Platform != "douyin" || job.Title != "nm" {
		t.Fatalf("unexpected job: %+v", job)
	}
	if genDouYinDetailJobs(1012, nil, "douyin") != nil {
		t.Fatalf("expect nil for nil input")
	}
}

func Test_generateDouYinDetailCrawJob(t *testing.T) {
	ctx := trpc.BackgroundContext()
	var cd *dao.CrawDetail
	patch := gomonkey.ApplyMethod(reflect.TypeOf(cd), "GetCrawDetailByTitle",
		func(_ *dao.CrawDetail, _ context.Context, _ string) ([]*dao.CrawDetail, error) {
			ret := []*dao.CrawDetail{
				{ID: 1, Detail: `{"topic_id":"1","topic_name":"X","rank":3}`, Ext: "e"},
				{ID: 2, Detail: `{"topic_id":"1","topic_name":"X","rank":20}`},
				{ID: 3, Detail: `bad_json`},
			}
			return ret, nil
		})
	defer patch.Reset()
	jobs, err := generateDouYinDetailCrawJob(ctx, 1013, "X", "douyin")
	if err != nil || len(jobs) != 1 {
		t.Fatalf("unexpected: %v %d", err, len(jobs))
	}

	patch.Reset()
	patch = gomonkey.ApplyMethod(reflect.TypeOf(cd), "GetCrawDetailByTitle",
		func(_ *dao.CrawDetail, _ context.Context, _ string) ([]*dao.CrawDetail, error) {
			return nil, errors.New("db")
		})
	defer patch.Reset()
	if _, err := generateDouYinDetailCrawJob(ctx, 1013, "X", "douyin"); err == nil {
		t.Fatalf("expect error")
	}
}

func Test_HandleBuildDouYinSearchJob_and_Detail(t *testing.T) {
	ctx := trpc.BackgroundContext()
	// Clean then Insert
	var job *dao.CrawJob
	cleanPatch := gomonkey.ApplyMethod(reflect.TypeOf(job), "CleanCrawJobs",
		func(_ *dao.CrawJob, _ context.Context, _ string) error { return nil })
	defer cleanPatch.Reset()

	getPlanPatch := gomonkey.ApplyFunc(dao.GetAllPlanInfo,
		func(_ context.Context, _ []string) ([]*dao.CoverInfo, error) {
			return []*dao.CoverInfo{{Title: "T1", CoverID: "c1"}}, nil
		})
	defer getPlanPatch.Reset()

	insertPatch := gomonkey.ApplyMethod(reflect.TypeOf(job), "InsertCrawJob",
		func(_ *dao.CrawJob, _ context.Context) error { return nil })
	defer insertPatch.Reset()

	if err := HandleBuildDouYinSearchJob(ctx, "", "", 0); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}

	// Detail job
	getAllPatch := gomonkey.ApplyMethod(reflect.TypeOf(job), "GetAllCrawJobs",
		func(_ *dao.CrawJob, _ context.Context, _ string) ([]*dao.CrawJob, error) {
			return []*dao.CrawJob{{Title: "T1", Platform: "douyin"}}, nil
		})
	defer getAllPatch.Reset()

	var cd *dao.CrawDetail
	getDetailPatch := gomonkey.ApplyMethod(reflect.TypeOf(cd), "GetCrawDetailByTitle",
		func(_ *dao.CrawDetail, _ context.Context, _ string) ([]*dao.CrawDetail, error) {
			return []*dao.CrawDetail{{ID: 10, Detail: `{"topic_id":"1","topic_name":"T1","rank":1}`}}, nil
		})
	defer getDetailPatch.Reset()

	if err := HandleBuildDouYinDetailJob(ctx, "", "", 0); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
}
