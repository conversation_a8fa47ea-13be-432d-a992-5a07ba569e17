package logic

import (
	"context"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
	mediautils "git.code.oa.com/video_media/media_go_commlib/utils"
)

// IPCrawler 抓取IP的爬虫（IP话题、IP官号、IP话题属性）
type IPCrawler struct {
	CrawTask       dao.IPCrawTask
	ArticleInfo    report.ReportArticleInfo
	AccountInfo    report.ReportAccountInfo
	TopicFieldInfo report.ReportTopicFieldInfo
}

// NewIPCrawler 生成IP爬虫代理
func NewIPCrawler(c dao.IPCrawTask,
	atl report.ReportArticleInfo, acc report.ReportAccountInfo, tf report.ReportTopicFieldInfo) *IPCrawler {
	return &IPCrawler{CrawTask: c, ArticleInfo: atl, AccountInfo: acc, TopicFieldInfo: tf}
}

// NOCC:CCN_threshold(设计如此:)
func (i *IPCrawler) getIPCrawTaskArgs() *dao.CrawTaskArgs {
	// IP 官号默认参数
	defaultArgs := &dao.CrawTaskArgs{
		CrawType: i.CrawTask.CrawType,
		Transmit: genTransmit(i.CrawTask.CrawType, i.CrawTask.ID, i.CrawTask.OrderID, "craw"),
		Source:   i.CrawTask.Platform,
		URL:      i.CrawTask.PageURL,
		CrawTxt:  i.CrawTask.Nick,
		BacktraceDays: CalcBacktraceDays(i.CrawTask.CrawType, i.CrawTask.ConfirmStatus,
			i.CrawTask.BacktrackStatus, i.CrawTask.CreateTime),
	}

	// IP 话题参数
	if i.CrawTask.CrawType == model.TopicType {
		defaultArgs.URL = i.CrawTask.TopicURL
		defaultArgs.TopicName = i.CrawTask.TopicName
	}

	if i.CrawTask.ConfirmStatus != model.ManualCorrection {
		return defaultArgs
	}

	// 人工纠正，用纠正 key 去抓取
	switch i.CrawTask.CrawType {
	case model.OfficeAccountType:
		if i.CrawTask.Platform == "shipinghao" {
			defaultArgs.CrawTxt = i.CrawTask.CorrectKey
		} else {
			defaultArgs.URL = i.CrawTask.CorrectKey
		}
	case model.TopicType:
		if mediautils.Contain([]string{"douyin", "xiaohongshu"}, i.CrawTask.Platform) {
			defaultArgs.URL = i.CrawTask.CorrectKey
		} else {
			defaultArgs.TopicName = i.CrawTask.CorrectKey
		}
	default: // nothing to do
	}

	return defaultArgs
}

// SetCrawTaskRunningState 设置抓取任务为抓取中
func (i *IPCrawler) SetCrawTaskRunningState(ctx context.Context) error {
	i.CrawTask.DayCrawlerState = model.CrawlerRunning
	return i.CrawTask.SetCrawTaskState(ctx)
}

// SetCrawTaskSucState 设置抓取任务为抓取成功及已回溯状态
func (i *IPCrawler) SetCrawTaskSucState(ctx context.Context) error {
	i.CrawTask.DayCrawlerState = model.CrawlerSuc
	i.CrawTask.BacktrackStatus = model.Backtracked
	var handlers []func() error
	handlers = append(handlers, func() error {
		return i.CrawTask.SetCrawTaskState(ctx)
	})
	handlers = append(handlers, func() error {
		return i.CrawTask.SetCrawTaskBackTrack(ctx)
	})
	return trpc.GoAndWait(handlers...)
}

// SetCrawTaskFailState 设置抓取任务为抓取失败
func (i *IPCrawler) SetCrawTaskFailState(ctx context.Context) error {
	i.CrawTask.DayCrawlerState = model.CrawlerFail
	return i.CrawTask.SetCrawTaskState(ctx)
}

// SetCrawTaskEmptyState 设置抓取任务为 空状态
func (i *IPCrawler) SetCrawTaskEmptyState(ctx context.Context) error {
	i.CrawTask.DayCrawlerState = model.CrawlerRetEmpty
	return i.CrawTask.SetCrawTaskState(ctx)
}

// IsPushCraw 判断是否应该推送抓取
func (i *IPCrawler) IsPushCraw() bool {
	// 人工纠正的，且纠正 key 为空的，不抓取
	if i.CrawTask.ConfirmStatus == model.ManualCorrection && i.CrawTask.CorrectKey == "" {
		return false
	}
	return isPushCraw(i.CrawTask.Platform, i.CrawTask.Valid, i.CrawTask.CrawType,
		i.CrawTask.DayCrawlerEndTime, i.CrawTask.DayCrawlerState)
}

func (i *IPCrawler) reportArticleInfo(ctx context.Context) {
	i.ArticleInfo.CrawType = i.CrawTask.CrawType
	i.ArticleInfo.CoverIDs = i.CrawTask.CoverID
	i.ArticleInfo.IPName = i.CrawTask.CrawKey
	i.ArticleInfo.PriorityFlag = i.CrawTask.PriorityFlag
	i.ArticleInfo.EnvFlag = model.NewEnvTag
	i.ArticleInfo.ConfirmStatus = strconv.Itoa(i.CrawTask.ConfirmStatus)
	i.ArticleInfo.CorrectKey = i.CrawTask.CorrectKey
	i.ArticleInfo.DoReport(ctx)
}

func (i *IPCrawler) handleArticleEmptyOrErrorResult(ctx context.Context) {
	if i.ArticleInfo.StaticInfo.CrawlSource == "" || i.ArticleInfo.ErrorMsg != "" {
		if err := i.SetCrawTaskEmptyState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawTaskEmptyState ERR:%+v", err)
			return
		}
	}
}

// CrawlerRetArticleCallBack 爬虫返回发文信息的回调
func (i *IPCrawler) CrawlerRetArticleCallBack(ctx context.Context, crawID int) error {
	if err := i.CrawTask.GetCrawTaskByID(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "cannot find %d account id.", crawID)
		return err
	}
	i.handleArticleEmptyOrErrorResult(ctx)
	i.reportArticleInfo(ctx)

	if err := i.SetCrawTaskSucState(ctx); err != nil {
		log.ErrorContextf(ctx, "SetCrawTaskState ERR:%+v", err)
		return err
	}
	return nil
}

func (i *IPCrawler) reportAccountInfo(ctx context.Context) {
	i.AccountInfo.MsgID = i.AccountInfo.AccountID
	i.AccountInfo.AccountPlatform = i.CrawTask.Platform
	i.AccountInfo.CrawType = i.CrawTask.CrawType
	i.AccountInfo.CrawlerState = model.CrawlerSuc
	i.AccountInfo.CoverIDs = i.CrawTask.CoverID
	i.AccountInfo.IPName = i.CrawTask.CrawKey
	i.AccountInfo.PriorityFlag = i.CrawTask.PriorityFlag
	i.AccountInfo.OrderID = i.CrawTask.OrderID
	i.AccountInfo.CrawlerStartTime = i.CrawTask.DayCrawlerStartTime.Format("2006-01-02 15:04:05")
	(&i.AccountInfo).DoReport(ctx)
}

func (i *IPCrawler) handleAccountEmptyOrErrorResult(ctx context.Context) {
	if i.AccountInfo.StaticInfo.CrawlSource == "" || i.AccountInfo.ErrorMsg != "" {
		if err := i.SetCrawTaskEmptyState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawTaskEmptyState ERR:%+v", err)
			return
		}
	}
}

// CrawlerRetAccountCallBack 爬虫返回账号结果的回调; ReportInfo
func (i *IPCrawler) CrawlerRetAccountCallBack(ctx context.Context, crawID int) error {
	// 查询账号ID
	if err := i.CrawTask.GetCrawTaskByID(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "cannot find %d account id.", crawID)
		return nil
	}

	i.handleAccountEmptyOrErrorResult(ctx)
	i.reportAccountInfo(ctx)

	if err := i.SetCrawTaskSucState(ctx); err != nil {
		log.ErrorContextf(ctx, "SetCrawTaskSucState ERR:%+v", err)
	}
	return nil
}

// CrawlerRetTopicFieldCallBack 爬虫话题属性结果回调处理
func (i *IPCrawler) CrawlerRetTopicFieldCallBack(ctx context.Context, crawID int) error {
	// 查询账号ID的详情信息
	if err := i.CrawTask.GetCrawTaskByID(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "cannot find %d account id.", crawID)
		return nil
	}

	i.TopicFieldInfo.OrderID = i.CrawTask.OrderID
	i.TopicFieldInfo.EnvFlag = model.NewEnvTag
	i.TopicFieldInfo.CoverIDs = i.CrawTask.CoverID
	i.TopicFieldInfo.TopicName = i.CrawTask.CrawKey // 不用抓取侧返回结果中的话题名，用我们配置表中的话题名
	i.TopicFieldInfo.ConfirmStatus = strconv.Itoa(i.CrawTask.ConfirmStatus)
	i.TopicFieldInfo.CorrectKey = i.CrawTask.CorrectKey
	if i.TopicFieldInfo.RetCrawlStatus == model.CrawlerSuc {
		// 上报ATTa
		(&i.TopicFieldInfo).DoReport(ctx)
		// 更新抓取状态为"抓取成功"
		if err := i.SetCrawTaskSucState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawTaskSucState ERR:%+v", err)
		}
	} else {
		// 更新抓取状态为"抓取失败"
		if err := i.SetCrawTaskFailState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawTaskSucState ERR:%+v", err)
		}
	}
	return nil
}

func processCrawTasks(ctx context.Context, ipCrawTasks []*dao.IPCrawTask) error {
	for _, t := range ipCrawTasks {
		crawler := NewIPCrawler(*t, report.ReportArticleInfo{},
			report.ReportAccountInfo{}, report.ReportTopicFieldInfo{})
		crawArgs := crawler.getIPCrawTaskArgs()
		if err := dao.AddCrawTask(trpc.CloneContext(ctx), crawArgs, false); err != nil {
			log.ErrorContextf(ctx, "add craw task err[%+v], crawArgs[%v]", err, crawArgs)
			return err
		}

		log.InfoContextf(ctx, "add craw task success crawArgs[%v]", crawArgs)

		if err := crawler.SetCrawTaskRunningState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawTaskRunningState ERR:%+v", err)
		}
	}
	return nil
}

// HandlePushLongtailIPInfoCrawTask 推送长尾IP内容抓取任务
func HandlePushLongtailIPInfoCrawTask(ctx context.Context, _, _ string, _ int32) error {
	log.InfoContextf(ctx, "HandlePushLongtailIPInfoCrawTask enter")
	return pushIPInfoCrawTask(ctx, model.LongTailIPTopicOrder)
}

// HandlePushIPInfoCrawTask 推送IP抓取任务（重点剧）
func HandlePushIPInfoCrawTask(ctx context.Context, _, _ string, _ int32) error {
	log.InfoContextf(ctx, "HandlePushIPInfoCrawTask enter")
	return pushIPInfoCrawTask(ctx, 0) // 默认 重点剧IP
}

func pushIPInfoCrawTask(ctx context.Context, orderID int) error {
	var ipCrawTask dao.IPCrawTask
	ipCrawTask.OrderID = orderID
	IPCrawTasks, err := ipCrawTask.GetAllCrawTasks(ctx)
	if err != nil {
		return err
	}

	// 区分未进行搜索的任务和已搜索的任务
	var searchTasks, crawTasks []*dao.IPCrawTask
	for _, task := range IPCrawTasks {
		crawler := NewIPCrawler(*task, report.ReportArticleInfo{},
			report.ReportAccountInfo{}, report.ReportTopicFieldInfo{})
		if !crawler.IsPushCraw() {
			log.Debugf("Do not Push:%d", task.ID)
			continue
		}
		if ipTaskSearched(task) {
			crawTasks = append(crawTasks, task)
			continue
		}
		searchTasks = append(searchTasks, task)
	}

	var handlers []func() error
	handlers = append(handlers, func() error {
		return processSearchTasks(ctx, searchTasks)
	})
	handlers = append(handlers, func() error {
		return processCrawTasks(ctx, crawTasks)
	})

	if err = trpc.GoAndWait(handlers...); err != nil {
		log.ErrorContextf(ctx, "pushIPInfoCrawTask err[%v]", err)
		return err
	}
	log.InfoContextf(ctx, "pushIPInfoCrawTask suc searchTasks[%v] crawTasks[%v]", searchTasks, crawTasks)
	return nil
}

func ipTaskSearched(task *dao.IPCrawTask) bool {
	switch task.CrawType {
	case model.OfficeAccountType:
		if task.Platform == "shipinhao" {
			return task.Nick != ""
		}
		return task.PageURL != ""
	case model.TopicType:
		if mediautils.Contain([]string{"douyin", "xiaohongshu"}, task.Platform) {
			return task.TopicURL != ""
		}
		return task.TopicName != ""
	default: // nothing to do
	}
	return false
}

func processSearchTasks(ctx context.Context, tasks []*dao.IPCrawTask) error {
	for _, task := range tasks {
		args := &dao.CrawTaskArgs{
			Source:   task.Platform,
			Transmit: genTransmit(task.CrawType, task.ID, task.OrderID, "search"),
			CrawTxt:  task.CrawKey,
			CrawType: task.CrawType,
		}
		if err := dao.AddCrawTask(ctx, args, true); err != nil {
			log.ErrorContextf(ctx, "AddCrawTask err[%v] task[%v]", err, task)
			continue
		}
		log.InfoContextf(ctx, "search success args[%v]", args)
	}
	return nil
}
