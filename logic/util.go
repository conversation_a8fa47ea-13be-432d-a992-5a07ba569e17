package logic

import "regexp"

// 预编译正则表达式，提升性能
var (
	variableReg = regexp.MustCompile(`\$\{([^}]+)\}`)
)

// RenderTemplate 渲染模板，对模板中的 ${var} 进行替换
func RenderTemplate(data map[string]string, templateStr string) string {
	return variableReg.ReplaceAllStringFunc(templateStr, func(match string) string {
		subMatches := variableReg.FindStringSubmatch(match)
		if len(subMatches) < 2 {
			return match
		}
		varName := subMatches[1]
		if val, ok := data[varName]; ok {
			return val
		} else {
			return ""
		}
	})
}

// GetVariables 获取模板中的变量列表
func GetVariables(templateStr string) []string {
	matches := variableReg.FindAllStringSubmatch(templateStr, -1)
	var vars []string
	for _, match := range matches {
		if len(match) > 1 {
			vars = append(vars, match[1])
		}
	}
	return vars
}
