package logic

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

func getAllLongTailCovers(ctx context.Context) ([]*dao.LongTailCoverInfo, error) {
	var c dao.LongTailCoverInfo // 注意：这里由于长尾IP输出的时间较晚，隔两天才能获取到数据
	return c.GetDateLongTailCovers(ctx, time.Now().AddDate(0, 0, -2).Format("20060102"))
}

func generateLongTailCrawTasks(coverInfos []*dao.LongTailCoverInfo) ([]*dao.IPCrawTask, error) {
	var crawInfos []*dao.IPCrawTask
	for _, info := range coverInfos {
		for _, platform := range conf.GetCrawPlatforms(model.LongTailIPTopicOrder) {
			crawInfos = append(crawInfos, createLongTailCrawInfo(model.TopicType, platform, info))
		}
	}
	return crawInfos, nil
}

// HandleBuildLongTailIPCrawTask 构建长尾IP抓取任务池；每天凌晨 更新 t_long_tail_ip_crawler_task 表
func HandleBuildLongTailIPCrawTask(ctx context.Context, _, _ string, _ int32) error {
	coverInfos, err := getAllLongTailCovers(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getAllLongTailCovers ERR:%+v", err)
		return err
	}
	crawInfos, err := generateLongTailCrawTasks(coverInfos)
	if err != nil {
		log.ErrorContextf(ctx, "generateLongTailCrawTasks ERR:%+v", err)
		return err
	}

	// 由于每天的长尾列表不同，故每天需要清空抓取列表，重新生成
	var crawInfo dao.IPCrawTask
	crawInfo.OrderID = model.LongTailIPTopicOrder
	crawInfo.CleanIPCrawInfo(ctx)
	return insertIPCrawInfos(ctx, crawInfos)
}

func createLongTailCrawInfo(crawType int, platform string, info *dao.LongTailCoverInfo) *dao.IPCrawTask {
	_, topicName := calcCrawName(model.TransTypeName(info.CoverType), info.Title)
	crawInfo := &dao.IPCrawTask{
		OrderID:   model.LongTailIPTopicOrder,
		CrawType:  crawType,
		Platform:  platform,
		CoverID:   info.CoverID,
		CoverType: model.TransTypeName(info.CoverType),
		CrawKey:   topicName,
		Valid:     1,
	}
	return crawInfo
}
