package logic

import (
	"context"
	"strconv"
	"time"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/conf"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	mediautils "git.code.oa.com/video_media/media_go_commlib/utils"
)

// Crawler 推送爬虫抓取任务的接口
type Crawler interface {
	SetCrawTaskRunningState(ctx context.Context) error
	SetCrawTaskSucState(ctx context.Context) error
	SetCrawTaskFailState(ctx context.Context) error
	SetCrawTaskEmptyState(ctx context.Context) error
	IsPushCraw() bool
	CrawlerRetArticleCallBack(ctx context.Context, crawID int) error
	CrawlerRetAccountCallBack(ctx context.Context, crawID int) error
	CrawlerRetTopicFieldCallBack(ctx context.Context, crawID int) error
}

// CalcBacktraceDays 计算抓取回溯天数
func CalcBacktraceDays(crawTaskType, confirmStatus, backtrackStatus int, createTime time.Time) int {
	// CP信息抓取天数默认为30，IP话题以及官号发文默认为全量，其他默认为7天
	var backTraceDays int
	switch crawTaskType {
	case model.CPInfoType:
		backTraceDays = 30
	case model.TopicType, model.OfficeAccountType:
		backTraceDays = 365 * 10
	default:
		backTraceDays = 7
	}
	// 若是首次进入爬取list，抓全量数据
	yesDate := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	if createTime.Format("2006-01-02") == yesDate ||
		(confirmStatus == model.ManualCorrection && backtrackStatus != model.Backtracked) {
		backTraceDays = 365 * 10
	}
	return backTraceDays
}

func isPushCraw(platform string, accountState int, crawType int, crawlerEndTime time.Time, crawlerState int) bool {
	curDate := time.Now().Format("2006-01-02")
	crawlerEndDate := crawlerEndTime.Format("2006-01-02")
	// 1、黑名单平台 不推送；2、账号状态非正常的 不推送；3、今天已经抓取成功的，不推送
	if mediautils.Contain(conf.GetConfig().BlackListPlatform, strconv.Itoa(crawType)+"_"+platform) ||
		accountState != model.AccountStateOK ||
		(curDate == crawlerEndDate && crawlerState == model.CrawlerSuc) {
		return false
	}
	return true
}
