package logic

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"github.com/agiledragon/gomonkey/v2"
)

func TestHandleBuildLongTailIPCrawTask(t *testing.T) {
	var longTailCoverInfo *dao.LongTailCoverInfo
	gomonkey.ApplyMethod(reflect.TypeOf(longTailCoverInfo), "GetDateLongTailCovers",
		func(_ *dao.LongTailCoverInfo, ctx context.Context, date string) ([]*dao.LongTailCoverInfo, error) {
			return []*dao.LongTailCoverInfo{
				{
					CoverType: "电影",
					Title:     "titleTest",
				},
			}, nil
		})
	var ipCrawTask *dao.IPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(ipCrawTask), "CleanIPCrawInfo",
		func(_ *dao.IPCrawTask, ctx context.Context) error {
			return nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(ipCrawTask), "InsertIPCrawInfo",
		func(_ *dao.IPCrawTask, ctx context.Context) error {
			return nil
		})

	type args struct {
		ctx context.Context
		in1 string
		in2 string
		in3 int32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := HandleBuildLongTailIPCrawTask(tt.args.ctx, tt.args.in1, tt.args.in2, tt.args.in3); (err != nil) != tt.wantErr {
				t.Errorf("HandleBuildLongTailIPCrawTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
