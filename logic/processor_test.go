package logic

import (
	"context"
	"testing"
	"time"

	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	toolkitrepo "git.woa.com/video_media/media_event_hub/toolkit/repo"

	"github.com/IBM/sarama"
	"google.golang.org/protobuf/proto"
)

// Mock implementations for testing
type mockEventMetaReader struct {
	subscribers []*metadata_access.EventSubscribe
	returnError bool
}

func (m *mockEventMetaReader) GetEventBridge(ctx context.Context, eventBridgeID int64) *metadata_access.EventBridge {
	return nil
}

func (m *mockEventMetaReader) GetAllEventBridges(ctx context.Context) []*metadata_access.EventBridge {
	return nil
}

func (m *mockEventMetaReader) GetEventSources(ctx context.Context, eventBridgeID int64) []*metadata_access.EventSource {
	return nil
}

func (m *mockEventMetaReader) GetEventSource(ctx context.Context, eventBridgeID int64) *metadata_access.EventSource {
	return nil
}

func (m *mockEventMetaReader) GetEventType(ctx context.Context, eventBridgeID int64) []*metadata_access.EventType {
	return nil
}

func (m *mockEventMetaReader) GetEventSubscribes(ctx context.Context, eventBridgeID int64, eventType string) []*metadata_access.EventSubscribe {
	if m.returnError {
		return nil
	}
	return m.subscribers
}

func (m *mockEventMetaReader) GetEventSubscribeByAppID(ctx context.Context, appID string) *metadata_access.EventSubscribe {
	return nil
}

func (m *mockEventMetaReader) GetEventSubscribeByID(ctx context.Context, id int64) *metadata_access.EventSubscribe {
	return nil
}

type mockExtender struct {
	shouldError bool
}

func (m *mockExtender) ExtendEventData(ctx context.Context, bridgeID int64, eventType string, event *common_event.ProcessorEvent) error {
	if m.shouldError {
		return &testError{"extend event failed"}
	}
	return nil
}

type mockFilter struct {
	shouldError bool
	emptyResult bool
}

func (m *mockFilter) ProcessSubscriberEvents(ctx context.Context, event *common_event.ProcessorEvent, rules []*metadata_access.EventSubscribeRule) (map[int64]*common_event.PostEvent, error) {
	if m.shouldError {
		return nil, &testError{"filter events failed"}
	}
	if m.emptyResult {
		return map[int64]*common_event.PostEvent{}, nil
	}
	return map[int64]*common_event.PostEvent{
		1: {
			Event: event.Event,
		},
	}, nil
}

type mockDispatcher struct {
	shouldError bool
}

func (m *mockDispatcher) PublishEvents(ctx context.Context, events map[int64]*common_event.PostEvent) error {
	if m.shouldError {
		return &testError{"publish events failed"}
	}
	return nil
}

// testError 用于测试的错误类型
type testError struct {
	msg string
}

func (e *testError) Error() string {
	return e.msg
}

// 创建辅助函数用于生成通用的消息
func createTestMessage(value []byte) *sarama.ConsumerMessage {
	return &sarama.ConsumerMessage{
		Topic:     "test_topic",
		Partition: 0,
		Offset:    123,
		Value:     value,
	}
}

// 创建辅助函数用于生成通用的订阅者
func createTestSubscriber() *metadata_access.EventSubscribe {
	return &metadata_access.EventSubscribe{
		Id:       1,
		AppId:    "test_app",
		BridgeId: 12345,
		Rules: map[string]*metadata_access.EventSubscribeRule{
			"test_event_type": {
				Id:        1,
				WatchType: "test_event_type",
			},
		},
	}
}

// 创建辅助函数用于生成通用的mock对象
func createTestMocks(withSubs bool, extErr, filtErr, dispErr, emptyFilter bool) (*mockEventMetaReader, *mockExtender, *mockFilter, *mockDispatcher) {
	var subscribers []*metadata_access.EventSubscribe
	if withSubs {
		subscribers = []*metadata_access.EventSubscribe{createTestSubscriber()}
	}

	return &mockEventMetaReader{subscribers: subscribers},
		&mockExtender{shouldError: extErr},
		&mockFilter{shouldError: filtErr, emptyResult: emptyFilter},
		&mockDispatcher{shouldError: dispErr}
}

// 运行测试的辅助函数
func runProcessorTest(t *testing.T, name string, msg *sarama.ConsumerMessage, metaReader *mockEventMetaReader, extender *mockExtender, filter *mockFilter, dispatcher *mockDispatcher, wantErr bool) {
	t.Run(name, func(t *testing.T) {
		testProcessor := &testEventBridgeProcessor{
			metaReader: metaReader,
			extender:   extender,
			filter:     filter,
			dispatcher: dispatcher,
			bridgeID:   12345,
		}

		startTime := time.Now()
		err := testProcessor.ProcessEventMessage(context.Background(), msg)
		processingTime := time.Since(startTime)

		if (err != nil) != wantErr {
			t.Errorf("ProcessEventMessage() error = %v, wantErr %v", err, wantErr)
		}

		if processingTime > time.Second*5 {
			t.Errorf("ProcessEventMessage() 处理时间过长: %v", processingTime)
		}
	})
}

func TestEventBridgeProcessor_ProcessEventMessage(t *testing.T) {
	validEventBytes := createValidProducerEventBytes(t, "test_event_id", "test_event_type")
	emptyEventBytes := createEmptyProducerEventBytes(t)
	invalidEventBytes := []byte("invalid_proto_data")

	// 处理消息成功
	metaReader, extender, filter, dispatcher := createTestMocks(true, false, false, false, false)
	runProcessorTest(t, "处理消息成功", createTestMessage(validEventBytes), metaReader, extender, filter, dispatcher, false)

	// 消息解析失败
	metaReader, extender, filter, dispatcher = createTestMocks(false, false, false, false, false)
	runProcessorTest(t, "消息解析失败", createTestMessage(invalidEventBytes), metaReader, extender, filter, dispatcher, true)

	// 消息内容为空
	metaReader, extender, filter, dispatcher = createTestMocks(false, false, false, false, false)
	runProcessorTest(t, "消息内容为空", createTestMessage(emptyEventBytes), metaReader, extender, filter, dispatcher, false)

	// 没有订阅者
	metaReader, extender, filter, dispatcher = createTestMocks(false, false, false, false, false)
	runProcessorTest(t, "没有订阅者", createTestMessage(validEventBytes), metaReader, extender, filter, dispatcher, false)

	// 扩展事件失败
	metaReader, extender, filter, dispatcher = createTestMocks(true, true, false, false, false)
	runProcessorTest(t, "扩展事件失败", createTestMessage(validEventBytes), metaReader, extender, filter, dispatcher, true)

	// 过滤事件失败
	metaReader, extender, filter, dispatcher = createTestMocks(true, false, true, false, false)
	runProcessorTest(t, "过滤事件失败", createTestMessage(validEventBytes), metaReader, extender, filter, dispatcher, true)

	// 发布事件失败
	metaReader, extender, filter, dispatcher = createTestMocks(true, false, false, true, false)
	runProcessorTest(t, "发布事件失败", createTestMessage(validEventBytes), metaReader, extender, filter, dispatcher, true)

	// 过滤后没有匹配的订阅方
	metaReader, extender, filter, dispatcher = createTestMocks(true, false, false, false, true)
	runProcessorTest(t, "过滤后没有匹配的订阅方", createTestMessage(validEventBytes), metaReader, extender, filter, dispatcher, false)
}

// testEventBridgeProcessor 用于测试的处理器，复制原始ProcessEventMessage逻辑但使用mock组件
type testEventBridgeProcessor struct {
	metaReader toolkitrepo.EventMetaReader
	extender   *mockExtender
	filter     *mockFilter
	dispatcher *mockDispatcher
	bridgeID   int64
}

func (p *testEventBridgeProcessor) ProcessEventMessage(ctx context.Context, msg *sarama.ConsumerMessage) error {
	// 记录处理开始时间，用于计算总耗时
	startTime := time.Now()
	defer func() {
		// 这里使用defer是为了复制原始代码的逻辑
		_ = time.Since(startTime).Milliseconds()
	}()

	// 1. 解析原始消息
	var event common_event.ProducerEvent
	if err := proto.Unmarshal(msg.Value, &event); err != nil {
		return err
	}
	if event.Event == nil {
		return nil
	}

	// 2. 获取该事件类型关联的所有订阅规则
	subscribers := p.metaReader.GetEventSubscribes(ctx, p.bridgeID, event.Event.Type)
	if len(subscribers) == 0 {
		return nil
	}

	// 提取所有订阅规则
	var eventRules []*metadata_access.EventSubscribeRule
	for _, subscriber := range subscribers {
		if subscriber == nil {
			continue
		}
		if rule, exists := subscriber.Rules[event.Event.Type]; exists && rule != nil {
			eventRules = append(eventRules, rule)
		}
	}
	if len(eventRules) == 0 {
		return nil
	}

	// 3. 调用Extender.ExtendEventData完成字段扩展
	processorEvent := common_event.ProcessorEvent{
		Event: event.Event,
	}
	err := p.extender.ExtendEventData(ctx, p.bridgeID, event.Event.Type, &processorEvent)
	if err != nil {
		return err
	}

	// 4. 调用Filter.ProcessSubscriberEvents完成过滤和裁剪
	filteredEvents, err := p.filter.ProcessSubscriberEvents(ctx, &processorEvent, eventRules)
	if err != nil {
		return err
	}

	if len(filteredEvents) == 0 {
		return nil
	}

	// 5. 调用Dispatcher.PublishEvents将数据发送到订阅方队列
	err = p.dispatcher.PublishEvents(ctx, filteredEvents)
	if err != nil {
		return err
	}

	return nil
}

// 测试bridgeID设置
func TestEventBridgeProcessor_BridgeID(t *testing.T) {
	processor := &EventBridgeProcessor{}
	expectedBridgeID := int64(12345)

	processor.bridgeID = expectedBridgeID

	if processor.bridgeID != expectedBridgeID {
		t.Errorf("bridgeID = %v, want %v", processor.bridgeID, expectedBridgeID)
	}
}

// 辅助函数：创建有效的ProducerEvent字节数组
func createValidProducerEventBytes(t *testing.T, eventID, eventType string) []byte {
	event := &common_event.ProducerEvent{
		Event: &common_event.BaseEvent{
			Id:        eventID,
			Type:      eventType,
			Subject:   "test_subject",
			BridgeId:  12345,
			Timestamp: time.Now().UnixMilli(),
			SourceId:  1,
			Data:      `{"test": "data"}`,
		},
	}

	data, err := proto.Marshal(event)
	if err != nil {
		t.Fatalf("创建测试ProducerEvent失败: %v", err)
	}
	return data
}

// 辅助函数：创建空的ProducerEvent字节数组
func createEmptyProducerEventBytes(t *testing.T) []byte {
	event := &common_event.ProducerEvent{
		Event: nil,
	}

	data, err := proto.Marshal(event)
	if err != nil {
		t.Fatalf("创建空测试ProducerEvent失败: %v", err)
	}
	return data
}
