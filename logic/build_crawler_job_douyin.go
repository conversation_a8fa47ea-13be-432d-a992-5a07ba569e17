package logic

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

// HandleBuildDouYinSearchJob 生成抖音搜索的job（根据重点剧抓取池中的数据）,定时触发（在重点剧抓取池构造之后）
func HandleBuildDouYinSearchJob(ctx context.Context, _, _ string, _ int32) error {
	log.InfoContextf(ctx, "HandleBuildDouYinSearchJob Enter")
	// 先清空当前的任务池（这是抓取源头任务池，要保持干净）
	for _, orderID := range []int{model.DouYinCompetitorSearchOrder} {
		var c dao.CrawJob
		c.OrderID = orderID
		if err := c.CleanCrawJobs(ctx, time.Now().AddDate(-1, 0, 0).Format("2006-01-02")); err != nil {
			return err
		}
	}

	// 竞品排播IP抓取任务（抖音只需要排播的数据）
	var crawInfos []*dao.CrawJob
	competitorCoverInfos, err := dao.GetAllPlanInfo(ctx, []string{"电视剧", "综艺"}) // 数据过滤逻辑都在GetAllPlanInfo中
	if err != nil {
		log.ErrorContextf(ctx, "GetAllPlanInfo ERR:%+v", err)
		return err
	}
	competitorCrawInfos, err := generateDoyinSearchCrawJobs(competitorCoverInfos, model.DouYinCompetitorSearchOrder)
	if err != nil {
		return err
	}
	crawInfos = append(crawInfos, competitorCrawInfos...)

	for _, info := range crawInfos {
		if err = info.InsertCrawJob(ctx); err != nil {
			log.ErrorContextf(ctx, "InsertCrawJob ERR:%+v", err)
			return err
		}
	}
	log.InfoContextf(ctx, "HandleBuildDouYinSearchJob Suc")
	return nil
}

// HandleBuildDouYinDetailJob 生成抖音IP详情抓取的job（根据抖音搜索结果数据）
func HandleBuildDouYinDetailJob(ctx context.Context, _, _ string, _ int32) error {
	for _, orderID := range []int{model.DouYinCompetitorIPDetailOrder} {
		if err := buildDouYinDetailJob(ctx, orderID); err != nil {
			log.ErrorContextf(ctx, "buildDouYinDetailJob ERR:%+v", err)
			return err
		}
	}
	return nil
}

func generateDoyinSearchCrawJobs(coverInfos []*dao.CoverInfo, orderID int) ([]*dao.CrawJob, error) {
	var crawJobs []*dao.CrawJob
	for _, info := range mergeDupCoverInfo(coverInfos) {
		jobArguments := map[string]interface{}{
			"query": info.Title,
		}
		bs, _ := json.Marshal(jobArguments)
		crawJobs = append(crawJobs, &dao.CrawJob{
			OrderID:      orderID,
			Platform:     "douyin",
			TransmitKey:  genTransmit(0, 0, orderID, info.CoverID),
			Title:        info.Title,
			JobArguments: string(bs),
			Valid:        1,
			Ext:          info.Ext,
		})
	}
	return crawJobs, nil
}

func buildDouYinDetailJob(ctx context.Context, detailOrderID int) error {
	log.InfoContextf(ctx, "buildDouYinDetailJob Enter: %d", detailOrderID)
	// 先清空 详情抓取任务表（由于抓取范围不同，每天要清空一次）
	var c dao.CrawJob
	c.OrderID = detailOrderID
	if err := c.CleanCrawJobs(ctx, time.Now().AddDate(-1, 0, 0).Format("2006-01-02")); err != nil {
		return err
	}

	// 扫描 搜索job表（此表即为抖音项目要处理的数据）
	c.OrderID = transDetail2SearchOrderID(detailOrderID)                      // 从 搜索job表（即抓取源头）中获取数据
	douyinJobs, err := c.GetAllCrawJobs(ctx, time.Now().Format("2006-01-02")) // 拉取今天所有需要抓取的数据
	if err != nil {
		return err
	}

	// 生成当天详情抓取任务(根据搜索结果)
	for _, job := range douyinJobs {
		// 根据剧集标题 生成 详情抓取任务
		crawJobs, err := generateDouYinDetailCrawJob(ctx, detailOrderID, job.Title, job.Platform)
		if err != nil {
			log.ErrorContextf(ctx, "generateMaoyanCrawJobByTitle ERR:%+v", err)
			return err
		}

		// 写入抖音详情抓取表
		for _, crawJob := range crawJobs {
			if err = crawJob.InsertCrawJob(ctx); err != nil {
				return err
			}
		}
	}
	return nil
}

// DouyinSearchCrawRet 抖音搜索抓取返回结果
type DouyinSearchCrawRet struct {
	OriCrawID int    // 触发抓取时的抓取ID（标识一次抓取任务）
	TopicID   string `json:"topic_id"`   // ID
	TopicName string `json:"topic_name"` // 剧集名
	URL       string `json:"topic_url"`  // 跳转链接
	Rank      int    `json:"rank"`       // 排名
	Ext       string // 扩展信息
}

// generateDouYinDetailCrawJob 生成抖音详情抓取任务
func generateDouYinDetailCrawJob(ctx context.Context, orderID int, crawTitle, platform string) ([]*dao.CrawJob, error) {
	log.InfoContextf(ctx, "generateDouYinDetailCrawJob enter:%d-%s", orderID, crawTitle)
	var c dao.CrawDetail
	c.OrderID = transDetail2SearchOrderID(orderID) // 从搜索结果表中获取数据
	douyinSearchDetail, err := c.GetCrawDetailByTitle(ctx, crawTitle)
	if err != nil {
		return nil, err
	}

	// 取搜索返回剧集名与原剧集名相同，且搜索返回频道与原剧集频道相同的
	var douyinInfos []*dao.CrawJob
	mapDouyinTopic := make(map[string]bool)
	for _, info := range douyinSearchDetail {
		var douyinSearchDetail DouyinSearchCrawRet
		if err = json.Unmarshal([]byte(info.Detail), &douyinSearchDetail); err != nil {
			log.ErrorContextf(ctx, "Unmarshal err-%+v", err)
			continue
		}
		if douyinSearchDetail.Rank > 10 { // 取排名Top10的
			continue
		}
		if _, exists := mapDouyinTopic[douyinSearchDetail.TopicID]; exists { // 对topicID进行去重
			continue
		}
		mapDouyinTopic[douyinSearchDetail.TopicID] = true

		douyinSearchDetail.OriCrawID = info.ID
		douyinSearchDetail.Ext = info.Ext
		douyinInfos = append(douyinInfos, genDouYinDetailJobs(orderID, &douyinSearchDetail, platform))
	}
	return douyinInfos, nil
}

type jobArgs struct {
	TopicID   string `json:"topic_id"`
	TopicName string `json:"topic_name"`
}

func genDouYinDetailJobs(orderID int, douyinSearchDetail *DouyinSearchCrawRet, platform string) *dao.CrawJob {
	if douyinSearchDetail == nil {
		return nil
	}
	orderID = transSearch2DetailOrderID(orderID) // 确保是详情抓取订单

	bs, _ := json.Marshal(&jobArgs{
		TopicID:   douyinSearchDetail.TopicID,
		TopicName: douyinSearchDetail.TopicName,
	})
	return &dao.CrawJob{
		OrderID:  orderID,
		Platform: platform,
		TransmitKey: genTransmit(0, douyinSearchDetail.OriCrawID, orderID,
			time.Now().Format("20060102150405")),
		Title:        douyinSearchDetail.TopicName,
		JobArguments: string(bs),
		Valid:        1,
		Ext:          douyinSearchDetail.Ext,
	}
}
