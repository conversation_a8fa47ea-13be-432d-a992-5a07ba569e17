package logic

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/trpcprotocol/media_event_hub/common_errcode"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"
	"git.woa.com/trpcprotocol/media_event_hub/metadata_access"
	"git.woa.com/video_media/media_event_hub/toolkit/repo"
	toolkitrepo "git.woa.com/video_media/media_event_hub/toolkit/repo"

	"github.com/apache/pulsar-client-go/pulsar"
	"google.golang.org/protobuf/proto"
)

// Dispatcher 事件分发器，负责将过滤后的事件发送到订阅者队列
type Dispatcher struct {
	metaReader repo.EventMetaReader // 元数据读取器
	producer   EventProducer        // 事件生产者
}

// NewDispatcher 创建新的事件分发器
func NewDispatcher(producer EventProducer) (*Dispatcher, error) {
	// 初始化元数据读取器
	metaReader, err := toolkitrepo.NewEventMetaReader()
	if err != nil {
		return nil, err
	}
	return &Dispatcher{
		metaReader: metaReader,
		producer:   producer,
	}, nil
}

// PublishEvents 将事件发布到订阅者队列
// 参数：subscriberEvents 为订阅者ID到事件的映射
func (d *Dispatcher) PublishEvents(ctx context.Context, subscriberEvents map[int64]*common_event.ProcessorEvent) error {
	if len(subscriberEvents) == 0 {
		return nil
	}

	// 记录开始时间，用于计算耗时
	startTime := time.Now()
	defer func() {
		log.DebugContextf(ctx, "PublishEvents cost time: %v ms, subscriber count: %d",
			time.Since(startTime).Milliseconds(), len(subscriberEvents))
	}()

	// 准备并发任务
	tasks := make([]func() error, 0, len(subscriberEvents))
	for subscriberID, event := range subscriberEvents {
		if event == nil {
			continue
		}

		subID := subscriberID
		evt := event
		tasks = append(tasks, func() error {
			// 1. 获取订阅者的队列信息
			subscribe := d.metaReader.GetEventSubscribeByID(ctx, subID)
			if subscribe == nil {
				return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL),
					fmt.Sprintf("获取订阅者信息失败, subscriberID: %d", subID))
			}

			// 检查订阅者是否有Topic配置
			if subscribe.ProcessorTopic == nil {
				return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL),
					fmt.Sprintf("订阅者未配置Topic, subscriberID: %d", subID))
			}

			// 2. 发送事件到订阅者队列
			err := d.producer.Send(ctx, subscribe.ProcessorTopic, evt)
			if err != nil {
				log.ErrorContextf(ctx, "发送事件到订阅者队列失败, subscriberID: %d, topic: %s, eventID: %s, error: %v",
					subID, subscribe.ProcessorTopic.Topic.TopicName, evt.Event.Id, err)
				return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL),
					fmt.Sprintf("发送事件到订阅者队列失败, subscriberID: %d, error: %v", subID, err))
			}

			log.DebugContextf(ctx, "发送事件到订阅者队列成功, subscriberID: %d, eventID: %s",
				subID, evt.Event.Id)
			return nil
		})
	}

	err := trpc.GoAndWait(tasks...)
	if err != nil {
		log.ErrorContextf(ctx, "事件发布过程中发生错误: %v", err)
		return err
	}
	return nil
}

// EventProducer 事件生产者接口
type EventProducer interface {
	// Send 发送事件到指定队列
	Send(ctx context.Context, topic *metadata_access.Producer, event *common_event.ProcessorEvent) error
}

// PulsarSender 实现EventProducer接口，负责向Pulsar发送消息
type PulsarSender struct {
	// 生产者映射，key为TopicName
	producers map[string]pulsar.Producer
	// 客户端映射，key为TopicName
	clients map[string]pulsar.Client
	// 用于保护producers和clients的锁
	lock sync.RWMutex
}

// NewPulsarSender 创建新的Pulsar消息发送器
func NewPulsarSender() *PulsarSender {
	return &PulsarSender{
		producers: make(map[string]pulsar.Producer),
		clients:   make(map[string]pulsar.Client),
		lock:      sync.RWMutex{},
	}
}

// Send 发送事件到指定队列
func (s *PulsarSender) Send(ctx context.Context, topic *metadata_access.Producer, event *common_event.ProcessorEvent) error {
	if topic == nil || topic.Topic == nil {
		return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL), "topic配置为空")
	}

	// 根据topic信息获取或创建生产者
	producer, err := s.getOrCreateProducer(ctx, topic.Topic)
	if err != nil {
		return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL),
			fmt.Sprintf("获取Pulsar生产者失败: %v", err))
	}

	// 直接序列化为Protobuf二进制格式
	eventData, err := proto.Marshal(event)
	if err != nil {
		return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL),
			fmt.Sprintf("序列化事件失败: %v", err))
	}

	// 发送消息
	msg := &pulsar.ProducerMessage{
		Key:     event.Event.Subject,
		Payload: eventData,
	}
	_, err = producer.Send(ctx, msg)
	if err != nil {
		return errs.New(int(common_errcode.ErrCode_TARGET_METADATA_ILLEGAL),
			fmt.Sprintf("发送消息失败: %v", err))
	}
	log.InfoContextf(ctx, "消息发送成功, Topic: %s, EventID: %s; Subject: %s",
		topic.Topic.TopicName, event.Event.Id, event.Event.Subject)
	return nil
}

// getOrCreateProducer 根据topic信息获取或创建Pulsar生产者
func (s *PulsarSender) getOrCreateProducer(ctx context.Context, topic *metadata_access.Topic) (pulsar.Producer, error) {
	if topic == nil {
		return nil, fmt.Errorf("topic参数为空")
	}

	// 使用TopicName作为缓存的key
	key := topic.TopicName
	s.lock.RLock()
	if producer, ok := s.producers[key]; ok {
		s.lock.RUnlock()
		return producer, nil
	}
	s.lock.RUnlock()

	// 没有找到，需要创建新的生产者
	s.lock.Lock()
	defer s.lock.Unlock()

	// 双重检查，防止其他协程已经创建
	if producer, ok := s.producers[key]; ok {
		return producer, nil
	}

	// 创建Pulsar客户端
	clientOpts := pulsar.ClientOptions{
		URL:               topic.Addr,
		OperationTimeout:  30 * time.Second,
		ConnectionTimeout: 30 * time.Second,
		Authentication:    pulsar.NewAuthenticationToken(topic.AuthParams),
	}
	client, err := pulsar.NewClient(clientOpts)
	if err != nil {
		log.ErrorContextf(ctx, "pulsar.NewClient: %v", err)
		return nil, fmt.Errorf("创建Pulsar客户端失败: %v", err)
	}

	// 创建生产者
	producerOpts := pulsar.ProducerOptions{
		Topic:           topic.TopicName,
		SendTimeout:     30 * time.Second,
		DisableBatching: false,
	}
	producer, err := client.CreateProducer(producerOpts)
	if err != nil {
		client.Close()
		log.ErrorContextf(ctx, "client.CreateProducer: %v", err)
		return nil, fmt.Errorf("创建Pulsar生产者失败: %v", err)
	}

	// 缓存创建的客户端和生产者
	s.clients[key] = client
	s.producers[key] = producer
	return producer, nil
}

// Close 关闭所有Pulsar客户端和生产者
func (s *PulsarSender) Close() {
	s.lock.Lock()
	defer s.lock.Unlock()

	for _, producer := range s.producers {
		producer.Close()
	}
	for _, client := range s.clients {
		client.Close()
	}
	s.producers = make(map[string]pulsar.Producer)
	s.clients = make(map[string]pulsar.Client)
}
