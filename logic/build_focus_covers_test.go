package logic

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	"github.com/agiledragon/gomonkey/v2"
)

func TestHandleCoverMsg(t *testing.T) {
	patches := gomonkey.ApplyFunc(updateFocusCoversPool, func(ctx context.Context, c dao.CoverInfo) error {
		fmt.Printf("updateFocusCoversPool enter!!!!")
		return nil
	})
	defer patches.Reset()

	type args struct {
		w http.ResponseWriter
		r *http.Request
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				w: httptest.NewRecorder(),
				r: httptest.NewRequest(http.MethodPost, "/",
					strings.NewReader(`{"id": "123", 
						"fieldInfos": {
							"type": { "id":80001, "value":"1" },
							"title": { "id":80002, "value":"test" }, 
							"ip_online_status": { "id":80003, "value":"1" }}
						}`,
					)),
			},
		},
		{
			name: "case2",
			args: args{
				w: httptest.NewRecorder(),
				r: httptest.NewRequest(http.MethodPost, "/",
					strings.NewReader(`{"id": "123", 
						"fieldInfos": {
							"type": { "id":80001, "value":"1" },
							"title": { "id":80002, "value":"test" },
							"cinema_flag": { "id":80005, "value":"92972" },
							"hot_level": { "id":80006, "value":"1525777" },
							"ip_online_status": { "id":80003, "value":"1" }}
						}`,
					)),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := HandleCoverMsg(tt.args.w, tt.args.r); (err != nil) != tt.wantErr {
				t.Errorf("HandleCoverMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_calcCrawFlag(t *testing.T) {
	type args struct {
		m *msghub.MediaInfo
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		want1 bool
	}{
		{
			name: "case2",
			args: args{
				m: &msghub.MediaInfo{
					FieldInfos: map[string]msghub.FieldInfo{
						"type": {
							Id:    80001,
							Value: "1",
						},
						"hot_level": {
							Id:    80001,
							Value: "224426",
						},
						"cinema_flag": {
							Id:    80001,
							Value: "92972",
						},
					},
				},
			},
			want:  true,
			want1: false,
		},
		{
			name: "case3",
			args: args{
				m: &msghub.MediaInfo{
					FieldInfos: map[string]msghub.FieldInfo{
						"type": {
							Id:    80001,
							Value: "2",
						},
						"hot_level": {
							Id:    80001,
							Value: "224426",
						},
						"category_value": {
							Id:    80001,
							Value: "10890",
						},
					},
				},
			},
			want:  true,
			want1: true,
		},
		{
			name: "case4",
			args: args{
				m: &msghub.MediaInfo{
					FieldInfos: map[string]msghub.FieldInfo{
						"type": {
							Id:    80001,
							Value: "9",
						},
						"hot_level": {
							Id:    80001,
							Value: "224426",
						},
						"nature_of_the_content_id": {
							Id:    80001,
							Value: "1585431",
						},
					},
				},
			},
			want:  true,
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := calcCrawFlag(tt.args.m)
			if got != tt.want {
				t.Errorf("calcCrawFlag() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("calcCrawFlag() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_updateFocusCoversPool(t *testing.T) {
	var coverInfo *dao.CoverInfo
	gomonkey.ApplyMethod(reflect.TypeOf(coverInfo), "IsExistFocusCoverLoop",
		func(_ *dao.CoverInfo, ctx context.Context) (bool, error) {
			return true, nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(coverInfo), "InsertCoverInfo",
		func(_ *dao.CoverInfo, ctx context.Context) error {
			return nil
		})

	type args struct {
		ctx context.Context
		c   dao.CoverInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				ctx: trpc.BackgroundContext(),
				c: dao.CoverInfo{
					CoverID: "z6twjp44in088fe",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := updateFocusCoversPool(tt.args.ctx, tt.args.c); (err != nil) != tt.wantErr {
				t.Errorf("updateFocusCoversPool() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
