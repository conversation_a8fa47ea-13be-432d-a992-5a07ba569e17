package main

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	access "git.code.oa.com/trpcprotocol/storage_service/access_layer"
	adaptor "git.code.oa.com/trpcprotocol/storage_service/adaptor_layer"
	protocol "git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/storage_service/access_layer/config"
	"git.code.oa.com/video_media/storage_service/access_layer/util"
	"git.code.oa.com/video_media/storage_service/access_layer/util/errs"
	"git.code.oa.com/video_media/storage_service/access_layer/util/getter"
	"git.code.oa.com/video_media/storage_service/access_layer/util/report"
	cache "git.code.oa.com/video_media/storage_service/config_cache"

	"github.com/samber/lo"
)

// newRetInfo 初始化返回响应信息，避免panic
func newRetInfo() *protocol.CommRetInfo {
	return &protocol.CommRetInfo{
		ErrCode:  protocol.EnumMediaErrorCode_RetSuccess,
		FailList: make(map[string]protocol.EnumMediaErrorCode),
	}
}

// BatchGetMediaInfo 批量查询媒资数据接口
func (s *mediaInterfaceServiceImpl) BatchGetMediaInfo(ctx context.Context, req *access.BatchGetRequest,
	rsp *access.BatchGetResponse,
) (err error) {
	authInfo := req.GetAuthInfo()
	reporter := report.New(ctx, "BatchGetMediaInfo", authInfo.GetAppId(), strings.Join(req.GetId(), ","),
		req.GetDataSetID())
	defer func() {
		reporter.DoReport(err != nil, report.GetReqEnd, errs.Code(err), rsp.String())
	}()
	reporter.ReportFields(req.GetFieldNames()).DoReport(true, report.GetReqBegin, protocol.EnumMediaErrorCode(0),
		fmt.Sprintf("%+v", req.GetFieldNames()))

	// appID鉴权&频控
	if err = util.CheckRequest(authInfo.GetAppId(), authInfo.GetAppKey(), req.GetDataSetID()); err != nil {
		return
	}

	// 整理可读字段并按照数据源分类请求
	adaptorReps, failList, err := util.ClassifyGetReqBySourceID(ctx, authInfo.GetAppId(), req.GetDataSetID(),
		req.GetDataSourceID(), req.GetId(), req.GetFieldNames())
	if err != nil {
		return err
	}

	// 发送请求到数据适配层
	getIns := getter.New(authInfo.GetPriority(), req.GetDataSetID(), req.GetDataSourceID())
	if mErr := getIns.SendGetRequests(ctx, adaptorReps); mErr != nil {
		rsp.ErrCode = protocol.EnumMediaErrorCode_RetCallAdaptorErr
		rsp.ErrMsg = mErr.Error()
	}
	rsp.FieldInfos, rsp.FailList = getIns.MergeBatchGetRsps(req.GetId(), len(req.GetFieldNames()), failList)
	return
}

// GetMediaInfo 获取字段信息接口
func (s *mediaInterfaceServiceImpl) GetMediaInfo(ctx context.Context, req *access.MediaGetRequest,
	rsp *access.MediaGetResponse,
) (err error) {
	rsp.RetInfo, rsp.FieldInfos = newRetInfo(), make(map[string]*protocol.FieldInfo)
	authInfo := req.GetAuthInfo()
	reporter := report.New(ctx, "GetMediaInfo", authInfo.GetAppId(), req.GetId(), req.GetDataSetId())
	defer func() {
		reporter.DoReport(err != nil, report.GetReqEnd, errs.Code(err), rsp.String())
	}()
	reporter.ReportFields(req.GetFieldNames()).DoReport(true, report.GetReqBegin, protocol.EnumMediaErrorCode(0),
		fmt.Sprintf("%+v", req.GetFieldNames()))

	// appID鉴权&频控
	if err = util.CheckRequest(authInfo.GetAppId(), authInfo.GetAppKey(), req.GetDataSetId()); err != nil {
		return
	}

	// 整理可读字段并按照数据源分类请求
	rspRet := rsp.GetRetInfo()
	var adaptorReps []*adaptor.BatchGetFieldsRequest
	adaptorReps, rspRet.FailList, err = util.ClassifyGetReqBySourceID(ctx, authInfo.GetAppId(), req.GetDataSetId(),
		req.GetDataSourceId(), []string{req.GetId()}, req.GetFieldNames())
	if err != nil {
		return err
	}

	// 发送请求到数据适配层
	getIns := getter.New(authInfo.GetPriority(), req.GetDataSetId(), req.GetDataSourceId())
	if mErr := getIns.SendGetRequests(ctx, adaptorReps); mErr != nil {
		rspRet.ErrCode = protocol.EnumMediaErrorCode_RetCallAdaptorErr
		rspRet.ErrMsg = mErr.Error()
	}
	rsp.FieldInfos = getIns.MergeGetRsps(req.GetId(), rspRet.GetFailList())
	return
}

// GetAllMediaInfo 获取全部字段信息接口
func (s *mediaInterfaceServiceImpl) GetAllMediaInfo(ctx context.Context, req *access.MediaGetAllRequest,
	rsp *access.MediaGetAllResponse,
) (err error) {
	rsp.RetInfo, rsp.FieldInfos = newRetInfo(), make(map[string]*protocol.FieldInfo)
	authInfo := req.GetAuthInfo()
	reporter := report.New(ctx, "GetAllMediaInfo", authInfo.GetAppId(), req.GetId(), req.GetDataSetId())
	defer func() {
		errCode := rsp.GetRetInfo().GetErrCode()
		if err != nil {
			errCode = errs.Code(err)
		}
		reporter.DoReport(errCode != protocol.EnumMediaErrorCode_RetSuccess, report.GetReqEnd, errCode, rsp.String())
	}()
	reporter.DoReport(true, report.GetReqBegin, protocol.EnumMediaErrorCode(0), "GetAllMediaInfo")
	// appID鉴权&频控
	if err = util.CheckRequest(authInfo.GetAppId(), authInfo.GetAppKey(),
		req.GetDataSetId()); err != nil {
		return
	}

	// 整理可读字段并按照数据源分类请求
	rspRet := rsp.GetRetInfo()
	adaptorReps, err := util.ClassifyGetAllReqBySourceID(ctx, req, rspRet.GetFailList())
	if err != nil {
		return err
	}

	if len(req.GetDataSourceIds()) > 0 {
		// 指定getAll的数据源范围时需要过滤掉多余的数据源请求
		adaptorReps = util.FilterAdaptorReqs(req.GetDataSourceIds(), adaptorReps)
	}

	// 发送请求到数据适配层
	const showDel = 1
	getIns := getter.New(authInfo.GetPriority(), req.GetDataSetId(), 0).SetShowDel(req.GetShowDel() == showDel)
	if mErr := getIns.SendGetRequests(ctx, adaptorReps); mErr != nil {
		rspRet.ErrCode = protocol.EnumMediaErrorCode_RetCallAdaptorErr
		rspRet.ErrMsg = mErr.Error()
	}
	rsp.FieldInfos = getIns.MergeGetRsps(req.GetId(), rspRet.GetFailList())
	return
}

// UpdateMediaInfo 更新字段信息接口
func (s *mediaInterfaceServiceImpl) UpdateMediaInfo(ctx context.Context, req *access.MediaUpdateRequest,
	rsp *access.MediaUpdateResponse,
) (err error) {
	rsp.RetInfo = newRetInfo()
	authInfo := req.GetAuthInfo()
	reporter := report.New(ctx, "UpdateMediaInfo", authInfo.GetAppId(), req.GetId(), req.GetDataSetId())
	defer func() {
		reporter.DoReportFailList(errs.Code(err), rsp.GetRetInfo())
	}()
	reporter.ReportFields(lo.Map(req.UpdateFieldInfos, func(item *protocol.UpdateFieldInfo, index int) string {
		return item.GetFieldInfo().GetFieldName()
	})).DoReport(true, report.UpdateReqBegin, protocol.EnumMediaErrorCode(0), req.String())

	// appID鉴权&频控
	if err = util.CheckRequest(authInfo.GetAppId(), authInfo.GetAppKey(), req.GetDataSetId()); err != nil {
		return
	}

	// 检查数据集id是否合法
	tenantID := config.GetTenantID(ctx)
	dataSetInfo := cache.GetDataSet(req.GetDataSetId(), tenantID)
	if dataSetInfo == nil {
		return errs.New(protocol.EnumMediaErrorCode_RetInvalidDataSet)
	}

	// 整理可写字段并按照数据源分类请求
	adaptorReqs := make(map[int32]*adaptor.SetFieldInfosRequest) // 数据适配层请求集合
	err = util.ClassifyUpdateReqBySourceID(ctx, adaptorReqs, req, tenantID, rsp.GetRetInfo().GetFailList())
	if err != nil {
		return
	}

	util.AddBaseInfoByDataSetID(req.GetId(), dataSetInfo, adaptorReqs)
	// 并发发送数据适配层请求并合并结果
	util.SendAndRevAdaptorSetReq(ctx, req, rsp.GetRetInfo(), &util.UpdateReqData{
		Priority:    authInfo.GetPriority(),
		CallID:      reporter.CallID,
		ClientIP:    reporter.ClientIP,
		SetName:     config.GetSetName(),
		TopicName:   dataSetInfo.Topic,
		AdaptorReqs: adaptorReqs,
		BaseInfo:    make(map[string]*protocol.FieldInfo),
	})
	return
}

// ProcIncludeAndCheckSort 处理includes+检查排序字段类型
// 不传include,如果权限是*,忽略includes, 如果权限不是*,把有权限字段透传
// 传了includes, 会检查所有字段权限，有一个没有就报错
// 对sort字段,如果没字段配置或字段安配置是文本,则报错
func ProcIncludeAndCheckSort(req *access.MediaSearchRequest) ([]string, error) {
	if len(req.Includes) == 0 {
		return nil, errors.New("no includes")
	}
	if req.Includes[0] == "*" {
		return nil, nil
	}
	for _, sort := range req.GetSort() {
		fieldInfoMap := cache.GetFieldInfoByName(sort.GetFieldName(), req.GetDataSetID())
		if fieldInfoMap == nil {
			return nil, errors.New(sort.GetFieldName() + " not find config")
		}
		if fieldInfoMap.DataType == 1 { // 文本类型不能排序
			return nil, errors.New(sort.GetFieldName() + " is text,not support sort")
		}
	}
	return req.Includes, nil
}

func (s *mediaInterfaceServiceImpl) SearchMediaInfo(ctx context.Context,
	req *access.MediaSearchRequest, rsp *access.MediaSearchResponse,
) error {
	rsp.RetInfo = newRetInfo()
	reporter := report.New(ctx, "SearchMediaInfo", req.AuthInfo.GetAppId(),
		req.GetOperatorName(), req.GetDataSetID())
	var err error
	defer func() {
		if err != nil {
			rsp.RetInfo.ErrMsg = err.Error()
		}
		reporter.DoReport(true, report.SearchReqEnd, rsp.GetRetInfo().ErrCode, rsp.String())
		log.Debugf("Searching media info res:%v", err)
	}()
	reporter.DoReport(true, report.SearchReqBegin, protocol.EnumMediaErrorCode(0), req.String())
	// 检查数据集id是否合法
	tenantID := config.GetTenantID(ctx)
	dataSetInfo := cache.GetDataSet(req.GetDataSetID(), tenantID)
	if dataSetInfo == nil {
		return errs.New(protocol.EnumMediaErrorCode_RetInvalidDataSet)
	}
	if dataSetInfo.SearchDataSourceID == "" {
		return errs.New(protocol.EnumMediaErrorCode_RetInvalidDataSet)
	}
	iDataSource, err := strconv.Atoi(dataSetInfo.SearchDataSourceID)
	if err != nil {
		return errs.New(protocol.EnumMediaErrorCode_RetInvalidDataSet)
	}
	includes, err := ProcIncludeAndCheckSort(req)
	if err != nil {
		return errs.New(protocol.EnumMediaErrorCode_RetNoPermission)
	}
	adaptorReq := &adaptor.SearchMediaInfosRequest{
		DataSourceID: int32(iDataSource),
		DataSetID:    req.GetDataSetID(),
		PageInfo:     req.GetPageInfo(),
		CondGroups:   req.GetCondGroups(),
		Logical:      req.GetLogical(),
		Sort:         req.GetSort(),
		OperatorName: req.GetOperatorName(),
		ExtInfo:      req.GetExtInfo(),
		ExprCond:     req.GetExprCond(),
	}
	log.Debugf("Searching media req:%v", adaptorReq)
	if includes != nil && len(includes) > 0 {
		adaptorReq.Includes = includes
	}
	proxy := adaptor.NewDataAdaptorClientProxy(util.AdaptorLayerOptions(int32(iDataSource),
		trpc.GlobalConfig().Global.FullSetName)...)
	adaptorRsp, err := proxy.SearchMediaInfos(ctx, adaptorReq)
	if err != nil {
		log.Errorf("SearchMediaInfosErr:%v", err)
		return errs.New(protocol.EnumMediaErrorCode_RetCallAdaptorErr)
	}
	rsp.Total = adaptorRsp.GetTotal()
	rsp.Docs = adaptorRsp.GetDocs()
	return nil
}
