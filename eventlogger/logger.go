package eventlogger

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"go.opentelemetry.io/otel/trace"

	"git.woa.com/galileo/trpc-go-galileo/logs"
	"git.woa.com/trpcprotocol/media_event_hub/common_event"
)

// EventLogger 事件日志记录器
type EventLogger struct {
	logger log.Logger
	ctx    context.Context
}

// NewEventLogger 从事件中创建带有traceID的日志记录器
func NewEventLogger(ctx context.Context, event *common_event.BaseEvent) *EventLogger {
	// 如果事件为空或者没有traceID，返回默认logger
	if event == nil || event.Trace == "" {
		return &EventLogger{
			logger: log.GetDefaultLogger(),
			ctx:    ctx,
		}
	}

	// 使用事件的traceID创建新的span context
	sc := trace.SpanContextFromContext(ctx)
	traceIDBytes, err := trace.TraceIDFromHex(event.Trace)
	if err != nil {
		// 如果traceID格式不正确，返回默认logger
		return &EventLogger{
			logger: log.GetDefaultLogger(),
			ctx:    ctx,
		}
	}

	// 创建带有事件traceID的span context
	scTmp := sc.WithTraceID(traceIDBytes)
	traceLog := logs.WithSpanContext(log.GetDefaultLogger(), ctx, scTmp)

	return &EventLogger{
		logger: traceLog,
		ctx:    ctx,
	}
}

// GetLogger 获取Logger实例
func (el *EventLogger) GetLogger() log.Logger {
	return el.logger
}

// GetContext 获取Context
func (el *EventLogger) GetContext() context.Context {
	return el.ctx
}

// Debug 记录Debug级别日志
func (el *EventLogger) Debug(args ...interface{}) {
	el.logger.Debug(args...)
}

// Debugf 记录Debug级别格式化日志
func (el *EventLogger) Debugf(template string, args ...interface{}) {
	el.logger.Debugf(template, args...)
}

// Info 记录Info级别日志
func (el *EventLogger) Info(args ...interface{}) {
	el.logger.Info(args...)
}

// Infof 记录Info级别格式化日志
func (el *EventLogger) Infof(template string, args ...interface{}) {
	el.logger.Infof(template, args...)
}

// Warn 记录Warn级别日志
func (el *EventLogger) Warn(args ...interface{}) {
	el.logger.Warn(args...)
}

// Warnf 记录Warn级别格式化日志
func (el *EventLogger) Warnf(template string, args ...interface{}) {
	el.logger.Warnf(template, args...)
}

// Error 记录Error级别日志
func (el *EventLogger) Error(args ...interface{}) {
	el.logger.Error(args...)
}

// Errorf 记录Error级别格式化日志
func (el *EventLogger) Errorf(template string, args ...interface{}) {
	el.logger.Errorf(template, args...)
}

// DebugContext 记录带上下文的Debug级别日志
func (el *EventLogger) DebugContext(ctx context.Context, args ...interface{}) {
	log.DebugContext(ctx, args...)
}

// DebugContextf 记录带上下文的Debug级别格式化日志
func (el *EventLogger) DebugContextf(ctx context.Context, template string, args ...interface{}) {
	log.DebugContextf(ctx, template, args...)
}

// InfoContext 记录带上下文的Info级别日志
func (el *EventLogger) InfoContext(ctx context.Context, args ...interface{}) {
	log.InfoContext(ctx, args...)
}

// InfoContextf 记录带上下文的Info级别格式化日志
func (el *EventLogger) InfoContextf(ctx context.Context, template string, args ...interface{}) {
	log.InfoContextf(ctx, template, args...)
}

// WarnContext 记录带上下文的Warn级别日志
func (el *EventLogger) WarnContext(ctx context.Context, args ...interface{}) {
	log.WarnContext(ctx, args...)
}

// WarnContextf 记录带上下文的Warn级别格式化日志
func (el *EventLogger) WarnContextf(ctx context.Context, template string, args ...interface{}) {
	log.WarnContextf(ctx, template, args...)
}

// ErrorContext 记录带上下文的Error级别日志
func (el *EventLogger) ErrorContext(ctx context.Context, args ...interface{}) {
	log.ErrorContext(ctx, args...)
}

// ErrorContextf 记录带上下文的Error级别格式化日志
func (el *EventLogger) ErrorContextf(ctx context.Context, template string, args ...interface{}) {
	log.ErrorContextf(ctx, template, args...)
}
