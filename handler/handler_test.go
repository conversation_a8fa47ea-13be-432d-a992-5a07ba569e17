package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"log"
	"net/http"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"

	"bou.ke/monkey"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/media_go_commlib/dataaccess"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

func Test_isValidType(t *testing.T) {
	type args struct {
		typeId int
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			"Test ValidType 2",
			args{typeId: 2},
			true,
		},
		{
			"Test ValidType 3",
			args{typeId: 3},
			true,
		},
		{
			"Test inValidType",
			args{typeId: 1},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isValidType(tt.args.typeId); got != tt.want {
				t.Errorf("isValidType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isValidTitle(t *testing.T) {
	type args struct {
		title string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			"Test title1 : xxx_num",
			args{title: "asdfasg测试_01"},
			true,
		},
		{
			"Test title2 : 番外num",
			args{title: "asdfasg番外1asf"},
			true,
		},
		{
			"Test title3 : xxx_num",
			args{title: "asdfasg测试_"},
			false,
		},
		{
			"Test title4 : xxx_num",
			args{title: "asdfasg测试_01asf"},
			false,
		},
		{
			"Test title5 : 番外num",
			args{title: "ag番外asf"},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isValidTitle(tt.args.title); got != tt.want {
				t.Errorf("isValidTitle() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSetStandardSeriesFlag(t *testing.T) {
	var dataAccess *dataaccess.DataAccess
	patch1 := monkey.PatchInstanceMethod(reflect.TypeOf(dataAccess), "DataUpdate",
		func(_ *dataaccess.DataAccess, ctx context.Context, CID string,
			fields []*common_storage_common.UpdateFieldInfo, operator string, opt ...client.Option) (int, error) {
			return 0, nil
		})
	defer patch1.Unpatch()

	type args struct {
		CID           string
		CurSeriesFlag int
		DstSeriesType int
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			"TestSetStandardSeriesFlag",
			args{"pvwg8usl2r5i8em", 0, nonstandardSeries},
			nil,
		},
		{
			"TestSetStandardSeriesFlag",
			args{"pvwg8usl2r5i8em", 0, standardSeries},
			nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SetStandardSeriesFlag(trpc.BackgroundContext(), tt.args.CID,
				tt.args.CurSeriesFlag, tt.args.DstSeriesType); err != tt.wantErr {
				t.Errorf("SetStandardSeriesFlag() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func createRequest(mediaInfo *msghub.MediaInfo) *http.Request {
	var (
		data []byte
		err  error
	)
	if mediaInfo == nil {
		data = []byte("")

	} else {
		data, err = json.Marshal(mediaInfo)
		if err != nil {
			log.Fatalf("Fail to marshal, err is %s.", err)
		}
	}
	request, _ := http.NewRequest("POST", "testUrl", bytes.NewReader(data))
	return request
}

func TestHandleCoverMsg(t *testing.T) {
	var dataAccess *dataaccess.DataAccess
	patch1 := monkey.PatchInstanceMethod(reflect.TypeOf(dataAccess), "DataBatchGet",
		func(_ *dataaccess.DataAccess, ctx context.Context, dataIDs []string, fields []string,
			opt ...client.Option) (int, map[string]map[string]string, map[string]error) {
			return 0, map[string]map[string]string{
				"u004039x5ih": {"title": "叮咚test_02"},
				"i0040nha2wo": {"title": "叮咚test_03"},
				"w0040dk5pde": {"title": "叮咚test_04"}}, nil
		})
	defer patch1.Unpatch()

	patch2 := monkey.PatchInstanceMethod(reflect.TypeOf(dataAccess), "DataUpdate",
		func(_ *dataaccess.DataAccess, ctx context.Context, CID string,
			fields []*common_storage_common.UpdateFieldInfo, operator string, opt ...client.Option) (int, error) {
			return 0, nil
		})
	defer patch2.Unpatch()

	type args struct {
		in0 http.ResponseWriter
		r   *http.Request
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"unmarsh error", args{r: createRequest(nil)}, true},
		{"set flag", args{r: createRequest(&msghub.MediaInfo{
			Id: "mzc0020044iesvo",
			FieldInfos: map[string]msghub.FieldInfo{
				"long_video_list": {
					Value: "u004039x5ih,i0040nha2wo,w0040dk5pde",
				},
				"type": {
					Value: "2",
				},
			},
		})}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := HandleCoverMsg(tt.args.in0, tt.args.r); (err != nil) != tt.wantErr {
				t.Errorf("HandleMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestHandleVideoMsg(t *testing.T) {
	var dataAccess *dataaccess.DataAccess
	patch1 := monkey.PatchInstanceMethod(reflect.TypeOf(dataAccess), "DataBatchGet",
		func(_ *dataaccess.DataAccess, ctx context.Context, dataIDs []string, fields []string,
			opt ...client.Option) (int, map[string]map[string]string, map[string]error) {
			return 0, map[string]map[string]string{
				"mzc0020044iesvo": {"long_video_list": "i0040nha2wo"},
				"u004039x5ih":     {"title": "叮咚test_02", "type": "2", "covers": "mzc0020044iesvo"},
				"i0040nha2wo":     {"title": "叮咚test_03", "type": "2", "covers": "mzc0020044iesvo"},
				"w0040dk5pde":     {"title": "叮咚test_04", "type": "2", "covers": "mzc0020044iesvo"}}, nil
		})
	defer patch1.Unpatch()

	patch2 := monkey.PatchInstanceMethod(reflect.TypeOf(dataAccess), "DataUpdate",
		func(_ *dataaccess.DataAccess, ctx context.Context, CID string,
			fields []*common_storage_common.UpdateFieldInfo, operator string, opt ...client.Option) (int, error) {
			return 0, nil
		})
	defer patch2.Unpatch()

	type args struct {
		in0 http.ResponseWriter
		r   *http.Request
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"unmarsh error", args{r: createRequest(nil)}, true},
		{"set flag", args{r: createRequest(&msghub.MediaInfo{
			Id: "i0040nha2wo",
			FieldInfos: map[string]msghub.FieldInfo{
				"covers": {
					Value: "mzc0020044iesvo",
				},
				"type": {
					Value: "2",
				},
			},
		})}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := HandleVideoMsg(tt.args.in0, tt.args.r); (err != nil) != tt.wantErr {
				t.Errorf("HandleMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
