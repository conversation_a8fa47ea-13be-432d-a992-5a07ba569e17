package handler

import (
	"reflect"
	"testing"

	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	"github.com/stretchr/testify/assert"
)

func Test_getMapValInt(t *testing.T) {
	type args struct {
		m   map[string]msghub.FieldInfo
		key string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "Test_getMapValInt",
			args: args{
				map[string]msghub.FieldInfo{"testKey": {Id: 1000, Value: "123"}}, "testKey"},
			want: 123,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMapValInt(tt.args.m, tt.args.key); got != tt.want {
				t.Errorf("getMapValInt() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getMapValStr(t *testing.T) {
	type args struct {
		m   map[string]msghub.FieldInfo
		key string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Test_getMapValStr",
			args: args{
				map[string]msghub.FieldInfo{"testKey": {Id: 1000, Value: "testVal"}}, "testKey"},
			want: "testVal",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMapValStr(tt.args.m, tt.args.key); got != tt.want {
				t.Errorf("getMapValStr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getMapValSpilt(t *testing.T) {
	type args struct {
		m   map[string]msghub.FieldInfo
		key string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "Test_getMapValSpilt",
			args: args{
				map[string]msghub.FieldInfo{"testKey": {Id: 1000, Value: "Val1,Val2,Val3"}}, "testKey"},
			want: []string{"Val1", "Val2", "Val3"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMapValSpilt(tt.args.m, tt.args.key); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getMapValSpilt() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_transListType(t *testing.T) {
	type args struct {
		addCoverListType int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "Test_transListType",
			args: args{8323244},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := transListType(tt.args.addCoverListType); got != tt.want {
				t.Errorf("transListType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestContain(t *testing.T) {
	target := []int{1, 2, 3, 4}

	tests := []struct {
		name     string
		s        int
		expected bool
	}{
		{
			name:     "case1: element exists",
			s:        1,
			expected: true,
		},
		{
			name:     "case2: element does not exist",
			s:        5,
			expected: false,
		},
		{
			name:     "case3: target is nil",
			s:        6,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := contain(target, tt.s)
			assert.Equal(t, tt.expected, result, tt.name)
		})
	}

	// Test with a nil target
	nilTarget := []int(nil)
	assert.False(t, contain(nilTarget, 1), "Expected to return false when target is nil")
}

func Test_isVideoCheckUp(t *testing.T) {
	type args struct {
		reqBody *msghub.MediaInfo
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Test_isVideoCheckUp",
			args: args{&msghub.MediaInfo{ModifyFieldInfos: map[string]msghub.ModifyFieldInfo{"state": {Id: 80082, Old: "0", New: "4"}}}},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isVideoCheckUp(tt.args.reqBody); got != tt.want {
				t.Errorf("isVideoCheckUp() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isVideoUnshelve(t *testing.T) {
	type args struct {
		reqBody *msghub.MediaInfo
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Test_isVideoUnshelve",
			args: args{&msghub.MediaInfo{FieldInfos: map[string]msghub.FieldInfo{
				"state": {Id: 80082, Value: "8"},
			}, ModifyFieldInfos: map[string]msghub.ModifyFieldInfo{"state": {Id: 80082, Old: "4", New: "8"}}}},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isVideoUnshelve(tt.args.reqBody); got != tt.want {
				t.Errorf("isVideoUnshelve() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isVideoEnterMedia(t *testing.T) {
	type args struct {
		reqBody *msghub.MediaInfo
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Test_isVideoEnterMedia",
			args: args{&msghub.MediaInfo{ModifyFieldInfos: map[string]msghub.ModifyFieldInfo{"media_flag": {Id: 80082, Old: "0", New: "1"}}}},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isVideoEnterMedia(tt.args.reqBody); got != tt.want {
				t.Errorf("isVideoEnterMedia() = %v, want %v", got, tt.want)
			}
		})
	}
}

// 跳过需要复杂mock的测试
func TestProcessVideoCheckUpEvent(t *testing.T) {
	t.Skip("Skipping complex mock test until mock integration is stable")
}

func TestProcessOutsourcingUploadLogic(t *testing.T) {
	t.Skip("Skipping complex mock test until mock integration is stable")
}

func TestHandleMsg(t *testing.T) {
	t.Skip("Skipping complex mock test until mock integration is stable")
}
