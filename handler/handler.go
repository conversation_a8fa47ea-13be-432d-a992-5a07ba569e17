// Package handler 专辑相关的自动操作
// 该包用作接消息总线的通知，订阅了视频状态state字段
// 1、当视频上架时检查其是否预设了加入专辑，若是，则根据预设信息自动加入专辑
// 2、当视频下架时，从专辑下视频列表移动到未发布视频列表
// 3、当视频入媒资时，若预设了加入专辑，则根据预设信息自动加入专辑的未发布视频列表
package handler

import (
	"context"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	tRpcErrs "git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/dao"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/errcodes"
	"git.code.oa.com/video_media/video_msghub_cb/cover_videos_auto_operation/model"
)

func getMapValInt(m map[string]msghub.FieldInfo, key string) int {
	res := 0
	if fieldInfo, ok := m[key]; ok {
		res, _ = strconv.Atoi(fieldInfo.Value)
	}
	return res
}

func getMapValStr(m map[string]msghub.FieldInfo, key string) string {
	var res string
	if fieldInfo, ok := m[key]; ok {
		res = fieldInfo.Value
	}
	return res
}

func getMapValSpilt(m map[string]msghub.FieldInfo, key string) []string {
	var res []string
	if fieldInfo, ok := m[key]; ok && len(fieldInfo.Value) > 0 {
		res = strings.Split(fieldInfo.Value, model.MsgHubDelimiter)
	}
	return res
}

func contain(target []int, s int) bool {
	if target == nil {
		return false
	}
	for _, t := range target {
		if t == s {
			return true
		}
	}
	return false
}

// 翻译品类库定义属性值与接口定义的值
func transListType(addCoverListType int) int {
	listType := 0
	if addCoverListType == 8323244 { // 长视频
		listType = model.LongVideoListType
	} else if addCoverListType == 8323245 { // 碎视频
		listType = model.ClipVideoListType
	} else if addCoverListType == 123124861 { // 看点列表
		listType = model.AspectVideoListType
	} else if addCoverListType == 123124862 { // 跟播列表
		listType = model.FollowingPlayList
	}
	return listType
}

// 视频上架事件
func isVideoCheckUp(reqBody *msghub.MediaInfo) bool {
	var oldState, newState int
	if modFieldInfo, ok := reqBody.ModifyFieldInfos["state"]; ok {
		oldState, _ = strconv.Atoi(modFieldInfo.Old)
		newState, _ = strconv.Atoi(modFieldInfo.New)
	}
	if oldState != newState && newState == model.VideoCheckUpState {
		return true
	}
	return false
}

// 视频下架事件（非上架）
func isVideoUnshelve(reqBody *msghub.MediaInfo) bool {
	var curState, typeID int
	if f, ok := reqBody.FieldInfos["state"]; ok {
		curState, _ = strconv.Atoi(f.Value)
	}
	if f, ok := reqBody.FieldInfos["type"]; ok {
		typeID, _ = strconv.Atoi(f.Value)
	}
	// 这里不在判断状态前后值变化，仅根据当前值判断，目的是让程序幂等，避免非上架的视频处理遗漏
	// 仅处理视频下架和链接替换状态(体育)的内容[0802-产品Connie需求]
	if curState == model.VideoOffState || (typeID == model.SportsType && curState == model.VideoLinkState) {
		return true
	}
	return false
}

// 视频入媒资事件
func isVideoEnterMedia(reqBody *msghub.MediaInfo) bool {
	var oldMediaFlag, newMediaFlag int
	if modFieldInfo, ok := reqBody.ModifyFieldInfos["media_flag"]; ok {
		oldMediaFlag, _ = strconv.Atoi(modFieldInfo.Old)
		newMediaFlag, _ = strconv.Atoi(modFieldInfo.New)
	}
	if oldMediaFlag != newMediaFlag && newMediaFlag == 1 {
		return true
	}
	return false
}

// 处理视频上架事件逻辑
func processVideoCheckUpEvent(ctx context.Context, coverID, videoID string,
	listType int, waitAddCoverListID string, pos int) error {
	if err := dao.AddCoverVideosList(ctx, coverID, videoID, listType, waitAddCoverListID, pos); err != nil {
		log.ErrorContextf(ctx, "addCoverVideosList:%+v", err)
		return err
	}

	// 加入成功，需要把原先配置的add_cover_id置空，以免后续再次触发; 也可以根据状态转移来判断，比如状态从待审核->上架才触发本程序逻辑
	// 但是考虑到洗数据可能把状态置为待审核，而且以后可能有其他状态迁移到上架，为了避免被误触发，把add_cover_id置空
	if err := dao.CleanVideoAutoAddCoverConf(ctx, videoID); err != nil {
		log.ErrorContextf(ctx, "cleanVideoAutoAddCoverConf:%+v", err)
		return err
	}
	return nil
}

// recordVideoUnshelve 把下架的视频vid，暂存下来；定时扫表，异步缓慢处理，以免突增大量下架影响move操作的成功率
func recordVideoUnshelve(ctx context.Context, videoID string, videoCovers []string) error {
	var uv dao.UnshelvedVideo
	uv.VideoID = videoID

	// 过滤掉以mzc003开头的中视频专辑
	var filteredCovers []string
	for _, cover := range videoCovers {
		if !strings.HasPrefix(cover, "mzc003") {
			filteredCovers = append(filteredCovers, cover)
		}
	}
	if len(filteredCovers) == 0 {
		return nil
	}

	uv.Covers = strings.Join(filteredCovers, model.MediaDelimiter)
	if err := uv.Insert(ctx); err != nil {
		return err
	}
	return nil
}

// processOutsourcingUploadLogic 处理外包上传相关的逻辑
func processOutsourcingUploadLogic(ctx context.Context, src int, coverID string) error {
	// 判断是否外包上传
	outsourcingSrc := []int{1, 101, 102, 103, 104, 122, 123}
	if !contain(outsourcingSrc, src) {
		return nil
	}
	// 获取专辑信息
	coverInfo, err := dao.GetCoverInfo(ctx, coverID)
	if err != nil {
		log.ErrorContextf(ctx, "getCoverInfo:%+v", err)
		return err
	}
	// 长视频列表长度大于1, 不处理; 没有横图竖图的，不处理
	if coverInfo.LongVideoNum > 1 || coverInfo.HzPic == "" || coverInfo.VtPic == "" {
		return nil
	}
	// CheckupGrade==0,自动上架专辑
	if coverInfo.CheckupGrade == 0 {
		if err := dao.CheckupCover(ctx, coverID, model.Operator); err != nil {
			log.ErrorContextf(ctx, "checkupCover:%+v", err)
			return err
		}
	}
	return nil
}

// HandleMsg 接收视频数据总线的变更通知，触发一些专辑列表相关的自动逻辑
func HandleMsg(w http.ResponseWriter, r *http.Request) error {
	ctx := r.Context()
	reqData, e := ioutil.ReadAll(r.Body)
	if e != nil {
		log.ErrorContextf(ctx, "ioutil error:%v; ", e)
		return errs.New(500, e.Error())
	}
	reqBody, err := msghub.ParseMsghubJson(string(reqData))
	if err != nil {
		log.ErrorContextf(ctx, "Unmarshal data error:%v; reqData:%s", err, string(reqData))
		return errs.New(501, err.Error())
	}
	log.InfoContextf(ctx, "reqBody:%+v", reqBody)
	videoID := reqBody.Id
	waitAddCover := getMapValStr(reqBody.FieldInfos, "add_cover_id")
	waitAddCoverListID := getMapValStr(reqBody.FieldInfos, "add_cover_list_id")
	addCoverListType := getMapValInt(reqBody.FieldInfos, "add_cover_list_type")
	pos := getMapValInt(reqBody.FieldInfos, "add_cover_list_pos")
	src := getMapValInt(reqBody.FieldInfos, "src")
	contentForm := getMapValInt(reqBody.FieldInfos, "content_form")
	videoCovers := getMapValSpilt(reqBody.FieldInfos, "covers")
	listType := transListType(addCoverListType)

	// 视频上架,加入指定的列表
	if isVideoCheckUp(reqBody) && addCoverListType > 0 && len(waitAddCover) > 0 {
		if err = processVideoCheckUpEvent(ctx, waitAddCover, videoID, listType, waitAddCoverListID, pos); err != nil {
			if tRpcErrs.Code(err) == errcodes.ERR_INVALID_PARAM {
				return nil
			} else {
				w.WriteHeader(http.StatusInternalServerError)
				return err
			}
		}
		// 外包上传逻辑(视频上架，需要把专辑也上架)
		if err = processOutsourcingUploadLogic(ctx, src, waitAddCover); err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			return errs.New(504, err.Error())
		}
	}

	// 视频下架（状态变为非上架）,从所属专辑中移除（去掉中视频vid的自动移除逻辑；原因TAPD:881832763）
	if isVideoUnshelve(reqBody) && len(videoCovers) > 0 && contentForm != model.MidVideoContent {
		// 为了避免短时大流量对move列表操作的冲击，先把下架任务暂存
		if err = recordVideoUnshelve(ctx, videoID, videoCovers); err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			return err
		}
	}

	// 视频入媒资 并且预设了add_cover_id字段，加入未发布视频列表
	if isVideoEnterMedia(reqBody) && len(waitAddCover) > 0 {
		if err = dao.AddCoverVideosList(ctx, waitAddCover, videoID,
			model.UnpublishVideoListType, "", pos); err != nil {
			if tRpcErrs.Code(err) == errcodes.ERR_INVALID_PARAM {
				return nil
			} else {
				w.WriteHeader(http.StatusInternalServerError)
				return err
			}
		}
	}
	log.InfoContextf(ctx, "cover_videos_auto_operation suc")
	return nil
}
