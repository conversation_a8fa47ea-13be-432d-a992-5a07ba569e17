// Package handler 主处理逻辑
package handler

/*
该包用作接消息总线的通知，订阅了专辑的长视频列表long_video_list，专辑type；
计算专辑是否标准剧集：
1、针对电视剧(2)、动漫(3)频道下的上架专辑进行计算
2、获取专辑下的正片视频列表
3、对每个正片视频计算：获取视频标题
4、更新“是否标准剧集（798）”字段为是 ：所有视频标题需满足“title_num”格式，或“番外num”格式

订阅视频title的变更，重算视频所属专辑covers的专辑是否标准剧集
*/
import (
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/media_go_commlib/dataaccess"
	"git.code.oa.com/video_media/media_go_commlib/msghub"
)

// ModifyFieldInfo 通知中发生变更的字段
type ModifyFieldInfo struct {
	// Id 发生变更的字段ID
	Id int `json:"id"`
	// Old 发生变更前的旧值
	Old string `json:"old"`
	// New 发生变更后的新值
	New string `json:"new"`
}

// FieldInfo 订阅的字段ID和字段Value
type FieldInfo struct {
	// Id 字段ID
	Id int `json:"id"`
	// Value 字段值，如果是list类型，则使用,分隔
	Value string `json:"value"`
}

// UpdateDanmuStatusRsp 调用更新弹幕状态接口的响应
type UpdateDanmuStatusRsp struct {
	// Msg 表示调用是否成功，成功的消息为 succ.
	Msg string `json:"msg"`
	// Ret 调用结果的状态码，成功则为0，失败为-1
	Ret int `json:"ret"`
}

var (
	validCoverTypes   = []int{2, 3} // 仅对电视剧、动漫频道的的专辑进行计算
	standardSeries    = 152462      // 标识剧集
	nonstandardSeries = 152463      // 非标准剧集
)

// isValidType 检查专辑大分类是否是我们关心的目标分类
func isValidType(typeId int) bool {
	bValid := false
	for _, validType := range validCoverTypes {
		if validType == typeId {
			bValid = true
			break
		}
	}
	return bValid
}

// isValidTitle 检查视频标题是否满足我们设定的标题格式
func isValidTitle(title string) bool {
	bValid := false
	if len(title) <= 0 {
		return false
	}
	if matched, _ := regexp.MatchString(`.+_[0-9]+$`, title); matched { // 以数字结尾，是合法标题
		bValid = true
	}
	if matched, _ := regexp.MatchString(`.+番外[0-9]+`, title); matched { // 番外numxxx，是合法标题
		bValid = true
	}
	return bValid
}

// SetStandardSeriesFlag 设置专辑为标准剧集
func SetStandardSeriesFlag(ctx context.Context, coverID string, curSeriesFlag, dstSeriesFlag int) error {
	log.InfoContextf(ctx, "SetStandardSeriesFlag enter:%s-%d-%d", coverID, curSeriesFlag, dstSeriesFlag)
	if (curSeriesFlag == 0 || curSeriesFlag == nonstandardSeries) && dstSeriesFlag == nonstandardSeries {
		return nil
	}
	if curSeriesFlag == standardSeries && dstSeriesFlag == standardSeries {
		return nil
	}

	var dataAccessCover dataaccess.DataAccess
	dataAccessCover.AccessSetOptions(
		dataaccess.WithDataSet(2003),
		dataaccess.WithAppID("cover_standard_series_flag"),
		dataaccess.WithAppKey("e7f2b0e94baff5623a809ae59c126431"))

	var fields []*common_storage_common.UpdateFieldInfo
	fields = append(fields, &common_storage_common.UpdateFieldInfo{
		UpdateType: common_storage_common.EnumUpdateType_UpdateTypeSet,
		FieldInfo: &common_storage_common.FieldInfo{
			FieldName: "series_flag",
			FieldType: common_storage_common.EnumFieldType_FieldTypeIntVec,
			VecInt:    []uint32{uint32(dstSeriesFlag)},
		}})
	code, err := dataAccessCover.DataUpdate(ctx, coverID, fields,
		"cover_standard_series_flag",
		client.WithServiceName("trpc.storage_service.access_layer.MediaInterface"),
		client.WithCalleeSetName("cover.sz.*"))
	if err != nil {
		log.ErrorContextf(ctx, "DataUpdate CID info failed:%d %+v", code, err)
		return err
	}
	log.InfoContextf(ctx, "SetStandardSeriesFlag suc:%s", coverID)
	return nil
}

func getMediaInfo(ctx context.Context, dataSetID int, keyList []string, fields []string) (map[string]map[string]string, error) {
	var setName string
	if dataSetID == 2001 {
		setName = "video.sz.*"
	}
	if dataSetID == 2003 {
		setName = "cover.sz.*"
	}
	var dataAccess dataaccess.DataAccess
	dataAccess.AccessSetOptions(
		dataaccess.WithDataSet(dataSetID),
		dataaccess.WithAppID("cover_standard_series_flag"),
		dataaccess.WithAppKey("e7f2b0e94baff5623a809ae59c126431"))
	code, mediaInfos, VIDErrs := dataAccess.DataBatchGet(ctx, keyList, fields,
		client.WithServiceName("trpc.storage_service.access_layer.MediaInterface"),
		client.WithCalleeSetName(setName))
	if code != 0 {
		log.ErrorContextf(ctx, "DataBatchGet VID info failed:%+v", VIDErrs)
		return nil, errs.New(500, fmt.Sprintf("%+v", VIDErrs))
	}
	log.InfoContextf(ctx, "getMediaInfo suc:%+v", mediaInfos)
	return mediaInfos, nil
}

func calcCoverStandardSeries(ctx context.Context, coverID string) error {
	coverInfos, err := getMediaInfo(ctx, 2003, []string{coverID}, []string{"type", "series_flag", "long_video_list"})
	if err != nil {
		log.ErrorContextf(ctx, "getMediaInfo failed:%+v", err)
		return err
	}
	var longVideoList []string
	var coverTypeID, curSeriesFlag int
	if coverInfo, ok := coverInfos[coverID]; ok {
		longVideoList = splitMediaStr(coverInfo["long_video_list"])
		coverTypeID, _ = strconv.Atoi(coverInfo["type"])
		curSeriesFlag, _ = strconv.Atoi(coverInfo["series_flag"])
	}

	// 非标准剧集
	if !isValidType(coverTypeID) || len(longVideoList) <= 0 {
		if err = SetStandardSeriesFlag(ctx, coverID, curSeriesFlag, nonstandardSeries); err != nil {
			log.ErrorContextf(ctx, "SetStandardSeriesFlag failed:%+v", err)
			return err
		}
		return nil
	}

	// 获取长视频的标题
	// 对于动漫那种比较长的列表，一次全量请求可能会被频控；我们对于大于40的列表，取前20个和后20个做检测
	var checkVideoList []string
	if longVideoList != nil && len(longVideoList) > 40 {
		checkVideoList = append(checkVideoList, longVideoList[0:20]...)
		checkVideoList = append(checkVideoList, longVideoList[len(longVideoList)-20:]...)
	} else {
		checkVideoList = append(checkVideoList, longVideoList...)
	}

	VIDInfos, err := getMediaInfo(ctx, 2001, checkVideoList, []string{"title"})
	if err != nil {
		log.ErrorContextf(ctx, "getVideoInfo failed:%+v", err)
		return err
	}
	// 判断视频标题是否合法
	validTitleVIDNum := 0
	for _, VIDInfo := range VIDInfos {
		if title, ok := VIDInfo["title"]; ok && isValidTitle(title) {
			validTitleVIDNum = validTitleVIDNum + 1
		}
	}
	log.InfoContextf(ctx, "%s-validTitleVIDNum:%d", coverID, validTitleVIDNum)

	// 设置"是否标准剧集"字段
	retSeriesFlag := nonstandardSeries
	if validTitleVIDNum > 0 && len(checkVideoList) == validTitleVIDNum {
		retSeriesFlag = standardSeries
	}
	if err = SetStandardSeriesFlag(ctx, coverID, curSeriesFlag, retSeriesFlag); err != nil {
		log.ErrorContextf(ctx, "SetStandardSeriesFlag failed:%+v", err)
		return err
	}
	return nil
}

func splitMediaStr(ori string) []string {
	mediaSplitSymbol := "+"
	universalSplitSymbol := ","
	if strings.Contains(ori, universalSplitSymbol) {
		return strings.Split(ori, universalSplitSymbol)
	}
	return strings.Split(ori, mediaSplitSymbol)
}

// HandleVideoMsg 接收视频数据总线的变更通知(订阅title字段)，计算视频所属专辑是否标准剧集字段
func HandleVideoMsg(_ http.ResponseWriter, r *http.Request) error {
	ctx := r.Context()
	reqData, e := ioutil.ReadAll(r.Body)
	if e != nil {
		log.ErrorContextf(ctx, "ioutil error:%v; ", e)
		return errs.New(errs.RetServerSystemErr, e.Error())
	}
	reqBody, err := msghub.ParseMsghubJson(string(reqData))
	if err != nil {
		log.ErrorContextf(ctx, "Unmarshal data error:%v; reqData:%s", err, string(reqData))
		return errs.New(errs.RetServerSystemErr, err.Error())
	}

	var videoCovers []string
	if curFieldInfo, ok := reqBody.FieldInfos["covers"]; ok {
		videoCovers = splitMediaStr(curFieldInfo.Value)
	}
	if len(videoCovers) == 0 {
		return nil
	}
	// 对视频所属的每个专辑重算
	for _, coverID := range videoCovers {
		if err = calcCoverStandardSeries(ctx, coverID); err != nil {
			log.ErrorContextf(ctx, "calcCoverStandardSeries error:%v;", err)
			return err
		}
	}
	log.InfoContextf(ctx, "HandleVideoMsg success %s", reqBody.Id)
	return nil
}

// HandleCoverMsg 接收专辑数据总线的变更通知，计算专辑是否标准剧集字段
func HandleCoverMsg(_ http.ResponseWriter, r *http.Request) error {
	ctx := r.Context()
	reqData, e := ioutil.ReadAll(r.Body)
	if e != nil {
		log.ErrorContextf(ctx, "ioutil error:%v; ", e)
		return errs.New(errs.RetServerSystemErr, e.Error())
	}
	reqBody, err := msghub.ParseMsghubJson(string(reqData))
	if err != nil {
		log.ErrorContextf(ctx, "Unmarshal data error:%v; reqData:%s", err, string(reqData))
		return errs.New(errs.RetServerSystemErr, err.Error())
	}
	coverID := reqBody.Id
	if err = calcCoverStandardSeries(ctx, coverID); err != nil {
		log.ErrorContextf(ctx, "calcCoverStandardSeries error:%v;", err)
		return err
	}
	log.InfoContextf(ctx, "HandleCoverMsg success %s", coverID)
	return nil
}
