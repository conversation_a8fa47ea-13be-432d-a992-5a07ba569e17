package union

import (
	"context"
	"reflect"
	"testing"
)

func TestGetInfoFromUnion(t *testing.T) {
	type args struct {
		c      context.Context
		dataID string
	}
	tests := []struct {
		name    string
		args    args
		want    *CIDUnionInfo
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetInfoFromUnion(tt.args.c, tt.args.dataID)
			if (err != nil) != tt.wantErr {
				t.<PERSON><PERSON>("GetInfoFromUnion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("GetInfoFromUnion() got = %v, want %v", got, tt.want)
			}
		})
	}
}
