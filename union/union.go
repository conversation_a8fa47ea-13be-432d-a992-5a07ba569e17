// Package union declares union
package union

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	union "git.code.oa.com/videocommlib/trpc-go-union"
)

var (
	CidTable uint32 = 2003
)

// CIDUnionInfo CID UNION 信息
type CIDUnionInfo struct {
	ProductEndTime string `union:"product_endtime"`
}

// GetInfoFromUnion 获取union信息
func GetInfoFromUnion(c context.Context, dataID string) (*CIDUnionInfo, error) {
	ids := []string{dataID}
	result := make(map[string]*CIDUnionInfo)
	proxy := union.NewParamUnionProxy("get_union_data", 0,
		20023083, "1ff928c9207c3e46", "")
	err := proxy.GetUnion(CidTable, ids, result)
	if err != nil {
		log.ErrorContextf(c, "GetUnion info err:%+v, dataID:%s", err, dataID)
		return nil, err
	}
	if _, ok := result[dataID]; !ok {
		return &CIDUnionInfo{}, nil
	}
	log.InfoContextf(c, "getInfoFromUnion result:%+v", result[dataID])
	return result[dataID], nil
}
