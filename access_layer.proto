syntax = "proto3";

import "storage_service/common/storage_common.proto";
package trpc.storage_service.access_layer;
option go_package = "git.code.oa.com/trpcprotocol/storage_service/access_layer";

// 鉴权信息
message AuthInfo
{
  // 应用id，需要向后台申请
  string appId = 1;
  // 应用key,需要向后台申请
  string appKey = 2;
}

// 消费者通知消息
message ModifyNotify
{
  // 数据id
  string id = 1;
  // 数据集id
  int32 dataSetId = 2;
  // 时间戳
  int32 timeStamp = 3;
  // 变更字段信息
  repeated common.ModifyFieldInfo modifyFieldInfos = 4;
  // appId
  string appId = 5;
  //调用者标识
  string operatorName = 6;
  // 本地IP
  string localIp = 7;
  // 调用者IP
  string remoteIp = 8;
  // 其他扩展信息,透传调用方传入数据
  string extInfo = 9;
  // 请求流水id
  int64 sequenceId = 10;
  // 基础信息
  map<string, common.FieldInfo> baseInfo = 11;
}

// 媒资查询信息请求
message MediaGetRequest
{
  // 鉴权信息
  AuthInfo authInfo = 1;
  // 数据集id
  int32 dataSetId = 2;
  // 查询操作key值
  string id = 3;
  // 需要查询的字段列表 map类型字段使用{fieldName}.{key}/{fieldName}
  repeated string fieldNames = 4;
}

// 媒资查询信息应答
message MediaGetResponse
{
  // 返回错误码
  common.CommRetInfo retInfo = 1;
  // 查询成功的字段信息
  map<string, common.FieldInfo> fieldInfos = 2;
}

// 媒资全字段查询信息请求
message MediaGetAllRequest
{
  // 鉴权信息
  AuthInfo authInfo = 1;
  // 数据集id
  int32 dataSetId = 2;
  // 全字段查询操作key值
  string id = 3;
  // 0:不返回已删除的字段 1:返回已删除的字段（仅支持mysql数据源字段）
  int32 showDel = 4;
  // 仅返回指定数据源存储的字段，不传值则返回所有数据源的字段
  repeated int32 dataSourceIds = 5;
}

// 媒资全字段查询信息应答
message MediaGetAllResponse
{
  // 返回错误码
  common.CommRetInfo retInfo = 1;
  // 查询成功的字段信息
  map<string, common.FieldInfo> fieldInfos = 2;
}

// 媒资更新信息请求
message MediaUpdateRequest
{
  // 鉴权信息
  AuthInfo authInfo = 1;
  // 数据集id
  int32 dataSetId = 2;
  // 更新操作key值
  string id = 3;
  // 需要变更的字段列表
  repeated common.UpdateFieldInfo updateFieldInfos = 4;
  // 操作用户名
  string operatorName = 5;
  // 其他扩展信息,透传调用方传入数据
  string extInfo = 6;
}

// 媒资更新信息应答
message MediaUpdateResponse
{
  // 返回错误码
  common.CommRetInfo retInfo = 1;
}

// 数据接入层接口
service MediaInterface
{
  rpc GetMediaInfo(MediaGetRequest) returns (MediaGetResponse) {}
  rpc GetAllMediaInfo(MediaGetAllRequest) returns (MediaGetAllResponse) {}
  rpc UpdateMediaInfo(MediaUpdateRequest) returns (MediaUpdateResponse) {}
}
