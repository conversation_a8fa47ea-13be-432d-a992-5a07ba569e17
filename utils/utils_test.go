package utils

import (
	"testing"

	"git.woa.com/trpcprotocol/media_event_hub/common_event"
)

func TestFormatViewKey(t *testing.T) {
	tests := []struct {
		name     string
		viewType common_event.EnumViewType
		viewId   string
		expected string
	}{
		{
			name:     "UNIVERSAL_VIEW格式化",
			viewType: common_event.EnumViewType_UNIVERSAL_VIEW,
			viewId:   "test_view_id",
			expected: "2:test_view_id",
		},
		{
			name:     "UNION_VIEW格式化",
			viewType: common_event.EnumViewType_UNION_VIEW,
			viewId:   "union_view_id",
			expected: "1:union_view_id",
		},
		{
			name:     "空viewId",
			viewType: common_event.EnumViewType_UNIVERSAL_VIEW,
			viewId:   "",
			expected: "2:",
		},
		{
			name:     "包含特殊字符的viewId",
			viewType: common_event.EnumViewType_UNION_VIEW,
			viewId:   "view_123:test",
			expected: "1:view_123:test",
		},
		{
			name:     "数字viewId",
			viewType: common_event.EnumViewType_UNIVERSAL_VIEW,
			viewId:   "12345",
			expected: "2:12345",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatViewKey(tt.viewType, tt.viewId)
			if result != tt.expected {
				t.Errorf("FormatViewKey() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestParseViewKey(t *testing.T) {
	tests := []struct {
		name             string
		viewKey          string
		expectedViewType common_event.EnumViewType
		expectedViewId   string
		wantErr          bool
	}{
		{
			name:             "解析UNIVERSAL_VIEW键",
			viewKey:          "2:test_view_id",
			expectedViewType: common_event.EnumViewType_UNIVERSAL_VIEW,
			expectedViewId:   "test_view_id",
			wantErr:          false,
		},
		{
			name:             "解析UNION_VIEW键",
			viewKey:          "1:union_view_id",
			expectedViewType: common_event.EnumViewType_UNION_VIEW,
			expectedViewId:   "union_view_id",
			wantErr:          false,
		},
		{
			name:             "解析包含多个冒号的键",
			viewKey:          "1:view:with:colons",
			expectedViewType: common_event.EnumViewType_UNION_VIEW,
			expectedViewId:   "view",
			wantErr:          false,
		},
		{
			name:             "解析空viewId",
			viewKey:          "2:",
			expectedViewType: common_event.EnumViewType_UNIVERSAL_VIEW,
			expectedViewId:   "",
			wantErr:          false,
		},
		{
			name:    "无效格式-缺少冒号",
			viewKey: "invalid_format",
			wantErr: true,
		},
		{
			name:    "无效格式-只有一个部分",
			viewKey: "1",
			wantErr: true,
		},
		{
			name:    "无效的视图类型-非数字",
			viewKey: "abc:view_id",
			wantErr: true,
		},
		{
			name:    "空字符串",
			viewKey: "",
			wantErr: true,
		},
		{
			name:    "只有冒号",
			viewKey: ":",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			viewType, viewId, err := ParseViewKey(tt.viewKey)

			if (err != nil) != tt.wantErr {
				t.Errorf("ParseViewKey() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if viewType != tt.expectedViewType {
					t.Errorf("ParseViewKey() viewType = %v, expected %v", viewType, tt.expectedViewType)
				}
				if viewId != tt.expectedViewId {
					t.Errorf("ParseViewKey() viewId = %v, expected %v", viewId, tt.expectedViewId)
				}
			}
		})
	}
}

func TestParseViewID(t *testing.T) {
	tests := []struct {
		name          string
		viewID        string
		expectedPrjID int
		expectedDimID int
		wantErr       bool
	}{
		{
			name:          "解析正常的ViewID",
			viewID:        "123_456",
			expectedPrjID: 123,
			expectedDimID: 456,
			wantErr:       false,
		},
		{
			name:          "解析零值ViewID",
			viewID:        "0_0",
			expectedPrjID: 0,
			expectedDimID: 0,
			wantErr:       false,
		},
		{
			name:          "解析大数值ViewID",
			viewID:        "999999_888888",
			expectedPrjID: 999999,
			expectedDimID: 888888,
			wantErr:       false,
		},
		{
			name:          "解析包含多个下划线的ViewID",
			viewID:        "123_456_789",
			expectedPrjID: 123,
			expectedDimID: 456, // 只取前两个部分
			wantErr:       false,
		},
		{
			name:    "无效格式-缺少下划线",
			viewID:  "123456",
			wantErr: true,
		},
		{
			name:    "无效格式-只有一个数字",
			viewID:  "123",
			wantErr: true,
		},
		{
			name:    "无效格式-第一个部分非数字",
			viewID:  "abc_456",
			wantErr: true,
		},
		{
			name:    "无效格式-第二个部分非数字",
			viewID:  "123_def",
			wantErr: true,
		},
		{
			name:    "空字符串",
			viewID:  "",
			wantErr: true,
		},
		{
			name:    "只有下划线",
			viewID:  "_",
			wantErr: true,
		},
		{
			name:    "下划线开头",
			viewID:  "_123",
			wantErr: true,
		},
		{
			name:    "下划线结尾",
			viewID:  "123_",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prjID, dimID, err := ParseViewID(tt.viewID)

			if (err != nil) != tt.wantErr {
				t.Errorf("ParseViewID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if prjID != tt.expectedPrjID {
					t.Errorf("ParseViewID() prjID = %v, expected %v", prjID, tt.expectedPrjID)
				}
				if dimID != tt.expectedDimID {
					t.Errorf("ParseViewID() dimID = %v, expected %v", dimID, tt.expectedDimID)
				}
			}
		})
	}
}

// 测试FormatViewKey和ParseViewKey的往返转换
func TestFormatAndParseViewKey_RoundTrip(t *testing.T) {
	tests := []struct {
		name     string
		viewType common_event.EnumViewType
		viewId   string
	}{
		{
			name:     "UNIVERSAL_VIEW往返转换",
			viewType: common_event.EnumViewType_UNIVERSAL_VIEW,
			viewId:   "test_view",
		},
		{
			name:     "UNION_VIEW往返转换",
			viewType: common_event.EnumViewType_UNION_VIEW,
			viewId:   "union_view",
		},
		{
			name:     "包含下划线的往返转换",
			viewType: common_event.EnumViewType_UNIVERSAL_VIEW,
			viewId:   "view_with_underscores",
		},
		{
			name:     "空viewId往返转换",
			viewType: common_event.EnumViewType_UNION_VIEW,
			viewId:   "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 格式化为键
			formattedKey := FormatViewKey(tt.viewType, tt.viewId)

			// 解析回原始值
			parsedViewType, parsedViewId, err := ParseViewKey(formattedKey)

			if err != nil {
				t.Errorf("ParseViewKey() failed after FormatViewKey(): %v", err)
				return
			}

			if parsedViewType != tt.viewType {
				t.Errorf("Round-trip viewType = %v, expected %v", parsedViewType, tt.viewType)
			}

			if parsedViewId != tt.viewId {
				t.Errorf("Round-trip viewId = %v, expected %v", parsedViewId, tt.viewId)
			}
		})
	}
}

// 测试包含冒号的特殊情况（注意实际ParseViewKey的实现限制）
func TestParseViewKey_WithColons(t *testing.T) {
	tests := []struct {
		name             string
		viewKey          string
		expectedViewType common_event.EnumViewType
		expectedViewId   string
	}{
		{
			name:             "包含一个额外冒号",
			viewKey:          "1:view:extra",
			expectedViewType: common_event.EnumViewType_UNION_VIEW,
			expectedViewId:   "view",
		},
		{
			name:             "包含多个额外冒号",
			viewKey:          "2:view:part1:part2",
			expectedViewType: common_event.EnumViewType_UNIVERSAL_VIEW,
			expectedViewId:   "view",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			viewType, viewId, err := ParseViewKey(tt.viewKey)

			if err != nil {
				t.Errorf("ParseViewKey() error = %v, expected no error", err)
				return
			}

			if viewType != tt.expectedViewType {
				t.Errorf("ParseViewKey() viewType = %v, expected %v", viewType, tt.expectedViewType)
			}

			if viewId != tt.expectedViewId {
				t.Errorf("ParseViewKey() viewId = %v, expected %v", viewId, tt.expectedViewId)
			}
		})
	}
}

// 测试ParseViewID和FormatViewKey的组合使用场景
func TestParseViewID_Integration(t *testing.T) {
	tests := []struct {
		name   string
		viewID string
	}{
		{
			name:   "标准ViewID集成测试",
			viewID: "123_456",
		},
		{
			name:   "零值ViewID集成测试",
			viewID: "0_1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 解析ViewID
			prjID, dimID, err := ParseViewID(tt.viewID)
			if err != nil {
				t.Fatalf("ParseViewID() failed: %v", err)
			}

			// 验证解析结果可以用于构建其他标识符
			if prjID < 0 || dimID < 0 {
				t.Errorf("ParseViewID() returned negative values: prjID=%d, dimID=%d", prjID, dimID)
			}

			// 可以用解析结果构建新的标识符
			newViewKey := FormatViewKey(common_event.EnumViewType_UNIVERSAL_VIEW, tt.viewID)
			if newViewKey == "" {
				t.Errorf("FormatViewKey() with ParseViewID result returned empty string")
			}
		})
	}
}
