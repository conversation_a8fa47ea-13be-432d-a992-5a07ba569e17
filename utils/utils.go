// Package utils 提供一些工具函数
package utils

import (
	"fmt"
	"strconv"
	"strings"

	"git.woa.com/trpcprotocol/media_event_hub/common_event"
)

// FormatViewKey 生成视图键
// 视图键格式为"{viewType}:{viewId}"，用于唯一标识一个视图（视图类型为union或媒资）
func FormatViewKey(viewType common_event.EnumViewType, viewId string) string {
	return fmt.Sprintf("%d:%s", viewType, viewId)
}

// ParseViewKey 从视图键解析出视图类型和视图ID
func ParseViewKey(viewKey string) (common_event.EnumViewType, string, error) {
	parts := strings.Split(viewKey, ":")
	if len(parts) < 2 {
		return 0, "", fmt.Errorf("无效的视图键格式: %s", viewKey)
	}

	viewTypeInt, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, "", fmt.Errorf("无法解析视图类型: %w", err)
	}

	viewType := common_event.EnumViewType(viewTypeInt)
	viewId := parts[1]
	return viewType, viewId, nil
}

// ParseViewID 从视图键解析出视图类型和视图ID
func ParseViewID(viewID string) (int, int, error) {
	parts := strings.Split(viewID, "_")
	if len(parts) < 2 {
		return 0, 0, fmt.Errorf("无效的视图键格式: %s", viewID)
	}

	prjID, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, 0, fmt.Errorf("无法解析项目ID: %w", err)
	}
	dimID, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, 0, fmt.Errorf("无法解析维度ID: %w", err)
	}
	return prjID, dimID, nil
}
