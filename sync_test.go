package main

import (
	"fmt"
	"reflect"
	"strconv"
	"testing"

	wuji "git.code.oa.com/open-wuji/go-sdk/wujiclient"

	"bou.ke/monkey"
	"git.code.oa.com/trpcprotocol/storage_service/common_storage_common"
	"git.code.oa.com/video_media/media_go_commlib/dataaccess"
	"git.code.oa.com/video_media/media_go_commlib/msghub"

	wujimock "git.code.oa.com/open-wuji/go-sdk/wujiclient/mock"
	"github.com/agiledragon/gomonkey"
	"github.com/golang/mock/gomock"
)

func Test_getWujiTestData(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	m := wujimock.NewMockFilterInterface(ctrl)

	t.Run("Test_getWujiTestData:对输入的 testKey，返回目标结果", func(t *testing.T) {
		dataType := 2001
		dataID := "r3242jmulyd"
		testKey := fmt.Sprintf("data_type=%d&data_id=%s", dataType, dataID)
		m.EXPECT().Get(testKey).Return(&TestDataStruct{DataType: strconv.Itoa(dataType), DataID: dataID})

		hasData, err := getWujiTestData(m, dataType, dataID)
		if err != nil {
			t.Errorf("isTestData() error = %v", err)
			return
		}
		if !hasData {
			t.Errorf("not has Data!")
		}
	})
}

func Test_getWujiFieldType(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	m := wujimock.NewMockFilterInterface(ctrl)

	t.Run("Test_getWujiFieldType:对输入的 testKey，返回目标结果", func(t *testing.T) {
		dataSetID := 2001
		fieldID := 80037
		wantfieldType := common_storage_common.EnumFieldType_FieldTypeStr
		testKey := fmt.Sprintf("c_data_set=%d&c_field_id=%d", dataSetID, fieldID)
		m.EXPECT().Get(testKey).Return(&FieldInfoStruct{CFieldType: 1})

		fieldType, err := getWujiFieldType(m, dataSetID, fieldID)
		if err != nil {
			t.Errorf("isTestData() error = %v", err)
			return
		}
		if fieldType != wantfieldType {
			t.Errorf("fieldType != wantfieldType; %d-%d", fieldType, wantfieldType)
		}
	})
}

func Test_writeDataToTestEnv(t *testing.T) {
	patches := gomonkey.ApplyFunc(getWujiFieldType, func(filter wuji.FilterInterface, dataSetID int,
		fieldID int) (common_storage_common.EnumFieldType, error) {
		return common_storage_common.EnumFieldType_FieldTypeStr, nil
	})
	defer patches.Reset()

	var dataAccess *dataaccess.DataAccess
	patch1 := monkey.PatchInstanceMethod(reflect.TypeOf(dataAccess), "DataUpdate",
		func(_ *dataaccess.DataAccess, CID string, fields []*common_storage_common.UpdateFieldInfo, operator string) (int, error) {
			return 0, nil
		})
	defer patch1.Unpatch()

	type args struct {
		dataSetID      int
		dataID         string
		mediaFieldInfo map[string]msghub.FieldInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			"Test_writeDataToTestEnv1",
			args{dataSetID: 2001, dataID: "x3242sgfj24", mediaFieldInfo: map[string]msghub.FieldInfo{"titleX": {Id: 9, Value: "testtitle"}}},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := writeDataToTestEnv(tt.args.dataSetID, tt.args.dataID, tt.args.mediaFieldInfo); (err != nil) != tt.wantErr {
				t.Errorf("writeDataToTestEnv() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
