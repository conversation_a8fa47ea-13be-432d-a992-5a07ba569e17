package conf

import (
	"fmt"
	"strconv"
	"testing"

	"github.com/bmizerany/assert"
	"gopkg.in/yaml.v2"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

func TestGetCrawlerID(t *testing.T) {
	type args struct {
		crawType int
		source   string
		url      string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Case: OfficeAccountType without URL",
			args: args{
				crawType: model.OfficeAccountType,
				source:   "",
				url:      "",
			},
			want: GetConfig().IPOfficeCrawlerTaskID,
		},
		{
			name: "Case: OfficeAccountType with URL",
			args: args{
				crawType: model.OfficeAccountType,
				source:   "",
				url:      "http://example-url.com",
			},
			want: GetConfig().CPCrawlerTaskID,
		},
		{
			name: "Case: TopicType",
			args: args{
				crawType: model.TopicType,
				source:   "",
				url:      "",
			},
			want: GetConfig().IPTopicNameCrawTaskID,
		},
		{
			name: "Case: CPInfoType",
			args: args{
				crawType: model.CPInfoType,
				source:   "",
				url:      "",
			},
			want: GetConfig().CPCrawlerTaskID,
		},
		// TODO: Add a case for model.TopicFieldType with matching source and crawlerID
		// TODO: Add a case for model.TopicFieldType without matching any source and crawlerID
		{
			name: "Case: Invalid crawType",
			args: args{
				crawType: 999,
				source:   "",
				url:      "",
			},
			want: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetCrawlerID(tt.args.crawType, tt.args.source, tt.args.url, false); got != tt.want {
				t.Errorf("GetCrawlerID() = %v, want %v", got, tt.want)
			}
		})
	}
}

type Content struct {
	Data []byte
	cfg  map[string]interface{}
}

func (c *Content) Load() error {
	err := yaml.Unmarshal(c.Data, &c.cfg)
	return err
}

func (c *Content) Reload() {
	_ = c.Load()
}

func (c *Content) Get(key string, defaultValue interface{}) interface{} {
	value, found := c.cfg[key]
	if !found {
		return defaultValue
	}
	return value
}

func (c *Content) Unmarshal(target interface{}) error {
	return yaml.Unmarshal(c.Data, target)
}

func (c *Content) IsSet(key string) bool {
	_, found := c.cfg[key]
	return found
}

func (c *Content) GetInt(key string, defaultValue int) int {
	if value, found := c.cfg[key]; found {
		if v, ok := c.parseInt(value); ok {
			return v
		}
	}
	return defaultValue
}

func (c *Content) parseInt(value interface{}) (int, bool) {
	switch v := value.(type) {
	case int:
		return v, true
	case float64:
		return int(v), true
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i, true
		}
	}
	return 0, false
}

func (c *Content) GetInt32(key string, defaultValue int32) int32 {
	return int32(c.GetInt(key, int(defaultValue)))
}

func (c *Content) GetInt64(key string, defaultValue int64) int64 {
	return int64(c.GetInt(key, int(defaultValue)))
}

func (c *Content) GetUint(key string, defaultValue uint) uint {
	if value, found := c.cfg[key]; found {
		if v, ok := c.parseUint(value); ok {
			return v
		}
	}
	return defaultValue
}

func (c *Content) parseUint(value interface{}) (uint, bool) {
	switch v := value.(type) {
	case uint:
		return v, true
	case float64:
		return uint(v), true
	case string:
		if i, err := strconv.ParseUint(v, 10, strconv.IntSize); err == nil {
			return uint(i), true
		}
	}
	return 0, false
}

func (c *Content) GetUint32(key string, defaultValue uint32) uint32 {
	return uint32(c.GetUint(key, uint(defaultValue)))
}

func (c *Content) GetUint64(key string, defaultValue uint64) uint64 {
	if value, found := c.cfg[key]; found {
		if v, ok := c.parseUint64(value); ok {
			return v
		}
	}
	return defaultValue
}

func (c *Content) parseUint64(value interface{}) (uint64, bool) {
	switch v := value.(type) {
	case uint64:
		return v, true
	case float64:
		return uint64(v), true
	case string:
		if i, err := strconv.ParseUint(v, 10, 64); err == nil {
			return i, true
		}
	}
	return 0, false
}

func (c *Content) GetFloat32(key string, defaultValue float32) float32 {
	if value, found := c.cfg[key]; found {
		if v, ok := c.parseFloat(value); ok {
			return float32(v)
		}
	}
	return defaultValue
}

func (c *Content) GetFloat64(key string, defaultValue float64) float64 {
	if value, found := c.cfg[key]; found {
		if v, ok := c.parseFloat(value); ok {
			return v
		}
	}
	return defaultValue
}

func (c *Content) parseFloat(value interface{}) (float64, bool) {
	switch v := value.(type) {
	case float32:
		return float64(v), true
	case float64:
		return v, true
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f, true
		}
	}
	return 0, false
}

func (c *Content) GetString(key string, defaultValue string) string {
	if value, found := c.cfg[key]; found {
		if v, ok := value.(string); ok {
			return v
		}
	}
	return defaultValue
}

func (c *Content) GetBool(key string, defaultValue bool) bool {
	if value, found := c.cfg[key]; found {
		if v, ok := c.parseBool(value); ok {
			return v
		}
	}
	return defaultValue
}

func (c *Content) parseBool(value interface{}) (bool, bool) {
	switch v := value.(type) {
	case bool:
		return v, true
	case string:
		if b, err := strconv.ParseBool(v); err == nil {
			return b, true
		}
	}
	return false, false
}

func (c *Content) Bytes() []byte {
	return c.Data
}

type MockContent struct {
	Content
}

func (m *MockContent) Load() error {
	if string(m.Bytes()) == "server.yaml" {
		return fmt.Errorf("mock download error")
	}

	m.Content.Data = []byte(`
foo: bar
`)
	return nil
}

func TestNewConf(t *testing.T) {
	mockConfig := &MockContent{}

	t.Run("load fail", func(t *testing.T) {
		mockConfig.Content.Data = []byte("server.yaml")
		err := mockConfig.Load()
		if err == nil {
			t.Error("expected error but got nil")
		}
		expected := "mock download error"
		if err.Error() != expected {
			t.Errorf("expected error message '%s' but got '%s'", expected, err.Error())
		}
	})

	t.Run("load success", func(t *testing.T) {
		mockConfig.Content.Data = []byte("valid config")
		err := mockConfig.Load()
		if err != nil {
			t.Errorf("unexpected error: %v", err)
		}
		expected := map[string]interface{}{"foo": "bar"}
		var actual map[string]interface{}
		err = yaml.Unmarshal(mockConfig.Bytes(), &actual)
		if err != nil {
			t.Errorf("failed to unmarshal config data: %v", err)
		}
		assert.Equal(t, expected, actual)
	})
}
