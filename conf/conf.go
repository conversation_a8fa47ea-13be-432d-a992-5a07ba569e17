// Package conf 访问配置
package conf

import (
	"fmt"

	"gopkg.in/yaml.v2"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
)

// ConfKV 配置数据结构体
type ConfKV struct {
	AccessWuJiConf          *WuJiConf         `yaml:"cpInfoWuJi"`              // AccessWuJiConf 访问无极的配置
	BacktraceDays           int               `yaml:"backtraceDays"`           // BacktraceDays 抓取最近发文的天数
	StartPushHour           int               `yaml:"startPushHour"`           // StartPushHour 每天推送抓取任务的时间点（小时）
	MaxPushNum              int               `yaml:"maxPushNum"`              // MaxPushNum 一次最大的推送数量（用于测试新功能，控制推送量）
	BlackListPlatform       []string          `yaml:"blackListPlatform"`       // BlackListPlatform 暂时不推送的平台；格式为：抓取类型_平台ID
	CPCrawlerTaskID         string            `yaml:"CPCrawlerTaskID"`         // CPCrawlerTaskID CP信息抓取接口的TaskID
	IPOfficeCrawlerTaskID   string            `yaml:"IPOfficeCrawlerTaskID"`   // IPOfficeCrawlerTaskID IP官号信息抓取接口的TaskID
	IPTopicIDCrawlerTaskID  string            `yaml:"IPTopicIDCrawlerTaskID"`  // IPTopicIDCrawlerTaskID IP话题ID信息抓取接口的TaskID
	IPTopicNameCrawTaskID   string            `yaml:"IPTopicNameCrawTaskID"`   // IPTopicNameCrawTaskID 抓取发文信息TaskID
	IPTopicFieldCrawTaskID  map[string]string `yaml:"IPTopicFieldCrawTaskID"`  // IPTopicFieldCrawTaskID 抓取话题属性信息TaskID
	CrawPltNames            []string          `yaml:"CrawPltNames"`            // CrawPltNames 抓取的平台名
	IPOfficeCrawPltNames    []string          `yaml:"IPOfficeCrawPltNames"`    // IPOfficeCrawPltNames ip官号抓取的平台名
	IPTopicFieldCrawPlts    []string          `yaml:"IPTopicFieldCrawPlts"`    // IPTopicFieldCrawPlts 话题属性抓取的平台名
	LongTailIPCrawPlts      []string          `yaml:"LongTailIPCrawPlts"`      // LongTailIPCrawPlts 长尾IP抓取的平台名
	RobotCallbackPath       string            `yaml:"RobotCallbackPath"`       // RobotCallbackPath 企业微信机器人回调地址
	MaoyanRobotCallbackPath string            `yaml:"MaoyanRobotCallbackPath"` // MaoyanRobotCallbackPath 企业微信机器人回调地址
	TopicSearchTaskID       map[string]string `yaml:"TopicSearchTaskID"`       // TopicSearchTaskID 话题搜索任务 id 配置
	IPOfficeSearchTaskID    map[string]string `yaml:"IPOfficeSearchTaskID"`    // IPOfficeSearchTaskID ip 官号搜索任务 id 配置
	PlaylistTaskID          string            `yaml:"PlaylistTaskID"`          // PlaylistTaskID 竞品排播列表TaskID
	PlaylistDetailTaskID    string            `yaml:"PlaylistDetailTaskID"`    // PlaylistDetailTaskID 排播详情页TaskID
	CrawAPISecret           string            `yaml:"CrawAPISecret"`           // CrawAPISecret 抓取接口的SecretID
}

// WuJiConf 无极配置
type WuJiConf struct {
	WuJiAppID     string `yaml:"appID"`     // WuJiAppID 无极 appID
	WuJiSchemaID  string `yaml:"schemaID"`  // WuJiSchemaID 无极 schemaID
	WuJiSchemaKey string `yaml:"schemaKey"` // WuJiSchemaKey 无极 schemaKey
	WuJiAuthID    string `yaml:"authID"`    // WuJiAuthID 无极 写表的权限ID
	WuJiAuthkey   string `yaml:"authkey"`   // WuJiAuthkey 无极 写表的权限Key
}

// NewConf 加载t-config配置数据
func NewConf() error {
	// 下载配置
	name := "server.yaml"
	conf, err := config.Load(name, config.WithCodec("yaml"), config.WithProvider("tconf"))
	if err != nil {
		return fmt.Errorf("load tconf fail, name:[%s], err:[%s]", name, err)
	}
	// 解析配置
	if err = yaml.Unmarshal(conf.Bytes(), _config); err != nil {
		return fmt.Errorf("parse config fail fail, err:[%s], conf:[%s]", err, string(conf.Bytes()))
	}
	log.InfoContextf(trpc.BackgroundContext(), "conf[%v]", _config)
	return nil
}

var _config = &ConfKV{}

// GetConfig 获取全局配置
func GetConfig() *ConfKV {
	return _config
}

// GetCrawPlatforms 根据不同的订单ID，获取可抓取平台名
func GetCrawPlatforms(orderID int) []string {
	switch orderID {
	case model.IPTopicOrder:
		return GetConfig().IPTopicFieldCrawPlts
	case model.IPAccountOrder:
		return GetConfig().IPOfficeCrawPltNames
	case model.LongTailIPTopicOrder:
		return GetConfig().LongTailIPCrawPlts
	default:
		return GetConfig().CrawPltNames
	}
}

// GetCrawlerID 根据抓取类型，获取不同的爬虫ID
func GetCrawlerID(crawType int, source string, url string, isSearch bool) string {
	// 第一阶段搜索
	if isSearch {
		switch crawType {
		case model.TopicType, model.TopicFieldType:
			return GetConfig().TopicSearchTaskID[source]
		case model.OfficeAccountType:
			return GetConfig().IPOfficeSearchTaskID[source]
		default: // nothing to do
		}
	}

	// 第二阶段抓取
	mapCrawlerID := map[int]string{
		model.OfficeAccountType: GetConfig().IPOfficeCrawlerTaskID,
		model.TopicType:         GetConfig().IPTopicIDCrawlerTaskID,
		model.CPInfoType:        GetConfig().CPCrawlerTaskID,
		model.TopicFieldType:    GetConfig().IPTopicFieldCrawTaskID[source],
	}

	crawlerID := mapCrawlerID[crawType]
	// 特殊逻辑：IP账号的抓取，若指定了URL账号链接，则走原先的CP账号抓取渠道
	if crawType == model.OfficeAccountType && url != "" {
		crawlerID = GetConfig().CPCrawlerTaskID
	}
	return crawlerID
}
