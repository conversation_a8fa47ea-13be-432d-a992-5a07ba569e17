package consumer

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/IBM/sarama"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/logic"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
)

// TopicListHandler 接收返回的topic名下的topic列表信息（旧架构体系下的，话题搜索结果接收逻辑）
// NOCC:CCN_threshold(设计如此:)
func TopicListHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	res := msg.Value
	log.InfoContextf(ctx, "TopicListHandler enter-%s", res)
	var err error
	topicList, err := parseTopicInfo(res)
	if err != nil {
		log.ErrorContextf(ctx, "Unmarshal error: %+v", err)
		return nil
	}
	transmit := topicList.Transmit
	if topicList.Transmit == "" {
		transmit = topicList.BizID
	}
	if transmit == "" {
		log.ErrorContextf(ctx, "empty transmit topicInfo[%v]", topicList)
		return nil
	}
	if model.IsNewFrameResult(transmit) { // 新链路的返回结果
		return OrderResultHandler(ctx, msg)
	}

	var reportInfo report.ReportCrawlTraceInfo
	reportInfo.ReqInfo = string(res)
	ctx = context.WithValue(ctx, "report_info", &reportInfo)
	defer ProcessReport(ctx, err)()
	crawType, crawID, orderID, err := model.ExtractCrawID(transmit)
	if err != nil {
		log.InfoContextf(ctx, "old craw[%s]", res)
		return nil
	}
	var ipCrawTask dao.IPCrawTask
	ipCrawTask.OrderID = orderID
	if err = ipCrawTask.GetCrawTaskByID(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "GetCrawTaskByID error: %+v", err)
		return err
	}
	if err = handleEmptyTopicID(ctx, topicList.Info.TopicID, &ipCrawTask); err != nil {
		return nil
	}
	// 记录首次搜索结果
	if err = dao.CreateTopicInfo(ctx, &dao.TopicInfo{
		OrderID:   orderID,
		IPID:      crawID,
		TopicID:   topicList.Info.TopicID,
		TopicName: topicList.Info.TopicName,
		TopicURL:  topicList.Info.TopicURL,
		Rank:      topicList.Info.Rank,
		CrawKey:   ipCrawTask.CrawKey,
		CrawCID:   ipCrawTask.CoverID,
	}); err != nil {
		log.ErrorContextf(ctx, "CreateTopicInfo err[%v]", err)
		return nil
	}

	// 只抓取和更新排名第一的
	if topicList.Info.Rank != 1 {
		return nil
	}
	ipCrawTask.TopicName, ipCrawTask.TopicURL = topicList.Info.TopicName, topicList.Info.TopicURL
	if err = ipCrawTask.UpdateTopicInfo(ctx); err != nil {
		log.ErrorContextf(ctx, "UpdateTopicInfo err[%v]", err)
		return err
	}

	log.InfoContextf(ctx, "ipCrawTask[%v]", ipCrawTask)
	ctx = prepareReportInfo(ctx, &ipCrawTask, transmit, crawType)
	if err = dao.AddCrawTask(trpc.CloneContext(ctx), &dao.CrawTaskArgs{
		CrawType:  crawType,
		Transmit:  fmt.Sprintf("%d_%d_craw", ipCrawTask.CrawType, ipCrawTask.ID),
		Source:    ipCrawTask.Platform,
		TopicName: topicList.Info.TopicName,
		TopicID:   topicList.Info.TopicID,
		URL:       topicList.Info.TopicURL,
		BacktraceDays: logic.CalcBacktraceDays(crawType, ipCrawTask.ConfirmStatus,
			ipCrawTask.BacktrackStatus, ipCrawTask.CreateTime),
	}, false); err != nil {
		log.ErrorContextf(ctx, "AddCrawTask ERR:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "TopicListHandler suc-%+v", topicList)
	return nil
}

func parseTopicInfo(res []byte) (*model.TopicList, error) {
	topicList := &model.TopicList{}
	if err := json.Unmarshal(res, topicList); err != nil {
		return nil, fmt.Errorf("unmarshal topicList err[%v] data[%s]", err, string(res))
	}
	if topicList.Data == "" {
		return nil, fmt.Errorf("empty topicInfo data[%s]", string(res))
	}
	topicInfo := &model.TopicInfo{}
	if err := json.Unmarshal([]byte(topicList.Data), topicInfo); err != nil {
		return nil, fmt.Errorf("unmarshal topicInfo err[%v], data[%s]", err, topicList.Data)
	}
	topicList.Info = topicInfo
	return topicList, nil
}

func handleEmptyTopicID(ctx context.Context, topicID string, ipCrawTask *dao.IPCrawTask) error {
	if topicID == "" {
		ipCrawTask.DayCrawlerState = model.CrawlerRetEmpty
		if err := ipCrawTask.SetCrawTaskState(ctx); err != nil {
			log.ErrorContextf(ctx, "SetCrawTaskState ERR:%+v", err)
		}
		return errors.New("topic ID is empty")
	}
	return nil
}

func prepareReportInfo(ctx context.Context, ipCrawTask *dao.IPCrawTask, transmit string, crawType int) context.Context {
	var reportInfo report.ReportCrawlTraceInfo
	reportInfo.CrawlSource = ipCrawTask.Platform
	reportInfo.CrawlKey = ipCrawTask.CrawKey
	reportInfo.CrawlType = crawType
	reportInfo.MsgType = model.ConsumerReport
	reportInfo.Transmit = transmit
	return context.WithValue(ctx, "report_info", &reportInfo)
}
