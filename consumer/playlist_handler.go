package consumer

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"github.com/IBM/sarama"
)

// PlaylistHandler 排播列表结果返回
func PlaylistHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	res := msg.Value
	log.InfoContextf(ctx, "PlaylistHandler enter-%s", res)
	competitorResult, err := unmarshalCompetitorResult(ctx, msg)
	if err != nil {
		return err
	}

	var playlistInfo dao.PlaylistInfo
	if err := json.Unmarshal([]byte(competitorResult.Data), &playlistInfo); err != nil {
		log.ErrorContextf(ctx, "Unmarshal err-%+v", err)
		return err
	}
	if len(competitorResult.TmsTaskTags) > 0 {
		playlistInfo.Platform = competitorResult.TmsTaskTags[0]
	}
	playlistInfo.UpdateDate, _ = formatDate(playlistInfo.UpdateDate)
	playlistInfo.UpdateTime, _ = formatTime(playlistInfo.UpdateTime)
	playlistInfo.OrderID = model.PlaylistOrder

	// 写入DB
	if err := playlistInfo.InsertPlaylistInfo(ctx); err != nil {
		return err
	}
	log.InfoContextf(ctx, "PlaylistHandler suc-%+v", playlistInfo)
	return nil
}

func formatDate(monthDay string) (string, error) {
	currentYear := time.Now().Year()
	dateString := fmt.Sprintf("%d-%s", currentYear, monthDay)
	date, err := time.Parse("2006-01-02", dateString)
	if err != nil {
		return "", err
	}
	return date.Format("2006-01-02"), nil
}

func formatTime(hourMinute string) (string, error) {
	date, err := time.Parse("15:04:05", hourMinute+":00")
	if err != nil {
		return "", err
	}
	return date.Format("15:04:05"), nil
}
