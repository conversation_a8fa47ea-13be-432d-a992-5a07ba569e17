package consumer

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"

	"github.com/IBM/sarama"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"github.com/agiledragon/gomonkey/v2"
)

func Test_parseTopicInfo(t *testing.T) {
	tests := []struct {
		name    string
		res     []byte
		want    *model.TopicList
		wantErr bool
	}{
		{
			name: "normal",
			res:  []byte(`{"transmit":"1_2_craw","data":"{\"topic_id\":\"2\"}"}`),
			want: &model.TopicList{
				Transmit: "1_2_craw",
				Data:     `{"topic_id":"2"}`,
				Info: &model.TopicInfo{
					TopicID: "2",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseTopicInfo(tt.res)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseTopicInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("parseTopicInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTopicListHandler(t *testing.T) {
	var reportInfo *report.ReportCrawlTraceInfo
	gomonkey.ApplyMethod(reflect.TypeOf(reportInfo), "DoReport",
		func(_ *report.ReportCrawlTraceInfo, ctx context.Context) {
			return
		})
	var ipCrawTask *dao.IPCrawTask
	gomonkey.ApplyMethod(reflect.TypeOf(ipCrawTask), "UpdateTopicInfo",
		func(_ *dao.IPCrawTask, ctx context.Context) error {
			return nil
		})
	gomonkey.ApplyMethod(reflect.TypeOf(ipCrawTask), "SetCrawTaskState",
		func(_ *dao.IPCrawTask, ctx context.Context) error {
			return nil
		})

	tests := []struct {
		name    string
		msg     *sarama.ConsumerMessage
		wantErr bool
	}{
		{
			name: "normal",
			msg: &sarama.ConsumerMessage{
				Value: []byte(`{"transmit":"1_2_craw","data":"{\"topic_url\":\"url\"}"}`),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var tsk *dao.IPCrawTask
			p1 := gomonkey.ApplyMethod(reflect.TypeOf(tsk), "GetCrawTaskByID",
				func(_ *dao.IPCrawTask, ctx context.Context, id int) error { return nil })
			defer p1.Reset()
			p2 := gomonkey.ApplyFunc(dao.CreateAccountInfo, func(ctx context.Context, a *dao.AccountInfo) error { return nil })
			defer p2.Reset()
			if err := TopicListHandler(trpc.BackgroundContext(), tt.msg); (err != nil) != tt.wantErr {
				t.Errorf("TopicListHandler() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
