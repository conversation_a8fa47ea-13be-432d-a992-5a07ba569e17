package consumer

import (
	"context"
	"encoding/json"

	"github.com/IBM/sarama"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/logic"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
)

// ArticleHandler 抓取侧返回的文章信息回调接口
func ArticleHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	res := msg.Value
	log.InfoContextf(ctx, "ArticleHandler enter-%s", res)
	var err error
	var reportInfo report.ReportCrawlTraceInfo
	reportInfo.ReqInfo = string(res)
	ctx = context.WithValue(ctx, "report_info", &reportInfo)
	defer ProcessReport(ctx, err)()

	articleInfo, transmit, err := parseArticleInfo(res)
	if err != nil {
		log.ErrorContextf(ctx, "Unmarshal error: %+v", err)
		return err
	}
	if transmit == "" {
		log.ErrorContextf(ctx, "transmit is empty-%s", res)
		return nil
	}

	crawType, crawID, orderID, err := model.ExtractCrawID(transmit)
	if err != nil {
		return nil
	}
	c := createCrawler(crawType, orderID, articleInfo)
	reportInfo.CrawlSource = articleInfo.Source
	reportInfo.CrawlType = crawType
	reportInfo.MsgType = model.ConsumerReport
	reportInfo.Transmit = transmit
	reportInfo.OrderID = orderID
	ctx = context.WithValue(ctx, "report_info", &reportInfo)
	if err = c.CrawlerRetArticleCallBack(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "CrawlerRetArticleCallBack ERR:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "ArticleHandler suc-%+v", articleInfo)
	return nil
}

func parseArticleInfo(res []byte) (report.ReportArticleInfo, string, error) {
	var articleInfo report.ReportArticleInfo
	if err := json.Unmarshal(res, &articleInfo); err != nil {
		return report.ReportArticleInfo{}, "", err
	}
	transmit := articleInfo.StaticInfo.Transmit
	if transmit == "" {
		transmit = articleInfo.Transmit
	}
	return articleInfo, transmit, nil
}

func createCrawler(crawType, orderID int, articleInfo report.ReportArticleInfo) logic.Crawler {
	if crawType == model.CPInfoType {
		return logic.NewCPCrawler(dao.CPCrawTask{}, articleInfo, report.ReportAccountInfo{})
	}
	if crawType == model.OfficeAccountType || crawType == model.TopicType {
		return logic.NewIPCrawler(dao.IPCrawTask{OrderID: orderID}, articleInfo,
			report.ReportAccountInfo{}, report.ReportTopicFieldInfo{})
	}
	return nil
}
