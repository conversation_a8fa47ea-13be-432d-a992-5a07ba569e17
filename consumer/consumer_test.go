package consumer

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	"github.com/IBM/sarama"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/logic"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
	"github.com/agiledragon/gomonkey/v2"
)

func TestArticleHandler(t *testing.T) {
	// 实现一个新的mock 函数
	var mockProcessReport processReportFunc = func(ctx context.Context, err error) func() {
		return func() {}
	}
	// 设置一个全局变量的备份，在完成测试后将其还原；使用mock函数替换全局函数
	originalGlobalProcessReport := ProcessReport
	ProcessReport = mockProcessReport

	cpProxy := logic.NewCPCrawler(dao.CPCrawTask{}, report.ReportArticleInfo{}, report.ReportAccountInfo{})
	ipProxy := logic.NewIPCrawler(dao.IPCrawTask{}, report.ReportArticleInfo{},
		report.ReportAccountInfo{}, report.ReportTopicFieldInfo{})

	patch := gomonkey.ApplyMethodSeq(reflect.TypeOf(cpProxy), "CrawlerRetArticleCallBack",
		[]gomonkey.OutputCell{
			{Values: gomonkey.Params{nil}},
			{Values: gomonkey.Params{nil}},
			{Values: gomonkey.Params{errors.New("Unexpected Error")}},
		})
	defer patch.Reset()
	patch2 := gomonkey.ApplyMethodSeq(reflect.TypeOf(ipProxy), "CrawlerRetArticleCallBack",
		[]gomonkey.OutputCell{
			{Values: gomonkey.Params{nil}},
			{Values: gomonkey.Params{nil}},
			{Values: gomonkey.Params{errors.New("Unexpected Error")}},
			{Values: gomonkey.Params{errors.New("Unexpected Error")}},
		})
	defer patch2.Reset()

	articleInfo1, _ := json.Marshal(report.ReportArticleInfo{
		StaticInfo: report.EntityStaticInfo{Transmit: "1_123_craw"},
	})
	articleInfo2, _ := json.Marshal(report.ReportArticleInfo{
		StaticInfo: report.EntityStaticInfo{Transmit: "2_123_craw"},
	})
	// Invalid JSON input
	invalidJSON := []byte(`{"Something"": "Invalid}`)

	type args struct {
		ctx context.Context
		msg *sarama.ConsumerMessage
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Case: CP Proxy",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: articleInfo1,
				},
			},
			wantErr: false,
		},
		{
			name: "Case: IP Proxy",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: articleInfo2,
				},
			},
			wantErr: false,
		},
		{
			name: "Case: Invalid JSON",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: invalidJSON,
				},
			},
			wantErr: true,
		},
		{
			name: "Case: CP Proxy - CrawlerRetArticleCallBack Error",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: articleInfo1,
				},
			},
			wantErr: true,
		},
		{
			name: "Case: IP Proxy - CrawlerRetArticleCallBack Error",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: articleInfo2,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ArticleHandler(tt.args.ctx, tt.args.msg); (err != nil) != tt.wantErr {
				t.Errorf("ArticleHandler() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	ProcessReport = originalGlobalProcessReport
}

func TestAccountHandler(t *testing.T) {
	// 实现一个新的mock 函数
	var mockProcessReport processReportFunc = func(ctx context.Context, err error) func() {
		return func() {}
	}
	// 设置一个全局变量的备份，在完成测试后将其还原；使用mock函数替换全局函数
	originalGlobalProcessReport := ProcessReport
	ProcessReport = mockProcessReport

	cpProxy := logic.NewCPCrawler(dao.CPCrawTask{}, report.ReportArticleInfo{}, report.ReportAccountInfo{})
	ipProxy := logic.NewIPCrawler(dao.IPCrawTask{}, report.ReportArticleInfo{},
		report.ReportAccountInfo{}, report.ReportTopicFieldInfo{})

	patch := gomonkey.ApplyMethodSeq(reflect.TypeOf(cpProxy), "CrawlerRetArticleCallBack",
		[]gomonkey.OutputCell{
			{Values: gomonkey.Params{nil}},
			{Values: gomonkey.Params{nil}},
			{Values: gomonkey.Params{errors.New("Unexpected Error")}},
		})
	defer patch.Reset()
	patch2 := gomonkey.ApplyMethodSeq(reflect.TypeOf(ipProxy), "CrawlerRetArticleCallBack",
		[]gomonkey.OutputCell{
			{Values: gomonkey.Params{nil}},
			{Values: gomonkey.Params{nil}},
			{Values: gomonkey.Params{errors.New("Unexpected Error")}},
			{Values: gomonkey.Params{errors.New("Unexpected Error")}},
		})
	defer patch2.Reset()

	accountInfo1, _ := json.Marshal(report.ReportAccountInfo{
		StaticInfo: report.CPStaticInfo{Transmit: "1_123"},
	})
	accountInfo2, _ := json.Marshal(report.ReportAccountInfo{
		StaticInfo: report.CPStaticInfo{Transmit: "2_123"},
	})
	// Invalid JSON input
	invalidJSON := []byte(`{"Something"": "Invalid}`)

	type args struct {
		ctx context.Context
		msg *sarama.ConsumerMessage
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Case: CP Proxy - Account",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: accountInfo1,
				},
			},
			wantErr: false,
		},
		{
			name: "Case: IP Proxy - Account",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: accountInfo2,
				},
			},
			wantErr: false,
		},
		{
			name: "Case: Invalid JSON",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: invalidJSON,
				},
			},
			wantErr: true,
		},
		{
			name: "Case: CP Proxy - CrawlerRetAccountCallBack Error",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: accountInfo1,
				},
			},
			wantErr: false,
		},
		{
			name: "Case: IP Proxy - CrawlerRetAccountCallBack Error",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: accountInfo2,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := AccountHandler(tt.args.ctx, tt.args.msg); (err != nil) != tt.wantErr {
				t.Errorf("AccountHandler() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	ProcessReport = originalGlobalProcessReport
}

func TestTopicFieldHandler(t *testing.T) {
	// 实现一个新的mock 函数
	var mockProcessReport processReportFunc = func(ctx context.Context, err error) func() {
		return func() {}
	}
	// 设置一个全局变量的备份，在完成测试后将其还原；使用mock函数替换全局函数
	originalGlobalProcessReport := ProcessReport
	ProcessReport = mockProcessReport

	ipProxy := logic.NewIPCrawler(dao.IPCrawTask{}, report.ReportArticleInfo{},
		report.ReportAccountInfo{}, report.ReportTopicFieldInfo{})

	out := []gomonkey.OutputCell{ // mock返回值
		{Values: gomonkey.Params{nil}},
		{Values: gomonkey.Params{errors.New("Unexpected Error")}},
		{Values: gomonkey.Params{nil}},
		{Values: gomonkey.Params{errors.New("Unexpected Error")}},
	}
	patch := gomonkey.ApplyMethodSeq(reflect.TypeOf(ipProxy), "CrawlerRetTopicFieldCallBack", out) // mock对象的方法
	defer patch.Reset()

	validJSONStatusNonZero, _ := json.Marshal(struct {
		Status   int    `json:"status"`
		Transmit string `json:"transmit"`
	}{
		Status:   1,
		Transmit: "2_123",
	})

	validJSONStatusZero, _ := json.Marshal(struct {
		Ext struct {
			TopicName    string `json:"name"`
			PlayCount    int    `json:"play_count"`
			ReadCount    int    `json:"read_count"`
			DiscussCount int    `json:"discuss_count"`
			OriCount     int    `json:"ori_count"`
			ContentCount int    `json:"content_count"`
		} `json:"ext"`
		Transmit string `json:"transmit"`
		Source   string `json:"source"`
	}{
		Ext: struct {
			TopicName    string `json:"name"`
			PlayCount    int    `json:"play_count"`
			ReadCount    int    `json:"read_count"`
			DiscussCount int    `json:"discuss_count"`
			OriCount     int    `json:"ori_count"`
			ContentCount int    `json:"content_count"`
		}{
			TopicName:    "test",
			PlayCount:    1000,
			ReadCount:    100,
			DiscussCount: 5,
			OriCount:     53,
			ContentCount: 30,
		},
		Transmit: "2_123",
		Source:   "source",
	})

	// Invalid JSON input
	invalidJSON := []byte(`{"Something"": "Invalid}`)

	type args struct {
		ctx context.Context
		msg *sarama.ConsumerMessage
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Case: Status Non-Zero",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: validJSONStatusNonZero,
				},
			},
			wantErr: false,
		},
		{
			name: "Case: Status Zero - Valid JSON",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: validJSONStatusZero,
				},
			},
			wantErr: false,
		},
		{
			name: "Case: Invalid JSON",
			args: args{
				ctx: trpc.BackgroundContext(),
				msg: &sarama.ConsumerMessage{
					Value: invalidJSON,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := TopicFieldHandler(tt.args.ctx, tt.args.msg); (err != nil) != tt.wantErr {
				t.Errorf("TopicFieldHandler() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	ProcessReport = originalGlobalProcessReport
}

func Test_getCrawKeyByID(t *testing.T) {
	tests := []struct {
		name     string
		transmit string
		want     string
	}{
		{
			name:     "cp",
			transmit: "3_2_1003_cp",
			want:     "accountName",
		},
		{
			name:     "topic",
			transmit: "2_2_1002_topic",
			want:     "key",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "cp" {
				var c *dao.CPCrawTask
				patch := gomonkey.ApplyMethod(reflect.TypeOf(c), "GetCrawTaskByID",
					func(x *dao.CPCrawTask, ctx context.Context, crawID int) error {
						x.AccountName = "accountName"
						return nil
					})
				defer patch.Reset()
			} else {
				var i *dao.IPCrawTask
				patch := gomonkey.ApplyMethod(reflect.TypeOf(i), "GetCrawTaskByID",
					func(x *dao.IPCrawTask, ctx context.Context, crawID int) error {
						x.CrawKey = "key"
						return nil
					})
				defer patch.Reset()
			}
			if got := getCrawKeyByID(trpc.BackgroundContext(), tt.transmit); got != tt.want {
				t.Errorf("getCrawKeyByID() = %v, want %v", got, tt.want)
			}
		})
	}
}
