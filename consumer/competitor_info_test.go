package consumer

import (
	"context"
	"encoding/json"
	"reflect"
	"testing"

	"github.com/IBM/sarama"
	"github.com/agiledragon/gomonkey/v2"

	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
)

func Test_unmarshalCompetitorResult(t *testing.T) {
	ctx := context.Background()
	b, _ := json.Marshal(CompetitorResult{Data: "{}", TmsTaskTags: []string{"dy"}, TmsBizID: "x"})
	ret, err := unmarshalCompetitorResult(ctx, &sarama.ConsumerMessage{Value: b})
	if err != nil || ret.TmsBizID != "x" {
		t.Fatalf("unexpected: %v %+v", err, ret)
	}
}

func Test_CompetitorHandler(t *testing.T) {
	ctx := context.Background()
	// valid data
	comp := CompetitorResult{Data: `{"title":"t","source":"s"}`, TmsTaskTags: []string{"dy"}, TmsBizID: "x"}
	b, _ := json.Marshal(comp)
	// stub DoReport
	var r *report.ReportCompetitor
	p := gomonkey.ApplyMethod(reflect.TypeOf(r), "DoReport", func(_ *report.ReportCompetitor, _ context.Context) {})
	defer p.Reset()
	if err := CompetitorHandler(ctx, &sarama.ConsumerMessage{Value: b}); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
}
