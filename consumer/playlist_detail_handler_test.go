package consumer

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"github.com/IBM/sarama"
	"github.com/agiledragon/gomonkey/v2"
)

func TestTransMarkType(t *testing.T) {
	testCases := []struct {
		markType int
		expected string
	}{
		{0, "无"},
		{4, "VIP"},
		{17, "预告"},
		{20, "限免"},
		{1, ""},
		{100, ""},
	}

	for _, testCase := range testCases {
		result := transMarkType(testCase.markType)
		if result != testCase.expected {
			t.Errorf("transMarkType(%d) = %q; want %q", testCase.markType, result, testCase.expected)
		}
	}
}

func TestExtractAndFormatDate(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
		err      error
	}{
		{"0_0_1005_153_20230911210632", "2023-09-11 21:06:32", nil},
		{"0_0_1005_153_20221001235959", "2022-10-01 23:59:59", nil},
		{"0_0_1005_153_invalid", "", fmt.Errorf("invalid input format")},
		{"0_0_1005_153", "", fmt.Errorf("invalid input format")},
	}

	for _, testCase := range testCases {
		result, err := extractAndFormatDate(testCase.input)
		if result != testCase.expected || (err != nil && testCase.err == nil) || (err == nil && testCase.err != nil) {
			t.Errorf("extractAndFormatDate(%q) = %q, %v; want %q, %v", testCase.input, result, err, testCase.expected, testCase.err)
		}
	}
}

func TestPlaylistDetailHandler(t *testing.T) {
	tests := []struct {
		name    string
		msg     *sarama.ConsumerMessage
		wantErr bool
	}{
		{
			name: "normal",
			msg: &sarama.ConsumerMessage{
				Value: []byte(`{"transmit":"1_2_craw","data":"{\"url\":\"url\"}"}`),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var c *dao.CommonCrawResult
			patch := gomonkey.ApplyMethod(reflect.TypeOf(c), "InsertCrawResult",
				func(_ *dao.CommonCrawResult, ctx context.Context, orderID int) error { return nil })
			defer patch.Reset()
			if err := PlaylistDetailHandler(trpc.BackgroundContext(), tt.msg); (err != nil) != tt.wantErr {
				t.Errorf("PlaylistDetailHandler() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
