package consumer

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	"github.com/IBM/sarama"
	"github.com/agiledragon/gomonkey/v2"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
)

func Test_extractAndFormatDate(t *testing.T) {
	cases := []struct {
		in  string
		ok  bool
		out string
	}{
		{in: "0_0_1006_20230912112233", ok: true, out: "2023-09-12 11:22:33"},
		{in: "bad_format", ok: false},
	}
	for _, c := range cases {
		got, err := extractAndFormatDate(c.in)
		if c.ok && err != nil {
			t.Fatalf("want ok, got err:%v", err)
		}
		if !c.ok && err == nil {
			t.Fatalf("want err, got nil")
		}
		if c.ok && got != c.out {
			t.Fatalf("unexpected out: %s", got)
		}
	}
}

func Test_unmarshalTopicFieldResult(t *testing.T) {
	msgBody := map[string]any{
		"ext": map[string]any{
			"name":          "topicA",
			"play_count":    1,
			"read_count":    2,
			"discuss_count": 3,
			"ori_count":     4,
			"content_count": 5,
			"user_count":    6,
			"tms_biz_id":    "0_1_1006_20230912112233",
		},
		"source": "douyin",
		"entity": "topic",
	}
	b, _ := json.Marshal(msgBody)
	msg := &sarama.ConsumerMessage{Value: b}
	ret, err := unmarshalTopicFieldResult(trpc.BackgroundContext(), msg)
	if err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
	if ret.TmsBizID == "" || ret.Data == "" || len(ret.TmsTaskTags) != 1 {
		t.Fatalf("unexpected ret: %+v", ret)
	}
}

func Test_getCrawJobTitle(t *testing.T) {
	var cj *dao.CrawJob
	seq := []gomonkey.OutputCell{
		{Values: gomonkey.Params{&dao.CrawJob{Title: "T", Ext: "E"}, nil}},
		{Values: gomonkey.Params{(*dao.CrawJob)(nil), errors.New("db")}},
	}
	patch := gomonkey.ApplyMethodSeq(reflect.TypeOf(cj), "GetCrawJobByID", seq)
	defer patch.Reset()

	title, ext := getCrawJobTitle(trpc.BackgroundContext(), 1006, 1)
	if title != "T" || ext != "E" {
		t.Fatalf("unexpected: %s %s", title, ext)
	}
	title, ext = getCrawJobTitle(trpc.BackgroundContext(), 1006, 1)
	if title != "" || ext != "" {
		t.Fatalf("expect empty on error")
	}
}

func Test_OrderResultHandler(t *testing.T) {
	ctx := trpc.BackgroundContext()

	// Case 1: invalid json
	if err := OrderResultHandler(ctx, &sarama.ConsumerMessage{Value: []byte(`{"bad"`)}); err == nil {
		t.Fatalf("expect error for invalid json")
	}

	// Common patches used by later cases
	var commonIns *dao.CommonCrawResult
	var job *dao.CrawJob
	insertPatch := gomonkey.ApplyMethod(reflect.TypeOf(commonIns), "InsertCrawResult",
		func(_ *dao.CommonCrawResult, _ context.Context, _ int) error { return nil })
	defer insertPatch.Reset()
	setStatePatch := gomonkey.ApplyMethod(reflect.TypeOf(job), "SetCrawJobState",
		func(_ *dao.CrawJob, _ context.Context) error { return nil })
	defer setStatePatch.Reset()
	getJobPatch := gomonkey.ApplyMethod(reflect.TypeOf(job), "GetCrawJobByID",
		func(_ *dao.CrawJob, _ context.Context, _ int) (*dao.CrawJob, error) {
			return &dao.CrawJob{Title: "TitleX", Ext: "ExtX"}, nil
		})
	defer getJobPatch.Reset()

	// Case 2: detail empty => early return
	comp := map[string]any{
		"data":          "",
		"tms_task_tags": []string{"douyin"},
		"tms_biz_id":    "0_1_1006_20230912112233",
	}
	b, _ := json.Marshal(comp)
	if err := OrderResultHandler(ctx, &sarama.ConsumerMessage{Value: b}); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}

	// Case 3: full success via topic-field JSON (tms_biz_id empty triggers second unmarshal)
	msgBody := map[string]any{
		"ext": map[string]any{
			"name":          "topicA",
			"play_count":    1,
			"read_count":    2,
			"discuss_count": 3,
			"ori_count":     4,
			"content_count": 5,
			"user_count":    6,
			"tms_biz_id":    "0_1_1006_20230912112233",
		},
		"source": "douyin",
		"entity": "topic",
	}
	b2, _ := json.Marshal(msgBody)
	if err := OrderResultHandler(ctx, &sarama.ConsumerMessage{Value: b2}); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}

	// Case 4: orderID <= 0 => return early
	comp2 := map[string]any{
		"data":          "{}",
		"tms_task_tags": []string{"douyin"},
		"tms_biz_id":    "0_1_0_20230912112233",
	}
	b3, _ := json.Marshal(comp2)
	if err := OrderResultHandler(ctx, &sarama.ConsumerMessage{Value: b3}); err != nil {
		t.Fatalf("unexpected err: %v", err)
	}
}
