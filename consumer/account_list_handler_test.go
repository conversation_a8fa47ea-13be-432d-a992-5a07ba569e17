package consumer

import (
	"context"
	"reflect"
	"testing"

	"github.com/IBM/sarama"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"github.com/agiledragon/gomonkey/v2"
)

func Test_parseAccountList(t *testing.T) {
	tests := []struct {
		name    string
		val     []byte
		want    *model.AccountList
		wantErr bool
	}{
		{
			name: "normal",
			val:  []byte(`{"transmit":"1_2_craw","data":"{\"url\":\"url\"}"}`),
			want: &model.AccountList{
				Transmit: "1_2_craw",
				Data:     `{"url":"url"}`,
				Info: &model.AccountInfo{
					URL: "url",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseAccountList(tt.val)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseAccountList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("parseAccountList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAccountListHandler(t *testing.T) {
	tests := []struct {
		name    string
		msg     *sarama.ConsumerMessage
		wantErr bool
	}{
		{
			name: "normal",
			msg: &sarama.ConsumerMessage{
				Value: []byte(`{"transmit":"1_2_craw","data":"{\"url\":\"url\"}"}`),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var tsk *dao.IPCrawTask
			p1 := gomonkey.ApplyMethod(reflect.TypeOf(tsk), "GetCrawTaskByID",
				func(_ *dao.IPCrawTask, ctx context.Context, id int) error { return nil })
			defer p1.Reset()
			p1b := gomonkey.ApplyMethod(reflect.TypeOf(tsk), "UpdateAccountInfo",
				func(_ *dao.IPCrawTask, _ context.Context) error { return nil })
			defer p1b.Reset()
			p2 := gomonkey.ApplyFunc(dao.CreateAccountInfo, func(ctx context.Context, a *dao.AccountInfo) error { return nil })
			defer p2.Reset()
			p3 := gomonkey.ApplyFunc(dao.AddCrawTask, func(_ context.Context, _ *dao.CrawTaskArgs, _ bool) error { return nil })
			defer p3.Reset()
			if err := AccountListHandler(trpc.BackgroundContext(), tt.msg); (err != nil) != tt.wantErr {
				t.Errorf("AccountListHandler() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
