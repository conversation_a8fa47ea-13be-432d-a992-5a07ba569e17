// Package consumer 接收抓取侧返回结果
package consumer

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
)

type processReportFunc func(ctx context.Context, err error) func()

var ProcessReport processReportFunc = func(ctx context.Context, err error) func() {
	return func() {
		var reportInfo *report.ReportCrawlTraceInfo
		if v := ctx.Value("report_info"); v != nil {
			reportInfo, _ = v.(*report.ReportCrawlTraceInfo)
		}
		if err == nil {
			reportInfo.RspCode = 0
			reportInfo.RspInfo = "ok"
		} else {
			reportInfo.RspCode = -1
			reportInfo.RspInfo = err.Error()
		}
		reportInfo.CrawlKey = getCrawKeyByID(ctx, reportInfo.Transmit)
		_, _, reportInfo.OrderID, _ = model.ExtractCrawID(reportInfo.Transmit)
		reportInfo.DoReport(ctx)
	}
}

// getCrawKeyByID 这里需要增加 根据ID 获取抓取Key的逻辑
func getCrawKeyByID(ctx context.Context, transmit string) string {
	_, crawID, orderID, _ := model.ExtractCrawID(transmit)
	if orderID == model.CPCrawOrder {
		return getCrawKeyFromCPCrawTask(ctx, crawID)
	}
	return getCrawKeyFromIPCrawTask(ctx, crawID, orderID)
}

func getCrawKeyFromCPCrawTask(ctx context.Context, crawID int) string {
	var cpCrawTask dao.CPCrawTask
	if err := cpCrawTask.GetCrawTaskByID(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "GetCrawTaskByID ERR:%+v", err)
		return ""
	}
	return cpCrawTask.AccountName
}

func getCrawKeyFromIPCrawTask(ctx context.Context, crawID, orderID int) string {
	var ipCrawTask dao.IPCrawTask
	ipCrawTask.OrderID = orderID
	if err := ipCrawTask.GetCrawTaskByID(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "GetCrawTaskByID ERR:%+v", err)
		return ""
	}
	return ipCrawTask.CrawKey
}
