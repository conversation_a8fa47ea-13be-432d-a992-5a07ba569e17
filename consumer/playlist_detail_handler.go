package consumer

import (
	"context"
	"encoding/json"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"github.com/IBM/sarama"
)

func unmarshalEpisodeDetailInfo(ctx context.Context, competitorResult CompetitorResult) (dao.EpisodeDetailInfo, error) {
	var episodeDetailInfo dao.EpisodeDetailInfo
	if err := json.Unmarshal([]byte(competitorResult.Data), &episodeDetailInfo); err != nil {
		log.ErrorContextf(ctx, "Unmarshal err-%+v", err)
		return episodeDetailInfo, err
	}
	return episodeDetailInfo, nil
}

func updateMarkTypeTxt(episodeDetailInfo *dao.EpisodeDetailInfo) {
	for i, episode := range episodeDetailInfo.Episodes {
		episodeDetailInfo.Episodes[i].MarkTypeTxt = transMarkType(episode.MarkType)
	}
}

// transMarkType 翻译角标值 （0：无 | 4：VIP | 17：预告 | 20：限免）
func transMarkType(markType int) string {
	switch markType {
	case 0:
		return "无"
	case 4:
		return "VIP"
	case 17:
		return "预告"
	case 20:
		return "限免"
	default:
		return ""
	}
}

// PlaylistDetailHandler 排播剧集详情页结果返回
func PlaylistDetailHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	res := msg.Value
	log.InfoContextf(ctx, "PlaylistDetailHandler enter-%s", res)

	competitorResult, err := unmarshalCompetitorResult(ctx, msg)
	if err != nil {
		return err
	}

	// 写入DB
	var commonCrawResult dao.CommonCrawResult
	if len(competitorResult.TmsTaskTags) > 0 {
		commonCrawResult.Platform = competitorResult.TmsTaskTags[0]
	}

	commonCrawResult.Detail = competitorResult.Data
	commonCrawResult.TransmitKey = competitorResult.TmsBizID
	commonCrawResult.CrawTime, _ = extractAndFormatDate(competitorResult.TmsBizID)
	if err := commonCrawResult.InsertCrawResult(ctx, model.PlaylistOrder); err != nil {
		return err
	}
	log.InfoContextf(ctx, "PlaylistDetailHandler suc-%+v", commonCrawResult)
	return nil
}
