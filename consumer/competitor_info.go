package consumer

import (
	"context"
	"encoding/json"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
	"github.com/IBM/sarama"
)

// CompetitorResult 抓取侧返回竞品抓取的结果
type CompetitorResult struct {
	Data        string   `json:"data"` // 竞品信息
	TmsTaskTags []string `json:"tms_task_tags"`
	TmsBizID    string   `json:"tms_biz_id"`
}

func unmarshalCompetitorResult(ctx context.Context, msg *sarama.ConsumerMessage) (CompetitorResult, error) {
	var competitorResult CompetitorResult
	if err := json.Unmarshal(msg.Value, &competitorResult); err != nil {
		log.ErrorContextf(ctx, "Unmarshal err-%+v", err)
		return competitorResult, err
	}
	return competitorResult, nil
}

// CompetitorHandler 竞品抓取
func CompetitorHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	res := msg.Value
	log.InfoContextf(ctx, "CompetitorHandler enter-%s", res)
	competitorResult, err := unmarshalCompetitorResult(ctx, msg)
	if err != nil {
		return err
	}

	var competitorInfo report.ReportCompetitor
	if err := json.Unmarshal([]byte(competitorResult.Data), &competitorInfo); err != nil {
		log.ErrorContextf(ctx, "Unmarshal err-%+v", err)
		return err
	}
	log.InfoContextf(ctx, "CompetitorHandler suc-%+v", competitorInfo)
	competitorInfo.DoReport(ctx) // 当前仅上报ATTA即可
	return nil
}
