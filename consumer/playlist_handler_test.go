package consumer

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"github.com/IBM/sarama"
	"github.com/agiledragon/gomonkey/v2"
)

func TestFormatDate(t *testing.T) {
	currentYear := time.Now().Year()

	testCases := []struct {
		monthDay string
		expected string
		err      error
	}{
		{"09-04", fmt.Sprintf("%d-09-04", currentYear), nil},
		{"02-29", "", fmt.Errorf("parsing time")},
		{"invalid", "", fmt.Errorf("parsing time")},
	}

	for _, testCase := range testCases {
		result, err := formatDate(testCase.monthDay)
		if result != testCase.expected || (err != nil && testCase.err == nil) || (err == nil && testCase.err != nil) {
			t.<PERSON>("formatDate(%q) = %q, %v; want %q, %v", testCase.monthDay, result, err, testCase.expected, testCase.err)
		}
	}
}

func TestFormatTime(t *testing.T) {
	testCases := []struct {
		hourMinute string
		expected   string
		err        error
	}{
		{"19:30", "19:30:00", nil},
		{"00:00", "00:00:00", nil},
		{"23:59", "23:59:00", nil},
		{"invalid", "", fmt.Errorf("parsing time")},
		{"25:00", "", fmt.Errorf("parsing time")},
	}

	for _, testCase := range testCases {
		result, err := formatTime(testCase.hourMinute)
		if result != testCase.expected || (err != nil && testCase.err == nil) || (err == nil && testCase.err != nil) {
			t.Errorf("formatTime(%q) = %q, %v; want %q, %v", testCase.hourMinute, result, err, testCase.expected, testCase.err)
		}
	}
}

func TestPlaylistHandler(t *testing.T) {

	tests := []struct {
		name    string
		msg     *sarama.ConsumerMessage
		wantErr bool
	}{
		{
			name: "normal",
			msg: &sarama.ConsumerMessage{
				Value: []byte(`{"transmit":"1_2_craw","data":"{\"url\":\"url\"}"}`),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var p *dao.PlaylistInfo
			patch := gomonkey.ApplyMethod(reflect.TypeOf(p), "InsertPlaylistInfo",
				func(_ *dao.PlaylistInfo, ctx context.Context) error { return nil })
			defer patch.Reset()
			if err := PlaylistHandler(trpc.BackgroundContext(), tt.msg); (err != nil) != tt.wantErr {
				t.Errorf("PlaylistHandler() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
