package consumer

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/logic"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"

	"github.com/IBM/sarama"
)

// AccountListHandler 账号抓取第一阶段回调接口
func AccountListHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	accountInfo, err := parseAccountList(msg.Value)
	if err != nil {
		log.ErrorContextf(ctx, "parseAccountList err[%v]", err)
		return nil
	}
	crawType, crawID, _, err := model.ExtractCrawID(accountInfo.Transmit)
	if err != nil {
		log.InfoContextf(ctx, "old craw[%v]", accountInfo)
		return nil
	}
	var ipCrawTask dao.IPCrawTask
	if err = ipCrawTask.GetCrawTaskByID(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "GetCrawTaskByID error: %+v", err)
		return err
	}

	// 记录首次搜索结果
	if err = dao.CreateAccountInfo(ctx, &dao.AccountInfo{
		IPID:    crawID,
		MID:     accountInfo.Info.MID,
		Avatar:  accountInfo.Info.Avatar,
		Author:  accountInfo.Info.Author,
		URL:     accountInfo.Info.URL,
		Rank:    accountInfo.Info.Rank,
		CrawKey: ipCrawTask.CrawKey,
		CrawCID: ipCrawTask.CoverID,
	}); err != nil {
		log.ErrorContextf(ctx, "CreateAccountInfo error[%v]", err)
		return err
	}

	// 只抓取和更新排名第一的
	if accountInfo.Info.Rank != 1 {
		return nil
	}
	ipCrawTask.Nick, ipCrawTask.Avatar, ipCrawTask.AccountID, ipCrawTask.PageURL =
		accountInfo.Info.Author, accountInfo.Info.Avatar, accountInfo.Info.MID, accountInfo.Info.URL
	if err = ipCrawTask.UpdateAccountInfo(ctx); err != nil {
		log.ErrorContextf(ctx, "UpdateAccountInfo err[%v]", err)
		return err
	}

	if err = dao.AddCrawTask(trpc.CloneContext(ctx), &dao.CrawTaskArgs{
		CrawType: crawType,
		Transmit: fmt.Sprintf("%d_%d_craw", ipCrawTask.CrawType, ipCrawTask.ID),
		Source:   ipCrawTask.Platform,
		URL:      accountInfo.Info.URL,
		CrawTxt:  accountInfo.Info.Author,
		BacktraceDays: logic.CalcBacktraceDays(model.OfficeAccountType, ipCrawTask.ConfirmStatus,
			ipCrawTask.BacktrackStatus, ipCrawTask.CreateTime),
	}, false); err != nil {
		log.ErrorContextf(ctx, "AddCrawTask ERR:%+v", err)
		return err
	}
	return nil
}

func parseAccountList(val []byte) (*model.AccountList, error) {
	accountList := &model.AccountList{}
	if err := json.Unmarshal(val, accountList); err != nil {
		return nil, fmt.Errorf("unmarshal account list err[%v] val[%s]", err, val)
	}
	if accountList.Data == "" {
		return nil, fmt.Errorf("empty data[%s]", val)
	}
	if accountList.Transmit == "" {
		return nil, fmt.Errorf("empty transmit")
	}
	accountList.Info = &model.AccountInfo{}
	if err := json.Unmarshal([]byte(accountList.Data), accountList.Info); err != nil {
		return nil, fmt.Errorf("unmarshal info err[%v] data[%v]", err, accountList.Data)
	}
	log.InfoContextf(trpc.BackgroundContext(), "accountInfo[%s]", accountList.String())
	return accountList, nil
}
