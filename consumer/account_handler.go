package consumer

import (
	"context"
	"encoding/json"

	"github.com/IBM/sarama"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/logic"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
)

// AccountHandler 抓取侧返回的 账号信息回调接口
func AccountHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	res := msg.Value
	log.InfoContextf(ctx, "AccountHandler enter-%s", res)
	var err error
	var reportInfo report.ReportCrawlTraceInfo
	reportInfo.ReqInfo = string(res)
	ctx = context.WithValue(ctx, "report_info", &reportInfo)
	defer ProcessReport(ctx, err)()

	accountInfo, transmit, err := parseAccountInfo(res)
	if err != nil {
		log.ErrorContextf(ctx, "Unmarshal error: %+v", err)
		return err
	}

	if transmit == "" {
		log.ErrorContextf(ctx, "transmit is empty-%s", res)
		return nil
	}

	crawType, crawID, orderID, err := model.ExtractCrawID(transmit)
	if err != nil {
		log.InfoContextf(ctx, "old craw[%s]", res)
		return nil
	}
	c := createAccountCrawler(crawType, orderID, accountInfo)
	ctx = prepareAccountReportInfo(ctx, &accountInfo, transmit, crawType)
	if err = c.CrawlerRetAccountCallBack(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "CrawlerRetArticleCallBack ERR:%+v", err)
		return nil
	}
	log.InfoContextf(ctx, "AccountHandler suc-%+v", accountInfo)
	return nil
}

func parseAccountInfo(res []byte) (report.ReportAccountInfo, string, error) {
	var accountInfo report.ReportAccountInfo
	if err := json.Unmarshal(res, &accountInfo); err != nil {
		return report.ReportAccountInfo{}, "", err
	}
	return accountInfo, accountInfo.StaticInfo.Transmit, nil
}

func prepareAccountReportInfo(ctx context.Context, accountInfo *report.ReportAccountInfo,
	transmit string, crawType int) context.Context {
	var reportInfo report.ReportCrawlTraceInfo
	reportInfo.CrawlSource = accountInfo.AccountPlatform
	reportInfo.CrawlType = crawType
	reportInfo.MsgType = model.ConsumerReport
	reportInfo.Transmit = transmit
	ctx = context.WithValue(ctx, "report_info", &reportInfo)
	return ctx
}

func createAccountCrawler(crawType, orderID int, accountInfo report.ReportAccountInfo) logic.Crawler {
	if crawType == model.CPInfoType {
		return logic.NewCPCrawler(dao.CPCrawTask{}, report.ReportArticleInfo{}, accountInfo)
	}
	if crawType == model.OfficeAccountType || crawType == model.TopicType {
		return logic.NewIPCrawler(dao.IPCrawTask{OrderID: orderID}, report.ReportArticleInfo{},
			accountInfo, report.ReportTopicFieldInfo{})
	}
	return nil
}
