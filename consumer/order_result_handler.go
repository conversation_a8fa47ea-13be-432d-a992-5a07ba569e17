package consumer

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"github.com/IBM/sarama"
)

func extractAndFormatDate(input string) (string, error) {
	// 从输入字符串中提取最后一个下划线后的内容
	parts := strings.Split(input, "_")
	if len(parts) < 2 {
		return "", fmt.Errorf("invalid input format")
	}
	dateString := parts[len(parts)-1]

	// 将提取的字符串转换为时间日期格式
	date, err := time.Parse("20060102150405", dateString)
	if err != nil {
		return "", err
	}
	return date.Format("2006-01-02 15:04:05"), nil
}

// unmarshalTopicFieldResult 解析话题详情的结果
func unmarshalTopicFieldResult(ctx context.Context, msg *sarama.ConsumerMessage) (CompetitorResult, error) {
	var topicFieldInfo struct {
		Ext struct {
			TopicName    string `json:"name"`
			PlayCount    int    `json:"play_count"`
			ReadCount    int    `json:"read_count"`
			DiscussCount int    `json:"discuss_count"`
			OriCount     int    `json:"ori_count"`
			ContentCount int    `json:"content_count"`
			UserCount    int    `json:"user_count"` // 参与人数
			BizID        string `json:"tms_biz_id"`
		} `json:"ext"`
		Source string `json:"source"`
		Entity string `json:"entity"`
	}
	if err := json.Unmarshal(msg.Value, &topicFieldInfo); err != nil {
		return CompetitorResult{}, err
	}

	topicFields, _ := json.Marshal(topicFieldInfo.Ext)
	return CompetitorResult{
		TmsBizID:    topicFieldInfo.Ext.BizID,
		TmsTaskTags: []string{topicFieldInfo.Source},
		Data:        string(topicFields),
	}, nil
}

// OrderResultHandler 通用的抓取结果处理器(消费抓取侧kafka)
func OrderResultHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	res := msg.Value
	log.InfoContextf(ctx, "OrderResultHandler enter-%s", res)
	competitorResult, err := unmarshalCompetitorResult(ctx, msg)
	if err != nil {
		return err
	}
	if competitorResult.TmsBizID == "" { // 使用新的话题结果结构解析
		competitorResult, err = unmarshalTopicFieldResult(ctx, msg)
	}

	// 写入DB-抓取结果表
	var commonCrawResult dao.CommonCrawResult
	if len(competitorResult.TmsTaskTags) > 0 {
		commonCrawResult.Platform = competitorResult.TmsTaskTags[0]
	}
	commonCrawResult.Detail = competitorResult.Data
	commonCrawResult.TransmitKey = competitorResult.TmsBizID
	commonCrawResult.CrawTime, _ = extractAndFormatDate(competitorResult.TmsBizID)
	_, jobID, orderID, _ := model.ExtractCrawID(competitorResult.TmsBizID)
	if orderID <= 0 {
		return nil
	}
	commonCrawResult.Title, commonCrawResult.Ext = getCrawJobTitle(ctx, orderID, jobID)
	if commonCrawResult.Detail == "" { // 返回详情结果为空的，不处理
		return nil
	}
	if err = commonCrawResult.InsertCrawResult(ctx, orderID); err != nil {
		return err
	}

	// 设置抓取job成功
	var crawJob dao.CrawJob
	crawJob.OrderID = orderID
	crawJob.ID = jobID
	crawJob.DayCrawlerState = model.CrawlerSuc
	if err = crawJob.SetCrawJobState(ctx); err != nil {
		log.ErrorContextf(ctx, "SetCrawJobState ERR:%+v", err)
	}
	log.InfoContextf(ctx, "OrderResultHandler suc-%+v", commonCrawResult)
	return nil
}

func getCrawJobTitle(ctx context.Context, orderID, crawJobID int) (string, string) {
	var crawJob dao.CrawJob
	crawJob.OrderID = orderID
	retCrawJob, err := crawJob.GetCrawJobByID(ctx, crawJobID)
	if err != nil {
		log.ErrorContextf(ctx, "GetCrawJobByID ERR:%+v", err)
		return "", ""
	}
	return retCrawJob.Title, retCrawJob.Ext
}
