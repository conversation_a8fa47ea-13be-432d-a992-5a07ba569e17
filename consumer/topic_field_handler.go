package consumer

import (
	"context"
	"encoding/json"

	"github.com/IBM/sarama"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/dao"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/logic"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/model"
	"git.code.oa.com/video_media/crawler_schedule/outer_circulation_monitor/report"
)

// TopicFieldHandler 接收话题属性抓取信息
func TopicFieldHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	res := msg.Value
	log.InfoContextf(ctx, "TopicFieldHandler enter-%s", res)
	var err error
	// 爬虫侧返回的抓取结果信息结构
	reportTopicFieldInfo, transmit, err := parseTopicFieldInfo(res)
	if err != nil {
		log.ErrorContextf(ctx, "parseTopicFieldInfo err[%v] res[%s]", err, res)
		return err
	}
	if transmit == "" {
		log.ErrorContextf(ctx, "transmit is empty-%s", res)
		return nil
	}
	if model.IsNewFrameResult(transmit) { // 新链路的返回结果，走新处理入口
		return OrderResultHandler(ctx, msg)
	}

	var reportInfo report.ReportCrawlTraceInfo
	reportInfo.ReqInfo = string(res)
	ctx = context.WithValue(ctx, "report_info", &reportInfo)
	defer ProcessReport(ctx, err)()
	reportInfo.MsgType = model.ConsumerReport
	reportInfo.Transmit = transmit
	ctx = context.WithValue(ctx, "report_info", &reportInfo)
	var c logic.Crawler
	crawType, crawID, orderID, err := model.ExtractCrawID(transmit)
	if err != nil {
		log.InfoContextf(ctx, "old craw[%s]", res)
		return nil
	}
	reportInfo.CrawlType = crawType
	c = logic.NewIPCrawler(dao.IPCrawTask{OrderID: orderID},
		report.ReportArticleInfo{}, report.ReportAccountInfo{}, reportTopicFieldInfo)
	if err = c.CrawlerRetTopicFieldCallBack(ctx, crawID); err != nil {
		log.ErrorContextf(ctx, "CrawlerRetTopicFieldCallBack ERR:%+v", err)
		return nil
	}
	log.InfoContextf(ctx, "TopicFieldHandler suc")
	return nil
}

func parseTopicFieldInfo(res []byte) (report.ReportTopicFieldInfo, string, error) {
	var resInfo struct {
		Source string `json:"source"`
		Entity string `json:"entity"`
	}
	if err := json.Unmarshal(res, &resInfo); err != nil {
		return report.ReportTopicFieldInfo{}, "", err
	}

	// 先用旧格式解码，若解析失败，则用新格式
	var err error
	var transmitID string
	var topicFieldInfo report.ReportTopicFieldInfo
	topicFieldInfo, transmitID, err = parseTopicFieldInfoOldVersion(res)
	if transmitID == "" {
		topicFieldInfo, transmitID, err = parseTopicFieldInfoNewVersion(res)
	}
	return topicFieldInfo, transmitID, err
}

// parseTopicFieldInfoOldVersion 旧版的topic信息
func parseTopicFieldInfoOldVersion(res []byte) (report.ReportTopicFieldInfo, string, error) {
	var topicFieldInfo struct {
		Ext struct {
			TopicName    string `json:"name"`
			PlayCount    int    `json:"play_count"`
			ReadCount    int    `json:"read_count"`
			DiscussCount int    `json:"discuss_count"`
			OriCount     int    `json:"ori_count"`
			ContentCount int    `json:"content_count"`
		} `json:"ext"`
		Transmit string `json:"transmit"`
		Source   string `json:"source"`
		Status   int    `json:"status"`
		BizID    string `json:"tms_biz_id"`
		Entity   string `json:"entity"`
	}
	if err := json.Unmarshal(res, &topicFieldInfo); err != nil {
		return report.ReportTopicFieldInfo{}, "", err
	}

	transmitID := topicFieldInfo.Transmit
	if transmitID == "" {
		transmitID = topicFieldInfo.BizID
	}
	var reportTopicFieldInfo report.ReportTopicFieldInfo
	if topicFieldInfo.Status != 0 {
		reportTopicFieldInfo.RetCrawlStatus = model.CrawlerFail
		if topicFieldInfo.Status == 18 {
			reportTopicFieldInfo.RetCrawlStatus = model.CrawlerSuc
		}
		return reportTopicFieldInfo, transmitID, nil
	}

	reportTopicFieldInfo = report.ReportTopicFieldInfo{
		PlayCount:      topicFieldInfo.Ext.PlayCount,
		Transmit:       transmitID,
		CrawlSource:    topicFieldInfo.Source,
		ReadCount:      topicFieldInfo.Ext.ReadCount,
		TalkCount:      topicFieldInfo.Ext.DiscussCount,
		OriginalNum:    topicFieldInfo.Ext.OriCount,
		RelateVideoNum: topicFieldInfo.Ext.ContentCount,
		RetCrawlStatus: model.CrawlerSuc,
	}
	return reportTopicFieldInfo, transmitID, nil
}

// parseTopicFieldInfoNewVersion 新版的topic信息
func parseTopicFieldInfoNewVersion(res []byte) (report.ReportTopicFieldInfo, string, error) {
	var topicFieldInfo struct {
		Ext struct {
			TopicName    string `json:"name"`
			PlayCount    int    `json:"play_count"`
			ReadCount    int    `json:"read_count"`
			DiscussCount int    `json:"discuss_count"`
			OriCount     int    `json:"ori_count"`
			ContentCount int    `json:"content_count"`
			UserCount    int    `json:"user_count"` // 参与人数
			BizID        string `json:"tms_biz_id"`
		} `json:"ext"`
		Source string `json:"source"`
		Entity string `json:"entity"`
	}
	if err := json.Unmarshal(res, &topicFieldInfo); err != nil {
		return report.ReportTopicFieldInfo{}, "", err
	}

	transmitID := topicFieldInfo.Ext.BizID
	var reportTopicFieldInfo report.ReportTopicFieldInfo
	reportTopicFieldInfo = report.ReportTopicFieldInfo{
		PlayCount:      topicFieldInfo.Ext.PlayCount,
		Transmit:       transmitID,
		CrawlSource:    topicFieldInfo.Source,
		ReadCount:      topicFieldInfo.Ext.ReadCount,
		TalkCount:      topicFieldInfo.Ext.DiscussCount,
		OriginalNum:    topicFieldInfo.Ext.OriCount,
		RelateVideoNum: topicFieldInfo.Ext.ContentCount,
		RetCrawlStatus: model.CrawlerSuc,
	}
	return reportTopicFieldInfo, transmitID, nil
}
