// Package errcodes 通用错误码定义
package errcodes

const (
	ERROR_OK              = 0    /**< 成功 */
	ERR_PARSE_JSON_FAIL   = 1001 /**< 解析json异常 */
	ERR_INVALID_PARAM     = 1002 /**< 常规参数错误 */
	ERR_SEND_REQUEST      = 2001 /**< 发送请求失败 */
	ERR_REQ_MEDIAAPI_FAIL = 2002 /**< 请求媒资接口失败 */
	ERR_REQ_THIRDSRV_FAIL = 2003 /**< 请求第三方服务失败 */
	ERR_INNER_FAIL        = 3001 /**< 内部失败 */
)

const (
	UnKnowErr = "未标识的错误"
)

// errorMsg 获取错误码详细描述
var errorMsg = map[int]string{
	ERROR_OK:              "成功",
	ERR_PARSE_JSON_FAIL:   "json unmarshal error.",
	ERR_INVALID_PARAM:     "常规参数错误!",
	ERR_SEND_REQUEST:      "发送请求失败.",
	ERR_REQ_MEDIAAPI_FAIL: "请求媒资接口失败.",
	ERR_REQ_THIRDSRV_FAIL: "请求第三方服务失败.",
	ERR_INNER_FAIL:        "内部失败.",
}

// GetErrMsg 翻译错误码
func GetErrMsg(errCode int) string {
	msg, ok := errorMsg[errCode]
	if !ok {
		return UnKnowErr
	}
	return msg
}
